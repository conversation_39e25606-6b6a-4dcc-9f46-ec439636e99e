<script setup lang="ts">
  import { ElSelect, ElOption } from 'element-sun';
  import { ref, computed, watch, toRef, Ref, useAttrs } from 'vue';
  import {
    ProTable,
    type AnyObject,
    type ColumnProps,
    useDirectionSelect,
  } from 'sun-biz';

  const { rowKey, columns, data, placeholder, selectLabel, loading } =
    defineProps<{
      selectLabel: string;
      rowKey: string;
      columns: ColumnProps[];
      data: AnyObject[];
      placeholder: string;
      loading: boolean;
    }>();

  const emit = defineEmits(['change']);

  const currentRow = ref(); /** 选中行 */
  const selectRef = ref(); /** select选择器 */
  const tableRef = ref(); /** table表格 */

  const tableData = toRef(() => data);
  const attrs = useAttrs();

  /** 获取select的input */
  const selectInputRef = computed(() => {
    if (selectRef?.value) {
      return selectRef.value?.$refs.inputRef;
    }
    return selectRef?.value;
  });

  /** 设置选中行 */
  const setCurrentRow = (row?: unknown) => {
    currentRow.value = row;
    if (row) {
      tableRef.value?.eleTable.setCurrentRow(row);
    } else {
      tableRef.value?.eleTable.clearSelection();
    }
  };

  /** 清空 */
  const handleClear = () => {
    currentRow.value = undefined; // 清空选中的行
    selectRef.value?.blur();
  };

  /** 监听键盘方向事件 支持方向盘上下切换table 目标行 */
  useDirectionSelect({
    triggerRef: selectInputRef,
    rowKey: rowKey,
    data: tableData as Ref<AnyObject[]>,
    activeItem: currentRow,
    setCurrentItem: setCurrentRow,
    scrollTo: (params) => {
      tableRef?.value!.eleTable.scrollTo(params);
    },
    enter: () => {
      tableRef.value?.eleTable.clearSelection();
    },
  });

  watch(
    () => currentRow.value,
    async () => {
      emit('change', currentRow.value);
    },
  );

  defineExpose({
    clear: handleClear,
    setCurrentRow,
  });
</script>
<template>
  <el-select
    ref="selectRef"
    :clearable="true"
    :model-value="currentRow"
    :placeholder="placeholder"
    @clear="handleClear"
    v-bind="{ ...attrs }"
  >
    <template #label>
      <el-tag v-if="currentRow" type="primary" effect="light">
        {{ selectLabel }}
      </el-tag>
    </template>
    <el-option
      :value="currentRow ?? ''"
      class="option-style flex flex-col p-2 !font-normal"
      style="height: 260px; overflow: hidden; background: #fff"
    >
      <slot></slot>
      <ProTable
        :loading="loading"
        ref="tableRef"
        :columns="columns"
        :data="data"
        :row-key="rowKey"
        :pagination="false"
        highlight-current-row
        @row-click="
          (row: AnyObject) => {
            currentRow = row;
          }
        "
      />
    </el-option>
  </el-select>
</template>
