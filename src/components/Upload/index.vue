<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { ElMessage } from 'element-sun';
  import { Upload } from '@element-sun/icons-vue';
  import { generateUUID } from '@sun-toolkit/shared';
  const { maxSize = 1024, desc } = defineProps<{
    maxSize?: number;
    desc: string;
  }>();
  const emits = defineEmits<{
    success: [url: string];
    change: [data?: string];
  }>();
  const modelValue = defineModel<string>();
  const preview = ref<string | null>(null);
  const dialogVisible = ref(false);
  const id = generateUUID();
  const getMime = (base64: string) => {
    if (base64) {
      const match = base64.match(/^data:(.*?);base64,/);
      return match ? match[1] : 'image/png';
    }
    return undefined;
  };

  const maxSizeMessage = computed(() => {
    if (maxSize < 1024) {
      return `图片大小不能超过 ${maxSize}KB`;
    } else {
      return `图片大小不能超过 ${(maxSize / 1024).toFixed(2)}MB`;
    }
  });

  function isBase64DataUrl(str: string): boolean {
    const regex = /^data:(.*);base64,[A-Za-z0-9+/=]+$/;
    return regex.test(str);
  }
  function base64ToUrl(base64: string, mime?: string): string {
    if (!isBase64DataUrl(base64)) {
      return '';
    }
    if (!mime) {
      mime = getMime(base64);
    }
    const byteString = atob(base64.split(',')[1]);
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([ab], { type: mime });
    return URL.createObjectURL(blob);
  }

  const setPreviewUrl = (base64: string) => {
    preview.value = base64ToUrl(base64);
  };
  /** 设置base64 值 */
  function setBase64(base64: string) {
    if (base64) {
      modelValue.value = base64;
      emits('change', base64);
    }
  }

  function onFileChange(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const MAX_SIZE = maxSize * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      ElMessage.warning({
        message: maxSizeMessage.value,
        showClose: true,
      });
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      setBase64(reader.result as string);
    };
    reader.readAsDataURL(file);
  }

  const handleDelete = () => {
    modelValue.value = '';
  };

  watch(
    () => modelValue?.value,
    (val) => {
      if (val) {
        setPreviewUrl(val);
      }
    },
    {
      immediate: true,
    },
  );
</script>

<template>
  <div>
    <template v-if="!modelValue || !preview">
      <!-- 自定义按钮，实际点击 input -->
      <label
        :for="id"
        style="width: 254px"
        class="inline-flex cursor-pointer items-center rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 px-4 py-1 font-medium text-white shadow transition hover:opacity-90"
      >
        <el-icon size="18"><Upload /></el-icon>
        选择图片 <sub>({{ maxSizeMessage }})</sub>
      </label>

      <!-- 隐藏原 input -->
      <input
        :id="id"
        type="file"
        accept="image/*"
        class="hidden"
        @change="onFileChange"
      />
    </template>
    <template v-if="modelValue && preview">
      <div class="group relative inline-block w-16 overflow-hidden bg-gray-500">
        <img :src="preview" class="w-full" alt="" />
        <!-- 删除按钮：默认隐藏，hover 显示 -->
        <div
          class="absolute left-0 right-0 top-0 h-full w-16 bg-gray-700 bg-opacity-60 opacity-0 shadow transition group-hover:opacity-100"
        >
          <el-button
            @click="
              () => {
                dialogVisible = true;
              }
            "
            link
            class="text-xs absolute right-4 top-2 transition"
          >
            <span class="text-white"> 查看 </span>
          </el-button>
        </div>
      </div>
      <el-button
        @click="handleDelete"
        type="danger"
        link
        class="text-xs mx-4 mb-2 inline-block text-white"
      >
        删除
      </el-button>
    </template>
  </div>

  <el-dialog :title="desc" v-model="dialogVisible">
    <div class="bg-gray-700">
      <img
        w-full
        v-if="preview"
        style="width: 100%"
        :src="preview"
        alt="Preview Image"
      />
    </div>
  </el-dialog>
</template>
