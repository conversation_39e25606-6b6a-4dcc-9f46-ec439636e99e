import { pluginMainRequest } from '@/utils/request';
import { dictRequest } from '@sun-toolkit/request';
import { CodeSystem } from '@/typings/codeManage';
import { FLAG } from '@/utils/constant';
import {
  UserReqItem,
  UserReqParams,
  TenantResItem,
  PrinterResItem,
  InterfaceReqItem,
  InterfaceReqParams,
  ComputerInfoResItem,
  SaveReceiptXPrinterResParams,
  GetReceiptXPrinterResParams,
  GetReceiptXPrinterResItem,
  UpdateUserEnabledFlagReqParams,
  DesignPreviewPrintReceiptResParams,
  DesignPreviewPrintReceiptResItem,
} from './types';

/**
 * [1-10012-1]获取值域列表
 * @param data
 * @returns
 */
export const queryDataSetByCodeSystemCodes2 = <
  T extends readonly string[],
>(params: {
  codeSystemCodes: readonly string[];
  enabledFlag?: number;
}) => {
  return dictRequest<
    { [P in T[number]]: CodeSystem[] },
    {
      codeSystemCodes: readonly string[];
    }
  >('/codeSystem/queryDataSetByCodeSystemCodes', params, {
    cancel: false,
  });
};

/**
 * [1-10013-1]根据条件查询用户列表
 * @param params
 * @returns
 */
export const queryUserList = (params: UserReqParams) => {
  return dictRequest<UserReqItem, UserReqParams>(
    '/user/queryUserListByExample',
    params,
  );
};

/**
 * [1-10016-1]根据标识停启用用户
 * @param params
 * @returns
 */
export const updateEnabledFlagById = (
  params: UpdateUserEnabledFlagReqParams,
) => {
  return dictRequest('/user/updateEnabledFlagById', params);
};

/**
 * [1-10140-1]根据条件查询组织列表（平铺)
 * @param params
 * @returns
 */
export const queryFlatOrgList = (params: Partial<Org.FlatOrgReqParams>) => {
  return dictRequest<Org.FlatOrgReqItem[], Partial<Org.FlatOrgReqParams>>(
    '/organization/queryOrgListByExampleFlat',
    params,
  );
};

/**
 * 根据条件查询科室列表
 * @param params
 * @returns
 */
export const queryDepartmentList = (
  params: Partial<Org.queryReqParams> & { hospitalId: string },
) => {
  return dictRequest<
    Org.Item[],
    Partial<Org.queryReqParams> & { hospitalId: string }
  >('/organization/queryDepartmentListByExample', params);
};

/**
 * 根据条件查询租户列表
 */
export const queryTenantList = (params = { enabledFlag: FLAG.YES }) => {
  return dictRequest<TenantResItem[]>(
    '/tenant/queryTenantListByExample',
    params,
  );
};

/**
 * [1-10137-1]根据条件查询接口列表
 * @param params
 * @returns
 */
export const queryInterfaceListByExample = (params: InterfaceReqParams) => {
  return dictRequest<InterfaceReqItem[]>(
    '/interface/queryInterfaceListByExample',
    params,
    { cancel: false },
  );
};

// 调用webView壳子相关方法
/**
 * [97-10001-1]查询可用的打印机列表
 * @param params
 * @returns
 */
export const getPrinterList = (params = {}) => {
  return pluginMainRequest<PrinterResItem[]>('getPrinterList', params);
};

/**
 * [97-10002-1]获取计算机信息（IP/MAC/机器名）
 * @param params
 * @returns
 */
export const getComputerInfo = (params = {}) => {
  return pluginMainRequest<ComputerInfoResItem>('getComputerInfo', params);
};

/**
 * [97-10003-1]保存单据的打印机
 * @param params
 * @returns
 */
export const saveReceiptXPrinter = (params: SaveReceiptXPrinterResParams) => {
  return pluginMainRequest('saveReceiptXPrinter', params);
};

/**
 * [97-10004-1]获取单据的打印机列表
 * @param params
 * @returns
 */
export const getReceiptXPrinter = (params: GetReceiptXPrinterResParams) => {
  return pluginMainRequest<GetReceiptXPrinterResItem[]>(
    'getReceiptXPrinter',
    params,
  );
};

/**
 * [97-10005-1]设计/预览/打印单据内容
 * @param params
 * @returns
 */
export const designPreviewPrintReceipt = (
  params: DesignPreviewPrintReceiptResParams,
) => {
  return pluginMainRequest<DesignPreviewPrintReceiptResItem[]>(
    'designPreviewPrintReceipt',
    params,
  );
};
/**
 * [97-10005-1]设计/预览/打印单据内容
 * @param params
 * @returns
 */
export const openReportFile = () => {
  return pluginMainRequest<{ fileName: string }>('openReportFile', {});
};

/**
 * [1-10240-1]列表排序
 * @param params
 * @returns
 */
export const commonSort = (params: {
  bizIdTypeCode: string;
  bizIdList: { bizId: string; sort: number }[];
}) => {
  return dictRequest('/dictCommon/sort', params, {
    cancel: false,
  });
};
