export enum CodeSystemType {
  'HBI000.4001' = 'HBI000.4001', // 数据库类型
  'COMMODITY_CATEGORY_WAY_CODE' = 'COMMODITY_CATEGORY_WAY_CODE', // 商品费用分类方式代码
  'MENU_SOURCE_CODE' = 'MENU_SOURCE_CODE', // 菜单来源代码
  'PARAM_INFLUENCE_SCOPE_CODE' = 'PARAM_INFLUENCE_SCOPE_CODE', // 参数影响范围代码
  'COMPONENT_CODE' = 'COMPONENT_CODE', // 组件代码
  'SEARCH_TYPE_CODE' = 'SEARCH_TYPE_CODE', // 检索方式代码
  'TRIGGER_TYPE_CODE' = 'TRIGGER_TYPE_CODE', // 触发方式代码
  'INDEX_TYPE_CODE' = 'INDEX_TYPE_COD', // 索引类型代码
  'REASON_USE_SCOPE_CODE' = 'REASON_USE_SCOPE_CODE', // 原因应用范围代码
  'BIZ_NO_OBJECT_CODE' = 'BIZ_NO_OBJECT_CODE', // 业务编码对象代码
  'VALUE_TYPE_CODE' = 'VALUE_TYPE_CODE', // 值类型代码
  'FULL_TYPE_CODE' = 'FULL_TYPE_CODE', // 补位方式代码
  'RESET_TYPE_CODE' = 'RESET_TYPE_CODE', // 重置方式代码
  'BASIC_DATA_SOURCE_CODE' = 'BASIC_DATA_SOURCE_CODE', // 基础数据来源代码
  'HIS_BASIC_DATA_TYPE_CODE' = 'HIS_BASIC_DATA_TYPE_CODE', // HIS基础数据分类代码
  'DATA_DOWNLOAD_STATUS_CODE' = 'DATA_DOWNLOAD_STATUS_CODE', // 数据下载状态代码
  'CODE_REPOSITORY_TYPE_CODE' = 'CODE_REPOSITORY_TYPE_CODE', // 代码仓库类型代码
  'ORG_TYPE_CODE' = 'ORG_TYPE_CODE', // 组织类型
  'COMMODITY_CLASS_CODE' = 'COMMODITY_CLASS_CODE', // 商品大类
  'TRIGGER_PERIOD_CODE' = 'TRIGGER_PERIOD_CODE', // 定时任务触发周期代码
  'WORK_ITEM_TYPE_CODE' = 'WORK_ITEM_TYPE_CODE', // 工作项类型代码
  'WI_DESC_TYPE_CODE' = 'WI_DESC_TYPE_CODE', // 工作项描述类型代码
  'CODE_BRANCH_TYPE_CODE' = 'CODE_BRANCH_TYPE_CODE', // 代码分支类型代码
  'MANAGE_OBJECT_TYPE_CODE' = 'MANAGE_OBJECT_TYPE_CODE', // 对象类型代码
  'RULE_EXECUTE_WAY_CODE' = 'RULE_EXECUTE_WAY_CODE', // 规则执行方式代码
  'MANAGE_RULE_SCOPE_CODE' = 'MANAGE_RULE_SCOPE_CODE', // 应用范围代码
  'MANAGE_RULE_TYPE_CODE' = 'MANAGE_RULE_TYPE_CODE', // 规则类型代码
  'TEMP_VARIABLE_TYPE_CODE' = 'TEMP_VARIABLE_TYPE_CODE', // 模板变量类型代码
  'TEMP_VARIABLE_SCOPE_CODE' = 'TEMP_VARIABLE_SCOPE_CODE', // 模板变量应用范围代码
  'DEDUCT_TYPE_CODE' = 'DEDUCT_TYPE_CODE', //扣分类型代码
  'HOLIDAY_CODE' = 'HOLIDAY_CODE', // 节假日代码
  'HOLIDAY_TYPE_CODE' = 'HOLIDAY_TYPE_CODE', // 节假日类型代码
}

export interface CodeSystem {
  createdOrgLocationId: string;
  createdOrgLocationName: string | null;
  createdUserId: string;
  createdUserName: string | null;
  createdAt: string;
  modifiedOrgLocationId: string;
  modifiedOrgLocationName: string | null;
  modifiedUserId: string;
  modifiedUserName: string | null;
  modifiedAt: string;
  dataValueId: string;
  dataValueNo: string;
  dataValueCnName: string;
  dataValue2ndName: string;
  dataValueExtName: string | null;
  dataValueNameDisplay: string | null;
  dataValueDescription: string | null;
  spellNo: string | null;
  wbNo: string | null;
  conceptId: string | null;
  conceptDescription: string | null;
  enabledFlag: number;
  sort: number;
}

export type DataSetObj = {
  [key in CodeSystemType]?: CodeSystem[];
};

export type SystemInfo = {
  sysId: string;
  enabledFlag: number;
  sysName: string;
  sys2ndName: string;
  sysExtName: string;
  sysNameDisplay: string;
  url: string;
  iconUri: string;
  sort: number;
  spellNo: string;
  wbNo: string;
  accessFlag: number;
  createdOrgLocationId: string;
  createdOrgLocationName: string;
  createdUserId: string;
  createdUserName: string;
  createdAt: string;
  modifiedOrgLocationId: string;
  modifiedOrgLocationName: string;
  modifiedUserId: string;
  modifiedUserName: string;
  modifiedAt: string;
  devGroupCode: string;
  devGroupCodeDesc: string;
  mdmApiUrl: string;
  codeRepositoryList: {
    systemDefId: string;
    codeRepositoryId: string;
    codeRepositoryName: string;
    codeRepositoryDesc: string;
    codeRepositoryTypeCode: string;
    codeRepositoryTypeCodeDesc: string;
    sort: number;
    enabledFlag: number;
    appPath: string;
    backupPath: string;
    port: number;
  }[];
};
