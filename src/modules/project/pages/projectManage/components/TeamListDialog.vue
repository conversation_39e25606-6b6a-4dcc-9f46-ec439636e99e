<script setup lang="tsx" name="TeamListDialog">
  import { ref, watch, computed, nextTick } from 'vue';
  import {
    queryPrjTeamInfoByExample,
    savePrjTeamInfo,
  } from '@/modules/project/api/projectManage';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTeamFormConfig } from '../config/useTeamFormConfigData.ts';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  import { UserReqItem } from '@/api/types';
  import { useGetUserInfo } from '@/hooks/useGetUserList';
  import { Title, ProForm, ProTable, ProDialog } from 'sun-biz';

  type teamFormModel = {
    user: UserReqItem;
    teamerType: { label: string; value: { label: string; value: string } };
  };

  const props = defineProps<{
    row?: {
      projectId: string;
      ownerOrgId?: string;
      projectName?: string;
    };
    teamerTypeCodeData?: SelectOptions[];
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: teamFormModel;
    getItemRef: (name: string) => HTMLInputElement | null;
  }>();

  const { t } = useTranslation();
  const searchParams = ref({
    projectId: props.row?.projectId || '',
  });
  const teamList = ref<ProjectManage.ProjectTeamInfo[]>([]);
  const loading = ref(false);
  const hospitalId = ref('');
  const dialogRef = ref();
  const teamerTypeCodeList = ref<SelectOptions[]>([]);
  const emits = defineEmits<{
    saveTeamSuccess: [];
  }>();

  const { userList, getUserList } = useGetUserInfo();

  const userSelectOptions = computed(() => {
    return userList.value.map((item) => ({
      label: item.userName,
      value: item,
    }));
  });

  const queryProjectTeamList = async (
    params: ProjectManage.ProjectTeamQueryParams = {},
  ) => {
    loading.value = true;
    if (params) {
      searchParams.value = {
        ...searchParams.value,
        ...params,
      };
    }
    const [, res] = await queryPrjTeamInfoByExample(searchParams.value);
    loading.value = false;
    if (res?.data) {
      teamList.value = res.data;
    }
  };

  watch(
    () => props,
    () => {
      searchParams.value.projectId = props.row?.projectId || '';
      hospitalId.value = props.row?.ownerOrgId || '';
      teamerTypeCodeList.value = props.teamerTypeCodeData || [];
      if (props.row?.projectId) {
        queryProjectTeamList();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const getColumns = () => {
    return [
      {
        label: t('projectManage.team.table.userNo', '工号'),
        prop: 'userNo',
        minWidth: 120,
      },
      {
        label: t('projectManage.team.table.userName', '姓名'),
        prop: 'userName',
        minWidth: 150,
      },
      {
        label: t('projectManage.team.table.teamerTypeCodeDesc', '项目角色'),
        prop: 'teamerTypeCodeDesc',
        minWidth: 150,
      },
      {
        label: t('projectManage.team.table.deptName', '所属团队'),
        prop: 'deptName',
        minWidth: 120,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (row: ProjectManage.ProjectTeamInfo, $index: number) => {
          return (
            <div
              class={'cursor-pointer text-blue-500'}
              onClick={() => onDeleteClick($index)}
            >
              {t('global:delete')}
            </div>
          );
        },
      },
    ];
  };

  const onAddTeamClick = () => {
    formRef?.value?.ref.validate(async (valid) => {
      if (valid) {
        const { user, teamerType } = formRef?.value?.model || {};
        if (!user || !teamerType) {
          ElMessage.warning(
            t(
              'projectManage.team.errorTip.userOrTeamerType',
              '请选择成员和角色！',
            ),
          );
          return;
        }
        const teamer = teamList.value.find((t) => t.userId === user.userId);
        if (teamer) {
          ElMessage.warning(
            t('projectManage.team.errorTip.repeatedTeamer', '该人员已存在！'),
          );
          return;
        }
        teamList.value.push({
          userId: user.userId,
          userNo: user.userNo,
          userName: user.userName,
          teamerTypeCode: teamerType.value,
          teamerTypeCodeDesc: teamerType.label,
        } as unknown as ProjectManage.ProjectTeamInfo);
        formRef.value?.ref.resetFields();
        nextTick(() => {
          const userRef = formRef?.value?.getItemRef('user');
          if (userRef) {
            userRef.focus();
          }
        });
      }
    });
  };

  const onCloseClick = () => {
    dialogRef.value.close();
  };

  const onSaveTeamClick = async () => {
    const params = {
      projectId: props.row!.projectId,
      projectTeamerList: teamList.value,
    };
    // const managers = params.projectTeamerList?.filter(
    //   (item) => item.teamerTypeCode === '1',
    // );
    // if (managers?.length !== 1) {
    //   ElMessage.warning(
    //     t(
    //       'projectManage.team.errorTip.teamerTypeCode',
    //       '必须有且仅有一名项目经理角色！',
    //     ),
    //   );
    //   return;
    // }
    const [, res] = await savePrjTeamInfo(params);
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      queryProjectTeamList();
      emits('saveTeamSuccess');
    }
  };

  const onDeleteClick = (index: number) => {
    teamList.value.splice(index, 1);
  };

  const formConfig = useTeamFormConfig(
    teamerTypeCodeList,
    userSelectOptions,
    getUserList,
  );
  const projectTeamColumns = computed(() => getColumns());

  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    @success="() => {}"
    :width="900"
    ref="dialogRef"
    :title="t('projectManage.team.modalTitle', '团队维护')"
    :link="true"
    :button-text="t('projectManage.team.title', '团队')"
    destroy-on-close
    :include-footer="false"
  >
    <div class="p-box flex h-full flex-col">
      <Title :title="props.row?.projectName" />
      <div class="mt-3 flex justify-between">
        <div v-if="formConfig" class="el-form-item mr-3">
          <ProForm ref="formRef" :data="formConfig" :column="2" />
        </div>
        <div>
          <el-button type="primary" @click="onAddTeamClick">
            {{ $t('global:add') }}
          </el-button>
          <el-button type="primary" @click="onSaveTeamClick">
            {{ $t('global:save') }}
          </el-button>
          <el-button @click="onCloseClick">{{ $t('global:close') }}</el-button>
        </div>
      </div>
      <pro-table
        :data="teamList"
        :columns="projectTeamColumns"
        :loading="loading"
        :max-height="420"
        row-key="proTeamerId"
      />
    </div>
  </ProDialog>
</template>
