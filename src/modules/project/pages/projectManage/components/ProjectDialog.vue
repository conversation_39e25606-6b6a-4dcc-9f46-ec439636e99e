<script setup lang="ts" name="projectDialog">
  import { ref, watch, useAttrs } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useProjectConfig } from '../config/useProjectFormConfigData';
  import { addProject, editProject } from '@modules/project/api/projectManage';
  import { ProDialog, ProForm } from 'sun-biz';
  import { PROJECT_STATUS_CODE } from '@/utils/constant';

  type ROW = {
    projectId?: string;
    beginDate?: string;
    closeDate?: string;
    endDate?: string;
    freeServiceEndDate?: string;
    freeServicePeriod?: number;
    onlineDate?: string;
    onlineDatePlan?: string;
    ownerOrgId?: string;
    projectName?: string;
    projectName2nd?: string;
    projectNameExt?: string;
    projectStatusCode?: string;
    spellNo?: string;
    wbNo?: string;
    disabled?: boolean;
    isAdd?: boolean | undefined;
  };

  const props = defineProps<{
    row?: ROW;
    projectStatusCodeData?: SelectOptions[];
    devGroupCodeData?: SelectOptions[];
    storySourceCodeData?: SelectOptions[];
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: ProjectManage.ProjectUpsertParams;
  }>();
  const dialogRef = ref();
  const attrs = useAttrs();
  const formModel = ref();
  const emits = defineEmits<{
    success: [];
  }>();
  const disabled = ref();
  const ownerOrgId = ref();
  const projectStatusCodeList = ref<SelectOptions[]>([]);
  const devGroupCodeList = ref<SelectOptions[]>([]);
  const storySourceCodeList = ref<SelectOptions[]>([]);

  const { t } = useTranslation();

  const beforeSubmitValidate = (params: ProjectManage.ProjectUpsertParams) => {
    if (
      params.beginDate &&
      params.endDate &&
      new Date(params.beginDate) >= new Date(params.endDate)
    ) {
      ElMessage.warning(
        t(
          'projectManage.errorTip.beginDateLessThanEndDate',
          '结束日期应晚于开始日期！',
        ),
      );
      return false;
    }
    if (
      (!params.onlineDate || !params.closeDate) &&
      params.freeServiceEndDate
    ) {
      ElMessage.warning(
        t(
          'projectManage.errorTip.freeServiceEndDate',
          '上线日期或验收日期为空，不允许维护免费维保截止日期！',
        ),
      );
      return false;
    }
    if (
      params.onlineDatePlan &&
      (new Date(params.onlineDatePlan) < new Date(params.beginDate as string) ||
        (params.endDate &&
          new Date(params.onlineDatePlan) > new Date(params.endDate)))
    ) {
      ElMessage.warning(
        t(
          'projectManage.errorTip.onlineDatePlan',
          '计划上线日期必须大于等于开始日期，小于等于结束日期！',
        ),
      );
      return false;
    }
    if (
      params.onlineDate &&
      (new Date(params.onlineDate) < new Date(params.beginDate as string) ||
        (params.endDate &&
          new Date(params.onlineDate) > new Date(params.endDate)))
    ) {
      ElMessage.warning(
        t(
          'projectManage.errorTip.onlineDate',
          '上线日期必须大于等于开始日期，小于等于结束日期！',
        ),
      );
      return false;
    }
    if (params.closeDate) {
      if (!params.onlineDate) {
        ElMessage.warning(
          t(
            'projectManage.errorTip.closeDate.onlineDate',
            '上线日期必须非空！',
          ),
        );
        return false;
      }
      if (
        new Date(params.closeDate) < new Date(params.beginDate as string) ||
        (params.endDate &&
          new Date(params.closeDate) > new Date(params.endDate))
      ) {
        ElMessage.warning(
          t(
            'projectManage.errorTip.closeDate.beginDateAndEndDate',
            '终验日期应大于上线日期，小于结束日期！',
          ),
        );
        return false;
      }
      if (
        params.projectStatusCode &&
        ![
          PROJECT_STATUS_CODE.ACCEPTED,
          PROJECT_STATUS_CODE.FREE_MAINTAINING,
          PROJECT_STATUS_CODE.CHARGE_MAINTAINING,
        ].includes(params.projectStatusCode)
      ) {
        ElMessage.warning(
          t(
            'projectManage.errorTip.closeDate.projectStatusCode',
            '项目状态必须为已终验、免费维保、收费维保，才需要录入终验日期',
          ),
        );
        return false;
      }
    } else if (params.projectStatusCode === PROJECT_STATUS_CODE.ACCEPTED) {
      ElMessage.warning(
        t(
          'projectManage.errorTip.projectStatusCode.closeDate',
          '项目已终验，终验日期必须非空！',
        ),
      );
      return false;
    }
    return true;
  };

  watch(
    () => props,
    () => {
      formModel.value = props.row;
      disabled.value = props.row?.disabled;
      ownerOrgId.value = props.row?.ownerOrgId;
      projectStatusCodeList.value = props.projectStatusCodeData || [];
      devGroupCodeList.value = props.devGroupCodeData || [];
      storySourceCodeList.value = props.storySourceCodeData || [];
    },
    {
      immediate: true,
      deep: true,
    },
  );

  const onSubmit = async () => {
    if (disabled.value) return;
    const isValid = await formRef.value?.ref?.validate();
    if (!isValid) {
      return [new Error('参数错误')];
    }
    const params = {
      projectId: props.row?.projectId,
      ...formRef?.value?.model,
    };
    if (!beforeSubmitValidate(params)) {
      return [new Error('参数错误')];
    }
    return props.row?.isAdd ? addProject(params) : editProject(params);
  };

  const onSetFormValueByFormKey = (
    setFormKey: string,
    formValueKey: string,
  ) => {
    formModel.value[setFormKey] = formModel.value[formValueKey];
  };

  const { projectDialogForm } = useProjectConfig(
    disabled,
    projectStatusCodeList,
    devGroupCodeList,
    storySourceCodeList,
    onSetFormValueByFormKey,
  );
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    :confirm-fn="onSubmit"
    :width="980"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    ref="dialogRef"
    @success="emits('success')"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    type="primary"
  >
    <ProForm
      v-model="formModel"
      :column="3"
      ref="formRef"
      :data="projectDialogForm"
    />
  </ProDialog>
</template>
