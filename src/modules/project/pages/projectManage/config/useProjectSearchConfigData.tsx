import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { SelectOptions } from '@/typings/common.ts';
import HospitalSelect from '@/modules/project/components/HospitalSelect/index.vue';

export function useSearchFormConfig(
  projectStatusCodeData: Ref<SelectOptions[]>,
  queryProjectData: (data?: ProjectManage.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'ownerOrgId',
        render: () => (
          <HospitalSelect
            useAllHospitalList={true}
            automaticallySetValue={false}
            onChange={(val: string) => queryProjectData({ ownerOrgId: val })}
          />
        ),
        extraProps: {
          className: 'w-64',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword.list'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryProjectData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryProjectData({
              keyWord: '',
            });
          },
        },
      },
      {
        label: t('projectManage.projectStatus', '项目状态'),
        name: 'projectStatusCodes',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('projectManage.projectStatus', '项目状态'),
        }),
        extraProps: {
          multiple: true,
          options: projectStatusCodeData.value,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}
