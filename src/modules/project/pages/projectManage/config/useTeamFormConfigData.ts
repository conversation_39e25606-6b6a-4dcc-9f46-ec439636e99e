import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { SelectOptions } from '@/typings/common.ts';
import { UserReqItem, UserReqParams } from '@/api/types';
import { INTERNAL_HOSPITAL_ID } from '@/utils/constant';

export function useTeamFormConfig(
  teamerTypeCodeList: Ref<SelectOptions[]>,
  userList: Ref<{ label: string; value: UserReqItem }[]>,
  getUserList: (params: UserReqParams) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('projectManage.team.user', '团队成员'),
        name: 'user',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('projectManage.team.user', '团队成员'),
        }),
        className: 'w-60',
        extraProps: {
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: userList.value,
          remoteMethod: (keyWord: string) => {
            getUserList({
              keyWord: keyWord,
              pageSize: 300,
              hospitalId: INTERNAL_HOSPITAL_ID,
            });
          },
          'value-key': 'userId',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('projectManage.team.user', '团队成员'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('projectManage.team.teamerType', '项目角色'),
        name: 'teamerType',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('projectManage.team.teamerType', '项目角色'),
        }),
        className: 'w-60',
        extraProps: {
          options: teamerTypeCodeList.value.map((item) => ({
            label: item.label,
            value: item,
          })),
          'value-key': 'value',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('projectManage.team.teamerType', '项目角色'),
            }),
            trigger: 'change',
          },
        ],
      },
    ],
  });
  return data;
}
