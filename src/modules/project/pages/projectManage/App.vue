<script setup lang="ts" name="projectManage">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { Title, ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import {
    TEAMER_TYPE_CODE_NAME,
    PROJECT_STATUS_CODE_NAME,
    DEV_GROUP_CODE_NAME,
    STORY_SOURCE_CODE_NAME,
  } from '@/utils/constant';
  import ProjectDialog from './components/ProjectDialog.vue';
  import TeamListDialog from './components/TeamListDialog.vue';
  import { queryProjectByExample } from '@modules/project/api/projectManage';
  import { useSearchFormConfig } from './config/useProjectSearchConfigData';
  import { useProjectColumnConfig } from './config/useProjectColumnConfig';

  type DialogData = {
    [key: string]: unknown;
  };
  const searchParams = ref<ProjectManage.QueryParams>({
    ownerOrgId: '',
    keyWord: '',
    projectStatusCodes: [],
    pageNumber: 1,
    pageSize: 25,
  });
  const total = ref<number>(0);
  const projectList = ref([]);
  const loading = ref(false);
  let dialogData = reactive<DialogData>({});
  let teamListData = reactive<DialogData>({});
  const teamListDialogRef = ref();
  const projectDialogRef = ref();

  const dataSetList = useFetchDataset([
    PROJECT_STATUS_CODE_NAME,
    TEAMER_TYPE_CODE_NAME,
    DEV_GROUP_CODE_NAME,
    STORY_SOURCE_CODE_NAME,
  ]);

  const projectStatusCodeData = computed(() =>
    (dataSetList?.value?.[PROJECT_STATUS_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  const teamerTypeCodeData = computed(() =>
    (dataSetList?.value?.[TEAMER_TYPE_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  const devGroupCodeData = computed(() =>
    (dataSetList?.value?.[DEV_GROUP_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  const storySourceCodeData = computed(() =>
    (dataSetList?.value?.[STORY_SOURCE_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );

  const queryProjectData = async (data?: ProjectManage.QueryParams) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryProjectByExample(searchParams.value);
    loading.value = false;
    if (res?.success) {
      projectList.value = res.data;
      total.value = res?.total;
    }
  };

  const searchConfig = useSearchFormConfig(
    projectStatusCodeData,
    queryProjectData,
  );
  const projectColumns = useProjectColumnConfig(openDialog, openTeamListDialog);

  function openDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (dialogData as DialogData)[key] = data[key];
    });
    projectDialogRef.value.dialogRef.open();
  }

  function openTeamListDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (teamListData as DialogData)[key] = data[key];
    });
    teamListDialogRef.value.dialogRef.open();
  }

  onMounted(async () => {
    await queryProjectData();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('projectManage.list.title', '项目列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :column="3"
          :show-search-button="true"
          :data="searchConfig"
          @model-change="queryProjectData"
        />
      </div>
      <el-button
        class="mr-5"
        type="primary"
        @click="
          () => {
            openDialog({
              title: $t('projectManage.addProject', '新增项目'),
              row: {
                ownerOrgId: searchParams.ownerOrgId,
                isAdd: true,
              },
            });
          }
        "
      >
        {{ $t('global:add') }}
      </el-button>
    </div>
    <pro-table
      row-key="projectId"
      :data="projectList"
      component-no="4"
      :columns="projectColumns"
      :loading="loading"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      @current-page-change="
        (val: number) => {
          queryProjectData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryProjectData({
            pageSize: val,
          });
        }
      "
    />
  </div>
  <ProjectDialog
    ref="projectDialogRef"
    v-bind="dialogData"
    :project-status-code-data="projectStatusCodeData"
    :dev-group-code-data="devGroupCodeData"
    :story-source-code-data="storySourceCodeData"
    @success="queryProjectData"
  />

  <TeamListDialog
    ref="teamListDialogRef"
    v-bind="teamListData"
    :teamer-type-code-data="teamerTypeCodeData"
    @save-team-success="queryProjectData"
  />
</template>
