<script setup lang="ts" name="codeBranchManage">
  import { ref, computed } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { commonSort } from '@/api/common';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useCodeBranchTableConfig } from './config/useTableConfig.tsx';
  import { useCodeBranchSearchFormConfig } from './config/useFormConfig.ts';
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    queryCodeBranchByExample,
    editCodeBranch,
  } from '@modules/project/api/codeBranchManage';
  import CodeBranchUpsertDialog from '@/modules/project/pages/codeBranchManage/components/CodeBranchUpsertDialog.vue';

  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<CodeBranchManage.QueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const loading = ref(false);
  const codeBranchList = ref<CodeBranchManage.CodeBranchInfo[]>([]);
  const selections = ref<CodeBranchManage.CodeBranchInfo[]>([]);
  const codeBranchUpsertParams = ref<CodeBranchManage.UpsertParams>({});
  const codeBranchUpsertDialogRef = ref();
  const codeBranchUpsertDialogMode = ref('');
  const codeBranchTableRef = ref();

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.codeBranchId || '';
    });
  });

  async function queryCodeBranchData(data?: CodeBranchManage.QueryParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryCodeBranchByExample(params);
    loading.value = false;
    if (res?.success) {
      codeBranchList.value = (res.data || []).sort(
        (
          a: CodeBranchManage.CodeBranchInfo,
          b: CodeBranchManage.CodeBranchInfo,
        ) => {
          return Number(a?.sort) - Number(b?.sort);
        },
      );
    }
  }

  const onOpenCodeBranchDialog = (
    mode: string,
    data?: CodeBranchManage.CodeBranchInfo,
  ) => {
    codeBranchUpsertDialogMode.value = mode;
    if (mode === 'add') {
      codeBranchUpsertParams.value = {
        enabledFlag: ENABLED_FLAG.YES,
      };
    } else if (mode === 'edit' && data) {
      const {
        codeBranchId,
        codeBranchName,
        codeBranchDesc,
        sort,
        codeBranchTypeList,
        enabledFlag,
        hospitalList,
        historyFlag,
      } = data;
      codeBranchUpsertParams.value = {
        codeBranchId,
        codeBranchName,
        codeBranchDesc,
        sort,
        codeBranchTypeCodes:
          codeBranchTypeList.length > 0
            ? codeBranchTypeList?.map((item) => item?.codeBranchTypeCode)
            : [],
        hospitalList,
        enabledFlag,
        historyFlag,
      };
    }
    codeBranchUpsertDialogRef.value.dialogRef.open();
  };

  const handleSelectChange = (value: CodeBranchManage.CodeBranchInfo[]) => {
    selections.value = value;
  };

  async function handleEnableSwitch(row: CodeBranchManage.CodeBranchInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.codeBranchName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const {
        codeBranchId,
        codeBranchName,
        codeBranchDesc,
        sort,
        hospitalList,
        historyFlag,
        codeBranchTypeList,
      } = row;
      const params = {
        codeBranchId,
        codeBranchName,
        codeBranchDesc,
        sort,
        hospitalList,
        historyFlag,
        codeBranchTypeCodes: codeBranchTypeList.map(
          (item) => item?.codeBranchTypeCode,
        ),
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editCodeBranch(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryCodeBranchData();
      }
    });
  }

  /** 拖拽排序 */
  const handleSortEnd = async (list: CodeBranchManage.CodeBranchInfo[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.codeBranchId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.CODE_BRANCH,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryCodeBranchData();
    }
  };

  queryCodeBranchData();
  const searchConfig = useCodeBranchSearchFormConfig(queryCodeBranchData);
  const tableColumnsConfig = useCodeBranchTableConfig(
    isCloudEnv,
    handleEnableSwitch,
    onOpenCodeBranchDialog,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('codeBranchManage.list.title', '代码分支列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :show-search-button="true"
          :data="searchConfig"
          @model-change="queryCodeBranchData"
        />
      </div>
      <div>
        <el-button
          class="mr-3"
          type="primary"
          :disabled="!isCloudEnv"
          @click="onOpenCodeBranchDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.CODE_BRANCH"
          @success="
            () => {
              codeBranchTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="codeBranchTableRef"
      row-key="codeBranchId"
      :loading="loading"
      :data="codeBranchList"
      :columns="tableColumnsConfig"
      draggable
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>
  <CodeBranchUpsertDialog
    ref="codeBranchUpsertDialogRef"
    :mode="codeBranchUpsertDialogMode"
    :data="codeBranchUpsertParams"
    @success="queryCodeBranchData"
  />
</template>
