import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG, FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';

export function useCodeBranchSearchFormConfig(
  queryCodeBranchData: (data?: CodeBranchManage.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryCodeBranchData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryCodeBranchData({
              keyWord: '',
            });
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}

type hospitalSelectionItem = {
  label: string;
  value: {
    hospitalId: string;
    hospitalNameDisplay: string;
  };
};

export function useCodeBranchUpsertFormConfig(
  hospitalList: Ref<hospitalSelectionItem[]>,
  codeBranchTypeList: Ref<SelectOptions[]>,
  disabled: Ref<boolean>,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('codeBranchManage.form.codeBranchName', '代码分支名称'),
        name: 'codeBranchName',
        component: 'input',
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'codeBranchManage.form.codeBranchName',
                '代码分支名称',
              ),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'codeBranchManage.form.codeBranchName',
                '代码分支名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'codeBranchDesc',
        isFullWidth: true,
        label: t('codeBranchManage.form.codeBranchDesc', '代码分支描述'),
        component: 'input',
        type: 'textarea',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'codeBranchManage.form.codeBranchDesc',
                '代码分支描述',
              ),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'codeBranchTypeCodes',
        label: t('codeBranchManage.form.codeBranchTypeCodes', '代码分支类型'),
        component: 'select',
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t(
                'codeBranchManage.form.codeBranchTypeCodes',
                '代码分支类型',
              ),
            }),
        extraProps: {
          options: codeBranchTypeList.value,
          multiple: true,
          remoteShowSuffix: true,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'codeBranchManage.form.codeBranchTypeCodes',
                '代码分支类型',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'historyFlag',
        label: t('codeBranchManage.form.historyFlag', '历史版本'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:yes'),
          'inactive-text': t('global:no'),
          disabled: disabled.value,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'hospitalList',
        label: t('person.belongHospital', '所属医院'),
        component: 'select',
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('person.belongHospital', '所属医院'),
            }),
        extraProps: {
          options: hospitalList.value,
          multiple: true,
          'value-key': 'hospitalId',
          remoteShowSuffix: true,
        },
      },
    ],
  });
}
