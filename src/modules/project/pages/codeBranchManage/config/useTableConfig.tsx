import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

export function useCodeBranchTableConfig(
  isCloudEnv: boolean | undefined,
  handleEnableSwitch: (data: CodeBranchManage.CodeBranchInfo) => void,
  onOpenCodeBranchDialog: (
    mode: string,
    data: CodeBranchManage.CodeBranchInfo,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global.sequence', '顺序'),
        minWidth: 60,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.codeBranchName',
          '代码分支名称',
        ),
        prop: 'codeBranchName',
        minWidth: 150,
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.codeBranchDesc',
          '代码分支描述',
        ),
        prop: 'codeBranchDesc',
        minWidth: 210,
      },
      {
        label: t('person.belongHospital', '所属医院'),
        prop: 'hospitalList',
        minWidth: 270,
        render: (row: CodeBranchManage.CodeBranchInfo) =>
          row.hospitalList?.length > 0
            ? row.hospitalList
                .map((item) => item?.hospitalNameDisplay)
                .join(' ')
            : '--',
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.codeBranchTypeList',
          '代码分支类型',
        ),
        prop: 'codeBranchTypeList',
        minWidth: 170,
        render: (row: CodeBranchManage.CodeBranchInfo) =>
          row.codeBranchTypeList?.length > 0
            ? row.codeBranchTypeList
                .map((item) => item.codeBranchTypeCodeDesc)
                .join(' ')
            : '--',
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.historyFlag',
          '历史版本',
        ),
        prop: 'historyFlag',
        minWidth: 90,
        render: (row: CodeBranchManage.CodeBranchInfo) => {
          return Number(row.historyFlag) ? t('global:yes') : t('global:no');
        },
      },
      {
        label: t('global:creator', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.modifiedUserName',
          '最后修改人',
        ),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 170,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: CodeBranchManage.CodeBranchInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 120,
        render: (row: CodeBranchManage.CodeBranchInfo) => {
          return (
            <el-button
              type="primary"
              link={true}
              disabled={!isCloudEnv}
              onClick={() => onOpenCodeBranchDialog('edit', row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
