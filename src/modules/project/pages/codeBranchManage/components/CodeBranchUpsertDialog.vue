<script setup lang="ts" name="CodeBranchUpsertDialog">
  import { ref, watch, onMounted, computed } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProDialog, useFetchDataset } from 'sun-biz';
  import { CODE_BRANCH_TYPE_CODE } from '../constant';
  import { ORG_TYPE_CODE, FLAG } from '@/utils/constant';
  import { CodeSystemType } from '@/typings/codeManage';
  import { queryOrgList } from '@modules/system/api/org';
  import {
    addCodeBranch,
    editCodeBranch,
  } from '@modules/project/api/codeBranchManage';
  import { useCodeBranchUpsertFormConfig } from '../config/useFormConfig.ts';

  const props = defineProps<{
    mode: string;
    data: CodeBranchManage.UpsertParams;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: CodeBranchManage.UpsertParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const disabled = ref(false);
  const dialogForm = ref({});
  const emits = defineEmits<{ success: [] }>();

  const dataSetList = useFetchDataset([CodeSystemType.CODE_BRANCH_TYPE_CODE]);
  const codeBranchTypeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.CODE_BRANCH_TYPE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  type hospitalSelectionItem = {
    label: string;
    value: {
      hospitalId: string;
      hospitalNameDisplay: string;
    };
  };

  const hospitalList = ref<hospitalSelectionItem[]>([]);
  const getOrgList = async () => {
    const [, res] = await queryOrgList({});
    if (res?.success) {
      if (!res.data?.length) {
        hospitalList.value = [];
        return;
      }
      const hospitalData: hospitalSelectionItem[] = [];
      res.data.forEach((item: Org.Item) => {
        if (item.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
          hospitalData.push({
            label: item.orgNameDisplay,
            value: {
              hospitalId: item.orgId,
              hospitalNameDisplay: item.orgNameDisplay,
            },
          });
        } else if (item.orgTypeCode === ORG_TYPE_CODE.GROUP) {
          if (item.subOrgList?.length) {
            item.subOrgList.forEach((subItem) => {
              if (subItem.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
                hospitalData.push({
                  label: subItem.orgNameDisplay,
                  value: {
                    hospitalId: subItem.orgId,
                    hospitalNameDisplay: subItem.orgNameDisplay,
                  },
                });
              }
            });
          }
        }
      });
      hospitalList.value = hospitalData;
    }
  };

  watch(
    () => props,
    () => {
      disabled.value = props.mode === 'view';
      dialogForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          if (
            params.historyFlag === FLAG.YES &&
            (params.codeBranchTypeCodes?.length !== 1 ||
              params.codeBranchTypeCodes?.[0] !== CODE_BRANCH_TYPE_CODE.MAIN)
          ) {
            ElMessage.warning(
              t(
                'codeBranchManage.codeBranchUpsertDialog.errorTip.codeBranchTypeCodesErrorTip',
                '代码分支的类型有且仅有一个生产分支，历史版本标志才能为是',
              ),
            );
            reject(['', new Error('参数错误')]);
            return;
          }
          if (params.historyFlag === FLAG.YES && !params.hospitalList?.length) {
            ElMessage.warning(
              t(
                'codeBranchManage.codeBranchUpsertDialog.errorTip.historyFlagHospitalListIsEmptyTip',
                '历史版本标志为是，所属医院列表不能为空',
              ),
            );
            reject(['', new Error('参数错误')]);
            return;
          }
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addCodeBranch(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editCodeBranch(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const formConfig = useCodeBranchUpsertFormConfig(
    hospitalList,
    codeBranchTypeList,
    disabled,
  );
  onMounted(() => {
    getOrgList();
  });
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${$t(`global:${props.mode}`)}${$t('codeBranchManage.name', '代码分支')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="4"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
