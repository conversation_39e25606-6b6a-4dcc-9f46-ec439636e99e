export const routes = [
  {
    path: '/',
    name: 'codeRepositoryManage',
    component: () => import('./App.vue'),
  },
  {
    path: '/apiPage/:codeRepositoryId',
    name: 'apiPage',
    component: () => import('./apiPage/index.vue'),
    keepAlive: false,
  },
  {
    path: '/apiPage/apiDesign/:apiId',
    name: 'apiDesign',
    component: () => import('./apiPage/components/apiDesign.vue'),
    keepAlive: false,
  },
  {
    path: '/settingPage',
    name: 'settingPage',
    component: () => import('./settingPage/index.vue'),
    keepAlive: false,
  },
];

export default routes;
