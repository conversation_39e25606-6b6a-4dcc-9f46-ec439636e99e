import { Ref } from 'vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
import CustomDropdown from '../components/CustomDropdown.vue';

export function useAPITableConfig(
  isCloudEnv: boolean | undefined,
  onOpenApiDialog: (mode: string, row: CodeRepositoryManageAPI.ApiList) => void,
  deleteApiAction: (row: CodeRepositoryManageAPI.ApiList) => void,
  handleDesignApi: (row: CodeRepositoryManageAPI.ApiList) => void,
  unlock: (row: CodeRepositoryManageAPI.ApiList) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
        width: 40,
      },
      {
        label: t('apiPage.apiList', 'API名称'),
        prop: 'apiName',
        minWidth: 100,
        extraProps: {
          'show-overflow-tooltip': false,
        },
        render: (row: CodeRepositoryManageAPI.ApiList) => {
          return (
            <CustomDropdown
              row={row}
              onOpenApiDialog={onOpenApiDialog}
              deleteApiAction={deleteApiAction}
              handleDesignApi={handleDesignApi}
              unlockRow={unlock}
              isCloudEnv={isCloudEnv}
            />
          );
        },
      },
    ],
  });
}

export function useAPIDesignTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>;
  dataTypeManageList?: Ref<DataTypeManage.DataTypeManageList[]>;
  sourceData: Ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>;
  addNewRow: (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
    index?: number | undefined,
  ) => void;
  insertNewRow: (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
    index?: number | undefined,
  ) => void;
  addChildRow: (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
    index?: number | undefined,
  ) => void;
  saveRow: (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
    index?: number | undefined,
  ) => void;
  deleteApiParaApi: (
    data?: CodeRepositoryManageAPIDesign.ApiRecordParaList,
    index?: number,
  ) => void;
  cancelEditRow: (
    row: CodeRepositoryManageAPIDesign.ApiRecordParaList,
    index?: number,
  ) => void;
}) {
  const {
    id,
    tableRef,
    data,
    dataTypeManageList,
    sourceData,
    addNewRow,
    insertNewRow,
    addChildRow,
    saveRow,
    deleteApiParaApi,
    cancelEditRow,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    id,
    tableRef,
    data: data as unknown as Ref<
      (CodeRepositoryManageAPIDesign.ApiRecordParaList & {
        editable: boolean;
      })[]
    >,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      // {
      //   label: t('global:sequenceNumber'),
      //   prop: 'indexNo',
      //   minWidth: 80,
      //   render: (row: { sort: string }) => <>{row.sort}</>,
      // },
      {
        label: t('apiPage.apiParaDesc', '参数描述'),
        prop: 'apiParaDesc',
        minWidth: 150,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <>
              {row.editable ? (
                <el-input
                  v-model={row.apiParaDesc}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('apiPage.apiParaDesc', '参数描述'),
                  })}
                  v-slots={{
                    append: () => (
                      <el-button
                        type={'text'}
                        onClick={() => {
                          console.log('引用功能');
                        }}
                      >
                        引用
                      </el-button>
                    ),
                  }}
                ></el-input>
              ) : (
                <>
                  {row.dateTypeOperateCode === '3' ? (
                    <del class={'text-gray-400'}>{row.apiParaDesc}</del>
                  ) : (
                    <>{row.apiParaDesc}</>
                  )}
                </>
              )}
            </>
          );
        },
      },
      {
        label: t('apiPage.apiParaName', '参数名称'),
        prop: 'apiParaName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiPage.apiParaName', '参数名称'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.apiParaName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('apiPage.apiParaName', '参数名称'),
                  })}
                />
              ) : (
                <>
                  {row.dateTypeOperateCode === '3' ? (
                    <del class={'text-gray-400'}>{row.apiParaName}</del>
                  ) : (
                    <>{row.apiParaName}</>
                  )}
                </>
              )}
            </div>
          );
        },
      },
      {
        label: t('apiPage.dataTypeName', '数据类型'),
        prop: 'dataTypeName',
        minWidth: 120,
        editable: true,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.dataTypeId}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('wardManage.deptTable.dataTypeName', '数据类型'),
                  })}
                  onChange={(val: string) => {
                    const item = dataTypeManageList?.value.find(
                      (dataType: DataTypeManage.DataTypeManageList) =>
                        dataType.dataTypeId === val,
                    );
                    row.dataTypeName = item?.dataTypeName as string;
                  }}
                >
                  {dataTypeManageList?.value?.map(
                    (item: DataTypeManage.DataTypeManageList) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ),
                  )}
                </el-select>
              ) : (
                <>{row.dataTypeName}</>
              )}
            </div>
          );
        },
      },

      {
        label: t('apiPage.mandatoryFlag', '必填'),
        prop: 'mandatoryFlag',
        minWidth: 80,
        editable: true,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <el-switch
              v-model={row.mandatoryFlag}
              inline-prompt
              disabled={!row.editable}
              class={'w-full justify-center'}
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('apiPage.mandatoryFlag', '必填')}
              inactive-text={t('apiPage.unMandatoryFlag', '可空')}
            />
          );
        },
      },
      {
        label: t('apiPage.expiredFlag', '过时'),
        prop: 'expiredFlag',
        minWidth: 60,
        editable: true,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          // 过时标志
          return (
            <>
              <el-checkbox
                disabled={!row.editable}
                class={'w-full justify-center'}
                true-label={ENABLED_FLAG.YES}
                false-label={ENABLED_FLAG.NO}
                v-model={row.expiredFlag}
                size={'large'}
              />
            </>
          );
        },
      },
      {
        label: t('apiPage.paramsDesc', '备注'),
        prop: 'memo',
        minWidth: 120,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.memo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('apiPage.apiParaDesc', '备注'),
                  })}
                ></el-input>
              ) : (
                <>{row.memo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 240,
        render: (
          row: CodeRepositoryManageAPIDesign.ApiRecordParaList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          const sort = (sourceData?.value || []).findIndex((item) => {
            return item?.apiRecordParaId === row.apiRecordParaId;
          });
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row)}
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                disabled={row.dateTypeOperateCode === '3'}
                onClick={() => cancelEditRow(row, sort)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type={'primary'}
                disabled={row.dateTypeOperateCode === '3'}
                onClick={() => addNewRow(row, $index)}
              >
                {t('global:add', '新增')}
              </el-button>
              <el-button
                link={true}
                type={'primary'}
                onClick={() => insertNewRow(row, $index)}
              >
                {t('global:insert', '插入')}
              </el-button>
              <el-button
                link={true}
                type={'primary'}
                disabled={row.dateTypeOperateCode === '3'}
                onClick={() => addChildRow(row, $index)}
              >
                {t('apiPage.append', '添加')}
              </el-button>
              <el-button
                link={true}
                type={'primary'}
                disabled={row.dateTypeOperateCode === '3'}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
              <el-button
                link={true}
                type={'danger'}
                disabled={row.dateTypeOperateCode === '3'}
                onClick={() => deleteApiParaApi(row)}
              >
                {t('global:delete', '删除')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, cancelEdit };
}

export function useAPIDesignShowTableConfig() {
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('apiPage.apiParaDesc', '参数描述'),
        prop: 'apiParaDesc',
        minWidth: 150,
        tree: true,
      },
      {
        label: t('apiPage.apiParaName', '参数名称'),
        prop: 'apiParaName',
        minWidth: 150,
      },
      {
        label: t('apiPage.dataTypeName', '数据类型'),
        prop: 'dataTypeName',
        minWidth: 120,
      },
      {
        label: t('apiPage.mandatoryFlag', '必填'),
        prop: 'mandatoryFlag',
        minWidth: 80,
        render: (row: { mandatoryFlag: string }) => {
          return (
            <el-switch
              disabled={true}
              v-model={row.mandatoryFlag}
              inline-prompt
              class={'w-full justify-center'}
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('apiPage.mandatoryFlag', '必填')}
              inactive-text={t('apiPage.unMandatoryFlag', '可空')}
            />
          );
        },
      },
      {
        label: t('apiPage.expiredFlag', '过时'),
        prop: 'expiredFlag',
        minWidth: 60,
        render: (row: { expiredFlag: string }) => {
          return (
            <>
              <el-checkbox
                disabled={true}
                class={'w-full justify-center'}
                true-label={ENABLED_FLAG.YES}
                false-label={ENABLED_FLAG.NO}
                v-model={row.expiredFlag}
                size={'large'}
              />
            </>
          );
        },
      },
      {
        label: t('apiPage.paramsDesc', '备注'),
        prop: 'memo',
        minWidth: 120,
        render: (row: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.memo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('apiPage.apiParaDesc', '参数描述'),
                  })}
                ></el-input>
              ) : (
                <>{row.memo}</>
              )}
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}
