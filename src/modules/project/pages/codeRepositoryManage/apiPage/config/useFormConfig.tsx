import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant.ts';
import { SelectOptions } from '@/typings/common.ts';
import CustomSelect from '../components/customSelect.vue';

export function useAPISearchFormConfig(
  queryApiList: (data?: CodeRepositoryManageAPI.QueryParams) => void,
) {
  const enabledFlagList = [
    { value: ENABLED_FLAG.YES, label: '启用' },
    { value: ENABLED_FLAG.NO, label: '停用' },
  ];
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryApiList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryApiList({
              keyWord: '',
            });
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag', '启用标志'),
        component: 'select',
        placeholder: t('computerManage.enabledFlagTips', '请选择启用标志'),
        triggerModelChange: true,
        extraProps: {
          className: 'w-80',
          options: enabledFlagList,
        },
      },
    ],
  });
  return data;
}

export function useAPIUpsertConfig(
  dialogForm: CodeRepositoryManageAPI.UpsertApi,
  codeRepositoryList: Ref<CodeRepositoryManage.CodeRepositoryInfo[]>,
  interfaceList: Ref<SelectOptions[]>,
  saveApiCategoryItem: (
    apiCategoryItem: CodeRepositoryManageAPI.UpsertApiCategory | undefined,
  ) => void,
  classNameRef: Ref<HTMLElement | undefined>,
  methodNameRef: Ref<HTMLElement | undefined>,
  modeName: Ref<string>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('apiFrom.apiNo', 'API编号'),
        name: 'apiNo',
        component: 'input',
        className: 'w-80',
        placeholder: t('global:placeholder.input.template', {
          content: t('apiFrom.apiNo', 'API编号'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiFrom.apiNo', 'API编号'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          disabled: modeName.value === 'edit',
        },
      },
      {
        label: t('apiFrom.apiNo', 'API名称'),
        name: 'apiName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('apiFrom.apiName', 'API名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiFrom.apiName', 'API名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
        extraProps: {},
      },
      {
        name: 'codeRepositoryId',
        label: t('apiFrom.codeRepositoryId', '代码仓库'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('apiFrom.codeRepositoryId', '代码仓库'),
        }),
        extraProps: {
          options: codeRepositoryList.value,
          className: 'w-60',
          disabled: true,
        },

        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('apiFrom.codeRepositoryId', '代码仓库'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'interfaceId',
        label: t('apiFrom.interfaceId', '接口'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('apiFrom.interfaceId', '接口'),
        }),
        extraProps: {
          options: interfaceList.value,
          className: 'w-60',
        },
      },
      {
        name: 'queryFlag',
        label: t('apiFrom.queryFlag', '查询/修改'),
        component: 'switch',
        isFullWidth: true,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          style:
            '--el-switch-on-color: #67c23a; --el-switch-off-color: #409eff',
          'active-text': t('apiFrom.queryFlag', '查询'),
          'inactive-text': t('apiFrom.editFlag', '修改'),
        },
      },
      {
        label: t('apiFrom.apiCategoryId', 'API类别'),
        name: 'apiCategoryId',
        className: 'w-80',
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('apiFrom.apiCategoryId', 'API类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: () => {
          return (
            <>
              <CustomSelect
                class="w-80"
                rowValue={dialogForm}
                onSuccess={(
                  apiCategoryItem: CodeRepositoryManageAPI.UpsertApiCategory,
                ) => saveApiCategoryItem(apiCategoryItem)}
              />
            </>
          );
        },
      },
      {
        label: t('apiFrom.className', '类名'),
        name: 'className',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiFrom.className', '类名'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
        render: () => {
          return (
            <el-input
              type="text"
              class={'el-input__inner w-80'}
              ref={classNameRef}
              placeholder={t('global:placeholder.input.template', {
                content: t('apiFrom.methodName', '类名'),
              })}
            />
          );
        },
      },
      {
        label: t('apiFrom.methodName', '方法名'),
        name: 'methodName',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiFrom.methodName', '方法名'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
        render: () => {
          return (
            <el-input
              type="text"
              class={'el-input__inner w-80'}
              ref={methodNameRef}
              placeholder={t('global:placeholder.input.template', {
                content: t('apiFrom.methodName', '方法名'),
              })}
            />
          );
        },
      },
      {
        label: t('apiFrom.classNamePostMapping', '类名注解'),
        name: 'classNamePostMapping',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('apiFrom.classNamePostMapping', '类名注解'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
        render: () => {
          return (
            <el-input
              type="text"
              class={'el-input__inner w-80'}
              placeholder={t('global:placeholder.input.template', {
                content: t('apiFrom.classNamePostMapping', '类名注解'),
              })}
            />
          );
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        isFullWidth: true,
        isHidden: modeName.value === 'add',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:enabledFlag'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}
