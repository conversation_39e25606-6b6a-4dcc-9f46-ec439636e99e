<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { type AnyObject, ProTable } from 'sun-biz';
  import { useAPIDesignTableConfig } from '@/modules/project/pages/codeRepositoryManage/apiPage/config/useTableConfig.tsx';
  import { buildTree } from '@/utils/buildTree.ts';
  import { queryDataTypeManageList } from '@/modules/baseConfig/api/dataTypeManage.ts';
  import { generateUUID } from '@sun-toolkit/shared';

  import {
    addApiPara,
    commitApiPara,
    deleteApiPara,
    designApi,
    editApiPara,
    queryApiRecordInfoByApiRecordId,
    unlockApi,
  } from '@/modules/project/api/codeRepositoryManageAPIDesign';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();

  const inPutParaFlag = ref(0);
  const route = useRoute();
  const router = useRouter();
  const { query } = route;
  const { apiId } = route.params;
  const { codeRepositoryId, codeRepositoryName, codeRepositoryDesc } =
    route.query;
  const sourceData = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const apiDesignListTableRef = ref();
  const refreshIndex = ref(0);
  const expandedRowKeys = ref<string[]>([]); // 展开行
  // 初始值应为空数组（避免 undefined）
  const dataTypeManageList = ref<DataTypeManage.DataTypeManageList[]>([]);
  const apiRecordId = ref('');
  const apiDesignList = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>(
    [],
  );
  const treeData1 = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const treeData2 = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const loading = ref(false);

  const newRowData = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList>({
    apiParaName: '',
    apiParaDesc: '',
    mandatoryFlag: 1,
    sort: 1,
    inPutParaFlag: 0,
    dataTypeId: '',
    apiRecordParaIdParent: null,
    expiredFlag: 0,
    memo: '',
    apiRecordParaNo: 1,
    editable: true,
    key: generateUUID(),
  });
  watch(
    () => apiDesignList.value,
    () => {
      refreshIndex.value = refreshIndex.value + 1; //拖拽后页面不刷新
    },
  );

  const handleInPutParaFlagChange = () => {
    apiDesignList.value =
      inPutParaFlag.value === 0 ? treeData1.value : treeData2.value;
  };

  // 查询数据类型
  const queryDataTypeManageListData = async () => {
    let [, res] = await queryDataTypeManageList({});
    if (res?.success) {
      dataTypeManageList.value =
        res.data.map((item: DataTypeManage.DataTypeManageList) => ({
          label: item.dataTypeName,
          value: item.dataTypeId,
          desc: item.dataTypeDesc,
        })) || [];
    }
  };

  // 查找指定父 ID 对应的列表
  function findListByParentId(
    list: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
    parentId: string | null,
  ): CodeRepositoryManageAPIDesign.ApiRecordParaList[] {
    if (parentId === null) {
      return list;
    }
    for (const item of list) {
      if (item.apiRecordParaId === parentId) {
        item.children = item.children || [];
        return item.children;
      }
      if (item.children) {
        const result = findListByParentId(item.children, parentId);
        if (result.length > 0) {
          return result;
        }
      }
    }
    return [];
  }

  // 新增行函数
  const addNewRow = (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
  ): CodeRepositoryManageAPIDesign.ApiRecordParaList => {
    let targetList: CodeRepositoryManageAPIDesign.ApiRecordParaList[];
    let parentId: string | null;

    if (row) {
      // 如果传入行，新增行与传入行同级
      parentId = row.apiRecordParaIdParent;
    } else {
      // 如果未传入行，新增行与根节点同级
      parentId = null;
    }

    targetList = findListByParentId(apiDesignList.value, parentId);
    // 找出有 apiRecordParaId 的最大 sort 值
    let maxSortWithId = 1;
    for (const item of targetList) {
      if (item.apiRecordParaId && item.sort > maxSortWithId) {
        maxSortWithId = item.sort;
      }
    }
    const newRow: CodeRepositoryManageAPIDesign.ApiRecordParaList = {
      ...newRowData.value,
      apiRecordParaId: '',
      apiRecordParaIdParent: parentId,
      inPutParaFlag: inPutParaFlag.value,
      sort: maxSortWithId + 1,
      key: generateUUID(),
      children: [],
    };

    console.log('新增', newRow.sort);
    targetList.push(newRow);
    return newRow;
  };

  const insertNewRow = (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
  ) => {
    if (!row) return;

    const parentList = findParentList(apiDesignList.value, row);
    if (!parentList) {
      return;
    }

    const index = parentList.findIndex(
      (item) => item.apiRecordParaId === row.apiRecordParaId,
    );
    if (index === -1) {
      return;
    }

    const newSort = row.sort;

    const newRow = {
      ...newRowData.value,
      apiRecordParaId: '',
      apiRecordParaIdParent: row.apiRecordParaIdParent,
      inPutParaFlag: inPutParaFlag.value,
      sort: newSort,
      key: generateUUID(),
      children: [],
    };

    parentList.splice(index, 0, newRow);
  };
  const findParentList = (
    list: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
    row: CodeRepositoryManageAPIDesign.ApiRecordParaList,
  ): CodeRepositoryManageAPIDesign.ApiRecordParaList[] | null => {
    if (!row.apiRecordParaIdParent) {
      return list;
    }
    for (const item of list) {
      if (item.apiRecordParaId === row.apiRecordParaIdParent) {
        return item.children;
      }
      if (item.children) {
        const result = findParentList(item.children, row);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  // 添加子集方法
  const addChildRow = (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList | undefined,
  ) => {
    if (!row) return;
    row.children = row.children || [];

    // 找出当前层级有 apiRecordParaId 的最大 sort 值
    let maxSort = 1;
    for (const item of row.children) {
      if (item.apiRecordParaId && item.sort > maxSort) {
        maxSort = item.sort;
      }
    }

    // 确定新行的 sort 值
    let newSort = maxSort + 1;

    const newRow: CodeRepositoryManageAPIDesign.ApiRecordParaList = {
      ...newRowData.value,
      apiRecordParaId: '',
      apiRecordParaIdParent: row.apiRecordParaId,
      inPutParaFlag: inPutParaFlag.value,
      sort: newSort,
      key: generateUUID(),
      children: [],
    };

    row.children.push(newRow);
    console.log('添加子集', newRow.sort);
    expandedRowKeys.value.push(row.apiRecordParaId);
  };

  const commitApi = async () => {
    const options = {
      apiRecordId: apiRecordId.value,
    };
    const [, res] = await commitApiPara(options);
    if (res?.success) {
      history.back();
    }
  };

  const cancelApi = () => {
    const opt: CodeRepositoryManageAPIDesign.upsertAPI = {
      apiId,
    };
    ElMessageBox.confirm(
      t(
        'switch.ask.title',
        '您确定要 {{action}} "{{name}}" 吗，取消后数据将全部清空！',
        {
          action: t('apiDesign.cancel.design', '取消设计'),
          name: query.apiName,
        },
      ),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await unlockApi(opt);
      if (res?.success) {
        goBack();
      }
    });
  };

  // 返回主页
  const goBack = () => {
    const params = {
      codeRepositoryId: codeRepositoryId,
    };
    const query = {
      codeRepositoryName: codeRepositoryName,
      codeRepositoryDesc: codeRepositoryDesc,
    };
    router.push({
      name: 'apiPage',
      params,
      query,
    });
  };

  function isObjectEmpty(obj: object): boolean {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        return false;
      }
    }
    return true;
  }

  // 构造树形数据
  const buildTreeDataFromApi = (
    list?: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
  ) => {
    const tree1 = list?.filter((item) => item.inPutParaFlag === 0) || [];
    const tree2 = list?.filter((item) => item.inPutParaFlag === 1) || [];
    treeData1.value = isObjectEmpty(buildTree(tree1))
      ? [{ ...newRowData.value }]
      : buildTree(tree1);
    treeData2.value = isObjectEmpty(buildTree(tree2))
      ? [{ ...newRowData.value, inPutParaFlag: 1 }]
      : buildTree(tree2);
    apiDesignList.value =
      inPutParaFlag.value === 0 ? treeData1.value : treeData2.value;
  };

  const designApiByApiId = async () => {
    loading.value = true;

    const params: CodeRepositoryManageAPIDesign.upsertAPI = {
      apiId,
    };
    let [, res] = await designApi(params);
    if (res?.success) {
      sourceData.value = res.data.apiRecordParaList;
      apiRecordId.value = res.data.apiRecordId as string;
      if (res.data.apiRecordParaList.length > 0) {
        // 处理数据
        buildTreeDataFromApi(res.data.apiRecordParaList);
      } else {
        // 如果没有数据，直接赋值

        treeData1.value = [{ ...newRowData.value }];
        treeData2.value = [{ ...newRowData.value, inPutParaFlag: 1 }];
        apiDesignList.value = treeData1.value;
      }
    }
    loading.value = false;
  };
  const queryApiRecordInfoByApiRecordIdFn = async (id: string) => {
    loading.value = true;
    const params = {
      apiRecordId: id,
    };
    let [, res] = await queryApiRecordInfoByApiRecordId(params);
    if (res?.success) {
      sourceData.value = res.data;
      if (res.data.length > 0) {
        // 处理数据
        buildTreeDataFromApi(res.data);
      } else {
        // 如果没有数据，直接赋值

        treeData1.value = [{ ...newRowData.value }];
        treeData2.value = [{ ...newRowData.value, inPutParaFlag: 1 }];
        apiDesignList.value = treeData1.value;
      }
    }
    loading.value = false;
  };

  function handleExpandChange(row: AnyObject, expanded: boolean) {
    if (expanded) {
      expandedRowKeys.value.push(row.apiRecordParaId);
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter(
        (item) => item !== row.apiRecordParaId,
      );
    }
  }

  // 递归校验所有节点
  function validateTree(
    tree: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
  ): boolean {
    for (const node of tree) {
      if (node.editable) {
        // 这里可以根据实际业务调整校验规则
        if (!node.apiParaName || !node.apiParaDesc) {
          return false;
        }
      }
      if (node.children && node.children.length > 0) {
        if (!validateTree(node.children)) {
          return false;
        }
      }
    }
    return true;
  }

  // 递归取消编辑/删除未保存节点，并能正确删除父数组中的节点
  function cancelEditTree(
    tree: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
  ) {
    for (let i = tree.length - 1; i >= 0; i--) {
      const node = tree[i];
      if (!node.apiRecordParaId && node.editable) {
        tree.splice(i, 1);
        continue;
      }
      if (node.children && node.children.length > 0) {
        cancelEditTree(node.children);
      }
      if (node.editable) {
        if (node.key && originalRowData.value[node.key]) {
          const old = originalRowData.value[node.key];
          Object.assign(node, old);
          node.editable = false;
          delete originalRowData.value[node.key];
        }
      }
    }
  }

  const cancelEditRow = (
    row: CodeRepositoryManageAPIDesign.ApiRecordParaList,
    sort: number,
  ) => {
    console.log(row);
    // 找到 row 所在的父数组
    const parentList =
      findParentList(apiDesignList.value, row) || apiDesignList.value;
    const idx = parentList.indexOf(row);
    if (idx !== -1) {
      // 递归取消当前节点及其所有子节点
      cancelEditTree([row]);
      // 如果是新增未保存的节点，直接删除
      if (!row.apiRecordParaId && row.editable) {
        parentList.splice(idx, 1);
      }
    }
    cancelEdit(row, sort);
  };

  const saveRow = async (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList,
  ) => {
    // 递归校验所有节点，未通过则阻止保存
    if (!validateTree(apiDesignList.value)) {
      ElMessage.error('请完善所有必填项！');
      return;
    }
    console.log('保存单条数据', row);
    if (row?.apiRecordParaId) {
      // 编辑
      await editApiPara(row);
    } else {
      // 新增
      // const newRow = flattenTree([row]);
      const options = {
        apiRecordId: apiRecordId.value,
        apiRecordParaList: [row],
      };
      await addApiPara(options);
    }

    queryApiRecordInfoByApiRecordIdFn(apiRecordId.value as string);
  };
  const deleteApiParaApi = (
    row?: CodeRepositoryManageAPIDesign.ApiRecordParaList,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row?.apiParaName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        await deleteApiPara({ apiRecordParaId: row.apiRecordParaId });
        queryApiRecordInfoByApiRecordIdFn(apiRecordId.value as string);
      })
      .catch(() => {});
  };

  // 记录编辑前的原始数据
  const originalRowData = ref<{
    [key: string]: CodeRepositoryManageAPIDesign.ApiRecordParaList;
  }>({});
  onMounted(() => {
    // 获取数据类型
    queryDataTypeManageListData();
    // 请求出入参接口
    designApiByApiId();
  });

  const { tableColumns, cancelEdit } = useAPIDesignTableConfig({
    id: 'apiRecordParaId',
    tableRef: apiDesignListTableRef,
    data: apiDesignList,
    dataTypeManageList: dataTypeManageList,
    sourceData: sourceData,
    addNewRow,
    insertNewRow,
    addChildRow,
    saveRow,
    deleteApiParaApi,
    cancelEditRow,
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <el-page-header @back="goBack">
      <template #content>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item class="text-base">
            <span
              >{{ query.codeRepositoryName }}({{ query.codeRepositoryDesc }})
            </span>
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ query.apiName }}</el-breadcrumb-item>
        </el-breadcrumb>
      </template>
    </el-page-header>
    <div class="mb-2 mt-2 flex justify-between">
      <el-radio-group
        v-model="inPutParaFlag"
        size="small"
        @change="handleInPutParaFlagChange"
      >
        <el-radio-button :value="0" label="入参" />
        <el-radio-button :value="1" label="出参" />
      </el-radio-group>
      <div>
        <el-button type="primary" @click="commitApi">
          {{ $t('apiDesign.commit', '提交') }}
        </el-button>
        <el-button plain type="primary" @click="cancelApi">
          {{ $t('apiDesign.cancel.design', '取消设计') }}
        </el-button>
      </div>
    </div>
    <ProTable
      :key="refreshIndex"
      ref="apiDesignListTableRef"
      :columns="tableColumns"
      :data="apiDesignList"
      :editable="true"
      :expand-row-keys="expandedRowKeys"
      :highlight-current-row="true"
      :loading="loading"
      :tree-props="{ children: 'children' }"
      row-class-name="cursor-pointer"
      row-key="apiRecordParaId"
      @expand-change="handleExpandChange"
    />
  </div>
</template>
