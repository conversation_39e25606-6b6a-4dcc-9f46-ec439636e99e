<script lang="ts" name="apiUpsertDialog" setup>
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ProDialog, ProForm } from 'sun-biz';
  import {
    addApi,
    editApi,
  } from '@modules/project/api/codeRepositoryManageAPI.ts';
  import { queryCodeRepositoryByExample } from '@modules/project/api/codeRepositoryManage.ts';

  import { useAPIUpsertConfig } from '../config/useFormConfig.tsx';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { queryInterfaceListByExample } from '@/modules/baseConfig/api/interface.ts';
  import { SelectOptions } from '@/typings/common.ts';
  import { useRoute } from 'vue-router';

  const classNameRef = ref<HTMLInputElement | null>(null);
  const methodNameRef = ref<HTMLInputElement | null>(null);

  const props = defineProps<{
    mode: string;
    data: CodeRepositoryManage.UpsertParams;
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: CodeRepositoryManage.UpsertParams;
  }>();

  const codeRepositoryList = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const interfaceList = ref<SelectOptions[]>([]);
  const dialogRef = ref();
  const dialogForm = ref({});
  const emits = defineEmits<{ success: [] }>();
  const route = useRoute();
  const modeName = ref('');
  watch(
    () => props.data,
    () => {
      modeName.value = props.mode;
      dialogForm.value = cloneDeep(props.data);
      const routeParams = route.params;
      dialogForm.value.codeRepositoryId = routeParams.codeRepositoryId;
      queryCodeRepositoryData();
      queryInterfaceList();
    },
    {
      deep: true,
      immediate: true,
    },
  );

  async function queryCodeRepositoryData() {
    const params = {
      enabledFlag: ENABLED_FLAG.YES,
      pageNumber: -1,
    };
    const [, res] = await queryCodeRepositoryByExample(params);
    if (res?.success) {
      res.data.sort(
        (
          a: CodeRepositoryManage.CodeRepositoryInfo,
          b: CodeRepositoryManage.CodeRepositoryInfo,
        ) => {
          return Number(a?.sort) - Number(b?.sort);
        },
      );
      codeRepositoryList.value = (res.data || []).map(
        (item: ComodityCategory.ComodityCategoryInfo) => ({
          label: item?.codeRepositoryName,
          value: item?.codeRepositoryId,
        }),
      );
    }
  }

  async function queryInterfaceList() {
    const [, res] = await queryInterfaceListByExample({
      enabledFlag: 1,
      pageNumber: -1,
    });
    if (res?.success) {
      interfaceList.value = (res.data || []).map((item) => ({
        label: item?.interfaceName as string,
        value: item?.interfaceId as string,
      }));
    }
  }

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (modeName.value === 'add') {
            const [, res] = await addApi(params);
            isSuccess = !!res?.success;
          } else if (modeName.value === 'edit') {
            const [, res] = await editApi(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const saveApiCategoryItem = async (
    apiCategoryItem?: CodeRepositoryManageAPI.UpsertApiCategory | undefined,
  ) => {
    if (apiCategoryItem?.apiCategoryId) {
      const { apiCategoryId, className, classNamePostMapping } =
        apiCategoryItem;
      dialogForm.value.apiCategoryId = apiCategoryId;
      dialogForm.value.className = className;
      dialogForm.value.classNamePostMapping = classNamePostMapping;

      await nextTick(() => {
        if (className) {
          if (methodNameRef.value) {
            methodNameRef.value.focus();
          }
        } else {
          if (classNameRef.value) {
            classNameRef.value.focus();
          }
        }
      });
    }
  };

  const formConfig = useAPIUpsertConfig(
    dialogForm,
    codeRepositoryList,
    interfaceList,
    saveApiCategoryItem,
    classNameRef,
    methodNameRef,
    modeName,
  );

  defineExpose({ dialogRef });
  onMounted(() => {
    // queryCodeRepositoryData();
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :title="`${$t(`global:${modeName}`)}${$t('codeRepositoryManage.name', 'API')}`"
    :width="700"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="2"
      :data="formConfig"
    />
  </ProDialog>
</template>
