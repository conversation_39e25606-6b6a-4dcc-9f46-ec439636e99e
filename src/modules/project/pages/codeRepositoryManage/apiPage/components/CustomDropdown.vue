<script lang="ts" setup>
  import { computed } from 'vue';
  import { Delete, Edit, Lock, Setting } from '@element-sun/icons-vue';
  import { MAIN_APP_CONFIG, useAppConfigData } from 'sun-biz';

  const userInfo = useAppConfigData(MAIN_APP_CONFIG.USER_INFO);

  // 定义props，使用默认空函数处理缺失的回调
  const props = defineProps({
    row: {
      type: Object as () => CodeRepositoryManageAPI.ApiList,
      required: true,
    },
    onOpenApiDialog: {
      type: Function as unknown as () => (
        mode: string,
        row: CodeRepositoryManageAPI.ApiList,
      ) => void,
      required: true,
    },
    deleteApiAction: {
      type: Function as unknown as () => (
        row: CodeRepositoryManageAPI.ApiList,
      ) => void,
      required: true,
    },
    handleDesignApi: {
      type: Function as unknown as () => (
        row: CodeRepositoryManageAPI.ApiList,
      ) => void,
      required: true,
    },
    unlockRow: {
      type: Function as unknown as () => (
        row: CodeRepositoryManageAPI.ApiList,
      ) => void,
      default: () => {
        console.warn('未提供unlock函数');
        return () => {};
      },
    },
    isCloudEnv: {
      type: Boolean,
      default: false,
    },
  });

  // 计算属性
  const iconColor = computed(() =>
    props.row.lockedByUserId === userInfo?.userId ? '#22c55e' : '#eab308',
  );

  const disabledFlag = computed(() => {
    if (props.row && props.row?.lockedByUserId) {
      return props.row?.lockedByUserId !== userInfo?.userId;
    }
    return false;
  });

  function onDesignApi() {
    props.handleDesignApi(props.row);
  }

  function onEdit() {
    props.onOpenApiDialog('edit', props.row);
  }

  function onDelete() {
    props.deleteApiAction(props.row);
  }

  function onUnlock() {
    if (!disabledFlag.value) {
      props.unlockRow(props.row);
    }
  }
</script>

<template>
  <el-dropdown class="w-full" trigger="contextmenu">
    <div class="flex w-full items-center justify-start">
      <el-tooltip
        :content="`${props.row.apiNo} ${props.row.apiName}`"
        effect="dark"
        style="width: 100%"
      >
        <div class="flex items-center justify-start">
          <div>
            <div
              :style="{
                width: '10px',
                height: '10px',
                borderRadius: '50%',
                marginRight: '4px',
                display: 'inline-block',
                backgroundColor: props.row.enabledFlag ? '#00ab44' : '#EF4444',
              }"
            ></div>
          </div>

          <span class="block w-full cursor-pointer">{{
            props.row.apiName
          }}</span>
        </div>
      </el-tooltip>
      <el-tooltip
        :content="`${props.row.lockedByUserName}于${props.row.lockedAt}锁定`"
        effect="dark"
        placement="top"
      >
        <el-icon
          v-show="props.row.lockedFlag === 1"
          :style="{ color: iconColor, cursor: 'pointer' }"
          class="ml-2"
          @click="onUnlock"
        >
          <Lock />
        </el-icon>
      </el-tooltip>
    </div>

    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          :disabled="disabledFlag"
          :style="{
            color: disabledFlag ? '#999' : '#2468da',
          }"
          class="flex items-center justify-center"
          @click="onDesignApi"
        >
          <el-icon>
            <Setting />
          </el-icon>
          设计API
        </el-dropdown-item>
        <el-dropdown-item
          :disabled="!props.isCloudEnv"
          :style="{
            color: !props.isCloudEnv ? '#999' : '#2468da',
          }"
          class="flex items-center justify-center"
          @click="onEdit"
        >
          <el-icon>
            <Edit />
          </el-icon>
          编辑
        </el-dropdown-item>
        <el-dropdown-item
          :disabled="!props.isCloudEnv"
          :style="{
            color: !props.isCloudEnv ? '#999' : 'red',
          }"
          class="flex items-center justify-center"
          @click="onDelete"
        >
          <el-icon>
            <Delete />
          </el-icon>
          删除
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
