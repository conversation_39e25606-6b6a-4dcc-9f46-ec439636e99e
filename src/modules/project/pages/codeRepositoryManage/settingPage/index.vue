<script lang="ts" name="settingPage" setup>
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProTable,
    TableRef,
    useAppConfigData,
  } from 'sun-biz';
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { useCodeRepositorySettingTableConfig } from '@/modules/project/pages/codeRepositoryManage/config/useTableConfig.tsx';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    queryCodeRepositoryByExample,
    saveCodeRepoXAppConfig,
  } from '@modules/project/api/codeRepositoryManage.ts';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { queryAppConfigByExample } from '@/modules/baseConfig/api/appConfigSetting.ts';

  const { t } = useTranslation();
  const router = useRouter();
  const route = useRoute();
  const codeRepositoryId = ref<string>('');
  const codeRepositoryName = ref<string>('');
  const selections = ref<CodeRepositoryManage.CodeRepoXAppConfigList[]>([]);
  const codeRepositorySettingTableRef = ref<TableRef>();
  const codeRepositoryInfo = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const codeRepoXAppConfigList = ref<
    CodeRepositoryManage.CodeRepoXAppConfigList[]
  >([]);
  const appConfigSettingList = ref<AppConfigSetting.AppConfigList[]>([]);
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const loading = ref(false);

  const bizData = computed(() => {
    const list = selections.value
      .map((item) => {
        return item.codeRepoXAppConfigId;
      })
      .filter(Boolean);
    return list;
  });

  const queryCodeRepositoryData = async () => {
    loading.value = true;
    try {
      const params = {
        keyWord: '',
        codeRepositoryIds: [codeRepositoryId.value],
      };
      const [, res] = await queryCodeRepositoryByExample(params);
      if (res?.success) {
        codeRepositoryInfo.value = res.data[0] || [];
        codeRepoXAppConfigList.value =
          res.data[0]?.codeRepoXAppConfigList || [];
      }
    } finally {
      loading.value = false;
    }
  };

  // 查询软件配置项
  const queryAppConfigByExampleList = async () => {
    const [, res] = await queryAppConfigByExample({});
    if (res?.success) {
      appConfigSettingList.value = res?.data ?? [];
    }
  };
  const canUpsertTableRow = () => {
    const isEditing = codeRepoXAppConfigList.value.some(
      (item) => !!item.editable,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  const handleSelectChange = (
    value: CodeRepositoryManage.CodeRepositorySetting[],
  ) => {
    selections.value = value;
  };

  const saveSetting = async () => {
    const params = {
      codeRepositoryId: codeRepositoryInfo.value.codeRepositoryId,
      codeRepoXAppConfigList: codeRepoXAppConfigList.value.map((item) => {
        return {
          codeRepoXAppConfigId: item.codeRepoXAppConfigId,
          appConfigItemId: item.appConfigItemId,
        };
      }),
    };
    const [, res] = await saveCodeRepoXAppConfig(params);
    if (res?.success) {
      queryCodeRepositoryData();
    }
  };
  const editRow = (item: CodeRepositoryManage.CodeRepositorySetting) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(item);
  };
  const insertRow = () => {
    if (!canUpsertTableRow()) return;

    const newRow = {
      appConfigItemId: '',
      editable: true,
    };
    codeRepoXAppConfigList.value.push(newRow);
  };
  const handleSave = async (
    item: CodeRepositoryManage.CodeRepoXAppConfigList,
  ) => {
    item.appConfigName = appConfigSettingList.value.find(
      (o) => o.appConfigItemId === item.appConfigItemId,
    )?.appConfigItemName;
    toggleEdit(item);
  };
  const deleteItem = async (
    item: CodeRepositoryManage.CodeRepoXAppConfigList,
    index: number,
  ) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: item.appConfigName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      codeRepoXAppConfigList.value.splice(index, 1);
      await saveSetting();
      codeRepositorySettingTableRef?.value.proTableRef.clearSelection();
      selections.value = [];
    });
  };

  const cancel = () => {
    router.push({
      name: 'codeRepositoryManage',
    });
  };

  const { tableColumns, toggleEdit } = useCodeRepositorySettingTableConfig(
    'codeRepoXAppConfigId',
    codeRepositorySettingTableRef,
    codeRepoXAppConfigList,
    appConfigSettingList,
    insertRow,
    handleSave,
    editRow,
    deleteItem,
    isCloudEnv,
  );

  onMounted(() => {
    codeRepositoryId.value = (route.query.codeRepositoryId as string) || '';
    codeRepositoryName.value = (route.query.codeRepositoryName as string) || '';
    queryCodeRepositoryData();
    queryAppConfigByExampleList();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item text-base">
        {{ $t('hospitalList.searchForm.productName', '仓库名称') }}：
        <el-button link>{{ codeRepositoryName }}</el-button>
      </div>
      <div>
        <el-button type="primary" @click="saveSetting">
          {{ $t('global:save') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_CODE_REPO_X_APP_CONFIG"
          class="mx-3"
          @success="
            () => {
              codeRepositorySettingTableRef?.proTableRef?.clearSelection();
              selections = [];
            }
          "
        />
        <el-button plain type="primary" @click="cancel">
          {{ $t('global:cancel', '取消') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="codeRepositorySettingTableRef"
      :columns="tableColumns"
      :data="codeRepoXAppConfigList"
      :editable="true"
      :loading="loading"
      row-key="codeRepoXAppConfigId"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
