import { ComputedRef, Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { UserReqItem, UserReqParams } from '@/api/types';

export function useCodeRepositorySearchFormConfig(
  queryCodeRepositoryData: (data?: CodeRepositoryManage.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryCodeRepositoryData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryCodeRepositoryData({
              keyWord: '',
            });
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}

export function useCodeRepositoryUpsertFormConfig(
  disabled: Ref<boolean>,
  codeRepositoryTypeCodeList: Ref<SelectOptions[]>,
  orgId: ComputedRef<string | undefined>,
  getUserList: (params: UserReqParams) => Promise<void>,
  userList: Ref<UserReqItem[]>,
  changeCodeRepositoryTypeCode: (code?: string) => void,
  portRequire: Ref<boolean>,
  onChangeCodeRepositoryName: (name: string) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('codeRepositoryManage.form.codeRepositoryName', '仓库名称'),
        name: 'codeRepositoryName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryName',
                '仓库名称',
              ),
            }),
        extraProps: {
          disabled: disabled.value,
          onBlur: (e: Event) => {
            onChangeCodeRepositoryName((e.target as HTMLInputElement).value);
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryName',
                '仓库名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('codeRepositoryManage.form.servicePrefix', '服务前缀'),
        name: 'servicePrefix',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('codeRepositoryManage.form.servicePrefix', '服务前缀'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('codeRepositoryManage.form.servicePrefix', '服务前缀'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'codeRepositoryDesc',
        isFullWidth: true,
        label: t('codeRepositoryManage.form.codeRepositoryDesc', '仓库描述'),
        component: 'input',
        type: 'textarea',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryDesc',
                '仓库描述',
              ),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryDesc',
                '仓库描述',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('codeRepositoryManage.form.codeRepositoryAddr', '仓库地址'),
        name: 'codeRepositoryAddr',
        component: 'input',
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryAddr',
                '仓库地址',
              ),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'codeRepositoryManage.form.codeRepositoryAddr',
                '仓库地址',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'codeRepositoryTypeCode',
        label: t(
          'codeRepositoryManage.form.codeRepositoryTypeCode',
          '仓库类型',
        ),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t(
                'codeRepositoryManage.form.codeRepositoryTypeCode',
                '仓库类型',
              ),
            }),
        extraProps: {
          options: codeRepositoryTypeCodeList.value,
          className: 'w-80',
          onChange: (val: string) => {
            changeCodeRepositoryTypeCode(val);
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'codeRepositoryManage.form.codeRepositoryTypeCode',
                '仓库类型',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'port',
        label: t('appManage.appManageTable.port', '端口'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appManage.appManageTable.port', '端口'),
        }),
        isHidden: portRequire.value,
        rules: [
          {
            required: !portRequire.value,
            message: t('global:placeholder.input.template', {
              content: t('appManage.appManageTable.port', '端口'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            pattern:
              /^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
            message: t(
              'appManage.appManageTable.portTips',
              '端口应在 1-65535之间',
            ),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'loginUserId',
        label: t('codeRepositoryManage.form.loginUserId', '登录用户'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('codeRepositoryManage.form.loginUserId', '登录用户'),
            }),
        extraProps: {
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: userList.value,
          remoteMethod: (keyWord: string) => {
            getUserList({
              keyWord: keyWord,
              pageSize: 300,
              hospitalId: orgId.value || '',
            });
          },
          props: {
            label: 'userName',
            value: 'userId',
          },
        },
      },
      {
        name: 'appPath',
        isFullWidth: true,
        label: t('appManage.appManageTable.appPath', '应用程序路径'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appManage.appManageTable.appPath', '应用程序路径'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('appManage.appManageTable.appPath', '应用程序路径'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'backupPath',
        isFullWidth: true,
        label: t('appManage.appManageTable.backupPath', '备份路径'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appManage.appManageTable.backupPath', '备份路径'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('appManage.appManageTable.backupPath', '备份路径'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
    ],
  });
}
