import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { useColumnConfig, useFormConfig } from 'sun-biz';

export function useCodeBranchFormConfig(
  queryCodeBranchData: (data?: CodeRepositoryManage.codeBranchSearch) => void,
  disabled: false,
  hospitalList: Ref<SelectOptions[]>,
) {
  return useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-50',
        extraProps: {
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryCodeBranchData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
      {
        name: 'hospitalId',
        label: t('person.belongHospital', '所属医院'),
        component: 'select',
        placeholder: disabled
          ? ''
          : t('global:placeholder.select.template', {
              name: t('person.belongHospital', '所属医院'),
            }),
        extraProps: {
          options: hospitalList.value,
          className: 'w-80',
        },
      },
      // {
      //   label: t('person.belongHospital.', '所属医院'),
      //   name: 'hospitalId',
      //   component: 'select',
      //   placeholder: t('global:placeholder.keyword'),
      //   extraProps: {
      //     style: { width: '170px' },
      //     onChange: (val: string) => {
      //       queryCodeBranchData({ hospitalId: val });
      //     },
      //   },
      // },
    ],
  });
}

export function useCodeBranchTableConfig(renderItem) {
  return useColumnConfig({
    getData: (t) => [
      {
        ...renderItem(t),
      },
      {
        label: t(
          'codeBranchManage.codeBranchManageTable.codeBranchName',
          '代码分支名称',
        ),
        prop: 'codeBranchName',
        minWidth: 150,
      },
      {
        label: t('person.belongHospital', '所属医院'),
        minWidth: 170,
        prop: 'hospitalList',
        render: (row: CodeBranchManage.CodeBranchInfo) =>
          row.hospitalList?.length > 0 ? (
            <div>
              {row.hospitalList.map((item) => (
                <div>{item?.hospitalNameDisplay}</div>
              ))}
            </div>
          ) : (
            '--'
          ),
      },
    ],
  });
}
