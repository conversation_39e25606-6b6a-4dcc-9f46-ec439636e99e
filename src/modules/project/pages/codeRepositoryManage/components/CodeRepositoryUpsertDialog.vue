<script lang="ts" name="CodeRepositoryUpsertDialog" setup>
  import { computed, ref, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import {
    MAIN_APP_CONFIG,
    ProDialog,
    ProForm,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useGetUserInfo } from '@/hooks/useGetUserList';
  import {
    addCodeRepository,
    editCodeRepository,
  } from '@modules/project/api/codeRepositoryManage';
  import { useCodeRepositoryUpsertFormConfig } from '../config/useFormConfig';
  import { UserReqItem } from '@/api/types';

  const { userList, getUserList } = useGetUserInfo();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const orgId = computed(() => currentOrg?.orgId);
  const props = defineProps<{
    mode: string;
    data: CodeRepositoryManage.UpsertParams;
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: CodeRepositoryManage.UpsertParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const disabled = ref(false);
  const dialogForm = ref<CodeRepositoryManage.UpsertParams>({});
  const portRequire = ref(false);
  const emits = defineEmits<{ success: [] }>();

  const dataSetList = useFetchDataset([
    CodeSystemType.CODE_REPOSITORY_TYPE_CODE,
  ]);
  // 值类型代码数据
  const codeRepositoryTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.CODE_REPOSITORY_TYPE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  watch(
    () => props,
    () => {
      disabled.value = props.mode === 'view';
      dialogForm.value = cloneDeep(props.data);

      if (dialogForm.value.codeRepositoryTypeCode === '1') {
        portRequire.value = true;
        dialogForm.value.port = '';
      } else {
        portRequire.value = false;
      }
      if (
        props.data?.loginUserId &&
        !userList.value.some((item) => item.userId === props.data?.loginUserId)
      ) {
        userList.value.push({
          userId: props.data?.loginUserId,
          userName: props.data?.loginUserName || '',
        } as unknown as UserReqItem);
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addCodeRepository(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editCodeRepository(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const onChangeCodeRepositoryName = (name: string) => {
    if (name) {
      console.log(dialogForm.value.servicePrefix);
      if (
        dialogForm.value.servicePrefix === undefined ||
        dialogForm.value.servicePrefix.length <= 0
      ) {
        dialogForm.value.servicePrefix = name;
      }
    }
  };
  const handleClose = () => {
    dialogRef.value.close();
  };
  const changeCodeRepositoryTypeCode = (code?: string) => {
    console.log(code);
    portRequire.value = code === '1';
  };
  const formConfig = useCodeRepositoryUpsertFormConfig(
    disabled,
    codeRepositoryTypeCodeList,
    orgId,
    getUserList,
    userList,
    changeCodeRepositoryTypeCode,
    portRequire,
    onChangeCodeRepositoryName,
  );
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :title="`${$t(`global:${props.mode}`)}${$t('codeRepositoryManage.name', '代码仓库')}`"
    :width="900"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="2"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
