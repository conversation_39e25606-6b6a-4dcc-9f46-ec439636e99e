<script lang="tsx" name="CodeBranchDialog" setup>
  import { computed, ref } from 'vue';
  import { ProDialog, ProForm, ProTable } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { TFunction } from 'i18next';
  import {
    useCodeBranchFormConfig,
    useCodeBranchTableConfig,
  } from '../config/useBranchConfig';
  import { queryOrgList } from '@modules/system/api/org';
  import { ENABLED_FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { createRepositoryBranch } from '@modules/project/api/codeRepositoryManage';
  import { queryCodeBranchByExample } from '../../../api/codeBranchManage';

  const props = defineProps<{
    mode: string;
    selections: CodeRepositoryManage.CodeRepositoryInfo[];
    prviewCodeBranchData: CodeBranchManage.CodeBranchInfo[];
  }>();
  const dialogRef = ref();
  const codeBranchSearchForm = ref({ keyWord: '', hospitalId: '' });
  const tableData = ref<CodeBranchManage.CodeBranchInfo[]>([]);
  const hospitalList = ref<SelectOptions[]>([]);
  const selectionsList = ref<CodeBranchManage.CodeBranchInfo[]>([]);
  const branchListTemp = ref<CodeBranchManage.CodeBranchInfo[]>([]);
  const previewData = ref({
    codeRepositoryName: '',
  });
  const emits = defineEmits<{ success: [] }>();
  const { t } = useTranslation();
  const getHospitalList = async () => {
    let [, res] = await queryOrgList({});
    if (res?.success) {
      if (!res.data?.length) {
        hospitalList.value = [];
        return;
      }
      const hospitalData: SelectOptions[] = [];
      res.data.forEach((item: Org.Item) => {
        if (item.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
          hospitalData.push({
            label: item.orgName,
            value: item.orgId,
          });
        } else if (item.orgTypeCode === ORG_TYPE_CODE.GROUP) {
          if (item.subOrgList?.length) {
            item.subOrgList.forEach((subItem) => {
              if (subItem.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
                hospitalData.push({
                  label: subItem.orgName,
                  value: subItem.orgId,
                });
              }
            });
          }
        }
      });
      hospitalList.value = hospitalData;
    }
  };
  const queryCodeBranchData = async (): Promise<void> => {
    let [, res] = await queryCodeBranchByExample({
      keyWord: codeBranchSearchForm.value.keyWord,
    });
    if (res?.success) {
      branchListTemp.value = (res.data || []).filter(
        (item) => item.enabledFlag === ENABLED_FLAG.YES,
      );
      if (codeBranchSearchForm.value.hospitalId) {
        console.log(tableData.value);
        console.log(codeBranchSearchForm.value.hospitalId);
        tableData.value = res.data.filter(
          (item) =>
            item.hospitalList?.some(
              (hospital) =>
                hospital.hospitalId === codeBranchSearchForm.value.hospitalId,
            ) && item.enabledFlag === ENABLED_FLAG.YES,
        );
        console.log(tableData.value);
      } else {
        tableData.value = (res.data || []).filter(
          (item) => item.enabledFlag === ENABLED_FLAG.YES,
        );
      }
    }
  };
  const handleSelectChange = (val: CodeBranchManage.CodeBranchInfo[]) => {
    selectionsList.value = val;
  };
  const handleConfirm = async () => {
    if (props.mode !== 'add') {
      dialogRef.value.close();
      return;
    }
    const data = {
      codeRepostitoryIds: props.selections.map((item) => item.codeRepositoryId),
      codeBranchIds: selectionsList.value.map((item) => item.codeBranchId),
    };
    if (!data.codeBranchIds.length)
      return ElMessage.warning('请先勾选代码分支后进行保存！');
    let [, res] = await createRepositoryBranch(data);
    return new Promise<[never, unknown]>((resolve, reject) => {
      if (res?.success) {
        ElMessage.success(t('global:add.success'));
        dialogRef.value.close();
        resolve([] as unknown as [never, unknown]);
      } else {
        reject(['', new Error('接口错误')]);
      }
    });
  };
  const handleClose = () => {
    tableData.value = [];
    dialogRef.value.close();
    codeBranchSearchForm.value = {
      keyWord: '',
      hospitalId: '',
    };
  };
  const searchData = useCodeBranchFormConfig(
    queryCodeBranchData,
    false,
    hospitalList,
  );
  getHospitalList();
  queryCodeBranchData();
  const renderItem = (t: TFunction) => {
    if (props.mode === 'preview') {
      return {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      };
    } else {
      return {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      };
    }
  };
  const tableColumnsConfig = useCodeBranchTableConfig(renderItem);
  defineExpose({
    dialogRef,
    tableData,
    branchListTemp,
    queryCodeBranchData,
    previewData,
  });
  const title = computed(() => {
    const action = props.mode === 'add' ? '创建' : '查看';
    const repoName =
      props.mode === 'add' ? '' : previewData.value.codeRepositoryName;
    return `${action}${repoName}代码分支`;
  });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="handleConfirm"
    :include-footer="props.mode === 'add'"
    :title="$t('codeRepositoryManage.name', `${title}`)"
    :width="900"
    destroy-on-close
    @close="handleClose"
    @success="emits('success')"
  >
    <div v-if="props.mode === 'add'" class="el-form-item mr-3 mt-3 flex">
      <ProForm
        v-model="codeBranchSearchForm"
        :column="2"
        :data="searchData"
        :inline="true"
        class="inline"
      />
      <el-button type="primary" @click="queryCodeBranchData">
        {{ $t('global:search') }}
      </el-button>
    </div>

    <ProTable
      ref="tableRef"
      :columns="tableColumnsConfig"
      :data="tableData"
      max-height="401px"
      row-key="codeBranchId"
      @selection-change="handleSelectChange"
    />
    <div v-if="props.mode !== 'add'" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
