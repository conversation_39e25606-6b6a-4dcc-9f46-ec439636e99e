<script lang="ts" name="codeRepositoryManage" setup>
  import { computed, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { commonSort } from '@/api/common';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant';
  import { useCodeRepositoryTableConfig } from './config/useTableConfig.tsx';
  import { useCodeRepositorySearchFormConfig } from './config/useFormConfig.ts';
  import {
    editCodeRepository,
    queryCodeRepositoryByExample,
  } from '@modules/project/api/codeRepositoryManage';
  import CodeRepositoryUpsertDialog from '@/modules/project/pages/codeRepositoryManage/components/CodeRepositoryUpsertDialog.vue';
  import CodeBranchDialog from '@/modules/project/pages/codeRepositoryManage/components/CodeBranchDialog.vue';

  const router = useRouter();

  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<CodeRepositoryManage.QueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
    codeRepositoryIds: [],
  });
  const loading = ref(false);
  const codeRepositoryList = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const selections = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const codeRepositoryUpsertParams = ref<CodeRepositoryManage.UpsertParams>({});
  const codeRepositoryUpsertDialogRef = ref();
  const codeRepositoryUpsertDialogMode = ref('');
  const codeRepositoryTableRef = ref();
  const codeBranchDialogRef = ref();
  const codeBranchDialogModeMode = ref('');
  const prviewCodeBranchData = ref([]);

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.codeRepositoryId || '';
    });
  });

  async function queryCodeRepositoryData(
    data?: CodeRepositoryManage.QueryParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryCodeRepositoryByExample(params);
    loading.value = false;
    if (res?.success) {
      codeRepositoryList.value = (res.data || []).sort(
        (
          a: CodeRepositoryManage.CodeRepositoryInfo,
          b: CodeRepositoryManage.CodeRepositoryInfo,
        ) => {
          return Number(a?.sort) - Number(b?.sort);
        },
      );
    }
  }

  const onOpenCodeRepositoryDialog = (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => {
    codeRepositoryUpsertDialogMode.value = mode;
    if (mode === 'add') {
      codeRepositoryUpsertParams.value = {
        servicePrefix: '',
        enabledFlag: ENABLED_FLAG.YES,
      };
    } else if (mode === 'edit' && data) {
      const {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        enabledFlag,
        loginUserId,
        loginUserName,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
      } = data;
      codeRepositoryUpsertParams.value = {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        enabledFlag,
        loginUserId,
        loginUserName,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
      };
    }
    codeRepositoryUpsertDialogRef.value.dialogRef.open();
  };

  const handleSelectChange = (
    value: CodeRepositoryManage.CodeRepositoryInfo[],
  ) => {
    selections.value = value;
  };

  /** 拖拽排序 */
  const handleSortEnd = async (
    list: CodeRepositoryManage.CodeRepositoryInfo[],
  ) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.codeRepositoryId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.CODE_REPOSITORY,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryCodeRepositoryData();
    }
  };
  const onOpenCodeBranchDialog = (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => {
    if (mode === 'add' && !selections.value.length)
      return ElMessage.warning(
        t('codeBranch.selectCodeRepository', '请先选择要创建分支的代码仓库'),
      );
    codeBranchDialogModeMode.value = mode;
    codeBranchDialogRef.value.dialogRef.open();
    if (mode !== 'add' && data) {
      codeBranchDialogRef.value.previewData = data;
      const branchListTemp = codeBranchDialogRef.value.branchListTemp;
      codeBranchDialogRef.value.tableData = data.codeBranchList.map((item) => {
        return {
          ...item,
          hospitalList:
            branchListTemp.find(
              (branchItem: CodeBranchManage.CodeBranchInfo) =>
                branchItem.codeBranchId === item.codeBranchId,
            )?.hospitalList || [],
        };
      });
    } else {
      codeBranchDialogRef.value.queryCodeBranchData();
    }
  };
  const goToApiPage = (data?: CodeRepositoryManage.CodeRepositoryInfo) => {
    if (!data) return;
    const params = {
      codeRepositoryId: data.codeRepositoryId,
    };
    const query = {
      codeRepositoryName: data.codeRepositoryName,
      codeRepositoryDesc: data.codeRepositoryDesc,
    };
    router.push({
      name: 'apiPage',
      params,
      query,
    });
  };
  const goToSettingPage = (data?: CodeRepositoryManage.CodeRepositoryInfo) => {
    if (!data) return;
    const query = {
      codeRepositoryId: data.codeRepositoryId,
      codeRepositoryName: data.codeRepositoryName,
    };
    router.push({
      name: 'settingPage',
      query,
    });
  };

  async function handleEnableSwitch(
    row: CodeRepositoryManage.CodeRepositoryInfo,
  ) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.codeRepositoryName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
      } = row;
      const params = {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editCodeRepository(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryCodeRepositoryData();
      }
    });
  }

  queryCodeRepositoryData();
  const searchConfig = useCodeRepositorySearchFormConfig(
    queryCodeRepositoryData,
  );
  const tableColumnsConfig = useCodeRepositoryTableConfig(
    isCloudEnv,
    onOpenCodeRepositoryDialog,
    onOpenCodeBranchDialog,
    goToApiPage,
    handleEnableSwitch,
    goToSettingPage,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('codeRepositoryManage.list.title', '代码仓库列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="queryCodeRepositoryData"
        />
      </div>
      <div>
        <el-button
          :disabled="!isCloudEnv"
          type="primary"
          @click="onOpenCodeRepositoryDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.CODE_REPOSITORY"
          class="mx-3"
          @success="
            () => {
              codeRepositoryTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
        <el-button type="primary" @click="onOpenCodeBranchDialog('add')">
          {{ $t('codeBranch.add', '创建分支') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="codeRepositoryTableRef"
      :columns="tableColumnsConfig"
      :data="codeRepositoryList"
      :loading="loading"
      draggable
      row-key="codeRepositoryId"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>
  <CodeRepositoryUpsertDialog
    ref="codeRepositoryUpsertDialogRef"
    :data="codeRepositoryUpsertParams"
    :mode="codeRepositoryUpsertDialogMode"
    @success="queryCodeRepositoryData"
  />
  <CodeBranchDialog
    ref="codeBranchDialogRef"
    :mode="codeBranchDialogModeMode"
    :prview-code-branch-data="prviewCodeBranchData"
    :selections="selections"
    @success="queryCodeRepositoryData"
  />
</template>
