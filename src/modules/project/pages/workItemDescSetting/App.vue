<script setup lang="ts" name="workItemDescSetting">
  import { ref, onMounted, computed } from 'vue';
  import { Title, ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { commonSort } from '@/api/common';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useWorkItemXDescTableConfig } from './config/useTableConfig';
  import { useWorkItemXDescSearchFormConfig } from './config/useFormConfig';
  import {
    queryWorkItemXDescByExample,
    saveWorkItemXDesc,
  } from '@modules/project/api/projectManage';

  type WorkItemXDescItemEdit = ProjectManage.WorkItemXDescItem & {
    editable: boolean;
  };
  const { t } = useTranslation();
  const workItemXDescTableRef = ref();
  const searchParams = ref<ProjectManage.WorkItemXDescQueryParams>({});
  const loading = ref(false);
  const workItemXDescList = ref<WorkItemXDescItemEdit[]>([]);

  const dataSetList = useFetchDataset([
    CodeSystemType.WORK_ITEM_TYPE_CODE,
    CodeSystemType.WI_DESC_TYPE_CODE,
  ]);
  const workItemTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.WORK_ITEM_TYPE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const wIDescTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.WI_DESC_TYPE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  const queryWorkItemXDescList = async (
    data?: ProjectManage.WorkItemXDescQueryParams,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryWorkItemXDescByExample(searchParams.value);
    loading.value = false;
    if (res?.success) {
      workItemXDescList.value = (res.data || []).sort(
        (
          a: ProjectManage.WorkItemXDescItem,
          b: ProjectManage.WorkItemXDescItem,
        ) => {
          return Number(a?.sort) - Number(b?.sort);
        },
      );
    }
  };

  /** 拖拽排序 */
  const handleSortEnd = async (list: ProjectManage.WorkItemXDescItem[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.workItemXDescId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.WORK_ITEM_X_DESC,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      queryWorkItemXDescList();
    }
  };

  const handleSave = async () => {
    if (workItemXDescList.value.some((item) => !!item.editable)) {
      ElMessage({
        type: 'warning',
        message: t(
          'workItemDescSetting.errorTip.isEditing',
          '请先确定正在编辑的描述信息！',
        ),
      });
      return;
    }
    const params = {
      workItemXDescList: workItemXDescList.value.map((item) => ({
        workItemXDescId: item.workItemXDescId,
        workItemTypeCode: item.workItemTypeCode,
        workItemDescTypeCode: item.workItemDescTypeCode,
        deleteFlag: FLAG.NO,
      })),
    };
    const [, res] = await saveWorkItemXDesc(params);
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:save.success'),
      });
      queryWorkItemXDescList();
    }
  };

  const onAddClick = () => {
    addItem({ editable: true } as unknown as WorkItemXDescItemEdit);
  };

  const handleDelete = (
    data: ProjectManage.WorkItemXDescItem,
    index: number,
  ) => {
    ElMessageBox.confirm(
      t('workItemDescSetting.delete.ask.title', '您确定要删除“{{name}}”吗', {
        name: `${data.workItemTypeCodeDesc}-${data.workItemDescTypeCodeDesc}`,
      }),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          workItemXDescList: workItemXDescList.value.map((item, idx) => ({
            workItemXDescId: item.workItemXDescId,
            workItemTypeCode: item.workItemTypeCode,
            workItemDescTypeCode: item.workItemDescTypeCode,
            deleteFlag: idx === index ? FLAG.YES : FLAG.NO,
          })),
        };
        const [, res] = await saveWorkItemXDesc(params);
        if (res?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryWorkItemXDescList();
        }
      })
      .catch(() => {});
  };

  const searchConfig = useWorkItemXDescSearchFormConfig(workItemTypeCodeList);
  const { workItemXDescTableConfig, addItem } = useWorkItemXDescTableConfig(
    workItemXDescTableRef,
    workItemXDescList,
    workItemTypeCodeList,
    wIDescTypeCodeList,
    handleDelete,
  );

  onMounted(() => {
    queryWorkItemXDescList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('workItemDescSetting.list.title', '工作项描述设置列表')"
    />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          show-search-button
          :data="searchConfig"
          @model-change="queryWorkItemXDescList"
        />
      </div>
      <div class="flex-shrink-0">
        <el-button type="primary" @click="onAddClick()">
          {{ $t('global:add') }}
        </el-button>
        <el-button
          :disabled="!workItemXDescList.length"
          type="primary"
          @click="handleSave()"
        >
          {{ $t('global:save') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="workItemXDescTableRef"
      row-key="workItemXDescId"
      :loading="loading"
      :data="workItemXDescList"
      :columns="workItemXDescTableConfig"
      draggable
      editable
      @drag-end="handleSortEnd"
    />
  </div>
</template>
