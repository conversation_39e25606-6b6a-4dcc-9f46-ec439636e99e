import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { SelectOptions } from '@/typings/common';

export function useWorkItemXDescSearchFormConfig(
  workItemTypeCodeList: Ref<SelectOptions[]>,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('workItemDescSetting.search.workItemTypeCode', '工作项类型'),
        name: 'workItemTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataDict.search.workItemTypeCode', '工作项类型'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: workItemTypeCodeList.value,
          className: 'w-60',
        },
      },
    ],
  });
}
