import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10223-1]根据条件查询代码分支
 * @param params
 * @returns
 */
export const queryCodeBranchByExample = (
  params: CodeBranchManage.QueryParams,
) => {
  return dictRequest<CodeBranchManage.CodeBranchInfo[]>(
    '/codeRepository/queryCodeBranchByExample',
    params,
  );
};

/**
 * [1-10224-1]新增代码分支
 * @param params
 * @returns
 */
export const addCodeBranch = (params: CodeBranchManage.UpsertParams) => {
  return dictRequest<{ codeBranchId: string }>(
    '/codeRepository/addCodeBranch',
    params,
  );
};

/**
 * [1-10225-1]编辑代码分支
 * @param params
 * @returns
 */
export const editCodeBranch = (params: CodeBranchManage.UpsertParams) => {
  return dictRequest('/codeRepository/editCodeBranch', params);
};
