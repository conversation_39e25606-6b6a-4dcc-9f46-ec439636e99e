import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10307-1]查询 API
 * @param params
 * @returns
 */
export const queryApiByExample = (
  params: CodeRepositoryManageAPI.QueryParams,
) => {
  return dictRequest<CodeRepositoryManageAPI.ApiList[]>(
    '/apimanage/queryApiByExample',
    params,
  );
};
/**
 * [1-10308-1]新增API
 * @param params
 * @returns
 */
export const addApi = (params: CodeRepositoryManageAPI.UpsertApi) => {
  return dictRequest<CodeRepositoryManageAPI.ApiList>(
    '/apimanage/addApi',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10309-1]编辑 API
 * @param params
 * @returns
 */
export const editApi = (params: CodeRepositoryManageAPI.UpsertApi) => {
  return dictRequest<CodeRepositoryManageAPI.ApiList>(
    '/apimanage/editApi',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};
/**
 * [1-10310-1]删除 API
 * @param params
 * @returns
 */
export const deleteApi = (params: CodeRepositoryManageAPI.UpsertApi) => {
  return dictRequest<CodeRepositoryManageAPI.ApiList>(
    '/apimanage/deleteApi',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};

/**
 * [1-10320-1]查询 API 分类
 * @param params
 * @returns
 */
export const queryApiCategoryByExample = (
  params: CodeRepositoryManageAPI.QueryApiCategoryByExample,
) => {
  return dictRequest<CodeRepositoryManageAPI.ApiCategoryList[]>(
    '/apimanage/queryApiCategoryByExample',
    params,
  );
};
/**
 * [1-10321-1]新增 API 分类
 * @param params
 * @returns
 */
export const addApiCategory = (
  params: CodeRepositoryManageAPI.UpsertApiCategory,
) => {
  return dictRequest<CodeRepositoryManageAPI.ApiCategoryList>(
    '/apimanage/addApiCategory',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10322-1]编辑 API 分类
 * @param params
 * @returns
 */
export const editApiCategory = (
  params: CodeRepositoryManageAPI.UpsertApiCategory,
) => {
  return dictRequest<CodeRepositoryManageAPI.ApiCategoryList>(
    '/apimanage/editApiCategory',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};
/**
 * [1-10323-1]删除 API 分类
 * @param params
 * @returns
 */
export const deleteApiCategory = (
  params: CodeRepositoryManageAPI.UpsertApiCategory,
) => {
  return dictRequest<CodeRepositoryManageAPI.ApiCategoryList>(
    '/apimanage/deleteApiCategory',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};
