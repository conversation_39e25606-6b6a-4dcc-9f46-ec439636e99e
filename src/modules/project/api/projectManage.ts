import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10144-1]根据条件查询项目信息
 * @param params
 * @returns
 */
export const queryProjectByExample = (params: ProjectManage.QueryParams) => {
  return dictRequest<ProjectManage.ProjectInfo[]>(
    '/projectmanage/queryProjectByExample',
    params,
  );
};

/**
 * [1-10145-1]新增项目信息
 * @param params
 * @returns
 */
export const addProject = (params: ProjectManage.ProjectUpsertParams) => {
  return dictRequest<{ projectId: string }>(
    '/projectmanage/addProject',
    params,
    {
      successMsg: translation('global:add.success'),
      failMsg: translation('global:add.fail'),
    },
  );
};

/**
 * [1-10146-1]编辑项目信息
 * @param params
 * @returns
 */
export const editProject = (params: ProjectManage.ProjectUpsertParams) => {
  return dictRequest('/projectmanage/editProject', params, {
    successMsg: translation('global:edit.success'),
    failMsg: translation('global:edit.fail'),
  });
};

/**
 * [1-10169-1]根据条件查询项目团队信息
 * @param params
 * @returns
 */
export const queryPrjTeamInfoByExample = (
  params: ProjectManage.ProjectTeamQueryParams,
) => {
  return dictRequest<ProjectManage.ProjectTeamInfo[]>(
    '/projectmanage/queryPrjTeamInfoByExample',
    params,
  );
};

/**
 * [1-10170-1]保存项目团队人员信息
 * @param params
 * @returns
 */
export const savePrjTeamInfo = (params: {
  projectId: string;
  projectTeamerList: {
    proTeamerId?: string;
    userId: string;
    teamerTypeCode: string;
  }[];
}) => {
  return dictRequest<ProjectManage.ProjectTeamInfo[]>(
    '/projectmanage/savePrjTeamInfo',
    params,
  );
};

/**
 * [1-10239-1] 根据条件查询工作项描述对应关系
 * @param params
 * @returns
 */
export const queryWorkItemXDescByExample = (
  params: ProjectManage.WorkItemXDescQueryParams,
) => {
  return dictRequest<ProjectManage.WorkItemXDescItem[]>(
    '/projectmanage/queryWorkItemXDescByExample',
    params,
  );
};

/**
 * [1-10252-1] 保存工作项描述设置
 * @param params
 * @returns
 */
export const saveWorkItemXDesc = (params: {
  workItemXDescList: ProjectManage.WorkItemXDescUpsertItem[];
}) => {
  return dictRequest('/projectmanage/saveWorkItemXDesc', params);
};
