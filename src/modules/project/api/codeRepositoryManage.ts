import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10226-1]根据条件查询代码仓库
 * @param params
 * @returns
 */
export const queryCodeRepositoryByExample = (
  params: CodeRepositoryManage.QueryParams,
) => {
  return dictRequest<CodeRepositoryManage.CodeRepositoryInfo[]>(
    '/codeRepository/queryCodeRepositoryByExample',
    params,
  );
};

/**
 * [1-10227-1]新增代码仓库
 * @param params
 * @returns
 */
export const addCodeRepository = (
  params: CodeRepositoryManage.UpsertParams,
) => {
  return dictRequest<{ codeRepositoryId: string }>(
    '/codeRepository/addCodeRepository',
    params,
  );
};

/**
 * [1-10228-1]编辑代码仓库
 * @param params
 * @returns
 */
export const editCodeRepository = (
  params: CodeRepositoryManage.UpsertParams,
) => {
  return dictRequest('/codeRepository/editCodeRepository', params);
};

/**
 * [1-10247-1] 创建分支
 * @param params
 * @returns
 */
export const createRepositoryBranch = (
  params: CodeRepositoryManage.CreateBranchParams,
) => {
  return dictRequest('/codeRepository/createRepositoryBranch', params);
};

/**
 * [1-10425-1] 创建分支
 * @param params
 * @returns
 */
export const saveCodeRepoXAppConfig = (
  params: CodeRepositoryManage.CodeRepositorySetting,
) => {
  return dictRequest('/codeRepository/saveCodeRepoXAppConfig', params, {
    successMsg: translation(
      'codeRepositoryManage.codeRepositoryManageTable.operationSuccess',
      '操作成功',
    ),
  });
};
