<script setup lang="tsx" name="HospitalSelect">
  import { useAttrs, ref, computed } from 'vue';
  import { ORG_TYPE_CODE } from '@/typings/common';
  import { getHospitalSelections } from '@/utils/common';
  import { watch, onBeforeUnmount, onMounted } from 'vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';

  type Props = {
    useAllHospitalList?: boolean;
    automaticallySetValue?: boolean;
  };
  const props = withDefaults(defineProps<Props>(), {
    useAllHospitalList: false,
    automaticallySetValue: true,
  });

  const useConfigHospitalList = ref<boolean>(true);
  const hospitalSelections = ref<Org.Item[]>([]);
  const { currentOrg, hospitalList } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.HOSPITAL_LIST,
  ]);
  const isDisabled = computed(() => {
    return currentOrg?.orgTypeCode === ORG_TYPE_CODE.HOSPITAL;
  });

  const getOrgList = async () => {
    hospitalSelections.value = await getHospitalSelections();
  };

  watch(
    () => props,
    () => {
      useConfigHospitalList.value = true;
      if (props.useAllHospitalList) {
        getOrgList();
        useConfigHospitalList.value = false;
      } else {
        hospitalSelections.value = (hospitalList ||
          []) as unknown as Org.Item[];
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const attrs: {
    hospitalId?: string | undefined;
    modelValue?: string;
    'onUpdate:modelValue'?: (value: string) => void;
  } = useAttrs();

  const emit = defineEmits(['change']);
  /**
   *
   * @param value 下拉框选择
   */
  function change(value: string) {
    if (attrs['onUpdate:modelValue']) {
      attrs['onUpdate:modelValue'](value);
    }
    emit('change', value);
  }
  let stopWatch: () => void;
  /** 主动触发change */
  onMounted(() => {
    stopWatch = watch(
      () => currentOrg?.orgId,
      () => {
        const id =
          attrs?.hospitalId ||
          attrs?.modelValue ||
          (currentOrg?.orgTypeCode === ORG_TYPE_CODE.HOSPITAL
            ? currentOrg.orgId
            : hospitalSelections?.value[0]?.orgId);
        if (props.automaticallySetValue && id !== undefined) {
          change(id);
        }
      },
      {
        immediate: true,
      },
    );
  });
  onBeforeUnmount(() => {
    if (stopWatch) {
      stopWatch();
    }
  });
</script>
<template>
  <el-select
    @change="change"
    filterable
    clearable
    :placeholder="
      $t('global:placeholder.select.template', {
        name: $t('person.belongHospital', '所属医院'),
      })
    "
    :disabled="useConfigHospitalList && isDisabled"
    v-bind="{ ...attrs }"
  >
    <el-option
      v-for="item in hospitalSelections"
      :key="item.orgId"
      :label="item.orgName"
      :value="item.orgId"
    />
  </el-select>
</template>
