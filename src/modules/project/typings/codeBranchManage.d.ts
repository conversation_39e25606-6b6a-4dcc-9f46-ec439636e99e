declare namespace CodeBranchManage {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
  }

  interface CodeBranchInfo {
    codeBranchId: string;
    codeBranchName: string;
    codeBranchDesc: string;
    sort: number;
    enabledFlag: number;
    createdUserId: string;
    createdAt: string;
    modifiedUserId: string;
    modifiedAt: string;
    hospitalList: { hospitalId: string; hospitalNameDisplay: string }[];
    codeBranchTypeList: {
      codeBranchTypeCode: string;
      codeBranchTypeCodeDesc: string;
    }[];
    historyFlag: number;
  }

  interface UpsertParams {
    codeBranchId?: string;
    codeBranchName?: string;
    codeBranchDesc?: string;
    sort?: number;
    historyFlag?: number;
    enabledFlag?: number;
    hospitalList?: { hospitalId: string }[];
    codeBranchTypeCodes?: string[];
  }
}
