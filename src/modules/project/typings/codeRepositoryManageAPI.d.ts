declare namespace CodeRepositoryManageAPI {
  interface QueryParams {
    codeRepositoryId?: string;
    keyWord?: string;
    deletedFlag?: number;
    apiIds?: string[];
    enabledFlag?: number;
  }

  interface ApiList {
    apiId?: string;
    apiName?: string;
    apiNo?: string;
    interfaceId?: string;
    interfaceName?: string;
    queryFlag?: number;
    className?: string;
    classNamePostMapping: string;
    methodName?: string;
    codeRepositoryId?: string;
    codeRepositoryName?: string;
    deletedFlag?: number;
    apiCategoryId?: string;
    apiCategoryName?: string;
    lockedByUserId?: string;
    lockedByUserName?: string;
    lockedAt?: string;
    lockedFlag?: number;
    enabledFlag?: number;
  }

  interface UpsertApi {
    apiId?: string;
    apiName?: string;
    apiNo?: string;
    interfaceId?: string;
    queryFlag?: number;
    className?: string;
    methodName?: string;
    codeRepositoryId?: string;
    apiCategoryId?: string;
    enabledFlag?: number;
    classNamePostMapping?: string;
  }

  interface QueryApiCategoryByExample {
    keyWord?: string;
    enabledFlag?: number;
    deletedFlag?: number;
    codeRepositoryId?: string;
  }

  interface ApiCategoryList {
    apiCategoryId?: string;
    apiCategoryName?: string;
    className?: string;
    enabledFlag?: number;
    deletedFlag?: number;
    codeRepositoryId?: string;
    codeRepositoryName?: string;
  }

  interface UpsertApiCategory {
    apiCategoryName?: string;
    className?: string;
    codeRepositoryId?: string;
    apiCategoryId?: string;
    enabledFlag?: number;
    classNamePostMapping?: string;
  }
}
