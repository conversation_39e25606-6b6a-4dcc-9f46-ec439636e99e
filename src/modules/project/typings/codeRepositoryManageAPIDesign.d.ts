declare namespace CodeRepositoryManageAPIDesign {
  // 查询、锁定、解锁 API
  interface upsertAPI {
    apiId?: string | string[];
  }

  interface ApiXParaList {
    apiXParaId?: string;
    apiParaName?: string;
    apiParaDesc?: string;
    inParaFlag?: number;
    sort?: number;
    mandatoryFlag?: number;
    apiXParaIdParent?: string;
    apiParaId?: string;
    dataTypeId?: string;
    dataTypeName?: string;
    expiredFlag?: number;
    memo?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
  }

  // 新增 API 参数
  interface AddApiPara {
    apiRecordId?: string;
    apiRecordParaList?: ApiRecordParaList[];
  }

  interface ApiRecordParaList {
    apiParaId?: string;
    apiRecordParaId?: string;
    apiParaName?: string;
    apiParaDesc?: string;
    mandatoryFlag?: number;
    sort?: number;
    inPutParaFlag?: number;
    dataTypeId?: string;
    dataTypeName?: string;
    apiRecordParaIdParent?: string | null;
    apiXParaId?: string;
    apiXParaIdParent?: string;
    dateTypeOperateCode?: string;
    dateTypeOperateCodeDesc?: string;
    expiredFlag?: number;
    memo?: string;
    apiRecordParaNo?: number;
    hasChildren?: boolean;
    children?: ApiRecordParaList[];
    editable: boolean;
    key?: string;
    level?: number;
  }

  interface EditApiPara {
    apiRecordParaIdParent?: string;
    apiParaName?: string;
    apiParaDesc?: string;
    mandatoryFlag?: number;
    dataTypeId?: string;
    expiredFlag?: number;
    memo?: string;
  }

  // 查询、提交变更 记录信息
  interface ApiRecordId {
    apiRecordId?: string;
  }

  interface QueryApiParaByExample {
    keyWord?: string;
    enabledFlag?: number;
  }

  interface ApiParaList {
    apiParaId?: string;
    apiParaName?: string;
    apiParaDesc?: string;
    enabledFlag?: number;
    dataTypeId?: string;
    dataTypeName?: string;
    apiParaIdParent?: string;
  }

  interface DeleteApiPara {
    apiRecordParaId?: string;
  }
}
