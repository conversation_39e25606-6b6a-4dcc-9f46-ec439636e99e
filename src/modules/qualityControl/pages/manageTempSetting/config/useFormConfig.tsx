import { queryMrTempByExample } from '@/modules/qualityControl/api/manageTemplate';
import { CodeSystemType } from '@/typings/codeManage';
import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

// 查询质控模板表单配置
export function useManageTemplateSearchConfig(
  queryManageTemplateList: (
    data: ManageTemplate.SearchManageTemplateParams,
  ) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [CodeSystemType.MANAGE_RULE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('manageTemplate.search.tempeScope', '应用范围'),
        name: 'manageRuleScopeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-40',
          options: dataSet?.value
            ? dataSet.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE]
            : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },

      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryManageTemplateList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryManageTemplateList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

// 质控规则表单配置
export function useManageTemplateRuleConfig(
  mrTempList: Ref<ManageTemplate.MrTempItem[]>,
) {
  const data = useFormConfig({
    dataSetCodes: [CodeSystemType.MANAGE_RULE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('manageTemplate.form.manageTempName', '质控模板'),
        name: 'manageTempName',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input'),
        extraProps: {
          clearable: true,
        },
      },
      {
        label: t('manageTemplate.form.tempeScope', '质控范围'),
        name: 'manageRuleScopeCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          multiple: true,
          options: dataSet?.value
            ? dataSet.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE]
            : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
          style: { width: '250px' },
        },
      },
      {
        label: '是否启用',
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('manageTemplate.form.mrTemp', '病历模板'),
        name: 'mrTempListId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          multiple: true,
          options: mrTempList.value,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (query: string) =>
            queryMrTempByExample({ keyWord: query }),
          props: {
            label: 'mrTempName',
            value: 'mrTempId',
          },
        },
      },
    ],
  });
  return data;
}

// 查询提示词表单配置
export function useManageTemplatePromptSearchConfig(
  promptList: Ref<ManageTemplate.ManageTemplatePromptItem[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageTemplatePrompt.form.prompt', '提示词记录'),
        name: 'promptWordId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: false,
          filterable: true,
          options: promptList.value || [],
          props: {
            value: 'promptWordId',
            label: 'promptWordTitle',
          },
        },
      },
    ],
  });
  return data;
}

// 新增+编辑提示词表单配置
export function useManageTemplatePromptUpsertConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageTemplatePrompt.form.promptTitle', '提示词标题'),
        name: 'promptWordTitle',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input'),
        extraProps: {
          clearable: true,
          style: {
            width: '220px',
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}
