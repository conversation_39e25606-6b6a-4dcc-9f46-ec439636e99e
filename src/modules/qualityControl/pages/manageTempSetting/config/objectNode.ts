import { Node } from '@tiptap/core';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    objectNode: {
      insertObjectNode: (attrs: { id: string; name: string }) => ReturnType;
    };
  }
}

export interface ObjectNodeOptions {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  HTMLAttributes: Record<string, any>;
}

export const ObjectNode = Node.create<ObjectNodeOptions>({
  name: 'objectNode',
  inline: true,
  group: 'inline',
  atom: true,
  addOptions() {
    return {
      HTMLAttributes: {
        class: 'object-node text-blue-500',
      },
    };
  },

  renderText(node) {
    return '{{#' + node.node.attrs.id + '#}}';
  },

  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => ({
          'data-id': attributes.id,
        }),
      },
      name: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-name'),
        renderHTML: (attributes) => ({
          'data-name': attributes.name,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-id][data-name]',
        getAttrs: (element) => ({
          id: element.getAttribute('data-id'),
          name: element.getAttribute('data-name'),
        }),
      },
    ];
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      {
        ...this.options.HTMLAttributes,
        ...HTMLAttributes,
        'data-id': node.attrs.id,
        'data-name': node.attrs.name,
      },
      node.attrs.name,
    ];
  },

  addCommands() {
    return {
      insertObjectNode:
        (attrs) =>
        ({ chain }) => {
          return chain()
            .insertContent({
              type: this.name,
              attrs,
            })
            .run();
        },
    };
  },
});
