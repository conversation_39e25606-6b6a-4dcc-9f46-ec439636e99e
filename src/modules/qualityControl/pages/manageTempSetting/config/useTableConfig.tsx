import { ColumnProps, useColumnConfig } from 'sun-biz';
// 质控模板对应的质控规则表格配置
export function useManageRuleConfig() {
  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('qualityControl.table.ruleNo', '质控规则编号'),
        prop: 'manageRuleNo',
        minWidth: 120,
        render: (row: ManageTemplate.ManageTempXRule) => {
          return <div>{row.manageRuleNo || '--'}</div>;
        },
      },
      {
        label: t('qualityControl.table.objectType', '质控对象'),
        prop: 'manageObjectName',
        minWidth: 160,
        render: (row: ManageTemplate.ManageTempXRule) => {
          return <div>{row.manageObjectName || '--'}</div>;
        },
      },
      {
        label: t('qualityControl.table.ruleName', '质控对象类型'),
        prop: 'manageObjectTypeCodeDesc',
        minWidth: 160,
        render: (row: ManageTemplate.ManageTempXRule) => {
          return <div>{row.manageObjectTypeCodeDesc || '--'}</div>;
        },
      },
      {
        label: t('qualityControl.table.ruleType', '质控规则'),
        prop: 'manageRuleContent',
        minWidth: 250,
        render: (row: ManageTemplate.ManageTempXRule) => {
          return <div>{row.manageRuleContent || '--'}</div>;
        },
      },
    ],
  });
  return data;
}

// 质控规则编辑表格配置
export function useManageRuleSettingTableConfig(options: {
  canEdit?: boolean;
  canSelect?: boolean;
  onAddClick?: (index: number) => void;
  onDeleteClick?: (index: number) => void;
  onInputFocus?: (e: Event, index: number) => void;
}) {
  const { canEdit, canSelect, onAddClick, onDeleteClick, onInputFocus } =
    options;

  return useColumnConfig({
    getData: (t) => {
      let data: ColumnProps[] = [
        {
          label: t('qualityControl.table.ruleNo', '规则编号'),
          prop: 'manageRuleNo',
          minWidth: 200,
          render: (row: ManageTemplate.ManageTempXRule, index: number) => {
            if (!canEdit) {
              return <div>{row.manageRuleNo || '--'}</div>;
            }
            return !row.manageRuleId ? (
              <el-input
                placeholder={t('global:placeholder.select.template', {
                  name: t('qualityControl.table.ruleNo', '质控规则编号'),
                })}
                onClick={(e: Event) => {
                  onInputFocus?.(e, index);
                }}
              ></el-input>
            ) : (
              <div>{row.manageRuleNo || '--'}</div>
            );
          },
        },
        {
          label: t('qualityControl.table.objectType', '质控对象'),
          prop: 'manageObjectId',
          minWidth: 120,
          render: (row: ManageTemplate.ManageTempXRule) => {
            return <div>{row.manageObjectName || '--'}</div>;
          },
        },
        {
          label: t('qualityControl.table.ruleName', '规则内容'),
          prop: 'manageRuleContent',
          minWidth: 250,
          render: (row: ManageTemplate.ManageTempXRule) => {
            return <div>{row.manageRuleContent || '--'}</div>;
          },
        },
      ];
      if (canSelect) {
        data.unshift({
          prop: 'manageRuleId',
          editable: false,
          type: 'selection',
        });
      }
      if (canEdit) {
        data = data.concat([
          {
            label: t('qualityControl.table.deductPoints', '扣除分数'),
            prop: 'deductPoints',
            minWidth: 120,
            render: (row: ManageTemplate.ManageTempXRule) => {
              return (
                <el-input-number
                  v-model={row.deductPoints}
                  min={0}
                  max={30}
                  controls-position="right"
                  class="w-20"
                />
              );
            },
          },

          {
            label: t('global:operation'),
            prop: 'operation',
            minWidth: 120,
            render: (row: ManageTemplate.ManageTempXRule, index: number) => {
              return (
                <div class="flex justify-around">
                  <div>
                    <el-button
                      link={true}
                      type="danger"
                      onClick={() => onDeleteClick?.(index)}
                    >
                      {t('global:delete')}
                    </el-button>
                    <el-button
                      link={true}
                      type="primary"
                      onClick={() => onAddClick?.(index)}
                    >
                      {t('global:add')}
                    </el-button>
                  </div>
                </div>
              );
            },
          },
        ]);
      }
      return data;
    },
  });
}
