<script setup lang="tsx">
  import { queryManageTempByExample } from '@/modules/qualityControl/api/manageTemplate';
  import { ref } from 'vue';
  import { useRoute } from 'vue-router';
  import ManageRuleForm from '../components/ManageRuleForm.vue';

  const manageTemp = ref({} as ManageTemplate.ManageTemplateItem);

  const route = useRoute();
  const tempId = route.query.tempId;
  async function queryData() {
    if (!tempId) return;
    const [, res] = await queryManageTempByExample({
      manageTempIds: [tempId + ''],
    });
    if (res?.success) {
      const data = res.data || [];
      if (data.length) {
        manageTemp.value = data[0];
      }
    }
  }
  queryData();
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <ManageRuleForm
      :manage-temp="manageTemp"
      ref="manageRuleForm"
    ></ManageRuleForm>
  </div>
</template>
