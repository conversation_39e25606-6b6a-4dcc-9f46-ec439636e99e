<script setup lang="ts" name="reasonManage">
  import { queryManageTempByExample } from '@modules/qualityControl/api/manageTemplate';
  import { ProForm } from 'sun-biz';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import TemplateDetail from '../components/TemplateDetail.vue';
  import TemplateList from '../components/TemplateList.vue';
  import { useManageTemplateSearchConfig } from '../config/useFormConfig.tsx';

  const searchParams = ref<ManageTemplate.SearchManageTemplateParams>({
    keyWord: '',
  });
  const manageTempList = ref<ManageTemplate.ManageTemplateItem[]>([]);
  const loading = ref(false);
  async function queryData(data?: ManageTemplate.SearchManageTemplateParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageTempByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.success) {
      const data = res.data || [];
      manageTempList.value = data.map(
        (item: ManageTemplate.ManageTemplateItem) => ({
          ...item,
        }),
      );
      // 当前没有选项时，默认选择第一项
      if (
        selectedTemplate.value.manageTempId === undefined &&
        data.length > 0
      ) {
        selectedTemplate.value = manageTempList.value[0];
      }
    }
  }

  const router = useRouter();
  const templateDetail = ref();
  // 新增按钮点击事件
  function onAddBtnClick() {
    router.push({
      name: 'manageTempSettingDetail',
    });
  }

  const selectedTemplate = ref<ManageTemplate.ManageTemplateItem>(
    {} as ManageTemplate.ManageTemplateItem,
  );
  // 列表项点击事件
  function onItemClick(item: ManageTemplate.ManageTemplateItem) {
    selectedTemplate.value = item;
  }

  // 列表项编辑事件
  function handleEditItem(
    item: ManageTemplate.ManageTemplateItem,
    isEditPrompt?: boolean,
  ) {
    selectedTemplate.value = item;
    if (isEditPrompt) {
      templateDetail.value?.showPrompt();
    } else {
      router.push({
        name: 'manageTempSettingDetail',
        query: {
          tempId: item.manageTempId,
        },
      });
    }
  }

  queryData();
  const searchConfig = useManageTemplateSearchConfig(queryData);
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>
      <div>
        <el-button class="mr-3" type="primary" @click="onAddBtnClick">
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>

    <el-row class="p-box h-full">
      <el-col :span="5" class="h-full border">
        <TemplateList
          :manage-temp-list="manageTempList"
          :selected-template="selectedTemplate"
          @item-click="onItemClick"
          @edit-item="handleEditItem"
        ></TemplateList>
      </el-col>
      <el-col class="flex h-full flex-col overflow-hidden pl-5" :span="19">
        <TemplateDetail
          :manage-temp="selectedTemplate"
          ref="templateDetail"
        ></TemplateDetail>
      </el-col>
    </el-row>
  </div>
</template>
