<script setup lang="tsx">
  import { queryTempVarByExample } from '@/modules/qualityControl/api/promptTempVar';
  import { FLAG } from '@sun-toolkit/enums';
  import StarterKit from '@tiptap/starter-kit';
  import { EditorContent, JSONContent, useEditor } from '@tiptap/vue-3';
  import { useTranslation } from 'i18next-vue';
  import { debounce } from 'lodash';
  import {
    ProDialog,
    ProForm,
    ProTable,
    useColumnConfig,
    useFormConfig,
  } from 'sun-biz';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { ObjectNode } from '../config/objectNode';

  const { t } = useTranslation();
  type Props = {
    promptItem?: ManageTemplate.ManageTemplatePromptItem;
    editable?: boolean;
  };
  const props = defineProps<Props>();

  const state = reactive({
    keyWord: '',
    promptVarList: [] as PromptTempVar.PromptTempVarItem[],
    content: '',
  });
  const debounceQuery = debounce(queryData, 300);
  const queryForm = useFormConfig({
    getData: (t) => [
      {
        label: t('qualityControl.manageTemplate.prompt.form.keyWord', '关键字'),
        name: 'keyWord',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.keyword'),
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onInput: (val: string) => {
            debounceQuery({
              keyWord: val,
            });
          },
          onClear: () => {
            queryData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });

  const tableColumns = useColumnConfig({
    getData: () => [
      {
        prop: 'tempVariableId',
        editable: false,
        type: 'selection',
      },
      {
        label: t('qualityControl.table.tempVariableName', '模板变量'),
        prop: 'tempVariableName',
        minWidth: 120,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return <div>{row.tempVariableName || '--'}</div>;
        },
      },
      {
        label: t('qualityControl.table.tempVarType', '分类'),
        prop: 'tempVariableTypeCodeDesc',
        minWidth: 160,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return <div>{row.tempVariableTypeCodeDesc || '--'}</div>;
        },
      },
    ],
  });

  // 选中的项目集合
  const selections = ref<PromptTempVar.PromptTempVarItem[]>([]);
  async function queryData(data?: PromptTempVar.SearchPromptTempVarParams) {
    const [, res] = await queryTempVarByExample({
      keyWord: data?.keyWord,
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      const data = res.data || [];
      state.promptVarList = data.map(
        (item: PromptTempVar.PromptTempVarItem) => ({
          ...item,
        }),
      );
    }
  }

  function handleSelectChange(value: PromptTempVar.PromptTempVarItem[]) {
    selections.value = value;
  }

  function handleImportClick() {
    selections.value.forEach((item) => {
      const { tempVariableId, tempVariableName } = item;
      editor.value?.commands.insertObjectNode({
        id: tempVariableId + '',
        name: `[${tempVariableName}]`,
      });
    });
    selections.value = [];
    dialogRef.value.close();
  }

  const editor = useEditor({
    extensions: [ObjectNode, StarterKit],
    content: '',
    editorProps: {
      attributes: {
        class: 'border h-[700px] p-2',
      },
    },
    editable: props.editable,
  });
  onMounted(() => {
    queryData();
  });

  watch(
    () => props.promptItem,
    () => {
      if (props.promptItem?.promptWordId) {
        const { promptWord, promptXTempVarList } = props.promptItem;
        const jsonContent = convertToTiptap(promptWord, promptXTempVarList);
        console.log(' jsonContent>>>>', jsonContent);
        editor.value?.commands.setContent(jsonContent);
      } else {
        editor.value?.commands.setContent('');
      }
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.editable,
    () => {
      editor.value?.setEditable(props.editable);
    },
  );

  // 将文本内容转换为tiptap 格式
  function convertToTiptap(
    inputText: string,
    tempVarList: ManageTemplate.PromptXTempVar[],
  ) {
    // 分割段落（按两个以上换行符）
    const paragraphs = inputText.split(/(\n{2,})/g);
    // 处理每个段落
    const docContent = paragraphs
      .map((paragraph) => {
        // 处理段落内的内容
        const lines = [paragraph];
        // 处理换行(2个\n表示一次换行)
        if (paragraph.includes('\n')) {
          let brLines = paragraph.split(/(\n{2})/g).filter(Boolean);
          //  如果段落内没有换行符，则返回null
          if (brLines.length === 1) {
            return null;
          }
          brLines = brLines.slice(-1);
          const brContent = [];
          // 一个换行，直接返回p标签
          if (brLines.length === 1) {
            return {
              type: 'paragraph',
            };
          }
          // 多个换行，返回p标签内嵌套br标签，br标签数据为换行数-1，因为tiptap会自动添加一个
          for (let i = 0; i < brLines.length - 1; i++) {
            brContent.push({
              type: 'hardBreak',
            });
          }
          if (!brContent.length) {
            return null;
          }
          return {
            type: 'paragraph',
            content: brContent,
          };
        }
        const paragraphContent = lines.flatMap((line) => {
          // 4. 处理对象节点和文本
          const segments = [];
          let remaining = line;

          // 使用正则匹配对象节点
          const objectRegex = /\{\{#(\d+)#\}\}/g;
          let match;
          let lastIndex = 0;

          while ((match = objectRegex.exec(remaining)) !== null) {
            // 添加前面的文本
            if (match.index > lastIndex) {
              segments.push({
                type: 'text',
                text: remaining.slice(lastIndex, match.index),
              });
            }

            const id = match[1];
            const tempVar = tempVarList.find(
              (item) => item.tempVariableId + '' == id,
            );
            // 添加对象节点
            segments.push({
              type: 'objectNode',
              attrs: {
                id: tempVar?.tempVariableId + '',
                name: `[${tempVar?.tempVariableName}]`,
              },
            });

            lastIndex = match.index + match[0].length;
          }

          // 添加剩余文本
          if (lastIndex < remaining.length) {
            segments.push({
              type: 'text',
              text: remaining.slice(lastIndex),
            });
          }

          return segments;
        });

        if (paragraphContent.length === 0) {
          return null;
        }
        return {
          type: 'paragraph',
          content: paragraphContent.flat(),
        };
      })
      .filter(Boolean); // 移除空段落

    // 构建最终文档
    return {
      type: 'doc',
      content: docContent,
    } as JSONContent;
  }

  // 获取提示词变量id集合
  function getAllTempVarIds(node: JSONContent) {
    let ids: string[] = [];
    if (node.type === 'objectNode' && node.attrs?.id) {
      ids.push(node.attrs.id);
    }
    // 递归处理子节点
    if (node.content) {
      node.content.forEach((child) => {
        ids = ids.concat(getAllTempVarIds(child));
      });
    }

    return ids;
  }
  const dialogRef = ref();
  // 暴露方法
  defineExpose({
    showDialog: () => {
      dialogRef.value.open();
    },
    getEditorData: () => {
      const content = editor.value?.getJSON();
      const ids = getAllTempVarIds(content || {});
      const text = editor.value?.getText();
      console.log('content>>>>', editor.value?.getJSON());
      return {
        text,
        ids,
      };
    },
  });
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <editor-content :editor="editor" />

    <ProDialog
      ref="dialogRef"
      :align-center="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :include-footer="false"
      :title="`${$t('qualityControl.manageTemplate', '模板变量')}`"
      :width="600"
      destroy-on-close
    >
      <div class="flex">
        <ProForm :column="1" :data="queryForm" @model-change="queryData" />
        <el-button type="primary" class="ml-2" @click="handleImportClick">
          {{
            $t('qualityControl.manageTemplate.prompt.tempVar.import', '引入')
          }}</el-button
        >
      </div>

      <pro-table
        ref="proTable"
        row-key="tempVariableId"
        :data="state.promptVarList"
        :columns="tableColumns"
        style="height: 260px"
        @selection-change="handleSelectChange"
      />
    </ProDialog>
  </div>
</template>
