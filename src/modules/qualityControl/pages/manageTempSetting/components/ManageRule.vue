<script setup lang="tsx">
  import { ProTable } from 'sun-biz';
  import { reactive, watch } from 'vue';
  import { useManageRuleConfig } from '../config/useTableConfig';

  type Props = {
    manageTemp: ManageTemplate.ManageTemplateItem;
  };
  const props = defineProps<Props>();
  const state = reactive({
    manageTemp: {} as ManageTemplate.ManageTemplateItem,
  });

  watch(
    () => props.manageTemp,
    () => {
      state.manageTemp = props.manageTemp;
    },
    { immediate: true },
  );

  const tableColumns = useManageRuleConfig();
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <pro-table
      ref="tableRef"
      row-key="manageTempXRuleId"
      :data="props.manageTemp.manageTempXRuleList"
      :columns="tableColumns"
    />
  </div>
</template>
