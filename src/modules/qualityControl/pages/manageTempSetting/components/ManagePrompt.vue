<script setup lang="tsx">
  import {
    addPromptWord,
    deletePromptWord,
    editPromptWord,
    queryPromptWordByBizId,
  } from '@/modules/qualityControl/api/manageTemplate';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProForm } from 'sun-biz';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import {
    useManageTemplatePromptSearchConfig,
    useManageTemplatePromptUpsertConfig,
  } from '../config/useFormConfig';
  import PromptContent from './PromptContent.vue';

  const BIZ_Id_TYPE_CODE = 'DICT_MANAGE_TEMP';
  const { t } = useTranslation();
  type Props = {
    manageTemp: ManageTemplate.ManageTemplateItem;
  };
  const props = defineProps<Props>();

  const selectedPrompt = computed(() => {
    const currentPrompt = promptList.value.find(
      (item) => item.promptWordId === state.selectedPromptForm.promptWordId,
    );
    return currentPrompt || ({} as ManageTemplate.ManageTemplatePromptItem);
  });
  const state = reactive({
    selectedPromptForm: {} as ManageTemplate.ManageTemplatePromptItem,
    editPromptForm: {} as ManageTemplate.ManageTemplatePromptItem,
    isView: true,
  });

  watch(
    () => props.manageTemp,
    () => {
      queryData();
    },
  );

  onMounted(async () => {
    if (props.manageTemp.manageTempId) {
      await queryData();
    }
  });

  // 新增
  function handleAddClick() {
    state.isView = false;
    state.editPromptForm = {} as ManageTemplate.ManageTemplatePromptItem;
  }

  // 修改
  function handleEditClick() {
    state.isView = false;
    state.editPromptForm = selectedPrompt.value;
  }

  // 取消按钮
  function handleCancelClick() {
    state.isView = true;
    state.editPromptForm = { ...selectedPrompt.value };
  }

  // 选择提示词
  function handleSelectChange(val: ManageTemplate.ManageTemplatePromptItem) {
    state.editPromptForm =
      promptList.value.find((item) => item.promptWordId === val.promptWordId) ||
      ({} as ManageTemplate.ManageTemplatePromptItem);
  }

  // 删除
  function handleDeleteClick() {
    ElMessageBox.confirm(
      t(
        'qualityControl.manageTemplate.prompt.delete.ask.title',
        '您确定要删除该项提示词吗',
      ),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          promptWordId: selectedPrompt.value.promptWordId,
          bizId: props.manageTemp.manageTempId,
          bizIdTypeCode: BIZ_Id_TYPE_CODE,
        };
        const [, result] = await deletePromptWord(params);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => {});
  }

  // 校验
  function validateForm(data: ManageTemplate.ManageTemplatePromptItem) {
    if (!data.promptWordTitle) {
      ElMessage.error(
        t(
          'qualityControl.manageTemplate.prompt.promptWordTitle.required',
          '请输入提示词标题',
        ),
      );
      return false;
    }
    if (!data.promptWord) {
      ElMessage.error(
        t(
          'qualityControl.manageTemplate.prompt.promptWord.required',
          '请输入提示词内容',
        ),
      );
      return false;
    }
    return true;
  }
  // 保存
  async function handleSaveClick() {
    const data: ManageTemplate.ManageTemplatePromptItem = state.editPromptForm;
    const editorData = editorRef.value.getEditorData();
    data.promptWord = editorData.text;
    data.promptXTempVarList = editorData.ids.map((item: string) => {
      return {
        tempVariableId: item,
      };
    });
    console.log(' data>>>>', data);
    if (!validateForm(data)) {
      return;
    }
    let promptWordId = data.promptWordId;
    let isSuccess = false;
    if (!promptWordId) {
      const params: ManageTemplate.ManageTemplatePromptInsertParams = {
        ...data,
        bizId: props.manageTemp.manageTempId,
        bizIdTypeCode: BIZ_Id_TYPE_CODE,
        tempVariableIds: editorData.ids,
      };
      const [, res] = await addPromptWord(params);
      isSuccess = !!res?.success;
      promptWordId = res?.data?.promptWordId || 0;
    } else {
      const params: ManageTemplate.ManageTemplatePromptUpdateParams = {
        ...data,
      };
      const [, res] = await editPromptWord(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(data.promptWordId ? 'global:edit.success' : 'global:add.success'),
      );
      state.isView = true;
      queryData();
    }
  }

  const formRef = ref();
  const loading = ref(false);
  const promptList = ref<ManageTemplate.ManageTemplatePromptItem[]>([]);
  async function queryData() {
    loading.value = true;
    const searchParams = {
      bizId: props.manageTemp.manageTempId,
      bizIdTypeCode: BIZ_Id_TYPE_CODE,
      getPromptWordFlag: 1,
    };

    const [, res] = await queryPromptWordByBizId(searchParams);
    loading.value = false;
    if (res?.success) {
      const data = res.data || [];
      promptList.value = data.map(
        (item: ManageTemplate.ManageTemplatePromptItem) => ({
          ...item,
        }),
      );

      // 默认选择启用的提示词，没有启用的默认选择一个
      const enabledPrompt = data.find((item) => item.enabledFlag);
      if (enabledPrompt) {
        state.selectedPromptForm = enabledPrompt;
      } else if (data.length) {
        state.selectedPromptForm = data[0];
      } else {
        state.selectedPromptForm =
          {} as ManageTemplate.ManageTemplatePromptItem;
        formRef.value?.ref?.resetFields();
      }

      state.editPromptForm = { ...state.selectedPromptForm };
    }
  }

  const editorRef = ref();
  function handleAddTempVarClick() {
    editorRef.value.showDialog();
  }

  const promptSearchForm = useManageTemplatePromptSearchConfig(promptList);
  const promptUpsertForm = useManageTemplatePromptUpsertConfig();
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <template v-if="state.isView">
      <div class="flex">
        <ProForm
          ref="formRef"
          v-model="state.selectedPromptForm"
          :column="1"
          :data="promptSearchForm"
          class="mr-8"
          @model-change="handleSelectChange"
        />

        <el-button class="mr-2" type="primary" @click="handleAddClick">
          {{ $t('global:add') }}
        </el-button>
        <el-button
          class="mr-2"
          type="primary"
          @click="handleEditClick"
          v-if="state.selectedPromptForm.promptWordId"
        >
          {{ $t('global:edit') }}
        </el-button>
        <el-button
          type="danger"
          @click="handleDeleteClick"
          v-if="state.selectedPromptForm.promptWordId"
        >
          {{ $t('global:delete') }}
        </el-button>
      </div>
    </template>
    <template v-else>
      <div class="flex justify-between">
        <div class="flex">
          <ProForm
            ref="formRef"
            v-model="state.editPromptForm"
            :column="2"
            :data="promptUpsertForm"
            class="mr-8"
          />

          <el-button class="mr-2" type="primary" @click="handleSaveClick">
            {{ $t('global:save') }}
          </el-button>
          <el-button class="mr-2" type="default" @click="handleCancelClick">
            {{ $t('global:cancel') }}
          </el-button>
        </div>
        <el-button class="mr-2" type="primary" @click="handleAddTempVarClick">
          {{ $t('qualityControl.manageTemplate.prompt.tempVar', '模板变量') }}
        </el-button>
      </div>
    </template>

    <div class="mt-1 h-full">
      <PromptContent
        ref="editorRef"
        :editable="!state.isView"
        :prompt-item="state.editPromptForm"
      ></PromptContent>
    </div>
  </div>
</template>
