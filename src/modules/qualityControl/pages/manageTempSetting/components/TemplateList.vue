<script setup lang="tsx">
  type Props = {
    manageTempList: ManageTemplate.ManageTemplateItem[];
    selectedTemplate: ManageTemplate.ManageTemplateItem;
  };

  const props = defineProps<Props>();

  const emit = defineEmits<{
    itemClick: [item: ManageTemplate.ManageTemplateItem];
    editItem: [item: ManageTemplate.ManageTemplateItem, isEditPrompt?: boolean];
  }>();

  function handleEditItem(
    item: ManageTemplate.ManageTemplateItem,
    isEditPrompt?: boolean,
  ) {
    emit('editItem', item, isEditPrompt);
  }

  function handleItemClick(item: ManageTemplate.ManageTemplateItem) {
    emit('itemClick', item);
  }
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <div class="h-9 bg-[#eaeefe] text-center leading-9">
      {{ $t('quantityControl.manageTemplate.title', '模板名称') }}
    </div>
    <template v-if="props.manageTempList.length > 0">
      <div
        v-for="item in props.manageTempList"
        :key="item.manageTempId"
        class="flex cursor-pointer items-center justify-between border-b border-[#dcdfe6] py-3"
        :class="{
          'bg-[#f5f7fa]':
            item.manageTempId === props.selectedTemplate.manageTempId,
        }"
        @click="handleItemClick(item)"
      >
        <div class="flex-1 text-center">{{ item.manageTempName }}</div>
        <div
          class="flex w-16 flex-shrink-0 flex-grow-0 items-center justify-center"
        >
          <el-icon
            class="text-blue mr-2"
            size="20"
            @click="handleEditItem(item, true)"
          >
            <Document />
          </el-icon>
          <el-icon size="20" @click="handleEditItem(item)">
            <EditPen />
          </el-icon>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="flex h-full items-center justify-center">
        <el-empty :description="$t('global:noData')"></el-empty>
      </div>
    </template>
  </div>
</template>
