<script setup lang="tsx">
  import { ref } from 'vue';
  import ManagePrompt from './ManagePrompt.vue';
  import ManageRule from './ManageRule.vue';

  type Props = {
    manageTemp: ManageTemplate.ManageTemplateItem;
  };

  const props = defineProps<Props>();

  const activeName = ref('manageRule');
  const manageRule = ref();
  // 向上暴露方法
  defineExpose({
    showPrompt: () => {
      activeName.value = 'prompt';
    },
  });
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane
        :label="$t('quantityControl.manageTemplate.detail.rule', '质控规则')"
        name="manageRule"
      >
        <ManageRule
          :manage-temp="props.manageTemp"
          ref="manageRule"
        ></ManageRule>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('quantityControl.manageTemplate.detail.prompt', '提示词')"
        name="prompt"
      >
        <ManagePrompt :manage-temp="props.manageTemp"></ManagePrompt>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
