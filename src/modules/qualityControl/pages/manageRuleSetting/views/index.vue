<script setup lang="ts" name="reasonManage">
  import { commonSort } from '@/api/common.ts';
  import { CodeSystemType } from '@/typings/codeManage';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { queryManageRuleByExample } from '@modules/qualityControl/api/manageRuleSetting';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { AnyObject, ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { computed, ref } from 'vue';
  import { useManageObjectList } from '../../../hooks/useManageObjectList.ts';
  import AddOrEditRuleSetting from '../components/AddOrEditRuleSetting.vue';
  import { useRuleSettingFormConfig } from '../config/useFormConfig.tsx';
  import { useRuleSettingTableConfig } from '../config/useTableConfig.tsx';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  // const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const ruleSettingList = ref<any[]>([]);
  const loading = ref(false);
  const selections = ref<ReasonManage.ReasonInfo[]>([]);
  const addOrEditRuleSettingRef = ref();
  const dialogData = ref<{
    title: string;
    row?: Partial<ManageRuleSetting.RuleSettingItem>;
  }>({
    title: '',
    row: undefined,
  });

  // 数据转换函数：将质控规则数据转换为支持合并单元格的格式
  function transformDataForMergedTable(
    data: ManageRuleSetting.RuleSettingItem[],
  ) {
    const result: any[] = [];

    data.forEach((rule) => {
      const deductRuleList = rule.deductRuleList || [];

      if (deductRuleList.length === 0) {
        // 如果没有缺陷内容，仍然显示一行
        result.push({
          ...rule,
          deductRuleDesc: '--',
          deductTypeCode: '--',
          deductTypeCodeDesc: '--',
          deductRulePoints: '--',
          deductRuleEnabledFlag: '--',
          assessmentDesc: '--',
          rowSpan: 1,
          isFirstRow: true,
        });
      } else {
        // 有缺陷内容，每个缺陷内容一行
        deductRuleList.forEach((deductRule, index) => {
          // 查找扣分类型描述
          const deductTypeDesc =
            deductTypeList.value?.find(
              (item) => item.value === deductRule.deductTypeCode,
            )?.label ||
            deductRule.deductTypeCodeDesc ||
            '--';

          result.push({
            ...rule,
            deductRuleDesc: deductRule.deductRuleDesc,
            deductTypeCode: deductRule.deductTypeCode,
            deductTypeCodeDesc: deductTypeDesc,
            deductRulePoints: deductRule.deductPoints,
            deductRuleEnabledFlag: deductRule.enabledFlag,
            assessmentDesc: deductRule.assessmentDesc,
            deductRuleId: deductRule.deductRuleId,
            rowSpan: index === 0 ? deductRuleList.length : 0, // 第一行显示合并的行数，其他行为0
            isFirstRow: index === 0, // 标记是否为第一行
          });
        });
      }
    });

    return result;
  }

  // 合并单元格方法
  function spanMethod({ row, column, rowIndex, columnIndex }: any) {
    // 需要合并的列：质控规则编号、质控内容、质控对象、质控方式、应用范围、最大扣减分数、启用标志、操作
    const mergeColumns = [
      'manageRuleNo',
      'manageRuleContent',
      'manageObjectName',
      'manageRuleExecuteWayList',
      'manageRuleScopeList',
      'deductPoints',
      'enabledFlag',
      'operation',
    ];

    if (mergeColumns.includes(column.property)) {
      if (row.rowSpan > 0) {
        return {
          rowspan: row.rowSpan,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }

    return {
      rowspan: 1,
      colspan: 1,
    };
  }
  // 质控方式字典
  const ruleWayDataSetList = useFetchDataset([
    CodeSystemType.RULE_EXECUTE_WAY_CODE,
  ]);
  const ruleWayList = computed(() =>
    (
      ruleWayDataSetList?.value?.[CodeSystemType.RULE_EXECUTE_WAY_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 病历节点
  const { manageObjectList, queryManageObjectList } = useManageObjectList();
  // 应用范围字典
  const ruleScopeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_SCOPE_CODE,
  ]);
  const ruleScopeList = computed(() =>
    (
      ruleScopeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 质控规则类型字典
  const ruleTypeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_TYPE_CODE,
  ]);
  const ruleTypeList = computed(() =>
    (
      ruleTypeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_TYPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 扣分类型字典
  const deductTypeDataSetList = useFetchDataset([
    CodeSystemType.DEDUCT_TYPE_CODE,
  ]);
  const deductTypeList = computed(() =>
    (deductTypeDataSetList?.value?.[CodeSystemType.DEDUCT_TYPE_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  async function queryData(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      res.data?.sort(
        (
          a: ManageRuleSetting.RuleSettingItem,
          b: ManageRuleSetting.RuleSettingItem,
        ) => {
          return a.sort - b.sort;
        },
      );
      const data = res.data || [];
      ruleSettingList.value = transformDataForMergedTable(data);
    }
  }

  function onAddRuleSettingClick() {
    dialogData.value = {
      title: t('add.manageRule.dialog.title', '新增质控规则'),
      row: undefined,
    };
    addOrEditRuleSettingRef.value?.dialogRef?.open();
  }

  function onEditRuleSettingClick(row: ManageRuleSetting.RuleSettingItem) {
    dialogData.value = {
      title: t('edit.manageRule.dialog.title', '编辑质控规则'),
      row: row,
    };
    addOrEditRuleSettingRef.value?.dialogRef?.open();
  }

  function handleSelectChange(value: ReasonManage.ReasonInfo[]) {
    selections.value = value;
  }

  async function handleSortEnd(list: AnyObject[]) {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.manageRuleId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_MANAGE_RULE,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success', '排序成功'),
      });
      await queryData();
    }
  }

  queryData();
  queryManageObjectList();
  const searchConfig = useRuleSettingFormConfig(
    {
      ruleWayList,
      objectTypeList: manageObjectList,
      ruleScopeList,
      queryManageObjectList,
    },
    queryData,
  );
  const columns = useRuleSettingTableConfig({
    ruleTypeList,
    ruleScopeList,
    ruleExecuteWayList: ruleWayList,
    objectTypeList: manageObjectList,
    queryData,
    onEditRuleSettingClick,
    queryManageObjectList,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>

      <el-button class="mr-3" type="primary" @click="onAddRuleSettingClick">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <pro-table
      component-no="28"
      ref="tableRef"
      row-key="manageRuleId"
      :data="ruleSettingList"
      :columns="columns"
      :loading="loading"
      :span-method="spanMethod"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />

    <AddOrEditRuleSetting
      ref="addOrEditRuleSettingRef"
      v-bind="dialogData"
      :rule-way-list="ruleWayList"
      :object-type-list="manageObjectList"
      :rule-scope-list="ruleScopeList"
      :query-manage-object-list="queryManageObjectList"
      @success="queryData"
    />
  </div>
</template>
