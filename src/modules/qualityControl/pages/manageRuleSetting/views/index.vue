<script setup lang="ts" name="reasonManage">
  import { commonSort } from '@/api/common.ts';
  import { CodeSystemType } from '@/typings/codeManage';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { queryManageRuleByExample } from '@modules/qualityControl/api/manageRuleSetting';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { AnyObject, ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { computed, ref } from 'vue';
  import { useManageObjectList } from '../../../hooks/useManageObjectList.ts';
  import { useRuleSettingFormConfig } from '../config/useFormConfig.tsx';
  import { useRuleSettingTableConfig } from '../config/useTableConfig.tsx';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  // const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const ruleSettingList = ref<ManageRuleSetting.RuleSettingItem[]>([]);
  const loading = ref(false);
  const selections = ref<ReasonManage.ReasonInfo[]>([]);
  // 质控方式字典
  const ruleWayDataSetList = useFetchDataset([
    CodeSystemType.RULE_EXECUTE_WAY_CODE,
  ]);
  const ruleWayList = computed(() =>
    (
      ruleWayDataSetList?.value?.[CodeSystemType.RULE_EXECUTE_WAY_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 病历节点
  const { manageObjectList, queryManageObjectList } = useManageObjectList();
  // 应用范围字典
  const ruleScopeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_SCOPE_CODE,
  ]);
  const ruleScopeList = computed(() =>
    (
      ruleScopeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 质控规则类型字典
  const ruleTypeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_TYPE_CODE,
  ]);
  const ruleTypeList = computed(() =>
    (
      ruleTypeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_TYPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  async function queryData(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      res.data?.sort(
        (
          a: ManageRuleSetting.RuleSettingItem,
          b: ManageRuleSetting.RuleSettingItem,
        ) => {
          return a.sort - b.sort;
        },
      );
      const data = res.data || [];
      ruleSettingList.value = data.map(
        (item: ManageRuleSetting.RuleSettingItem) => ({
          ...item,
          isEdit: false,
          form: {
            manageRuleId: item.manageRuleId,
            manageRuleNo: item.manageRuleNo,
            manageRuleContent: item.manageRuleContent,
            manageRuleTypeCode: item.manageRuleTypeCode,
            deductPoints: item.deductPoints,
            enabledFlag: item.enabledFlag,
            manageObject: {
              manageObjectId: item.manageObjectId,
              manageObjectName: item.manageObjectName,
              manageObjectTypeCode: item.manageObjectTypeCode,
            },
            manageRuleScopeCodes: (item.manageRuleScopeList || []).map(
              (item) => item?.manageRuleScopeCode,
            ),
            ruleExecuteWayCodes: (item.manageRuleExecuteWayList || []).map(
              (item) => item?.ruleExecuteWayCode,
            ),
          },
        }),
      );
    }
  }

  function onAddRuleSettingClick() {
    ruleSettingList.value.push({
      isEdit: true,
      form: {
        manageRuleNo: '',
        manageRuleContent: '',
        manageRuleTypeCode: '',
        deductPoints: 0,
        enabledFlag: 1,
        manageRuleScopeCodes: ruleScopeList.value.map((item) => item.value),
        ruleExecuteWayCodes: ['1'],
      },
    } as unknown as ManageRuleSetting.RuleSettingItem);
  }

  function handleSelectChange(value: ReasonManage.ReasonInfo[]) {
    selections.value = value;
  }

  function onItemCancelClick(
    item: ManageRuleSetting.RuleSettingItem,
    index: number,
  ) {
    const data = { ...item };
    if (data.manageRuleId) {
      data.isEdit = false;
      ruleSettingList.value.splice(index, 1, data);
    } else {
      ruleSettingList.value.splice(index, 1);
    }
  }

  async function handleSortEnd(list: AnyObject[]) {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.manageRuleId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_MANAGE_RULE,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success', '排序成功'),
      });
      await queryData();
    }
  }

  // 查询病历系统列表
  const mrqaSystemList = ref<ManageRuleSetting.MrqaSystemItem[]>([]);
  const mrqaSystemLoading = ref(false);

  async function queryMrqaSystemData(
    params?: ManageRuleSetting.SearchMrqaSystemParams,
  ) {
    mrqaSystemLoading.value = true;
    try {
      // 动态导入API方法
      const { queryMrqaSystemByExample } = await import(
        '@modules/qualityControl/api/manageRuleSetting'
      );
      const [, res] = await queryMrqaSystemByExample(params || {});
      if (res?.success) {
        mrqaSystemList.value = res.data || [];
        console.log('病历系统查询结果:', res.data);
      }
    } catch (error) {
      console.error('查询病历系统失败:', error);
    } finally {
      mrqaSystemLoading.value = false;
    }
  }

  queryData();
  queryManageObjectList();
  // 初始化时查询病历系统数据
  queryMrqaSystemData({ enabledFlag: FLAG.YES });
  const searchConfig = useRuleSettingFormConfig(
    {
      ruleWayList,
      objectTypeList: manageObjectList,
      ruleScopeList,
      queryManageObjectList,
    },
    queryData,
  );
  const columns = useRuleSettingTableConfig({
    ruleTypeList,
    ruleScopeList,
    ruleExecuteWayList: ruleWayList,
    objectTypeList: manageObjectList,
    queryData,
    onItemCancelClick,
    queryManageObjectList,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>

      <el-button class="mr-3" type="primary" @click="onAddRuleSettingClick">
        {{ $t('global:add') }}
      </el-button>
      <el-button
        class="mr-3"
        type="info"
        :loading="mrqaSystemLoading"
        @click="queryMrqaSystemData({ enabledFlag: FLAG.YES })"
      >
        查询病历系统
      </el-button>
    </div>
    <pro-table
      component-no="28"
      draggable
      ref="tableRef"
      row-key="manageRuleId"
      :data="ruleSettingList"
      :columns="columns"
      :loading="loading"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
