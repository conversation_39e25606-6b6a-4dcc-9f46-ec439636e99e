<script setup lang="ts" name="reasonManage">
  import { CodeSystemType } from '@/typings/codeManage';
import { FLAG } from '@/utils/constant';
import {
  queryManageRuleByExample,
  saveDeductRule,
} from '@modules/qualityControl/api/manageRuleSetting';
import { ElMessage } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { ProForm, ProTable, useFetchDataset } from 'sun-biz';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import AddOrEditDeductRule from '../components/AddOrEditDeductRule.vue';
import { useDeductRuleSettingFormConfig } from '../config/useFormConfig.tsx';
import { useDeductRuleSettingTableConfig } from '../config/useTableConfig.tsx';

  const { t } = useTranslation();

  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const manageRuleList = ref<ManageRuleSetting.RuleSettingItem[]>([]);
  const currentManageRule = ref<ManageRuleSetting.RuleSettingItem>(
    {} as ManageRuleSetting.RuleSettingItem,
  );
  const addOrEditDeductRuleRef = ref();
  const dialogData = ref<{
    title: string;
    row?: Partial<ManageRuleSetting.DeductRuleItem>;
  }>({
    title: '',
    row: undefined,
  });

  // 扣分类型字典
  const ruleTypeDataSetList = useFetchDataset([
    CodeSystemType.DEDUCT_TYPE_CODE,
  ]);
  const ruleTypeList = computed(() =>
    (ruleTypeDataSetList?.value?.[CodeSystemType.DEDUCT_TYPE_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const router = useRouter();
  const manageRuleId = computed(() => {
    const manageRuleId = router.currentRoute.value.query.manageRuleId;
    return manageRuleId + '';
  });

  onMounted(async () => {
    if (manageRuleId.value) {
      searchParams.value.manageRuleId = manageRuleId.value;
      await queryManageRuleData();
      // 获取当前规则
      currentManageRule.value =
        manageRuleList.value.find(
          (item: ManageRuleSetting.RuleSettingItem) =>
            item.manageRuleId + '' === manageRuleId.value,
        ) ||
        ({
          deductRuleList: [],
        } as unknown as ManageRuleSetting.RuleSettingItem);
    }
  });
  async function queryManageRuleData(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    if (res?.success) {
      const data = res.data || [];
      manageRuleList.value = data.map(
        (item: ManageRuleSetting.RuleSettingItem) => {
          if (!item.deductRuleList) {
            item.deductRuleList = [];
          }
          return item;
        },
      );
    }
  }

  function onAddRuleSettingClick() {
    dialogData.value = {
      title: t('add.deductRule.dialog.title', '新增缺陷内容'),
      row: undefined,
    };
    addOrEditDeductRuleRef.value?.dialogRef?.open();
  }

  function onEditDeductRuleClick(row: ManageRuleSetting.DeductRuleItem) {
    dialogData.value = {
      title: t('edit.deductRule.dialog.title', '编辑缺陷内容'),
      row: row,
    };
    addOrEditDeductRuleRef.value?.dialogRef?.open();
  }

  function onBackBtnClick() {
    router.back();
  }

  async function onDeductRuleSuccess() {
    // 弹框保存成功后，重新查询数据
    await queryManageRuleData();
    // 更新当前规则
    currentManageRule.value =
      manageRuleList.value.find(
        (item: ManageRuleSetting.RuleSettingItem) =>
          item.manageRuleId + '' === manageRuleId.value,
      ) || ({} as ManageRuleSetting.RuleSettingItem);
  }
  async function onItemDeleteClick(index: number) {
    const deductRuleList = currentManageRule.value.deductRuleList || [];
    const updatedList = [...deductRuleList];
    updatedList.splice(index, 1);

    // 立即保存删除后的列表
    const [, res] = await saveDeductRule(
      searchParams.value.manageRuleId + '',
      updatedList.map((item) => ({
        deductRuleId: item.deductRuleId,
        deductRuleDesc: item.deductRuleDesc,
        deductTypeCode: item.deductTypeCode,
        deductPoints: item.deductPoints,
        enabledFlag: item.enabledFlag,
        assessmentDesc: item.assessmentDesc,
      })),
    );

    if (res?.success) {
      ElMessage.success(t('global:delete.success'));
      await onDeductRuleSuccess();
    }
  }
  queryManageRuleData();
  const searchConfig = useDeductRuleSettingFormConfig(
    manageRuleList,
    queryManageRuleData,
  );
  const columns = useDeductRuleSettingTableConfig({
    ruleTypeList,
    onItemDeleteClick,
    onEditDeductRuleClick,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryManageRuleData"
        />
        <div class="mr-8">
          {{
            $t(
              'qualityControl.deductRuleSetting.manageObjectName',
              '质控病历节点',
            )
          }}：{{ currentManageRule.manageObjectName }}
        </div>
        <div>
          {{
            $t(
              'qualityControl.deductRuleSetting.maxDeductPoints',
              '最大扣减分数',
            )
          }}：{{ currentManageRule.deductPoints
          }}{{
            $t('qualityControl.deductRuleSetting.maxDeductPointsUnit', '分')
          }}
        </div>
      </div>
      <div>
        <el-button class="mr-3" type="primary" @click="onAddRuleSettingClick">
          {{ $t('global:add') }}
        </el-button>
        <el-button class="mr-3" type="default" @click="onBackBtnClick">
          {{ $t('global:back') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="tableRef"
      row-key="manageRuleId"
      :data="currentManageRule.deductRuleList"
      :columns="columns"
    />

    <AddOrEditDeductRule
      ref="addOrEditDeductRuleRef"
      v-bind="dialogData"
      :rule-type-list="ruleTypeList"
      :manage-rule-id="manageRuleId"
      :max-deduct-points="currentManageRule.deductPoints || 0"
      :current-deduct-rule-list="currentManageRule.deductRuleList || []"
      @success="onDeductRuleSuccess"
    />
  </div>
</template>
