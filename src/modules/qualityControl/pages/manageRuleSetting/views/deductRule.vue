<script setup lang="ts" name="reasonManage">
  import { saveDeductRule } from '@/modules/qualityControl/api/manageRuleSetting.ts';
  import { CodeSystemType } from '@/typings/codeManage';
  import { FLAG } from '@/utils/constant';
  import { queryManageRuleByExample } from '@modules/qualityControl/api/manageRuleSetting';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useDeductRuleSettingFormConfig } from '../config/useFormConfig.tsx';
  import { useDeductRuleSettingTableConfig } from '../config/useTableConfig.tsx';

  const { t } = useTranslation();

  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const manageRuleList = ref<ManageRuleSetting.RuleSettingItem[]>([]);
  const currentManageRule = ref<ManageRuleSetting.RuleSettingItem>(
    {} as ManageRuleSetting.RuleSettingItem,
  );

  // 扣分类型字典
  const ruleTypeDataSetList = useFetchDataset([
    CodeSystemType.DEDUCT_TYPE_CODE,
  ]);
  const ruleTypeList = computed(() =>
    (ruleTypeDataSetList?.value?.[CodeSystemType.DEDUCT_TYPE_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const router = useRouter();
  const manageRuleId = computed(() => {
    const manageRuleId = router.currentRoute.value.query.manageRuleId;
    return manageRuleId + '';
  });

  onMounted(async () => {
    if (manageRuleId.value) {
      searchParams.value.manageRuleId = manageRuleId.value;
      await queryManageRuleData();
      // 获取当前规则
      currentManageRule.value =
        manageRuleList.value.find(
          (item: ManageRuleSetting.RuleSettingItem) =>
            item.manageRuleId + '' === manageRuleId.value,
        ) || ({} as ManageRuleSetting.RuleSettingItem);
    }
  });

  watch(
    () => searchParams.value.manageRuleId,
    () => {
      // 获取当前规则
      currentManageRule.value =
        manageRuleList.value.find(
          (item: ManageRuleSetting.RuleSettingItem) =>
            item.manageRuleId + '' === searchParams.value.manageRuleId,
        ) || ({} as ManageRuleSetting.RuleSettingItem);
    },
  );
  async function queryManageRuleData(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    if (res?.success) {
      const data = res.data || [];
      manageRuleList.value = data;
    }
  }

  function onAddRuleSettingClick() {
    const deductRuleList = currentManageRule.value.deductRuleList || [];
    deductRuleList.push({
      deductRuleDesc: '',
      deductTypeCode: '',
      deductTypeCodeDesc: '',
      deductPoints: 0,
      enabledFlag: 1,
      assessmentDesc: '',
    } as ManageRuleSetting.DeductRuleItem);
    currentManageRule.value.deductRuleList = deductRuleList;
  }

  function onBackBtnClick() {
    router.back();
  }

  function validate() {
    const deductRuleList = currentManageRule.value.deductRuleList || [];
    // 是否所有描述都有值
    const isAllDesc = deductRuleList.every(
      (item: ManageRuleSetting.DeductRuleItem) => item.deductRuleDesc,
    );
    if (!isAllDesc) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t(
            'qualityControl.deductRuleSetting.deductRuleDesc',
            '缺陷内容',
          ),
        }),
      );
      return false;
    }
    // 是否所有扣分类型都有值
    const isAllType = deductRuleList.every(
      (item: ManageRuleSetting.DeductRuleItem) => item.deductTypeCode,
    );
    if (!isAllType) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.deductRuleSetting.deductType', '扣分类型'),
        }),
      );
      return false;
    }
    //是否所有扣分描述都有值
    const isAllPoints = deductRuleList.every(
      (item: ManageRuleSetting.DeductRuleItem) => item.deductPoints,
    );
    if (!isAllPoints) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t(
            'qualityControl.deductRuleSetting.deductPoints',
            '扣分描述',
          ),
        }),
      );
      return false;
    }
    // 扣减分数不能大于最大扣减分数
    const isAllPointsValid = deductRuleList.every(
      (item: ManageRuleSetting.DeductRuleItem) =>
        item.deductPoints <= currentManageRule.value.deductPoints,
    );
    if (!isAllPointsValid) {
      ElMessage.warning(
        t(
          'qualityControl.deductRuleSetting.deductPointsMax',
          '扣减分数不能大于最大扣减分数',
        ),
      );
      return false;
    }
    return true;
  }

  async function onSaveBtnClick() {
    if (!validate()) return;
    const form = currentManageRule.value.deductRuleList?.map((item) => ({
      deductRuleId: item.deductRuleId,
      deductRuleDesc: item.deductRuleDesc,
      deductTypeCode: item.deductTypeCode,
      deductPoints: item.deductPoints,
      enabledFlag: item.enabledFlag,
      assessmentDesc: item.assessmentDesc,
    }));
    const [, res] = await saveDeductRule(
      searchParams.value.manageRuleId + '',
      form,
    );
    const isSuccess = !!res?.success;
    if (isSuccess) {
      ElMessage.success(t('global:save.success'));
      router.back();
    }
  }
  function onItemDeleteClick(index: number) {
    currentManageRule.value.deductRuleList.splice(index, 1);
  }
  queryManageRuleData();
  const searchConfig = useDeductRuleSettingFormConfig(
    manageRuleList,
    queryManageRuleData,
  );
  const columns = useDeductRuleSettingTableConfig({
    ruleTypeList,
    onItemDeleteClick,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryManageRuleData"
        />
        <div class="mr-8">
          {{
            $t(
              'qualityControl.deductRuleSetting.manageObjectName',
              '质控病历节点',
            )
          }}：{{ currentManageRule.manageObjectName }}
        </div>
        <div>
          {{
            $t(
              'qualityControl.deductRuleSetting.maxDeductPoints',
              '最大扣减分数',
            )
          }}：{{ currentManageRule.deductPoints
          }}{{
            $t('qualityControl.deductRuleSetting.maxDeductPointsUnit', '分')
          }}
        </div>
      </div>
      <div>
        <el-button class="mr-3" type="primary" @click="onAddRuleSettingClick">
          {{ $t('global:add') }}
        </el-button>
        <el-button class="mr-3" type="primary" @click="onSaveBtnClick">
          {{ $t('global:save') }}
        </el-button>
        <el-button class="mr-3" type="default" @click="onBackBtnClick">
          {{ $t('global:back') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="tableRef"
      row-key="manageRuleId"
      :data="currentManageRule.deductRuleList"
      :columns="columns"
    />
  </div>
</template>
