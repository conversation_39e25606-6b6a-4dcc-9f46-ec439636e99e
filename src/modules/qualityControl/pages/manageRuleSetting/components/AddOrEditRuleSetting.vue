<script setup lang="ts">
  import { SelectOptions } from '@/typings/common';
  import { FLAG } from '@/utils/constant';
  import {
    addManageRule,
    editManageRule,
  } from '@modules/qualityControl/api/manageRuleSetting';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProForm } from 'sun-biz';
  import { computed, ref, watch } from 'vue';

  const { t } = useTranslation();

  export type Props = {
    row?: Partial<ManageRuleSetting.RuleSettingItem>;
    title?: string;
    ruleWayList: SelectOptions[];
    objectTypeList: ManageObjectSetting.ManageObject[];
    ruleScopeList: SelectOptions[];
    queryManageObjectList: (
      params: ManageObjectSetting.SearchManageObjectParams,
    ) => Promise<void>;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: unknown };
  }>();
  const formModel = ref<ManageRuleSetting.InsertRuleSettingParams>({
    manageRuleNo: '',
    manageRuleContent: '',
    manageRuleTypeCode: '1',
    manageObjectId: 0,
    deductPoints: 0,
    enabledFlag: FLAG.YES,
    manageRuleScopeCodes: [],
    ruleExecuteWayCodes: ['1'],
  });

  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();

  // 表单配置
  const formConfig = computed(() => [
    // {
    //   name: 'manageRuleNo',
    //   label: t('qualityControl.form.manageRuleNo', '质控规则编号'),
    //   component: 'input',
    //   type: 'textarea',
    //   placeholder: t('global:placeholder.input.template', {
    //     content: t('qualityControl.form.manageRuleNo', '质控规则编号'),
    //   }),
    //   isFullWidth: true,
    //   extraProps: {
    //     rows: 4,
    //   },
    // },
    {
      name: 'manageRuleContent',
      label: t('qualityControl.form.manageRuleContent', '质控规则内容'),
      component: 'input',
      type: 'textarea',
      placeholder: t('global:placeholder.input.template', {
        content: t('qualityControl.form.manageRuleContent', '质控规则内容'),
      }),
      rules: [
        {
          required: true,
          message: t('global:placeholder.input.template', {
            content: t('qualityControl.form.manageRuleContent', '质控规则内容'),
          }),
          trigger: 'blur',
        },
      ],
      isFullWidth: true,
      extraProps: {
        rows: 4,
      },
    },
    {
      name: 'manageObject',
      label: t('qualityControl.form.objectType', '病历类型'),
      component: 'select',
      placeholder: t('global:placeholder.select.template', {
        name: t('qualityControl.form.objectType', '病历类型'),
      }),
      options: props.objectTypeList.map((item) => ({
        value: item,
        label: item.manageObjectName,
      })),
      rules: [
        {
          required: true,
          message: t('global:placeholder.select.template', {
            name: t('qualityControl.form.objectType', '病历类型'),
          }),
          trigger: 'change',
        },
      ],
      extraProps: {
        valueKey: 'manageObjectId',
        filterable: true,
        remote: true,
        remoteShowSuffix: true,
        remoteMethod: (query: string) =>
          props.queryManageObjectList({ keyWord: query }),
      },
    },
    {
      name: 'ruleExecuteWayCodes',
      label: t('qualityControl.form.ruleExecuteType', '质控方式'),
      component: 'select',
      placeholder: t('global:placeholder.select.template', {
        name: t('qualityControl.form.ruleExecuteType', '质控方式'),
      }),
      options: props.ruleWayList,
      rules: [
        {
          required: true,
          message: t('global:placeholder.select.template', {
            name: t('qualityControl.form.ruleExecuteType', '质控方式'),
          }),
          trigger: 'change',
        },
      ],
      extraProps: {
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
      },
    },

    {
      name: 'manageRuleScopeCodes',
      label: t('qualityControl.form.ruleScope', '应用范围'),
      component: 'select',
      placeholder: t('global:placeholder.select.template', {
        name: t('qualityControl.form.ruleScope', '应用范围'),
      }),
      options: props.ruleScopeList,
      rules: [
        {
          required: true,
          message: t('global:placeholder.select.template', {
            name: t('qualityControl.form.ruleScope', '应用范围'),
          }),
          trigger: 'change',
        },
      ],
      extraProps: {
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
      },
    },
    {
      name: 'deductPoints',
      label: t('qualityControl.form.deductPoints', '最大扣减分数'),
      component: 'input-number',
      precision: 1,
      min: 0,
      max: 30,
      controlsPosition: 'right',
      style: { width: '200px' },
      rules: [
        {
          required: true,
          message: t(
            'qualityControl.form.deductPoints.required',
            '最大扣减分数必须大于0',
          ),
          trigger: 'blur',
          validator: (
            rule: unknown,
            value: number,
            callback: (error?: Error) => void,
          ) => {
            if (!value || value <= 0) {
              callback(
                new Error(
                  t(
                    'qualityControl.form.deductPoints.required',
                    '最大扣减分数必须大于0',
                  ),
                ),
              );
            } else {
              callback();
            }
          },
        },
      ],
    },
    {
      name: 'enabledFlag',
      label: t('qualityControl.form.enabledFlag', '启用标志'),
      component: 'switch',
      defaultValue: FLAG.YES,
      extraProps: {
        'active-value': FLAG.YES,
        'inactive-value': FLAG.NO,
        'inline-prompt': true,
        'active-text': t('global:enabled'),
        'inactive-text': t('global:disabled'),
      },
    },
    {
      name: 'manageRuleDesc',
      label: t('qualityControl.form.manageRuleDesc', '规则说明'),
      component: 'input',
      type: 'textarea',
      placeholder: t('global:placeholder.input.template', {
        content: t('qualityControl.form.manageRuleDesc', '规则说明'),
      }),
      isFullWidth: true,
      extraProps: {
        rows: 4,
      },
    },
  ]);

  watch(
    () => props.ruleScopeList,
    () => {
      formModel.value.manageRuleScopeCodes = props.ruleScopeList.map(
        (item) => item.value + '',
      );
    },
    { immediate: true },
  );

  watch(
    () => props.row,
    () => {
      if (props.row) {
        // 编辑模式，填充表单数据
        formModel.value = {
          manageRuleId: props.row.manageRuleId,
          manageRuleNo: props.row.manageRuleNo || '',
          manageRuleContent: props.row.manageRuleContent || '',
          manageRuleTypeCode: '1',
          manageObjectId: props.row.manageObjectId || 0,
          manageObject: props.row.manageObjectId
            ? ({
                manageObjectId: props.row.manageObjectId || 0,
                manageObjectName: props.row.manageObjectName || '',
                manageObjectTypeCode: props.row.manageObjectTypeCode || '',
              } as ManageObjectSetting.ManageObject)
            : undefined,
          deductPoints: props.row.deductPoints || 0,
          enabledFlag: props.row.enabledFlag || FLAG.YES,
          manageRuleScopeCodes: (props.row.manageRuleScopeList || []).map(
            (item) => item?.manageRuleScopeCode,
          ),
          ruleExecuteWayCodes: (props.row.manageRuleExecuteWayList || []).map(
            (item) => item?.ruleExecuteWayCode,
          ),
          manageRuleDesc: props.row.manageRuleDesc || '',
        };
      } else {
        // 新增模式，重置表单
        formModel.value = {
          manageRuleNo: '',
          manageRuleContent: '',
          manageRuleTypeCode: '1',
          manageObjectId: 0,
          deductPoints: 0,
          enabledFlag: FLAG.YES,
          manageRuleScopeCodes: props.ruleScopeList.map(
            (item) => item.value + '',
          ),
          ruleExecuteWayCodes: ['1'],
          manageRuleDesc: '',
        };
      }
    },
    { immediate: true },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = formModel.value;

          // 验证逻辑
          if (!params.manageRuleContent) {
            reject([
              '',
              new Error(
                t('global:placeholder.input.template', {
                  content: t(
                    'qualityControl.form.manageRuleContent',
                    '质控规则内容',
                  ),
                }),
              ),
            ]);
            return;
          }

          if (!params.ruleExecuteWayCodes?.length) {
            reject([
              '',
              new Error(
                t('global:placeholder.select.template', {
                  name: t('qualityControl.form.ruleExecuteType', '质控方式'),
                }),
              ),
            ]);
            return;
          }

          if (!params.manageObject?.manageObjectId) {
            reject([
              '',
              new Error(
                t('global:placeholder.select.template', {
                  name: t('qualityControl.form.objectType', '病历类型'),
                }),
              ),
            ]);
            return;
          }

          if (!params.manageRuleScopeCodes?.length) {
            reject([
              '',
              new Error(
                t('global:placeholder.select.template', {
                  name: t('qualityControl.form.ruleScope', '应用范围'),
                }),
              ),
            ]);
            return;
          }

          // 质控方式不包含人工时，质控病历节点只能选择段落、目录
          if (
            !params.ruleExecuteWayCodes.includes('2') &&
            params.manageObject.manageObjectTypeCode === '3'
          ) {
            reject([
              '',
              new Error(
                t(
                  'qualityControl.form.objectType.error',
                  '质控病历节点不能选择病历元素',
                ),
              ),
            ]);
            return;
          }

          if (!params.deductPoints || params.deductPoints <= 0) {
            reject([
              '',
              new Error(
                t(
                  'qualityControl.form.deductPoints.error',
                  '【最大扣减分数】需大于0',
                ),
              ),
            ]);
            return;
          }

          let result;
          if (props.row?.manageRuleId) {
            // 编辑
            const updateParams: ManageRuleSetting.UpdateRuleSettingParams = {
              manageRuleId: props.row.manageRuleId,
              manageRuleNo: params.manageRuleNo,
              manageRuleContent: params.manageRuleContent,
              manageRuleTypeCode: '1',
              manageObjectId: params.manageObject.manageObjectId,
              deductPoints: params.deductPoints,
              enabledFlag: params.enabledFlag,
              manageRuleExecuteWayList: params.ruleExecuteWayCodes.map(
                (item) => ({
                  ruleExecuteWayCode: item,
                }),
              ),
              manageRuleScopeList: params.manageRuleScopeCodes.map((item) => ({
                manageRuleScopeCode: item,
              })),
              manageRuleDesc: params.manageRuleDesc,
            };
            const [, res] = await editManageRule(updateParams);
            result = res;
          } else {
            // 新增
            const insertParams: ManageRuleSetting.InsertRuleSettingParams = {
              ...params,
              manageObjectId: params.manageObject.manageObjectId,
              manageRuleTypeCode: '1',
            };
            delete insertParams.manageObject;
            const [, res] = await addManageRule(insertParams);
            result = res;
          }

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.manageRuleId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('参数错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="props.title"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="3" :data="formConfig" />
  </ProDialog>
</template>
