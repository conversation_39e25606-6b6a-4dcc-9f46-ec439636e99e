import { SelectOptions } from '@/typings/common';
import { FLAG } from '@/utils/constant';
import {
  addManageRule,
  deleteManageRule,
  editManageRule,
} from '@modules/qualityControl/api/manageRuleSetting';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';
import { Ref } from 'vue';
import { useRouter } from 'vue-router';

export function useRuleSettingTableConfig(options: {
  ruleTypeList: Ref<SelectOptions[]>;
  ruleExecuteWayList: Ref<SelectOptions[]>;
  objectTypeList: Ref<ManageObjectSetting.ManageObject[]>;
  ruleScopeList: Ref<SelectOptions[]>;
  queryData: (data?: ManageRuleSetting.SearchManageRuleSettingParams) => void;
  onItemCancelClick: (
    item: ManageRuleSetting.RuleSettingItem,
    index: number,
  ) => void;
  queryManageObjectList: (
    params: ManageObjectSetting.SearchManageObjectParams,
  ) => Promise<void>;
}) {
  const { t } = useTranslation();
  const router = useRouter();
  const {
    ruleExecuteWayList,
    objectTypeList,
    ruleScopeList,
    queryData,
    onItemCancelClick,
    queryManageObjectList,
  } = options;
  async function onItemSaveClick(data: ManageRuleSetting.RuleSettingItem) {
    const params = data.form;
    if (!params.manageRuleContent) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t('qualityControl.form.manageRuleContent', '质控规则内容'),
        }),
      );
      return;
    }
    // if (!params.manageRuleTypeCode) {
    //   ElMessage.warning(
    //     t('global:placeholder.select.template', {
    //       name: t('qualityControl.form.ruleType', '质控规则类型'),
    //     }),
    //   );
    //   return;
    // }
    if (!params.ruleExecuteWayCodes?.length) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.ruleExecuteType', '质控方式'),
        }),
      );
      return;
    }
    if (!params.manageObject?.manageObjectId) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.objectType', '病历类型'),
        }),
      );
      return;
    }
    if (!params.manageRuleScopeCodes?.length) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.ruleScope', '应用范围'),
        }),
      );
      return;
    }
    // 质控方式不包含人工时，质控病历节点只能选择段落、目录
    if (
      !params.ruleExecuteWayCodes.includes('2') &&
      params.manageObject.manageObjectTypeCode === '3'
    ) {
      ElMessage.warning(
        t('qualityControl.form.objectType', '质控病历节点不能选择病历元素'),
      );
      return;
    }
    if (!params.deductPoints || params.deductPoints <= 0) {
      ElMessage.warning(
        t('qualityControl.form.deductPoints', '【最大扣减分数】需大于0'),
      );
      return;
    }
    let isSuccess = false;
    if (params.manageRuleId) {
      const updateParams: ManageRuleSetting.UpdateRuleSettingParams = {
        manageRuleId: params.manageRuleId,
        manageRuleNo: params.manageRuleNo,
        manageRuleContent: params.manageRuleContent,
        manageRuleTypeCode: '1',
        manageObjectId: params.manageObject.manageObjectId,
        deductPoints: params.deductPoints,
        enabledFlag: params.enabledFlag,
        manageRuleExecuteWayList: params.ruleExecuteWayCodes.map((item) => {
          return { ruleExecuteWayCode: item };
        }),
        manageRuleScopeList: params.manageRuleScopeCodes.map((item) => {
          return { manageRuleScopeCode: item };
        }),
      };
      const [, res] = await editManageRule(updateParams);
      isSuccess = !!res?.success;
    } else {
      const insertParams: ManageRuleSetting.InsertRuleSettingParams = {
        ...params,
        manageObjectId: params.manageObject.manageObjectId,
        manageRuleTypeCode: '1',
      };
      const [, res] = await addManageRule(insertParams);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(params.manageRuleId ? 'global:edit.success' : 'global:add.success'),
      );
      queryData();
    }
  }

  function deleteItem(id: number) {
    ElMessageBox.confirm(
      t(
        'qualityControl.ruleSetting.delete.ask.title',
        '您确定要删除该项规则吗',
      ),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, result] = await deleteManageRule(id);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => {});
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ManageRuleSetting.RuleSettingItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('qualityControl.table.ruleNo', '质控规则编号'),
        prop: 'manageRuleNo',
        minWidth: 120,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.manageRuleNo}
              placeholder={t('global:placeholder.input.template', {
                content: t('qualityControl.table.ruleNo', '质控规则编号'),
              })}
            />
          ) : (
            <div>{row.manageRuleNo || '--'}</div>
          );
        },
      },
      {
        label: t('qualityControl.table.ruleName', '质控规则内容'),
        prop: 'manageRuleContent',
        minWidth: 250,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.manageRuleContent}
              placeholder={t('global:placeholder.input.template', {
                content: t('qualityControl.table.ruleName', '质控规则内容'),
              })}
            />
          ) : (
            <div>{row.manageRuleContent || '--'}</div>
          );
        },
      },
      // {
      //   label: t('qualityControl.table.ruleType', '质控规则类型'),
      //   prop: 'manageRuleTypeCode',
      //   minWidth: 160,
      //   render: (row: ManageRuleSetting.RuleSettingItem) => {
      //     return row.isEdit ? (
      //       <el-select
      //         v-model={row.form.manageRuleTypeCode}
      //         multiple={false}
      //         filterable={true}
      //         collapse-tags={true}
      //         collapse-tags-tooltip={true}
      //         placeholder={t('global:placeholder.select.template', {
      //           name: t('qualityControl.table.ruleType', '质控规则类型'),
      //         })}
      //       >
      //         {ruleTypeList.value?.map((item) => (
      //           <el-option
      //             key={item.value}
      //             label={item.label}
      //             value={item.value}
      //           />
      //         ))}
      //       </el-select>
      //     ) : (
      //       <div>{row.manageRuleTypeCodeDesc || '--'}</div>
      //     );
      //   },
      // },
      {
        label: t('qualityControl.table.ruleExecuteType', '质控方式'),
        prop: 'manageRuleExecuteWayList',
        minWidth: 180,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.ruleExecuteWayCodes}
              multiple={true}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('qualityControl.table.ruleExecuteType', '质控方式'),
              })}
            >
              {ruleExecuteWayList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>
              {row.manageRuleExecuteWayList?.length
                ? row.manageRuleExecuteWayList
                    .map((item) => item.ruleExecuteWayCodeDesc)
                    .join(' ')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('qualityControl.table.objectType', '病历类型'),
        prop: 'manageObjectId',
        minWidth: 160,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.manageObject}
              value-key="manageObjectId"
              multiple={false}
              filterable={true}
              remote={true}
              remote-method={(query: string) =>
                queryManageObjectList({ keyWord: query })
              }
              remote-show-suffix
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('qualityControl.table.objectType', '病历类型'),
              })}
            >
              {objectTypeList.value?.map((item) => (
                <el-option
                  key={item.manageObjectId}
                  label={item.manageObjectName}
                  value={item}
                />
              ))}
            </el-select>
          ) : (
            <div>{row.manageObjectName || '--'}</div>
          );
        },
      },
      {
        label: t('qualityControl.table.ruleScope', '应用范围'),
        prop: 'manageRuleScopeList',
        minWidth: 180,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.manageRuleScopeCodes}
              multiple={true}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('qualityControl.table.ruleScope', '应用范围'),
              })}
            >
              {ruleScopeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>
              {row.manageRuleScopeList?.length
                ? row.manageRuleScopeList
                    .map((item) => item.manageRuleScopeCodeDesc)
                    .join(' ')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('qualityControl.table.deductPoints', '最大扣减分数'),
        prop: 'deductPoints',
        minWidth: 140,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return row.isEdit ? (
            <el-input-number
              v-model={row.form.deductPoints}
              precision={1}
              min={0}
              max={30}
              controls-position="right"
              style={{ width: '110px' }}
            />
          ) : (
            <div>{row.deductPoints}</div>
          );
        },
      },
      {
        label: t('qualityControl.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ManageRuleSetting.RuleSettingItem) => {
          return (
            <el-switch
              v-model={row.form.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              onChange={() => {
                if (!row.isEdit) {
                  onItemSaveClick(row);
                }
              }}
            />
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 160,
        fixed: 'right',
        render: (row: ManageRuleSetting.RuleSettingItem, $index: number) => {
          return (
            <div class="flex justify-around">
              {!row.isEdit && (
                <>
                  <el-button
                    onClick={() => (row.isEdit = true)}
                    link={true}
                    type="primary"
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    onClick={() => deleteItem(row.manageRuleId)}
                    link={true}
                    type="danger"
                  >
                    {t('global:delete')}
                  </el-button>
                  <el-button
                    onClick={() =>
                      router.push({
                        name: 'manageDeductSetting',
                        query: {
                          manageRuleId: row.manageRuleId,
                        },
                      })
                    }
                    link={true}
                    type="primary"
                  >
                    {t('manageRuleSetting.table.addDeductContent', '缺陷内容')}
                  </el-button>
                </>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemCancelClick(row, $index)}
                  link={true}
                  type="default"
                >
                  {t('global:cancel')}
                </el-button>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemSaveClick(row)}
                  link={true}
                  type="primary"
                >
                  {t('global:save')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
}

// 扣分规则表格
export function useDeductRuleSettingTableConfig(options: {
  ruleTypeList: Ref<SelectOptions[]>;
  onItemDeleteClick: (index: number) => void;
}) {
  const { ruleTypeList, onItemDeleteClick } = options;

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('qualityControl.deductRule.table.content', '缺陷内容'),
        prop: 'deductRuleDesc',
        minWidth: 350,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          return (
            <el-input
              v-model={row.deductRuleDesc}
              placeholder={t('global:placeholder.input.template', {
                content: t(
                  'qualityControl.deductRule.table.content',
                  '缺陷内容',
                ),
              })}
            />
          );
        },
      },
      {
        label: t('qualityControl.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('qualityControl.deductRule.table.deductType', '扣分类型'),
        prop: 'deductTypeCode',
        minWidth: 160,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          return (
            <el-select
              v-model={row.deductTypeCode}
              multiple={false}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t(
                  'qualityControl.deductRule.table.deductType',
                  '扣分类型',
                ),
              })}
            >
              {ruleTypeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          );
        },
      },
      {
        label: t('qualityControl.table.deductPoints', '扣分描述'),
        prop: 'deductPoints',
        minWidth: 140,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          return (
            <div>
              {t('qualityControl.deductRule.table.deductPoints', '扣减分数')}
              <el-input-number
                v-model={row.deductPoints}
                precision={1}
                min={0}
                max={30}
                controls-position="right"
                style={{ width: '110px', marginLeft: '10px' }}
              />
            </div>
          );
        },
      },
      {
        label: t('qualityControl.deductRule.table.assessmentDesc', '评估说明'),
        prop: 'assessmentDesc',
        minWidth: 350,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          return (
            <el-input
              v-model={row.assessmentDesc}
              placeholder={t('global:placeholder.input.template', {
                content: t(
                  'qualityControl.deductRule.table.assessmentDesc',
                  '评估说明',
                ),
              })}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: ManageRuleSetting.DeductRuleItem, $index: number) => {
          return (
            <div class="flex justify-around">
              {
                <>
                  <el-button
                    onClick={() => onItemDeleteClick($index)}
                    link={true}
                    type="danger"
                  >
                    {t('global:delete')}
                  </el-button>
                </>
              }
            </div>
          );
        },
      },
    ],
  });
}
