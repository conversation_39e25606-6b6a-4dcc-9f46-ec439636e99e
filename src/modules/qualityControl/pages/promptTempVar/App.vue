<script setup lang="ts" name="reasonManage">
  import { CodeSystemType } from '@/typings/codeManage';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { queryTempVarByExample } from '@modules/qualityControl/api/promptTempVar';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import { computed, ref } from 'vue';
  import { usePromptTempVarConfig } from './config/useFormConfig.tsx';
  import { usePromptTempVarTableConfig } from './config/useTableConfig.tsx';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<PromptTempVar.SearchPromptTempVarParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const tempVarList = ref<PromptTempVar.PromptTempVarItem[]>([]);
  const loading = ref(false);
  // 模板变量类型字典
  const tempVariableTypeDataSetList = useFetchDataset([
    CodeSystemType.TEMP_VARIABLE_TYPE_CODE,
  ]);
  const tempVariableTypeList = computed(() =>
    (
      tempVariableTypeDataSetList?.value?.[
        CodeSystemType.TEMP_VARIABLE_TYPE_CODE
      ] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 模板变量应用范围字典
  const tempVariableScopeDataSetList = useFetchDataset([
    CodeSystemType.TEMP_VARIABLE_SCOPE_CODE,
  ]);
  const tempVariableScopeList = computed(() =>
    (
      tempVariableScopeDataSetList?.value?.[
        CodeSystemType.TEMP_VARIABLE_SCOPE_CODE
      ] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 选中的项目集合
  const selections = ref<PromptTempVar.PromptTempVarItem[]>([]);
  async function queryData(data?: PromptTempVar.SearchPromptTempVarParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryTempVarByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      const data = res.data || [];
      tempVarList.value = data.map((item: PromptTempVar.PromptTempVarItem) => ({
        ...item,
        isEdit: false,
        form: {
          tempVariableId: item.tempVariableId,
          tempVariableName: item.tempVariableName,
          tempVariableTypeCode: item.tempVariableTypeCode,
          inputFlag: item.inputFlag,
          outputFlag: item.outputFlag,
          enabledFlag: item.enabledFlag,
          tempVariableScopeCodes: (item.tempVariableScopeList || []).map(
            (item) => item?.tempVariableScopeCode,
          ),
        },
      }));
    }
  }

  function onAddTempVarClick() {
    tempVarList.value.push({
      isEdit: true,
      form: {
        tempVariableName: '',
        tempVariableTypeCode: '',
        inputFlag: 1,
        outputFlag: 1,
        enabledFlag: 1,
        tempVariableScopeCodes: [],
      },
    } as unknown as PromptTempVar.PromptTempVarItem);
  }

  function onItemCancelClick(
    item: PromptTempVar.PromptTempVarItem,
    index: number,
  ) {
    const data = { ...item };
    if (data.tempVariableId) {
      data.isEdit = false;
      tempVarList.value.splice(index, 1, data);
    } else {
      tempVarList.value.splice(index, 1);
    }
  }
  queryData();
  const searchConfig = usePromptTempVarConfig(tempVariableScopeList, queryData);
  const columns = usePromptTempVarTableConfig({
    tempVariableTypeList,
    tempVariableScopeList,
    queryData,
    onItemCancelClick,
    isCloudEnv,
  });

  function handleSelectChange(value: PromptTempVar.PromptTempVarItem[]) {
    selections.value = value;
  }

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.tempVariableId || '';
    });
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>
      <div>
        <el-button
          class="mr-3"
          type="primary"
          @click="onAddTempVarClick"
          :disabled="!isCloudEnv"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_TEMP_VARIABLE"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <pro-table
      ref="tableRef"
      row-key="tempVariableId"
      :data="tempVarList"
      :columns="columns"
      :loading="loading"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
