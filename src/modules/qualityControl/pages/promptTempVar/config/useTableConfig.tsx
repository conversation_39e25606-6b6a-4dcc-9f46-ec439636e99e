import {
  addTempVar,
  deleteTempVar,
  editTempVar,
} from '@/modules/qualityControl/api/promptTempVar';
import { SelectOptions } from '@/typings/common';
import { FLAG } from '@/utils/constant';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';
import { Ref } from 'vue';

export function usePromptTempVarTableConfig(options: {
  tempVariableTypeList: Ref<SelectOptions[]>;
  tempVariableScopeList: Ref<SelectOptions[]>;
  queryData: (data?: PromptTempVar.SearchPromptTempVarParams) => void;
  onItemCancelClick: (
    item: PromptTempVar.PromptTempVarItem,
    index: number,
  ) => void;
  isCloudEnv: boolean | undefined;
}) {
  const { t } = useTranslation();
  const {
    tempVariableTypeList,
    tempVariableScopeList,
    queryData,
    onItemCancelClick,
    isCloudEnv,
  } = options;
  async function onItemSaveClick(data: PromptTempVar.PromptTempVarItem) {
    const params = data.form;
    if (!params.tempVariableName) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t('qualityControl.form.tempVariableName', '模板变量名称'),
        }),
      );
      return;
    }

    if (!params.tempVariableTypeCode) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.tempVariableTypeCode', '模板变量类型'),
        }),
      );
      return;
    }

    if (!params.tempVariableScopeCodes?.length) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.tempVariableScope', '应用范围'),
        }),
      );
      return;
    }

    let isSuccess = false;
    if (params.tempVariableId) {
      const updateParams: PromptTempVar.UpdateTempVarParams = {
        tempVariableId: params.tempVariableId,
        tempVariableName: params.tempVariableName,
        tempVariableTypeCode: params.tempVariableTypeCode,
        inputFlag: params.inputFlag,
        outputFlag: params.outputFlag,
        enabledFlag: params.enabledFlag,
        tempVariableScopeList: params.tempVariableScopeCodes.map((item) => {
          return { tempVariableScopeCode: item };
        }),
      };
      const [, res] = await editTempVar(updateParams);
      isSuccess = !!res?.success;
    } else {
      const [, res] = await addTempVar(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(params.tempVariableId ? 'global:edit.success' : 'global:add.success'),
      );
      queryData();
    }
  }

  function deleteItem(id: number) {
    ElMessageBox.confirm(
      t('qualityControl.tempVar.delete.ask.title', '您确定要删除该项变量吗'),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, result] = await deleteTempVar(id);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => {});
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'tempVariableId',
        type: 'selection',
      },
      {
        label: t('qualityControl.table.tempVariableId', '模板变量标识'),
        prop: 'tempVariableId',
        minWidth: 120,
      },
      {
        label: t('qualityControl.table.tempVariableName', '模板变量名称'),
        prop: 'tempVariableName',
        minWidth: 250,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.tempVariableName}
              placeholder={t('global:placeholder.input.template', {
                content: t(
                  'qualityControl.table.tempVariableName',
                  '模板变量名称',
                ),
              })}
            />
          ) : (
            <div>{row.tempVariableName || '--'}</div>
          );
        },
      },
      {
        label: t('qualityControl.table.tempVarType', '模板变量类型'),
        prop: 'tempVariableTypeCode',
        minWidth: 160,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.tempVariableTypeCode}
              multiple={false}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('qualityControl.table.tempVarType', '模板变量类型'),
              })}
            >
              {tempVariableTypeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>{row.tempVariableTypeCodeDesc || '--'}</div>
          );
        },
      },

      {
        label: t('qualityControl.table.tempVariableScope', '应用范围'),
        prop: 'tempVariableScopeCodes',
        minWidth: 160,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.tempVariableScopeCodes}
              multiple={true}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('qualityControl.table.tempVariableScope', '应用范围'),
              })}
            >
              {tempVariableScopeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>
              {row.tempVariableScopeList?.length
                ? row.tempVariableScopeList
                    .map((item) => item.tempVariableScopeCodeDesc)
                    .join(' ')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('qualityControl.table.enabledFlag', '输入标志'),
        prop: 'inputFlag',
        minWidth: 100,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return (
            <el-switch
              v-model={row.form.inputFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              onChange={() => {
                if (!row.isEdit) {
                  onItemSaveClick(row);
                }
              }}
            />
          );
        },
      },
      {
        label: t('qualityControl.table.outputFlag', '输出标志'),
        prop: 'outputFlag',
        minWidth: 100,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return (
            <el-switch
              v-model={row.form.outputFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              onChange={() => {
                if (!row.isEdit) {
                  onItemSaveClick(row);
                }
              }}
            />
          );
        },
      },
      {
        label: t('qualityControl.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: PromptTempVar.PromptTempVarItem) => {
          return (
            <el-switch
              v-model={row.form.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              onChange={() => {
                if (!row.isEdit) {
                  onItemSaveClick(row);
                }
              }}
            />
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: PromptTempVar.PromptTempVarItem, $index: number) => {
          return (
            <div class="flex justify-around">
              {!row.isEdit && (
                <>
                  <el-button
                    onClick={() => (row.isEdit = true)}
                    link={true}
                    type="primary"
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    onClick={() => deleteItem(row.tempVariableId)}
                    link={true}
                    disabled={!isCloudEnv}
                    type="danger"
                  >
                    {t('global:delete')}
                  </el-button>
                </>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemCancelClick(row, $index)}
                  link={true}
                  type="default"
                >
                  {t('global:cancel')}
                </el-button>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemSaveClick(row)}
                  link={true}
                  type="primary"
                >
                  {t('global:save')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
}
