import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

export function usePromptTempVarConfig(
  tempVariableScopeList: Ref<SelectOptions[]>,
  queryTempVarList: (data: PromptTempVar.SearchPromptTempVarParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('promptTempVar.search.tempVariableScope', '应用范围'),
        name: 'tempVariableScopeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-40',
          options: tempVariableScopeList.value,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-32',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryTempVarList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryTempVarList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
