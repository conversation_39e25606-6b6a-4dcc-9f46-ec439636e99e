<script setup lang="ts" name="mrqaStandardManage">
  import { FLAG } from '@/utils/constant';
  import { queryMrqaSystemByExample } from '@modules/qualityControl/api/manageRuleSetting';
  import { ElMessage } from 'element-sun';
  import { ProForm, ProTable } from 'sun-biz';
  import { ref } from 'vue';
  import { useSearchConfig } from './config/useFormConfig';
  import { useMrQaSystemTableConfig } from './config/useTableConfig';

  // 搜索参数
  const searchParams = ref<MrQaSystem.SearchMrQaSystemParams>({
    enabledFlag: FLAG.ALL,
  });

  // 表格相关
  const tableRef = ref();
  const mrqaSystemList = ref<MrQaSystem.MrQaSystemItem[]>([]);
  const loading = ref(false);
  // 查询质控评分标准数据
  async function queryData(data?: MrQaSystem.SearchMrQaSystemParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }

    try {
      const [, res] = await queryMrqaSystemByExample({
        ...searchParams.value,
        enabledFlag:
          searchParams.value.enabledFlag === FLAG.ALL
            ? undefined
            : searchParams.value.enabledFlag,
      });

      if (res?.success) {
        mrqaSystemList.value = res.data || [];
      } else {
        // 如果API调用失败，使用模拟数据进行测试
        mrqaSystemList.value = [
          {
            mrqaSystemId: 1,
            mrqaSystemName: '病历质控评分标准一',
            enabledFlag: FLAG.YES,
            mrqaSystemList: [
              {
                mrqaSystemDetailId: 1,
                mrqaSystemId: 1,
                mrqaResultCodeDesc: '优秀',
                lowerPoints: 90,
                upperPoints: 100,
              },
              {
                mrqaSystemDetailId: 2,
                mrqaSystemId: 1,
                mrqaResultCodeDesc: '良好',
                lowerPoints: 80,
                upperPoints: 89,
              },
            ],
          },
          {
            mrqaSystemId: 2,
            mrqaSystemName: '病历质控评分标准二',
            enabledFlag: FLAG.YES,
            mrqaSystemList: [
              {
                mrqaSystemDetailId: 3,
                mrqaSystemId: 2,
                mrqaResultCodeDesc: '合格',
                lowerPoints: 60,
                upperPoints: 79,
              },
            ],
          },
        ];
      }
    } catch (error) {
      console.error('查询质控评分标准失败:', error);
      // 使用模拟数据
      mrqaSystemList.value = [
        {
          mrqaSystemId: 1,
          mrqaSystemName: '病历质控评分标准一',
          enabledFlag: FLAG.YES,
          mrqaSystemList: [
            {
              mrqaSystemDetailId: 1,
              mrqaSystemId: 1,
              mrqaResultCodeDesc: '优秀',
              lowerPoints: 90,
              upperPoints: 100,
            },
          ],
        },
      ];
      ElMessage.warning('使用模拟数据进行展示');
    } finally {
      loading.value = false;
    }
  }

  // 新增评分标准
  function onAddStandardClick() {
    // TODO: 实现新增功能
    ElMessage.info('新增功能待实现');
  }

  // 编辑评分标准
  function onEditStandardClick(item: MrQaSystem.MrQaSystemItem) {
    console.log('编辑评分标准:', item);
    ElMessage.info('编辑功能待实现');
  }

  const searchConfig = useSearchConfig(queryData);
  // 表格列配置
  const columns = useMrQaSystemTableConfig(onEditStandardClick, queryData);

  // 初始化查询
  queryData();
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>

      <el-button class="mr-3" type="primary" @click="onAddStandardClick">
        新增
      </el-button>
    </div>
    <pro-table
      ref="tableRef"
      row-key="mrqaSystemId"
      :data="mrqaSystemList"
      :columns="columns"
      :loading="loading"
    />
  </div>
</template>
