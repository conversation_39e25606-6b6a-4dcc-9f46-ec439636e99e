import {
  addManageObject,
  editManageObject,
} from '@/modules/qualityControl/api/manageObject';
import { SelectOptions } from '@/typings/common';
import { FLAG } from '@/utils/constant';
import { ElMessage } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';
import { Ref } from 'vue';

export function useManageObjectTableConfig(options: {
  manageObjectTypeList: Ref<SelectOptions[]>;
  medicalRecordTypeList: Ref<ManageObjectSetting.MedicalRecordType[]>;
  queryData: (data?: ManageObjectSetting.SearchManageObjectParams) => void;
  queryMedicalRecordType: (
    params: ManageObjectSetting.SearchManageObjectParams,
  ) => Promise<void>;
  onItemCancelClick: (
    item: ManageObjectSetting.ManageObject,
    index: number,
  ) => void;
  isCloudEnv: boolean | undefined;
}) {
  const { t } = useTranslation();
  const {
    manageObjectTypeList,
    medicalRecordTypeList,
    queryData,
    queryMedicalRecordType,
    onItemCancelClick,
    isCloudEnv,
  } = options;
  async function onItemSaveClick(data: ManageObjectSetting.ManageObject) {
    const params = data.form;
    if (!params.manageObjectName) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t('qualityControl.form.manageObjectName', '质控节点名称'),
        }),
      );
      return;
    }

    if (!params.manageObjectTypeCode) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.form.manageObjectTypeCode', '质控节点类型'),
        }),
      );
      return;
    }

    if (!params.deductPoints || params.deductPoints <= 0) {
      ElMessage.warning(
        t(
          'qualityControl.manageObject.form.deductPoints',
          '【最大扣除分数】必须大于0',
        ),
      );
      return;
    }

    let isSuccess = false;
    if (params.manageObjectId) {
      const updateParams: ManageObjectSetting.UpsertParams = {
        manageObjectId: params.manageObjectId,
        manageObjectName: params.manageObjectName,
        manageObjectTypeCode: params.manageObjectTypeCode,
        manageObjectFid: params.manageObjectF?.medicalRecordTypeId,
        manageObjectFName: params.manageObjectF?.medicalRecordTypeName,
        enabledFlag: params.enabledFlag,
        deductPoints: params.deductPoints,
      };
      const [, res] = await editManageObject(updateParams);
      isSuccess = !!res?.success;
    } else {
      params.manageObjectFid = params.manageObjectF?.medicalRecordTypeId;
      params.manageObjectFName = params.manageObjectF?.medicalRecordTypeName;
      const [, res] = await addManageObject(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(params.manageObjectId ? 'global:edit.success' : 'global:add.success'),
      );
      queryData();
    }
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'manageObjectId',
        type: 'selection',
      },
      {
        label: t('manageObject.table.manageObjectId', '质控节点标识'),
        prop: 'manageObjectId',
        minWidth: 120,
      },
      {
        label: t('manageObject.table.manageObjectName', '质控节点名称'),
        prop: 'manageObjectName',
        minWidth: 250,
        render: (row: ManageObjectSetting.ManageObject) => {
          return row.isEdit && isCloudEnv ? (
            <el-input
              v-model={row.form.manageObjectName}
              placeholder={t('global:placeholder.input.template', {
                content: t(
                  'manageObject.table.manageObjectName',
                  '质控节点名称',
                ),
              })}
            />
          ) : (
            <div>{row.manageObjectName || '--'}</div>
          );
        },
      },
      {
        label: t('manageObject.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ManageObjectSetting.ManageObject) => {
          return (
            <el-switch
              v-model={row.form.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              enabled={isCloudEnv}
              onChange={() => {
                if (!row.isEdit) {
                  onItemSaveClick(row);
                }
              }}
            />
          );
        },
      },
      {
        label: t('manageObject.table.manageObjectType', '质控节点类型'),
        prop: 'manageObjectTypeCode',
        minWidth: 160,
        render: (row: ManageObjectSetting.ManageObject) => {
          return row.isEdit && isCloudEnv ? (
            <el-select
              v-model={row.form.manageObjectTypeCode}
              multiple={false}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('manageObject.table.manageObjectType', '质控节点类型'),
              })}
            >
              {manageObjectTypeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>{row.manageObjectTypeCodeDesc || '--'}</div>
          );
        },
      },

      {
        label: t('manageObject.table.manageObjectFid', '外部质控节点'),
        prop: 'manageObjectFid',
        minWidth: 160,
        render: (row: ManageObjectSetting.ManageObject) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.manageObjectF}
              value-key="medicalRecordTypeId"
              multiple={false}
              filterable={true}
              remote={true}
              remote-method={(query: string) =>
                queryMedicalRecordType({ keyWord: query })
              }
              remote-show-suffix
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('manageObject.table.manageObjectFid', '外部质控节点'),
              })}
              disabled={row.form.manageObjectTypeCode !== '1'}
            >
              {medicalRecordTypeList.value?.map((item) => (
                <el-option
                  key={item.medicalRecordTypeId}
                  label={item.medicalRecordTypeName}
                  value={item}
                />
              ))}
            </el-select>
          ) : (
            <div>{row.manageObjectFName || '--'}</div>
          );
        },
      },

      {
        label: t('manageObject.table.deductPoints', '最大扣除分数'),
        prop: 'deductPoints',
        minWidth: 140,
        render: (row: ManageObjectSetting.ManageObject) => {
          return row.isEdit && isCloudEnv ? (
            <el-input-number
              v-model={row.form.deductPoints}
              precision={1}
              min={0}
              max={30}
              controls-position="right"
              style={{ width: '110px' }}
            />
          ) : (
            <div>{row.deductPoints}</div>
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: ManageObjectSetting.ManageObject, $index: number) => {
          return (
            <div class="flex justify-around">
              {!row.isEdit && (
                <>
                  <el-button
                    onClick={() => (row.isEdit = true)}
                    link={true}
                    type="primary"
                  >
                    {t('global:edit')}
                  </el-button>
                </>
              )}

              {row.isEdit && (
                <el-button
                  onClick={() => onItemCancelClick(row, $index)}
                  link={true}
                  type="default"
                >
                  {t('global:cancel')}
                </el-button>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemSaveClick(row)}
                  link={true}
                  type="primary"
                >
                  {t('global:save')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
}
