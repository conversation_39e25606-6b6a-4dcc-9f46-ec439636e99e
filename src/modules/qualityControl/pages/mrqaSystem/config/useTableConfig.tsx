import {
  deleteMrqaSystem,
  enabledMrqaSystem,
} from '@/modules/qualityControl/api/mrqaSystem';
import { FLAG } from '@/utils/constant';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';

export function useMrQaSystemTableConfig(
  editRow: (row: MrQaSystem.MrQaSystemItem) => void,
  queryData: (data?: MrQaSystem.SearchMrQaSystemParams) => void,
) {
  const { t } = useTranslation();

  // 启停
  async function handleSwitchEnabled(id: string, enabled: number) {
    const [, res] = await enabledMrqaSystem({
      mrqaSystemId: id,
      enabledFlag: enabled,
    });
    const isSuccess = !!res?.success;
    if (isSuccess) {
      ElMessage.success(t('global:edit.success'));
      queryData();
    }
  }
  // 删除
  async function handleDelete(id: string) {
    ElMessageBox.confirm(
      t('qualityControl.mrQaSystem.delete.ask.title', '您确定要删除该项标准吗'),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, result] = await deleteMrqaSystem({
          mrqaSystemId: id,
        });
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => { });
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('mrQaSystem.table.mrqaSystemName', '标准名称'),
        prop: 'mrqaSystemName',
        minWidth: 120,
      },
      {
        label: t('manageObject.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: MrQaSystem.MrQaSystemItem) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              onChange={() => {
                handleSwitchEnabled(row.mrqaSystemId, row.enabledFlag);
              }}
            />
          );
        },
      },
      {
        label: t('mrQaSystem.table.mrqaResultCodeDesc', '病历质控结果'),
        prop: 'mrqaResultCodeDesc',
        minWidth: 250,
      },

      {
        label: t('mrQaSystem.table.lowerPoints', '区间下限（含）'),
        prop: 'lowerPoints',
        minWidth: 160,
      },
      {
        label: t('mrQaSystem.table.upperPoints', '区间上限（含）'),
        prop: 'upperPoints',
        minWidth: 160,
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: MrQaSystem.MrQaSystemItem) => {
          return (
            <div class="flex justify-around">
              <el-button
                onClick={() => editRow(row)}
                link={true}
                type="primary"
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => handleDelete(row.mrqaSystemId)}
                link={true}
                type="danger"
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
