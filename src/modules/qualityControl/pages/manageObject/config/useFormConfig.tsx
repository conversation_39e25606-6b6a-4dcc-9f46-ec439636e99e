import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

export function useManageObjectConfig(
  objectTypeList: Ref<SelectOptions[]>,
  queryManageObjectList: (
    data: ManageObjectSetting.SearchManageObjectParams,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageObject.search.manageObjectType', '质控节点类型'),
        name: 'manageObjectTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-40',
          options: objectTypeList.value,
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '260px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryManageObjectList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryManageObjectList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
