import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10454-1]根据条件查询质控评分标准
 * @param params
 * @returns
 */
export const queryMrqaSystemByExample = (
  params: MrQaSystem.SearchMrQaSystemParams,
) => {
  return dictRequest<MrQaSystem.MrQaSystemItem[]>(
    '/MrqaSystem/queryMrqaSystemByExample',
    params,
  );
};

/**
 * [1-10455-1]新增病历质控评分标准
 * @param params
 * @returns
 */
export const addMrqaSystem = (params: MrQaSystem.UpsertParams) => {
  return dictRequest<{ mrqaSystemId: string }>(
    '/MrqaSystem/addMrqaSystem',
    params,
  );
};

/**
 * [1-10456-1]保存病历质控评分标准
 * @param params
 * @returns
 */
export const saveMrqaSystem = (params: MrQaSystem.UpsertParams) => {
  return dictRequest<{ mrqaSystemId: string }>(
    '/MrqaSystem/saveMrqaSystem',
    params,
  );
};

/**
 * [1-10457-1]删除病历质控评分标准
 * @param params
 * @returns
 */
export const deleteMrqaSystem = (params: { mrqaSystemId: string }) => {
  return dictRequest('/MrqaSystem/deleteMrqaSystem', params);
};

/**
 * [1-10458-1]停\启用病历质控评分标准
 * @param params
 * @returns
 */
export const enabledMrqaSystem = (params: {
  mrqaSystemId: string;
  enabledFlag: number;
}) => {
  return dictRequest('/MrqaSystem/enabledMrqaSystem', params);
};
