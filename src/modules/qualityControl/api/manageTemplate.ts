import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10343-1]根据条件查询管理模版
 * @param params
 * @returns
 */
export const queryManageTempByExample = (
  params: ManageTemplate.SearchManageTemplateParams,
) => {
  return dictRequest<ManageTemplate.ManageTemplateItem[]>(
    '/manageRule/queryManageTempByExample',
    params,
  );
};

/**
 * [1-10344-1] 新增管理模板
 * @param params
 * @returns
 */
export const addManageTemp = (
  params: ManageTemplate.InsertManageTempParams,
) => {
  return dictRequest<{ manageTempId: number }>(
    '/manageRule/addManageTemp',
    params,
  );
};

/**
 * [1-10345-1] 编辑管理模板
 * @param params
 * @returns
 */
export const editManageTemp = (
  params: ManageTemplate.UpdateManageTempParams,
) => {
  return dictRequest('/manageRule/editManageTemp', params);
};

/**
 * [1-10347-1]根据业务标识查询提示词
 * @param params
 * @returns
 */
export const queryPromptWordByBizId = (
  params: ManageTemplate.SearchManageTemplatePromptParams,
) => {
  return dictRequest<ManageTemplate.ManageTemplatePromptItem[]>(
    '/promptWord/queryPromptWordByBizId',
    params,
  );
};

/**
 * [1-10346-1] 新增提示词
 * @param params
 * @returns
 */
export const addPromptWord = (
  params: ManageTemplate.ManageTemplatePromptInsertParams,
) => {
  return dictRequest<{ promptWordId: number }>(
    '/promptWord/addPromptWord',
    params,
  );
};

/**
 * [1-10349-1] 编辑提示词
 * @param params
 * @returns
 */
export const editPromptWord = (
  params: ManageTemplate.ManageTemplatePromptUpdateParams,
) => {
  return dictRequest('/promptWord/editPromptWord', params);
};

/**
 * [1-10348-1] 删除提示词
 * @param params
 * @returns
 */
export const deletePromptWord = (
  params: ManageTemplate.ManageTemplatePromptDeleteParams,
) => {
  return dictRequest('/promptWord/deletePromptWord', params);
};

/**
 * [1-10387-1]根据条件查询病历模板
 * @param params
 * @returns
 */
export const queryMrTempByExample = (
  params: ManageTemplate.SearchMrTempParams,
) => {
  return dictRequest<ManageTemplate.MrTempItem[]>(
    '/medicalRecordTemp/queryMrTempByExample',
    params,
  );
};

/**
 * [1-10388-1]根据病历模板标识查询病历模板
 * @param params
 * @returns
 */
export const queryMrTempById = (params: { mrTempIds: number[] }) => {
  return dictRequest<ManageTemplate.MrTempItem[]>(
    '/medicalRecordTemp/queryMrTempById',
    params,
  );
};
