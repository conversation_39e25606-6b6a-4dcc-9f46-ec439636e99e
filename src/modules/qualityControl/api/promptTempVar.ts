import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10336-1]根据条件查询模板变量
 * @param params
 * @returns
 */
export const queryTempVarByExample = (
  params: PromptTempVar.SearchPromptTempVarParams,
) => {
  return dictRequest<PromptTempVar.PromptTempVarItem[]>(
    '/promptWord/queryTempVarByExample',
    params,
  );
};

/**
 * [1-10337-1] 新增模板变量
 * @param params
 * @returns
 */
export const addTempVar = (params: PromptTempVar.InsertTempVarParams) => {
  return dictRequest<{ tempVariableId: number }>(
    '/promptWord/addTempVar',
    params,
  );
};

/**
 * [1-10338-1] 编辑模板变量
 * @param params
 * @returns
 */
export const editTempVar = (params: PromptTempVar.UpdateTempVarParams) => {
  return dictRequest('/promptWord/editTempVar', params);
};

/**
 * [1-10339-1] 删除模板变量
 * @param params
 * @returns
 */
export const deleteTempVar = (id: number) => {
  return dictRequest('/promptWord/deleteTempVar', { tempVariableId: id });
};
