import { CodeSystemType } from '@/typings/codeManage';
import { SelectOptions } from '@/typings/common';
import { useFetchDataset } from 'sun-biz';
import { onBeforeMount, ref } from 'vue';

// 质控模板应用范围字典
export function useManageScopeList() {
  const manageScopeList = ref<SelectOptions[]>([]);
  function fetchScopeList() {
    const tempScopeDataSetList = useFetchDataset([
      CodeSystemType.MANAGE_RULE_SCOPE_CODE,
    ]);
    const tempScopeList = (
      tempScopeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    }));
    manageScopeList.value = tempScopeList;
  }

  onBeforeMount(() => {
    fetchScopeList();
  });

  return {
    manageScopeList,
    fetchScopeList,
  };
}
