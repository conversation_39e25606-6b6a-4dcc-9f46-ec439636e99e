declare namespace MrQaSystem {
  import { type FLAG } from '@/utils/constant';
  interface SearchMrQaSystemParams {
    keyWord?: string;
    enabledFlag?: FLAG;
  }

  interface MrQaSystemItem {
    mrqaSystemId: string;
    mrqaSystemName: string;
    enabledFlag: number;
    mrqaSystemDefList: MrQaSystemDef[];
  }

  interface MrQaSystemDef {
    mrqaSystemDefId?: string;
    mrqaResultCode: string;
    mrqaResultCodeDesc?: string;
    lowerPoints: number;
    upperPoints: number;
  }

  interface UpsertParams {
    mrqaSystemId?: string;
    mrqaSystemName: string;
    mrqaSystemDefList: MrQaSystemDef[];
  }
}
