declare namespace PromptTempVar {
  import { type FLAG } from '@/utils/constant';
  interface SearchPromptTempVarParams {
    bizTypeCode?: string;
    keyWord?: string;
    enabledFlag?: FLAG;
  }

  interface PromptTempVarItem {
    tempVariableId: number;
    tempVariableName: string;
    tempVariableTypeCode: string;
    tempVariableTypeCodeDesc: string;
    inputFlag: number;
    outputFlag: number;
    enabledFlag: number;
    tempVariableScopeList: VariableScope[];
    isEdit: boolean;
    form: InsertTempVarParams;
  }

  interface VariableScope {
    tempVariableScopeId: number;
    tempVariableScopeCode: string;
    tempVariableScopeCodeDesc: string;
  }

  interface UpsertBaseParams {
    tempVariableId?: number;
    tempVariableName: string;
    tempVariableTypeCode: string;
    inputFlag: number;
    outputFlag: number;
    enabledFlag: number;
  }

  interface InsertTempVarParams extends UpsertBaseParams {
    tempVariableScopeCodes: string[];
  }

  interface UpdateTempVarParams extends UpsertBaseParams {
    tempVariableScopeList: {
      tempVariableScopeId?: number;
      tempVariableScopeCode: string;
    }[];
  }
}
