declare namespace ManageObjectSetting {
  import { type FLAG } from '@/utils/constant';
  interface SearchManageObjectParams {
    manageObjectTypeCode?: string;
    keyWord?: string;
    enabledFlag?: FLAG;
  }

  interface ManageObject {
    manageObjectId: number;
    manageObjectName: string;
    manageObjectTypeCode: string;
    manageObjectTypeCodeDesc: string;
    manageRuleTypeCodeDesc: string;
    manageObjectFid: string;
    manageObjectFName: string;
    deductPoints: number;
    enabledFlag: number;
    isEdit: boolean;
    form: UpsertParams;
  }

  interface UpsertParams {
    manageObjectId?: number;
    manageObjectName: string;
    manageObjectTypeCode: string;
    manageObjectFid?: string;
    manageObjectFName?: string;
    manageObjectF?: {
      medicalRecordTypeId: string;
      medicalRecordTypeName: string;
    };
    enabledFlag: number;
    deductPoints: number;
  }

  interface MedicalRecordType {
    medicalRecordTypeId: number;
    medicalRecordTypeName: string;
    enabledFlag: number;
  }
}
