declare namespace ManageRuleSetting {
  import { type FLAG } from '@/utils/constant';
  interface SearchManageRuleSettingParams {
    ruleExecuteWayCode?: string;
    manageObjectId?: number;
    manageRuleScopeCode?: string;
    pointLowerLimit?: number;
    pointUppererLimit?: number;
    keyWord?: string;
    enabledFlag?: FLAG;
    manageRuleIds?: string[];
    manageRuleId?: string;
  }

  interface RuleSettingItem {
    manageRuleId: string;
    manageRuleNo: string;
    manageRuleContent: string;
    manageRuleTypeCode: string;
    manageRuleTypeCodeDesc: string;
    manageObjectId: number;
    manageObjectName: string;
    manageObjectTypeCode: string;
    deductPoints: number;
    enabledFlag: number;
    manageRuleScopeList: RuleScope[];
    manageRuleExecuteWayList: RuleExecuteWay[];
    deductRuleList: DeductRuleItem[];
    sort: number;
    manageRuleDesc: string;
  }

  // 质控规则表格显示数据
  interface RuleSettingTableItem extends RuleSettingItem {
    deductRuleId: number;
    deductRuleDesc: string;
    assessmentDesc: string;
    ruleRowSpan: number;
    isFirstRuleRow: boolean;
    objectTypeRowSpan: number;
    isFirstObjectTypeRow: boolean;
  }

  interface RuleScope {
    manageRuleScopeId: string;
    manageRuleScopeCode: string;
    manageRuleScopeCodeDesc: string;
  }

  interface RuleExecuteWay {
    manageRuleExecuteWayId: string;
    ruleExecuteWayCode: string;
    ruleExecuteWayCodeDesc: string;
  }

  interface UpsertBaseParams {
    manageRuleId?: string;
    manageRuleNo: string;
    manageRuleContent: string;
    manageRuleTypeCode: string;
    manageObjectId: number;
    manageObject?: ManageObjectSetting.ManageObject;
    deductPoints: number;
    enabledFlag: number;
    manageRuleDesc?: string;
  }

  interface InsertRuleSettingParams extends UpsertBaseParams {
    manageRuleScopeCodes: string[];
    ruleExecuteWayCodes: string[];
  }

  interface UpdateRuleSettingParams extends UpsertBaseParams {
    manageRuleScopeList: {
      manageRuleScopeId?: number;
      manageRuleScopeCode: string;
    }[];
    manageRuleExecuteWayList: {
      manageRuleExecuteWayId?: number;
      ruleExecuteWayCode: string;
    }[];
  }

  // 关联的扣分规则
  interface DeductRuleItem {
    deductRuleId?: number;
    deductRuleDesc: string;
    deductTypeCode: string;
    deductTypeCodeDesc?: string;
    deductPoints: number;
    enabledFlag: number;
    manageRuleId?: number;
    assessmentDesc?: string;
    // isEdit: boolean;
    // form: InsertDeductRuleParams;
  }

  interface InsertDeductRuleParams {
    deductRuleId: number;
    deductRuleDesc: string;
    deductTypeCode: string;
    deductPoints: number;
    enabledFlag: number;
    assessmentDesc?: string;
  }
}
