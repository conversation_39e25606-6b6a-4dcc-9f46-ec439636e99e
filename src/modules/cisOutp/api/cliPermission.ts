import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10395-1]根据条件查询临床权限及值域列表
 * @param {CliPermission.SearchCliPermissionsReqParam} params
 * @returns
 */
export const queryCliPermissionList = (
  params: CliPermission.SearchCliPermissionsReqParam,
) => {
  return dictRequest<
    CliPermission.Permission,
    CliPermission.SearchCliPermissionsReqParam
  >('/cliPermission/queryCliPermissionDictAndValueByExample', params);
};

/**
 * [1-10395-1]根据条件查询临床权限及值域列表
 * @param {CliPermission.SearchCliPermissionsReqParam} params
 * @returns
 */
export const queryCodeSystemList = (
  params: CliPermission.codeSystemReqParam,
) => {
  return dictRequest<
    CliPermission.codeSystemResItem[],
    CliPermission.codeSystemReqParam
  >('/codeSystem/queryCodeSystemListByExample', params);
};

/**
 * [1-10392-1]新增临床权限字典
 * @param params
 * @returns
 */
export const addCliPermissionDict = (
  params: CliPermission.PermissionDictReqParam,
) => {
  return dictRequest<
    CliPermission.PermissionDictResParam,
    CliPermission.PermissionDictReqParam
  >('/cliPermission/addCliPermissionDict', params);
};

/**
 * [1-10393-1]根据标识修改临床权限
 * @param params
 * @returns
 */
export const updateCliPermissionById = (
  params: CliPermission.PermissionUpdatedReqParam,
) => {
  return dictRequest<null, CliPermission.PermissionUpdatedReqParam>(
    '/cliPermission/updateCliPermissionById',
    params,
  );
};

/**
 * [1-10394-1]根据权限标识修改对应的临床权限值域
 * @param params
 * @returns
 */
export const updateValueRangeOfPermission = (
  params: CliPermission.ValueRangeReqParam,
) => {
  return dictRequest<null, CliPermission.ValueRangeReqParam>(
    '/cliPermission/updateCliPermissionValueById',
    params,
  );
};
