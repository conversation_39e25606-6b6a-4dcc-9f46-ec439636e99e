import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10006-1]根据条件查询值域列表
 * @param data
 * @returns
 */
export const queryDataSetListByExample = (
  params: Code.ReqQueryDataSetListByExampleParams,
) => {
  return dictRequest<Code.CodeSystemInfo[]>(
    '/codeSystem/queryDataSetListByExample',
    params,
  );
};

/**
 * [1-10180-1]根据条件查询标签分组列表
 * @param params
 * @returns
 */
export const queryTagListByExample = (params: TagManage.ReqQueryTagList) => {
  return dictRequest<TagManage.TagInfo[]>('/tag/queryTagListByExample', params);
};

/**
 * [1-10350-1] 新增诊断字典
 * @param params
 * @returns
 */
export const addDiagnosisDict = (
  params: DiagnosisSearch.DiagnosisiAddItemReqParams,
) => {
  return dictRequest<
    { diagId: number },
    DiagnosisSearch.DiagnosisiAddItemReqParams
  >('/diagnosis/addDiagnosis', params);
};

/**
 * [1-10351-1] 根据标识修改诊断字典
 * @param params
 * @returns
 */
export const updateDiagnosisDict = (
  params: DiagnosisSearch.DiagnosisUpdateItemReqParams,
) => {
  return dictRequest<null, DiagnosisSearch.DiagnosisUpdateItemReqParams>(
    '/diagnosis/updateDiagnosis',
    params,
  );
};

/**
 * [1-10352-1] 根据条件查询诊断字典（定义态）
 * @param params
 * @returns
 */
export const queryDiagnosisList = (
  params: DiagnosisSearch.DiagnosisReqParams,
) => {
  return dictRequest<
    DiagnosisSearch.DiagnosisResItem,
    DiagnosisSearch.DiagnosisReqParams
  >('/diagnosis/queryDiagnosisByExample', params);
};
