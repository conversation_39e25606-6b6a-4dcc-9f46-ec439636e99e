<script setup lang="ts">
  import {
    ProDialog,
    ProForm,
    useAppConfigData,
    MAIN_APP_CONFIG,
    PageInfo,
  } from 'sun-biz';
  import { nextTick, onBeforeMount, reactive, ref, useTemplateRef } from 'vue';
  import { usePermissionFormConfig } from '../config/usePermissionFormConfig';
  import {
    addCliPermissionDict,
    queryCodeSystemList,
    updateCliPermissionById,
  } from '@/modules/cisOutp/api/cliPermission';
  import { DEFAULT_PAGE_SIZE, ENABLED_FLAG, FLAG } from '@sun-toolkit/enums';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, FormInstance } from 'element-sun';
  import { FORM_OPERATION } from '@/modules/cisOutp/constant';
  import { queryDataSetListByExample } from '@/modules/cisOutp/api/code';

  const { t } = useTranslation();
  // 新增/编辑
  const mode = defineModel('mode', { type: String });
  // 打开弹窗
  const show = defineModel('show', { type: Boolean });
  const props = defineProps<{
    rowEdited: CliPermission.Permission;
    typeOptions: CliPermission.SelectOption[];
  }>();
  const emit = defineEmits(['close-dialog']);
  const detailFormRef = useTemplateRef<{ ref: FormInstance }>('detailForm');
  const dialogRef = useTemplateRef<{
    open: () => void;
    close: () => void;
  }>('dialog');
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);

  const formData = reactive<CliPermission.PermissionFormData>({
    codeSystemId: '',
    /**
     * '' as unknown as number
     *
     * 当 el-select 的 options 选项为 { label: string, value: number } 类型时
     * el-select 设置默认未选中
     * 若 el-select v-model 绑定的值 默认设为其他number类型的数字
     * 则会导致 options 选项在 错位并报错
     */
    multiplyCheckFlag: '' as unknown as number,
    cliPermissionName: '',
    enabledFlag: FLAG.YES,
  });
  const keyWord = ref('');
  const codeSystemOptions = ref<CliPermission.SelectOption[]>([]);
  const valueRangeOfCodeSystemId = ref<
    Pick<
      CliPermission.ValuesOfPermissionRange,
      'cliPermissionValueName' | 'dataValueId' | 'enabledFlag'
    >[]
  >([]);
  const formConfigParam: CliPermission.PermissionFormConfigParam = {
    codeSystemOptions,
    typeOptions: props.typeOptions,
    remoteMethod: function (query: string) {
      keyWord.value = query;
      queryCodeSystemOptions();
    },
    isEdit: mode.value === FORM_OPERATION.ADD ? false : true,
  };
  const formConfig = usePermissionFormConfig(formConfigParam);

  async function initDialog() {
    await nextTick();
    dialogRef.value?.open();
    await queryCodeSystemOptions();
  }

  // 获取类别选项
  async function fetchCategories(codeSystemId: string) {
    let [, result] = await queryDataSetListByExample({
      codeSystemIds: [codeSystemId],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: FLAG.NO,
      enabledFlag: FLAG.YES,
    });
    if (result?.success) {
      let { data = [] } = result;
      valueRangeOfCodeSystemId.value = data.map(
        ({ dataValueNameDisplay, dataValueId, enabledFlag }) => ({
          cliPermissionValueName: dataValueNameDisplay,
          dataValueId,
          enabledFlag,
        }),
      );
    }
  }
  function clearFormAndcloseDialog() {
    clearForm();
    show.value = false;
    emit('close-dialog');
  }

  function handleConfirm() {
    detailFormRef.value?.ref.validate((valid) => {
      if (valid) {
        confirm();
      }
    });
  }

  function confirm() {
    if (mode.value === FORM_OPERATION.ADD) {
      confirmAdd();
    } else if (mode.value === FORM_OPERATION.EDIT) {
      confirmEdit();
    }
  }

  async function confirmAdd() {
    await fetchCategories(formData.codeSystemId);
    const params = {
      ...formData,
      multiplyCheckFlag: Number(formData.multiplyCheckFlag),
      cliPermissionValueList: valueRangeOfCodeSystemId.value,
    };
    const [, res] = await addCliPermissionDict(params);
    if (res?.success) {
      ElMessage.success(t('global:add.success'));
      clearFormAndcloseDialog();
    }
  }

  async function confirmEdit() {
    const params = {
      cliPermissionId: props.rowEdited.cliPermissionId,
      cliPermissionName: formData.cliPermissionName,
      multiplyCheckFlag: formData.multiplyCheckFlag,
      enabledFlag: formData.enabledFlag,
    };
    const [, res] = await updateCliPermissionById(params);
    if (res?.success) {
      ElMessage.success(t('global:edit.success'));
      clearFormAndcloseDialog();
    }
  }

  // 获取编码体系选项
  async function queryCodeSystemOptions() {
    let params: Pick<PageInfo, 'pageNumber' | 'pageSize'> &
      CliPermission.codeSystemReqParam = {
      pageSize: DEFAULT_PAGE_SIZE,
      pageNumber: FLAG.YES,
      keyWord: keyWord.value,
      enabledFlag: ENABLED_FLAG.YES,
    };
    if (mode.value === FORM_OPERATION.EDIT) {
      params = { ...params, codeSystemNo: props.rowEdited.codeSystemNo };
    }
    const [, res] = await queryCodeSystemList(params);
    if (res?.success) {
      const { data } = res;
      if (Array.isArray(data)) {
        codeSystemOptions.value = data.map(({ nameDisplay, codeSystemId }) => ({
          label: nameDisplay,
          value: codeSystemId,
        }));
      }
    }
  }

  function clearForm() {
    if (mode.value === FORM_OPERATION.EDIT) {
      // 清空类型+权限名称
      detailFormRef.value?.ref.resetFields([
        'multiplyCheckFlag',
        'cliPermissionName',
      ]);
    } else {
      detailFormRef.value?.ref.resetFields();
    }
  }

  function handleCancel() {
    clearForm();
    show.value = false;
    dialogRef.value?.close();
    emit('close-dialog');
  }

  onBeforeMount(async () => {
    await initDialog();
    if (mode.value === FORM_OPERATION.EDIT) {
      const edited = props.rowEdited;
      formData.codeSystemId = edited.codeSystemId;
      formData.multiplyCheckFlag = edited.multiplyCheckFlag;
      formData.cliPermissionName = edited.cliPermissionName;
      formData.enabledFlag = edited.enabledFlag;
    }
  });
</script>

<template>
  <div>
    <ProDialog
      ref="dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="`${$t(`global:${mode}`)}`"
      :width="900"
      destroy-on-close
      @close="clearFormAndcloseDialog"
      @cancel="clearFormAndcloseDialog"
      @success="clearFormAndcloseDialog"
    >
      <ProForm
        ref="detailForm"
        mode="column"
        v-model="formData"
        :column="2"
        :data="formConfig"
      >
      </ProForm>
      <template #footer>
        <!-- 清空 -->
        <el-button class="mr-2" @click="clearForm">{{
          $t('global:clearAll')
        }}</el-button>
        <!-- 取消 -->
        <el-button class="mr-2" @click="handleCancel">{{
          $t('global:cancel')
        }}</el-button>
        <!-- 确定 -->
        <el-button type="primary" @click="handleConfirm">{{
          $t('global:confirm')
        }}</el-button>
      </template>
    </ProDialog>
  </div>
</template>
