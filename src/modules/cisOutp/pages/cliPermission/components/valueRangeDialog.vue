<script setup lang="ts">
  import { nextTick, onBeforeMount, reactive, useTemplateRef } from 'vue';
  import { ElMessage, FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProForm } from 'sun-biz';
  import { FLAG } from '@sun-toolkit/enums';
  import { omit } from 'lodash';
  import { FORM_OPERATION } from '@/modules/cisOutp/constant';
  import { updateValueRangeOfPermission } from '@/modules/cisOutp/api/cliPermission';
  import { useValueRangeFormConfig } from '../config/useValueRangeFormConfig';

  const { t } = useTranslation();
  // 新增/编辑
  const mode = defineModel('mode', { type: String });
  // 打开弹窗
  const show = defineModel('show', { type: Boolean });
  const props = defineProps<{
    rowEdited: CliPermission.ValuesOfPermissionRange;
  }>();
  const emit = defineEmits(['close-dialog']);
  const dialogRef = useTemplateRef<{
    open: () => void;
    close: () => void;
  }>('dialog');
  const formRef = useTemplateRef<{
    ref: FormInstance;
  }>('form');
  const formData = reactive<
    CliPermission.ValueRangeReqParam & { dataValueNameDisplay?: string }
  >({
    cliPermissionValueId: '',
    cliPermissionValueName: '',
    enabledFlag: FLAG.YES,
  });
  const formConfig = useValueRangeFormConfig();

  function clearFormAndcloseDialog() {
    show.value = false;
    formRef.value?.ref.resetFields();
    dialogRef.value?.close();
    emit('close-dialog');
  }

  async function handleConfirm() {
    formRef.value?.ref.validate(async (valid) => {
      if (valid) {
        const [, res] = await updateValueRangeOfPermission(
          omit(formData, 'dataValueNameDisplay'),
        );
        if (res?.success) {
          ElMessage.success(t('global:edit.success'));
          clearFormAndcloseDialog();
        }
      }
    });
  }

  async function initDialog() {
    await nextTick();
    dialogRef.value?.open();
  }

  onBeforeMount(async () => {
    await initDialog();
    if (mode.value === FORM_OPERATION.EDIT) {
      const edited = props.rowEdited;
      formData.cliPermissionValueId = edited.cliPermissionValueId;
      formData.cliPermissionValueName = edited.cliPermissionValueName;
      formData.dataValueNameDisplay = edited.dataValueNameDisplay;
      formData.enabledFlag = edited.enabledFlag;
    }
  });
</script>

<template>
  <div>
    <ProDialog
      ref="dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="`${$t(`global:${mode}`)}`"
      :width="900"
      destroy-on-close
      :confirm-fn="handleConfirm"
      @close="clearFormAndcloseDialog"
      @cancel="clearFormAndcloseDialog"
      @success="clearFormAndcloseDialog"
    >
      <ProForm
        ref="form"
        mode="column"
        v-model="formData"
        :column="2"
        :data="formConfig"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>
