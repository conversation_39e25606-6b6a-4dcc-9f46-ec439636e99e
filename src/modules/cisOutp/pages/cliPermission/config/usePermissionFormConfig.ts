import { FLAG } from '@sun-toolkit/enums';
import { useFormConfig } from 'sun-biz';

export function usePermissionFormConfig({
  codeSystemOptions,
  typeOptions = [],
  remoteMethod,
  isEdit = false,
}: CliPermission.PermissionFormConfigParam) {
  return useFormConfig({
    getData: (t) => [
      // 编码体系
      {
        label: t('global:code') + t('cisOutp.cliPermission.system', '体系'),
        name: 'codeSystemId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('global:code') + t('cisOutp.cliPermission.system', '体系'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: codeSystemOptions.value,
          remote: true,
          filterable: true,
          remoteMethod,
          disabled: isEdit,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name:
                t('global:code') + t('cisOutp.cliPermission.system', '体系'),
            }),
            trigger: 'blur',
          },
        ],
      },
      // 类型
      {
        label: t('cisOutp.cliPermission.type', '类型'),
        name: 'multiplyCheckFlag',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('cisOutp.cliPermission.type', '类型'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: typeOptions,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('cisOutp.cliPermission.type', '类型'),
            }),
            trigger: 'blur',
          },
        ],
      },
      // 权限名称
      {
        label: t('cisOutp.cliPermission.permission', '权限') + t('global:name'),
        name: 'cliPermissionName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content:
            t('cisOutp.cliPermission.permission', '权限') + t('global:name'),
        }),
        triggerModelChange: true,
        extraProps: {},
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content:
                t('cisOutp.cliPermission.permission', '权限') +
                t('global:name'),
            }),
            trigger: 'blur',
          },
        ],
      },
      // 状态
      {
        label: t('global:status'),
        name: 'enabledFlag',
        component: 'switch',
        defaultValue: FLAG.YES,
        triggerModelChange: true,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
      },
    ],
  });
}
