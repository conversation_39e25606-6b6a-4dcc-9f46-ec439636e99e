import { FLAG } from '@sun-toolkit/enums';
import { useFormConfig } from 'sun-biz';

export function useValueRangeFormConfig() {
  return useFormConfig({
    getData: (t) => [
      // 值域名称(不可修改)
      {
        label:
          t('cisOutp.cliPermission.rangeOfValue', '值域') + t('global:name'),
        name: 'dataValueNameDisplay',
        component: 'input',
        extraProps: {
          disabled: true,
        },
      },
      // 权限值域名称
      {
        label:
          t('cisOutp.cliPermission.permission', '权限') +
          t('cisOutp.cliPermission.rangeOfValue', '值域') +
          t('global:name'),
        name: 'cliPermissionValueName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content:
            t('cisOutp.cliPermission.permission', '权限') +
            t('cisOutp.cliPermission.rangeOfValue', '值域') +
            t('global:name'),
        }),
        triggerModelChange: true,
        extraProps: {},
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content:
                t('cisOutp.cliPermission.permission', '权限') +
                t('cisOutp.cliPermission.rangeOfValue', '值域') +
                t('global:name'),
            }),
            trigger: 'blur',
          },
        ],
      },
      // 状态
      {
        label: t('global:status'),
        name: 'enabledFlag',
        component: 'switch',
        defaultValue: FLAG.YES,
        triggerModelChange: true,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
      },
    ],
  });
}
