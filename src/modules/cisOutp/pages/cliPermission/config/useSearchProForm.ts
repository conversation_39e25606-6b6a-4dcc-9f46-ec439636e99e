import { Search } from '@element-sun/icons-vue';
import { useFormConfig } from 'sun-biz';

export function useSearchProForm() {
  return useFormConfig({
    getData: (t) => [
      // 检索
      {
        label: t('cisOutp.diagnosis.retrival', '检索'),
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:content'),
        }),
        triggerModelChange: true,
        extraProps: {
          'suffix-icon': Search,
        },
      },
    ],
  });
}
