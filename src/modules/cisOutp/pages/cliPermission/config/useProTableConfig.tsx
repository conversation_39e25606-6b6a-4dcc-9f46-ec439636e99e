import { FLAG } from '@sun-toolkit/enums';
import { useColumnConfig } from 'sun-biz';
import { typeOptions } from '../../../constant';

/**
 * ProTable columns 权限列表配置
 * @param editFun 编辑callback
 * @returns
 */
export function usePermissionListConfig(
  editFun: (row: CliPermission.Permission) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        type: 'selection',
        width: 55,
      },
      // 序号
      {
        type: 'index',
        label: t('global:indexNo'),
        minWidth: 60,
      },
      // 术语名称
      {
        prop: 'codeSystemNameDisplay',
        label: t('cisOutp.cliPermission.term', '术语') + t('global:name'),
        minWidth: 120,
      },
      // 权限名称
      {
        prop: 'cliPermissionName',
        label: t('cisOutp.cliPermission.permission', '权限') + t('global:name'),
        minWidth: 120,
      },
      // 术语编码
      {
        prop: 'codeSystemNo',
        label: t('cisOutp.cliPermission.term', '术语') + t('global:code'),
        minWidth: 120,
      },
      // 类型
      {
        prop: 'multiplyCheckFlag',
        label: t('cisOutp.cliPermission.type', '类型'),
        minWidth: 120,
        render: (row: CliPermission.Permission) =>
          renderNameOfType(row.multiplyCheckFlag),
      },
      // 状态
      {
        prop: 'enabledFlag',
        label: t('global:status'),
        minWidth: 80,
        render: (row: { enabledFlag: number }) =>
          renderStatus(row, t('global:enabled'), t('global:disabled')),
      },
      // 操作
      {
        label: t('global:operation'),
        minWidth: 80,
        render: (row: CliPermission.Permission) => (
          <el-button
            onClick={(e: { preventDefault: () => void }) => {
              e.preventDefault();
              editFun(row);
            }}
            type="primary"
            link
          >
            {t('global:edit')}
          </el-button>
        ),
      },
    ],
  });
}

/**
 * ProTable columns 值域列表配置
 * @param editFun 编辑callback
 * @returns
 */
export function useValueRangeConfig(
  editFun: (row: CliPermission.ValuesOfPermissionRange) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        type: 'selection',
        width: 55,
      },
      // 序号
      {
        type: 'index',
        label: t('global:indexNo'),
        minWidth: 60,
      },
      // 值域名称
      {
        prop: 'dataValueNameDisplay',
        label:
          t('cisOutp.cliPermission.rangeOfValue', '值域') + t('global:name'),
        minWidth: 120,
      },
      // 权限值域名称
      {
        prop: 'cliPermissionValueName',
        label:
          t('cisOutp.cliPermission.permission', '权限') +
          t('cisOutp.cliPermission.rangeOfValue', '值域') +
          t('global:name'),
        minWidth: 120,
      },
      // 值域编码
      {
        prop: 'dataValueNo',
        label:
          t('cisOutp.cliPermission.rangeOfValue', '值域') + t('global:code'),
        minWidth: 120,
      },
      // 状态
      {
        prop: 'enabledFlag',
        label: t('global:status'),
        minWidth: 120,
        render: (row: { enabledFlag: number }) =>
          renderStatus(row, t('global:enabled'), t('global:disabled')),
      },
      // 操作
      {
        label: t('global:operation'),
        minWidth: 120,
        render: (row: CliPermission.ValuesOfPermissionRange) => (
          <el-button
            onClick={(e: { preventDefault: () => void }) => {
              e.preventDefault();
              editFun(row);
            }}
            type="primary"
            link
          >
            {t('global:edit')}
          </el-button>
        ),
      },
    ],
  });
}

function renderNameOfType(value: number) {
  const selected = typeOptions.filter((type) => type.value === value);
  if (selected.length) {
    return <span>{selected[0].label}</span>;
  } else {
    return '--';
  }
}

function renderStatus(
  row: { enabledFlag: number },
  yesLabel: string,
  noLabel: string,
) {
  if (row.enabledFlag === FLAG.YES) {
    return <el-tag type="primary">{yesLabel}</el-tag>;
  } else {
    return <el-tag type="danger">{noLabel}</el-tag>;
  }
}
