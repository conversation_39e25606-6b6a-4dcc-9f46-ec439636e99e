import { useFormConfig } from 'sun-biz';
import { DIAG_TYPE_CODE_NAME } from '../constant';
import CheckboxGroupWithIndeterminate from '../components/checkboxGroupWithIndeterminate.vue';
import { FLAG } from '@sun-toolkit/enums';
import { Ref } from 'vue';

export function useProFormConfig(
  catOptions: Ref<DiagnosisSearch.SelectOptions[]>,
  tagOptions: Ref<DiagnosisSearch.SelectOptions[]>,
) {
  return useFormConfig({
    dataSetCodes: [DIAG_TYPE_CODE_NAME],
    getData: (t) => [
      // 检索
      {
        label: t('cisOutp.diagnosis.retrival', '检索'),
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:content'),
        }),
        triggerModelChange: true,
        extraProps: {},
      },
      // 类别
      {
        label: t('cisOutp.diagnosis.category', '类别'),
        name: 'diagTypeCodes',
        triggerModelChange: true,
        extraProps: {
          options: catOptions.value,
        },
        render: () => CheckboxGroupWithIndeterminate,
        span: 3,
      },
      // 状态
      {
        label: t('global:status'),
        name: 'enabledFlag',
        component: 'switch',
        defaultValue: FLAG.YES,
        triggerModelChange: true,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
      },
      // 标签
      {
        label: t('cisOutp.diagnosis.tag', '标签'),
        name: 'tagIds',
        triggerModelChange: true,
        extraProps: {
          options: tagOptions.value,
        },
        span: 3,
        render: () => CheckboxGroupWithIndeterminate,
      },
    ],
  });
}
