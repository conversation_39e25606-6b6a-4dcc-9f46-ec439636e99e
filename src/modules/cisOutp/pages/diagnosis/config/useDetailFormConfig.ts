import { useFormConfig } from 'sun-biz';
import { DIAG_TYPE_CODE_NAME } from '../constant';
import { FLAG } from '@sun-toolkit/enums';

export function useDetailFormConfig(
  catOptions: DiagnosisSearch.SelectOptions[],
  tagOptions: DiagnosisSearch.SelectOptions[],
) {
  return useFormConfig({
    dataSetCodes: [DIAG_TYPE_CODE_NAME],
    getData: (t) => [
      // 检索
      {
        label: t('cisOutp.diagnosis.category', '类别'),
        name: 'diagTypeCode',
        triggerModelChange: true,
        component: 'select',
        isFullWidth: true,
        extraProps: {
          options: catOptions,
        },
        placeholder: t('global:placeholder.select.template', {
          name: t('cisOutp.diagnosis.category', '类别'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('cisOutp.diagnosis.category', '类别'),
            }),
            trigger: 'blur',
          },
        ],
      },
      {
        label: t('global:code'),
        name: 'diagNo',
        triggerModelChange: true,
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('global:code'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
            trigger: 'blur',
          },
        ],
      },
      {
        label: t('global:name'),
        name: 'diagName',
        triggerModelChange: true,
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'blur',
          },
        ],
      },
      {
        label: t('global:secondName'),
        name: 'diag2ndName',
        triggerModelChange: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        label: t('global:thirdName'),
        name: 'diagExtName',
        triggerModelChange: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },

      {
        label: t('global:spellNo'),
        name: 'spellNo',
        triggerModelChange: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
            trigger: 'blur',
          },
        ],
      },
      {
        label: t('global:wbNo'),
        name: 'wbNo',
        triggerModelChange: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
            trigger: 'blur',
          },
        ],
      },
      {
        label: t('cisOutp.diagnosis.tag', '标签'),
        name: 'tagIds',
        triggerModelChange: true,
        component: 'checkbox-group',
        isFullWidth: true,
        extraProps: {
          options: tagOptions,
        },
      },
      {
        label: t('global:status'),
        name: 'enabledFlag',
        component: 'switch',
        defaultValue: FLAG.YES,
        triggerModelChange: true,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
      },
    ],
  });
}
