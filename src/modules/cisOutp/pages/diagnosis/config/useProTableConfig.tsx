import { FLAG } from '@sun-toolkit/enums';
import { useColumnConfig } from 'sun-biz';

/**
 * ProTable columns 自定义设置
 * @param editFun 编辑callback
 * @returns
 */
export function useProTableConfig(
  editFun: (row: DiagnosisSearch.DiagnosisResItem) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      // 序号
      {
        type: 'index',
        label: t('global:indexNo'),
        minWidth: 60,
      },
      // 类别
      {
        prop: 'diagTypeCodeDesc',
        label: t('cisOutp.diagnosis.category', '类别'),
        minWidth: 120,
      },
      // 编码
      {
        prop: 'diagNo',
        label: t('global:code'),
        minWidth: 120,
      },
      // 名称
      {
        prop: 'diagName',
        label: t('global:name'),
        minWidth: 120,
      },
      // 辅助名称
      {
        prop: 'diag2ndName',
        label: t('global:secondName'),
        minWidth: 120,
      },
      // 扩展名称
      {
        prop: 'diagExtName',
        label: t('global:thirdName'),
        minWidth: 120,
      },
      // 标签
      {
        prop: 'diagTagList',
        label: t('cisOutp.diagnosis.tag', '标签'),
        minWidth: 120,
        render: (row: DiagnosisSearch.DiagnosisResItem) => {
          if (row.diagTagList) {
            const tags =
              row.diagTagList.map((item) => item.tagName && item.tagName) || [];
            return tags.length ? tags.join(', ') : '--';
          }
          return '--';
        },
      },
      // 拼音码
      {
        prop: 'spellNo',
        label: t('global:spellNo'),
        minWidth: 120,
      },
      // 五笔码
      {
        prop: 'wbNo',
        label: t('global:wbNo'),
        minWidth: 120,
      },
      // 状态
      {
        prop: 'enabledFlag',
        label: t('global:status'),
        minWidth: 120,
        render: (row: { enabledFlag: number }) => {
          if (row.enabledFlag === FLAG.YES) {
            return <el-tag type="primary">{t('global:enabled')}</el-tag>;
          } else {
            return <el-tag type="danger">{t('global:disabled')}</el-tag>;
          }
        },
      },
      // 操作
      {
        label: t('global:operation'),
        minWidth: 120,
        render: (row: DiagnosisSearch.DiagnosisResItem) => (
          <el-button
            onClick={(e: { preventDefault: () => void }) => {
              e.preventDefault();
              editFun(row);
            }}
            type="primary"
            link
          >
            {t('global:edit')}
          </el-button>
        ),
      },
    ],
  });
}
