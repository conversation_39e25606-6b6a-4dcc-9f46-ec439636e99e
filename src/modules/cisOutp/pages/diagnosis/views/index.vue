<script setup lang="ts">
  import {
    ProForm,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { useProFormConfig } from '../config/useProFormConfig';
  import { onBeforeMount, reactive, ref, useTemplateRef, nextTick } from 'vue';
  import {
    DIAG_TYPE_CODE_NAME,
    FORM_OPERATION,
    TAG_GROUP_ID,
  } from '../constant';
  import { useProTableConfig } from '../config/useProTableConfig';
  import {
    queryDataSetListByExample,
    queryDiagnosisList,
    queryTagListByExample,
  } from '../../../api/code';
  import { DEFAULT_PAGE_SIZE, FLAG } from '@sun-toolkit/enums';
  import Detail from '../components/detail.vue';

  const categoryOptions = ref<DiagnosisSearch.SelectOptions[]>([]);
  const tagOptions = ref<DiagnosisSearch.SelectOptions[]>([]);
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const proFormConfig = useProFormConfig(categoryOptions, tagOptions);
  const searchModel = ref<Partial<ParamSetting.ReqParams>>({});
  const handleProFormModelChange = (
    form: Partial<DiagnosisSearch.DiagnosisReqParams>,
  ) => {
    Object.entries(form).forEach(([key, value]) => {
      searchParams[key as keyof DiagnosisSearch.DiagnosisReqParams] =
        value as never;
    });
    searchParams.pageNumber = FLAG.YES;
    fetchDiagnosisList();
  };
  // 列表栏
  const loading = ref(false);
  const total = ref(0);
  const list = ref<DiagnosisSearch.DiagnosisResItem[]>([]);
  // 查询参数配置条件
  const searchParams = reactive<DiagnosisSearch.DiagnosisReqParams>({
    pageSize: DEFAULT_PAGE_SIZE,
    pageNumber: FLAG.YES,
    // 检索
    keyWord: '',
    // 类别
    diagTypeCodes: [],
    // 状态
    enabledFlag: FLAG.YES,
    // 标签
    tagIds: [],
  });
  const editedRow = ref<DiagnosisSearch.DiagnosisResItem>();
  const proTableConfig = useProTableConfig(editRow);
  const detailRef = useTemplateRef<{ dialogRef: { open: () => void } }>(
    'detail',
  );
  const detailType = ref(FORM_OPERATION.ADD);
  const isDialogOpen = ref(false);

  function handleDialogClose() {
    isDialogOpen.value = false;
    // 加载列表
    fetchDiagnosisList();
  }

  async function addRow() {
    isDialogOpen.value = true;
    detailType.value = FORM_OPERATION.ADD;
    await nextTick();
    detailRef.value!.dialogRef.open();
  }

  async function editRow(row: DiagnosisSearch.DiagnosisResItem) {
    detailType.value = FORM_OPERATION.EDIT;
    editedRow.value = row;
    isDialogOpen.value = true;
    // 打开窗口
    await nextTick();
    detailRef.value!.dialogRef.open();
  }

  // 获取类别选项
  async function fetchCategories() {
    let [, result] = await queryDataSetListByExample({
      codeSystemNos: [DIAG_TYPE_CODE_NAME],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: FLAG.NO,
    });
    if (result?.success) {
      let { data } = result;
      categoryOptions.value = data
        .filter((item) => item.enabledFlag === FLAG.YES)
        .map((item) => ({
          value: item.dataValueNo,
          label: item.dataValueCnName,
        }));
    }
  }

  // 获取标签选项
  async function fetchTags() {
    let [, result] = await queryTagListByExample({
      keyWord: '',
      tagGroupIds: [TAG_GROUP_ID],
    });
    if (result?.success) {
      let { data } = result;
      tagOptions.value = data
        .filter((item) => item.enabledFlag === FLAG.YES)
        .map((item) => ({
          value: item.tagId,
          label: item.tagNameDisplay,
        }));
    }
  }

  // 获取列表数据
  const fetchDiagnosisList = async () => {
    loading.value = true;
    let [, result] = await queryDiagnosisList(searchParams);
    loading.value = false;
    if (result?.success) {
      let { data, total: pageTotal } = result;
      list.value = data;
      total.value = Number(pageTotal);
    }
  };

  // 初始化分类/标签选项
  const initOptions = () => {
    fetchCategories();
    fetchTags();
  };

  onBeforeMount(() => {
    initOptions();
    fetchDiagnosisList();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <!-- 检索栏 -->
    <!-- 阻止提交默认刷新页面行为：@submit.prevent -->
    <ProForm
      @submit.prevent
      :data="proFormConfig"
      column="4"
      v-model="searchModel"
      @model-change="handleProFormModelChange"
    >
      <template #customButton>
        <div class="absolute right-2 top-2">
          <el-button type="primary" @click="addRow">{{
            $t('global:add')
          }}</el-button>
        </div>
      </template>
    </ProForm>
    <!-- 列表栏 -->
    <ProTable
      :data="list"
      :pagination="true"
      :loading="loading"
      :columns="proTableConfig"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      @current-page-change="
        (val: number) => {
          searchParams.pageNumber = val;
          fetchDiagnosisList();
        }
      "
      @size-page-change="
        (val: number) => {
          searchParams.pageSize = val;
          fetchDiagnosisList();
        }
      "
    ></ProTable>
    <!-- 编辑/添加 -->
    <Detail
      v-if="isDialogOpen"
      ref="detail"
      :mode="detailType"
      :cat-options="categoryOptions"
      :tag-options="tagOptions"
      :edited-row="editedRow"
      @dialog-close="handleDialogClose"
    />
  </div>
</template>
