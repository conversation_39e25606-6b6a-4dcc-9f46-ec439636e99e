<script setup lang="ts">
  import { FLAG } from '@sun-toolkit/enums';
  import { ProDialog, ProForm } from 'sun-biz';
  import { onBeforeMount, reactive, useTemplateRef } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { omit } from 'lodash';
  import { useDetailFormConfig } from '../config/useDetailFormConfig';
  import { FORM_OPERATION } from '../constant';
  import { ElMessage, FormInstance } from 'element-sun';
  import { addDiagnosisDict, updateDiagnosisDict } from '../../../api/code.ts';

  const emit = defineEmits(['dialog-close']);
  const props = defineProps<{
    mode: string;
    catOptions: DiagnosisSearch.SelectOptions[];
    tagOptions: DiagnosisSearch.SelectOptions[];
    editedRow?: DiagnosisSearch.DiagnosisResItem;
  }>();
  const { t } = useTranslation();
  const detailFormRef = useTemplateRef<{ ref: FormInstance }>('detailForm');
  const dialogRef = useTemplateRef('dialog');
  const formConfig = useDetailFormConfig(props.catOptions, props.tagOptions);
  const formData = reactive<DiagnosisSearch.DiagnosisiAddItemReqParams>({
    diagTypeCode: '',
    diagNo: '',
    diagName: '',
    diag2ndName: '',
    diagExtName: '',
    wbNo: '',
    spellNo: '',
    enabledFlag: FLAG.YES,
    tagIds: [],
  });

  function clearForm() {
    formData.diagTypeCode = '';
    formData.diagNo = '';
    formData.diagName = '';
    formData.diag2ndName = '';
    formData.diagExtName = '';
    formData.wbNo = '';
    formData.spellNo = '';
    formData.enabledFlag = FLAG.YES;
    formData.tagIds = [];
    // 重置表单项，清除校验
    detailFormRef?.value?.ref.resetFields();
  }

  function clearFormAndcloseDialog() {
    clearForm();
    emit('dialog-close');
  }

  async function confirmAdd() {
    const [, res] = await addDiagnosisDict(formData);
    if (res?.success) {
      // 添加成功
      ElMessage.success(t('global:create.success'));
      emit('dialog-close');
    }
  }

  async function confirmEdit() {
    const form = {
      ...omit(formData, 'tagIds'),
      diagId: props.editedRow?.diagId || '',
      diagTags:
        formData.tagIds?.map((tagId) => ({
          tagId,
        })) || [],
    };
    const [, res] = await updateDiagnosisDict(form);
    if (res?.success) {
      // 编辑成功
      ElMessage.success(t('global:edit.success'));
      emit('dialog-close');
    }
  }

  function confirm() {
    if (props.mode === FORM_OPERATION.ADD) {
      confirmAdd();
    } else {
      confirmEdit();
    }
  }

  function handleConfirm() {
    detailFormRef.value!.ref.validate((valid) => {
      if (valid) {
        confirm();
      }
    });
  }

  onBeforeMount(() => {
    // 编辑
    if (props.mode === FORM_OPERATION.EDIT) {
      if (props.editedRow) {
        formData.diagTypeCode = props.editedRow.diagTypeCode;
        formData.diagNo = props.editedRow.diagNo;
        formData.diagName = props.editedRow.diagName;
        formData.diag2ndName = props.editedRow.diag2ndName;
        formData.diagExtName = props.editedRow.diagExtName;
        formData.wbNo = props.editedRow.wbNo;
        formData.spellNo = props.editedRow.spellNo;
        formData.enabledFlag = props.editedRow.enabledFlag;
        if (props.editedRow.diagTagList?.length) {
          formData.tagIds = props.editedRow.diagTagList.map(
            (item) => item.tagId,
          );
        }
      }
    }
  });

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <div>
    <ProDialog
      ref="dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :confirm-fn="handleConfirm"
      :title="`${$t(`global:${props.mode}`)}`"
      :width="900"
      destroy-on-close
      @close="clearFormAndcloseDialog"
      @cancel="clearFormAndcloseDialog"
      @success="clearFormAndcloseDialog"
    >
      <ProForm
        ref="detailForm"
        mode="column"
        v-model="formData"
        :column="2"
        :data="formConfig"
      ></ProForm>
    </ProDialog>
  </div>
</template>
