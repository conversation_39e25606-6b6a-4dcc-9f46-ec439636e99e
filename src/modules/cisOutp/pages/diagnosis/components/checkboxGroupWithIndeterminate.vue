<script setup lang="ts">
  import { ref } from 'vue';
  import type { CheckboxValueType } from 'element-sun';

  const props = defineProps<{
    options: DiagnosisSearch.SelectOptions[];
  }>();
  const checkAll = ref(false);
  const isIndeterminate = ref(false);
  const checkedCities = defineModel<string[]>({
    default: [],
  });

  const handleCheckAllChange = (val: CheckboxValueType) => {
    checkedCities.value = val ? props.options.map((item) => item.value) : [];
    isIndeterminate.value = false;
  };
  const handleCheckedCitiesChange = (value: CheckboxValueType[]) => {
    const checkedCount = value.length;
    checkAll.value = checkedCount === props.options.length;
    isIndeterminate.value =
      checkedCount > 0 && checkedCount < props.options.length;
  };
</script>

<template>
  <div class="flex">
    <div class="mr-4">
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        {{ $t('global:all') }}
      </el-checkbox>
    </div>
    <el-checkbox-group
      v-model="checkedCities"
      @change="handleCheckedCitiesChange"
    >
      <el-checkbox
        v-for="cat in props.options"
        :key="cat.value"
        :label="cat.label"
        :value="cat.value"
      >
        {{ cat.label }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
