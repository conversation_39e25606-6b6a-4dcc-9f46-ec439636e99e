import { FLAG } from '@sun-toolkit/enums';
import { t } from '@sun-toolkit/micro-app';

export const FORM_OPERATION = {
  ADD: 'add',
  EDIT: 'edit',
};

// 临床权限表单类型选项
export const typeOptions = [
  {
    label: t('cisOutp.cliPermission.singleChoice', '单选') ?? '单选', // 解决页面无法渲染多语言词条
    value: FLAG.NO,
  },
  {
    label: t('cisOutp.cliPermission.multipleChoice', '多选') ?? '复选',
    value: FLAG.YES,
  },
];

// 临床权限字典-权限列表 row-key
export const CLI_PERMISSION_ROW_KEY = 'cliPermissionId';

// 临床权限字典-值域列表 row-key
export const CLI_PERMISSION_VALUE_ROW_KEY = 'cliPermissionValueId';
