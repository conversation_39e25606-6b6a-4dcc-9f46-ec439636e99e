declare namespace CliPermission {
  // 临床权限列表项
  export interface Permission {
    cliPermissionId: string; // 临床权限标识
    cliPermissionName: string; // 临床权限名称
    codeSystemId: string; // 编码体系标识
    codeSystemNo: string; // 编码体系NO
    codeSystemNameDisplay: string; // 中文名称（语言环境）
    codeSystemName: string; // 中文名称
    codeSystem2ndName: string; // 辅助名称
    codeSystemExtName: string; // 扩展名称
    multiplyCheckFlag: number; // 多选标志
    enabledFlag: number; // 启用标志
    sort: number; // 排序
    cliPermissionValueList: ValuesOfPermissionRange[]; // 临床权限值域列表
  }

  // 临床权限值域列表项目
  export interface ValuesOfPermissionRange {
    cliPermissionValueId: string; // 临床权限值域标识
    cliPermissionValueName: string; // 临床权限值域名称
    dataValueId: number; // 值标识
    dataValueNo: string; // 值编码
    dataValueNameDisplay: string; // 值中文名称（语言环境）
    dataValueCnName: string; // 值中文名称
    dataValue2ndName: string; // 值辅助名称
    dataValueExtName: string; // 值扩展名称
    enabledFlag: number; // 启用标志
    sort: number; // 排序
  }

  // 权限列表检索入参
  export interface SearchCliPermissionsReqParam {
    keyWord: string;
    pageSize: number;
    pageNumber: number;
  }

  export interface SelectOption {
    label: string;
    value: string | number;
  }

  export interface codeSystemReqParam {
    codeSystemNo?: string; // 编码体系NO
    keyWord?: string; // 关键字
    enabledFlag?: number; // 启用标志
  }

  export interface codeSystemResItem {
    codeSystemId: number; // 编码体系标识
    codeSystemNo: string; // 编码体系NO
    codeSystemName: string; // 中文名称
    codeSystem2ndName?: string; // 辅助名称
    codeSystemExtName?: string; // 扩展名称
    nameDisplay: string; // 名称（语言环境）
    description: string; // 描述
    spellNo: string; // 拼音码
    wbNo?: string; // 五笔码
    version: number; // 版本
  }

  // 新增/编辑：权限表单配置参数
  export interface PermissionFormConfigParam {
    codeSystemOptions: Ref<CliPermission.SelectOption[]>;
    typeOptions: CliPermission.SelectOption[];
    remoteMethod: (query: string) => void;
    isEdit: boolean;
  }

  // 新增/编辑：表单参数
  export interface PermissionFormData {
    codeSystemId: string;
    multiplyCheckFlag: number;
    cliPermissionName: string;
    enabledFlag: number;
  }

  // 权限对应域值编辑入参
  export interface ValueRangeReqParam {
    cliPermissionValueId: string; // 临床权限值域标识
    cliPermissionValueName: string; // 临床权限值域名称
    enabledFlag: number; // 启用标志
  }

  // 新增临床权限字典 入参
  export interface PermissionDictReqParam extends PermissionFormData {
    cliPermissionValueList: Pick<
      ValuesOfPermissionRange,
      'cliPermissionValueName' | 'dataValueId' | 'enabledFlag'
    >[];
  }

  // 新增临床权限字典 出参
  export interface PermissionDictResParam {
    cliPermissionId: string; // 临床权限标识
    codeSystemId: string; // 编码体系标识
    cliPermissionValueList: Pick<
      ValuesOfPermissionRange,
      'cliPermissionValueId' | 'dataValueId'
    >[];
  }

  // 编辑临床权限字典 入参
  export interface PermissionUpdatedReqParam {
    cliPermissionId: string; // 临床权限标识
    cliPermissionName: string; // 临床权限名称
    multiplyCheckFlag: number; // 多选标志
    enabledFlag: number; // 启用标志
  }
}
