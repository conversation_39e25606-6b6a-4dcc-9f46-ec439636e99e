declare namespace DbGridComponent {
  interface ReqSaveDbgridComponent {
    componentId?: string;
    componentNo: string;
    componentDesc: string;
    enabledFlag: number;
  }

  interface DbgridComponentSettingInfo {
    dbgridComptSettingId: string;
    dbgridSettingValue: string;
    bizId: string | underfind;
    bizName: string;
    bizIdTypeCode?: string;
    bizIdTypeDesc?: string;
    influenceScopeCode: string;
    influenceScopeDesc: string;
  }

  interface DbgridComponentInfo {
    componentId?: string;
    componentNo: string;
    componentDesc: string;
    enabledFlag: number;
    exportFileFlag: number;
    dbgridComponentSettingList: DbgridComponentSettingInfo[];
  }

  interface ReqQueryDbgridComponentList {
    keyWord?: string;
    menuId?: string;
    componentNo?: string;
    enabledFlag?: number;
    pageNumber: number;
    pageSize: number;
  }

  interface DbgridComponent {
    componentId: string;
    componentNo: string;
    componentDesc: string;
    dbgridComptSettingId: string;
    dbgridSettingValue: string;
    bizId: string;
    bizIdTypeCode: string;
    influenceScopeCode: string;
  }

  interface ReqDbgridComponentSettingInfo {
    dbgridComptSettingId?: string;
    dbgridSettingValue: string;
    bizId?: string | string;
    bizIdTypeCode?: string;
    influenceScopeCode: string;
  }

  interface ReqSaveDbgridComponentSetting {
    componentId: string;
    dbgridComponentSettingList: ReqDbgridComponentSettingInfo[];
  }
}
