declare namespace Banner {
  interface BannerReqParams {
    keyWord?: string;
    enabledFlag?: number;
    componentCode?: string;
  }

  interface BannerReqItem {
    componentId: string;
    componentCode?: string;
    componentDesc: string;
    enabledFlag: number;
    formId: string;
    formName: string;
  }

  interface AddBannerReqParams {
    componentId?: string;
    componentDesc: string;
    enabledFlag: number;
    formId: string;
  }

  interface AddBannerReqItem {
    componentId: string;
  }

  interface UpdateBannerReqParams {
    componentId: string;
    componentCode?: string;
    componentDesc: string;
    enabledFlag: number;
    formId: string;
  }
}
