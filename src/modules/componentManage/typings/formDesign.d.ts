declare namespace FormDesign {
  /**
   *新增表单入参
   */
  interface AddFormReq {
    formName: string;
    formDesc?: string;
    bindingApiUrl?: string;
    enabledFlag: number;
  }

  /**
   * 根据标识修改表单信息 入参
   */
  interface UpdateFormReq extends AddFormReq {
    formId: string;
  }

  interface FormControlInfo {
    formControlId: string;
    formCtlNo: string;
    formCtlName: string;
    labelName: string;
    label2ndName?: string;
    labelExtName?: string;
    hintMsg?: string;
    labelNameDisplay?: string;
    controlTypeCode: string;
    controlTypeName: string;
    maxLength?: number;
    dataSearchBizIdTypeCode?: string;
    dataSearchBizIdTypeDesc?: string;
    codeSystemNo?: string;
    codeSystemName?: string;
    displayFlag: number;
    mustInputFlag: number;
    bindingFieldNo: string;
    defaultValue?: string;
    defaultValueName?: string;
    sort: number;
    belongGroupElementId?: string;
    allowModifyFlag?: number;
    bindingFieldNo?: string;
    specialMark?: string;
    editable?: boolean;
    subFormControlList?: FormControlInfo[];
  }
  /**
   * 表单信息
   */
  interface FormInfo {
    formId: string;
    formName: string;
    formDesc?: string;
    bindingApiUrl?: string;
    enabledFlag: number;
    formControlList?: FormControlInfo[];
  }

  interface ReqAddFormControl extends Partial<FormControlInfo> {
    formId: string;
  }

  interface ReqQueryFormDesignList {
    keyWord?: string;
    enabledFlag?: number;
    formId: string;
  }

  interface BaseFormDesign {
    formDesignName?: string;
    formDesign2ndName?: string;
    formDesignExtName?: string;
    layoutTypeCode?: string;
    layoutTypeDesc?: string;
    enabledFlag?: 0 | 1;
    defaultFlag?: 0 | 1;
    menuIds?: string[];
    formDesignXMenuList?: {
      formDesignXMenuId: string;
      menuId: string;
      menuName: string;
    }[];
  }

  interface FormDesignInfo extends BaseFormDesign {
    formDesignId?: string;
  }

  interface ReqAddFormDesign extends BaseFormDesign {
    formId: string;
  }

  type SchemeInfoDetail = Omit<FormDesignInfo, 'formDesignId'> &
    Omit<ReqAddFormDesign, 'formId'> & {
      identification?: string | undefined;
      formId?: string | undefined;
      formDesignId?: string | undefined;
    };
  interface SchemeInfo {
    detail: SchemeInfoDetail;
    tableData: FormDesignDetailInfo[];
  }

  interface FormDesignDetailInfo extends SchemeInfoDetail {
    allowModifyFlag: number;
    formDesignDetailId?: string;
    editable?: boolean;
    displayWidth?: number;
    formControlId?: string;
    formDesignId?: string;
    cursorStayFlag: 0 | 1;
    belongGroupElementId?: string;
    focusAutoDropDownFlag: 0 | 1;
    displayWidthRatio: number;
    subFormControlList?: FormDesignDetailInfo[];
    labelName?: string;
    label2ndName?: string;
    labelExtName?: string;
    hintMsg?: string;
    displayFlag: number;
    mustInputFlag?: number;
    defaultValue?: string;
    sort?: number;
    formCtlNo?: string;
    formCtlName?: string;
  }

  interface DesignDetailInfo {
    formDesignDetailId?: string;
    labelName: string;
    label2ndName?: string;
    labelExtName?: string;
    hintMsg?: string;
    displayFlag: number;
    mustInputFlag: number;
    defaultValue?: string;
    formControlId?: string;
    formDesignId?: string;
    sort: number;
    cursorStayFlag: number;
    allowModifyFlag: number;
    focusAutoDropDownFlag: number;
    displayWidthRatio: number;
  }
  interface ReqDictDataListByExample {
    titleAndAttribute: {
      title: string;
      dataIndex: string;
    }[];
    primaryKey: string;
    displayKey: string;
    data: {
      data: [];
    };
  }

  interface SaveFormDesignDetailReq {
    formDesignId: string;
    formDesignName: string;
    formDesign2ndName?: string;
    formDesignExtName?: string;
    layoutTypeCode?: string;
    enabledFlag: number;
    defaultFlag: number;
    menuIds?: string[];
    formDesignDetailList: DesignDetailInfo[];
  }
}
