declare namespace BizSearch {
  interface SearchTypeReqParams {
    keyWord?: string;
    enabledFlag?: number;
  }

  interface SearchTypeItem {
    searchTypeId: string;
    searchTypeCode: string;
    searchTypeName: string;
    searchType2ndName?: string;
    searchTypeExtName?: string;
    searchTypeNameDisplay: string;
    indexTypeCode?: string;
    indexTypeDesc?: string;
    triggerTypeCode: string;
    triggerTypeDesc: string;
    inputHintContent?: string;
    interfaceId?: string;
    interfaceName?: string;
    enabledFlag: number;
    hotKeyNo?: string;
  }

  interface AddSearchTypeReqParams {
    searchTypeCode: string;
    searchTypeName: string;
    searchType2ndName?: string;
    searchTypeExtName?: string;
    indexTypeCode?: string;
    triggerTypeCode: string;
    inputHintContent?: string;
    interfaceId?: string;
    enabledFlag: number;
    hotKeyNo?: string;
  }

  interface AddSearchTypeItem {
    searchTypeId: string;
  }

  interface UpdateSearchTypeReqParams
    extends Omit<AddSearchTypeReqParams, 'searchTypeCode'> {
    searchTypeId: string;
  }

  interface UpdateSearchTypeEnabledFlagReqParams extends AddSearchTypeItem {
    enabledFlag: number;
  }

  interface SearchComponentReqParams {
    keyWord?: string;
    enabledFlag?: number;
    componentCode?: string;
  }

  interface SearchComponentItem {
    componentId: string;
    componentCode: string;
    componentName: string;
    componentDesc: string;
    enabledFlag: number;
    maxShowItemNum: number;
    bizSearchTypeList: {
      searchComptXSearchTypeId: string;
      vsEnabledFlag: number;
      searchTypeId: string;
      defaultFlag: number;
      sort: number;
      searchTypeCode: string;
      bizSearchTypeNameDisplay: string;
      indexTypeCode?: string;
      indexTypeDesc?: string;
      triggerTypeCode: string;
      triggerTypeDesc: string;
      inputHintContent?: string;
      interfaceId?: string;
      interfaceName?: string;
      enabledFlag: number;
      hotKeyNo?: string;
    }[];
  }

  interface AddSearchComponentReqParams {
    componentCode: string;
    componentDesc: string;
    enabledFlag: number;
    maxShowItemNum: number;
    bizSearchTypeList: {
      searchTypeId: string;
      defaultFlag: number;
      sort: number;
      enabledFlag: number;
      inputHintContent?: string;
    }[];
  }

  interface AddSearchComponentItem {
    componentId: string;
  }

  interface UpdateSearchComponentReqParams
    extends Omit<
      AddSearchComponentReqParams,
      'componentCode',
      'bizSearchTypeList'
    > {
    componentId: string;
    bizSearchTypeList: {
      searchComptXSearchTypeId?: string;
      searchTypeId: string;
      defaultFlag: number;
      sort: number;
      enabledFlag: number;
      inputHintContent?: string;
    }[];
  }

  interface UpdateSearchComponentEnabledFlagReqParams
    extends AddSearchComponentItem {
    enabledFlag: number;
  }

  interface SaveMenuXSearchTypeReqParams {
    searchComptXMenuId?: string;
    menuId: string;
    componentId: string;
    maxShowItemNum: number;
    hospitalId: string;
    menuXSearchTypeList: {
      menuXSearchTypeId?: string;
      searchComptXSearchTypeId: string;
      vsEnabledFlag: number;
      defaultFlag: number;
      sort: number;
      inputHintContent?: string;
    };
  }

  interface MenuXSearchTypeReqParams {
    componentCode: string;
    menuId?: string;
    enabledFlag?: number;
  }

  interface MenuXSearchTypeItem {
    searchComptXMenuId?: string;
    componentId: string;
    componentCode: string;
    componentDesc: string;
    enabledFlag: number;
    maxShowItemNum: number;
    bizSearchTypeList: {
      menuXSearchTypeId?: string;
      searchComptXSearchTypeId: string;
      vsEnabledFlag: number;
      searchTypeId: string;
      defaultFlag: number;
      sort: number;
      searchTypeCode: string;
      bizSearchTypeNameDisplay: string;
      indexTypeCode?: string;
      indexTypeDesc?: string;
      triggerTypeCode: string;
      triggerTypeDesc: string;
      inputHintContent?: string;
      interfaceId?: string;
      interfaceName?: string;
      enabledFlag: number;
      hotKeyNo?: string;
    }[];
  }

  interface SearchComponentXMenuReqParams {
    menuIds?: string;
    componentId?: string;
    hospitalId: string;
  }

  interface SearchComponentXMenuItem {
    searchComptXMenuId: string;
    menuId: string;
    menuNameDisplay: string;
    componentId: string;
    componentCode: string;
    componentDesc: string;
  }
}
