import {
  COMPONENT_MAP,
  CONTROL_TYPE_CODE,
  SWITCH_CODE,
} from '@/utils/constant';
const DICT_COMMON_TABLE = 'dictCommonTable';

/**
 * 获取组件类型
 * @param item
 * @returns
 */
export function getComponentType(item: FormDesign.FormControlInfo) {
  if (item.controlTypeCode === CONTROL_TYPE_CODE.SELECT) {
    return DICT_COMMON_TABLE;
  }
  return COMPONENT_MAP[item.controlTypeCode as keyof typeof COMPONENT_MAP];
}

export function getPlaceholder(
  item: FormDesign.FormControlInfo,
  t: (
    arg0: string,
    arg1: { name: string | undefined; content: string | undefined },
  ) => string,
) {
  return (
    item.hintMsg ||
    t(
      `global:placeholder.${item.controlTypeCode === CONTROL_TYPE_CODE.INPUT ? 'input' : 'select'}.template`,
      {
        name: item.labelNameDisplay || item.labelName,
        content: item.labelNameDisplay || item.labelName,
      },
    )
  );
}

export function getExtraProps(item: FormDesign.FormControlInfo) {
  const initObj = {
    defaultValue: item.defaultValue,
    defaultValueName: item.defaultValueName,
  };

  let resultObj: { [key: string]: unknown } = {
    style: { width: '100%' },
    filterable: true,
    codeSystemNo: item.codeSystemNo,
    dataSearchBizIdTypeCode: item?.dataSearchBizIdTypeCode,
    ...initObj,
  };
  if (item.controlTypeCode === CONTROL_TYPE_CODE.DATEPICKER) {
    resultObj.type = 'date';
    resultObj.format = 'YYYY-MM-DD';
    resultObj.valueFormat = 'YYYY-MM-DD  HH:mm:ss';
    resultObj.defaultValue = undefined;
  }
  if (item.controlTypeCode === CONTROL_TYPE_CODE.SWITCH) {
    resultObj = {
      ...resultObj,
      'inline-prompt': true,
      'active-value': SWITCH_CODE.YES,
      'inactive-value': SWITCH_CODE.NO,
    };
  }
  return resultObj;
}
