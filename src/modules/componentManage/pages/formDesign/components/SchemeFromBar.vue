<script setup lang="ts">
  import { ref, watch } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { useSchemeFromBar } from '../config/useSchemeFromBar';
  import { ProForm } from 'sun-biz';

  type Props = {
    schemeInfo?: FormDesign.SchemeInfo;
    formId?: string;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const infoModel = ref<FormDesign.FormDesignInfo>({
    formDesignName: '',
    formDesign2ndName: '',
    formDesignExtName: '',
    layoutTypeCode: '',
    layoutTypeDesc: '',
    enabledFlag: 1,
    defaultFlag: 1,
    menuIds: [],
  });
  const formData = useSchemeFromBar(infoModel);

  watch(
    () => props.schemeInfo?.detail,
    () => {
      if (props.schemeInfo?.detail) {
        infoModel.value = {
          ...props.schemeInfo?.detail,
          menuIds: (props.schemeInfo?.detail?.formDesignXMenuList || []).map(
            (item) => item.menuId,
          ),
        };
      }
    },
  );

  async function saveDesign() {
    return new Promise((resolve) => {
      formRef.value?.ref.validate().then(async () => {
        resolve({
          ...infoModel.value,
          formId: props.formId,
        });
      });
    });
  }
  defineExpose({
    saveDesign,
  });
</script>

<template>
  <ProForm ref="formRef" v-model="infoModel" :column="4" :data="formData">
  </ProForm>
</template>
