<script setup lang="ts">
  import { ref, useAttrs, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useCreateOrEditFormConfig } from '../config/useFormConfig';
  import { useTranslation } from 'i18next-vue';
  import { addForm, updateFormById } from '../../../api/formDesign';
  import { ProForm, ProDialog } from 'sun-biz';
  const { t } = useTranslation();

  type Props = {
    row?: {
      formId?: string;
      formName?: string;
      formDesc?: string;
      bindingApiUrl?: string;
      enabledFlag?: number;
    };
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const dialogRef = ref();
  const attrs = useAttrs();
  const formModel = ref();
  const formData = useCreateOrEditFormConfig();
  const emits = defineEmits<{
    success: [];
  }>();
  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = await (props.row?.formId
            ? updateFormById({
                ...(model as unknown as FormDesign.AddFormReq),
                formId: props.row?.formId,
              })
            : addForm({
                ...(model as unknown as FormDesign.AddFormReq),
              }));

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                `global:${props.row?.formId ? 'edit' : 'create'}.success`,
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
    },
  );

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="4" :data="formData" />
  </ProDialog>
</template>
