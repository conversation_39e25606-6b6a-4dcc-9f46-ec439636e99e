<script setup lang="ts" name="formUnit">
  import { ref, useAttrs } from 'vue';
  import { ElMessage } from 'element-sun';
  import { DictSelect, FormDescItem } from 'sun-biz';

  const componentRef = ref();
  // import {HospitalSelect} from 'sun-biz';
  // import {CopyTextWithTooltip} from 'sun-biz';
  /**
   * upload上传前操作
   */
  const beforeUpload = (file: File): boolean => {
    const isJsp =
      file.type === 'application/javascript' || file.name.endsWith('.jsp');
    const isSvg = file.type === 'image/svg+xml' || file.name.endsWith('.svg');
    const isPng = file.type === 'image/png' || file.name.endsWith('.png');
    const isValidType = isJsp || isSvg || isPng;
    const isValidSize = file.size / 1024 < 1000; // 转换为KB

    if (!isValidType) {
      ElMessage.error('只支持上传 .jsp、.svg 或 .png 文件!');
    }
    if (!isValidSize) {
      ElMessage.error('文件大小不能超过 1000KB!');
    }
    return isValidType && isValidSize;
  };

  /**
   * 选取文件
   */
  const handleChange = (file: File) => {
    const reader = new FileReader();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-expect-error
    reader.readAsDataURL(file.raw);
    reader.onload = () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (attrs as any)['onUpdate:modelValue'](reader.result as string);
    };
  };
  const attrs: Partial<FormDescItem> & {
    modelValue?: unknown;
    'support-copy-and-tips'?: boolean;
  } = useAttrs();

  defineExpose(
    new Proxy(
      {},
      {
        get(_target, prop) {
          if (componentRef?.value) {
            return componentRef.value[prop];
          }
        },
        has(_target, prop) {
          if (componentRef?.value) {
            return prop in componentRef.value;
          }
          return false;
        },
      },
    ),
  );
</script>

<template>
  <component
    v-if="attrs.component === 'select'"
    :is="`el-${attrs.component}`"
    :filterable="
      attrs?.filterable ||
      (attrs?.options?.length && attrs?.options?.length > 5)
        ? true
        : false
    "
    v-bind="{ ...attrs }"
  >
    <el-option
      v-for="item in attrs.options"
      :key="
        attrs?.props?.value
          ? item[attrs.props.value]
          : (item.value ?? item.dataValueNo)
      "
      :label="
        attrs?.props?.label
          ? item[attrs.props.label]
          : (item.label ?? item.dataValueNameDisplay)
      "
      :value="
        attrs?.props?.value
          ? item[attrs.props.value]
          : (item.value ?? item.dataValueNo)
      "
    />
  </component>
  <div class="overflow-hidden" v-else-if="attrs.component === 'text'">
    <!-- <CopyTextWithTooltip
      :text="String(attrs?.modelValue)"
      v-if="attrs['support-copy-and-tips']"
    /> -->

    <!-- <span v-else>
      {{ attrs?.modelValue }}
    </span> -->
  </div>

  <!-- <HospitalSelect
    v-else-if="attrs.component === 'hospitalSelect'"
    clearable
    v-bind="{ ...attrs }"
  /> -->
  <!-- @vue-ignore -->
  <DictSelect
    v-model="attrs.modelValue"
    v-else-if="attrs.component === 'dictCommonTable'"
    v-bind="{ ...attrs }"
  />

  <component
    v-else-if="attrs.component === 'upload'"
    :is="`el-${attrs.component}`"
    :before-upload="beforeUpload"
    :on-change="handleChange"
    :auto-upload="false"
    v-bind="{ ...attrs }"
  >
    <template #trigger>
      <span class="flex items-center justify-center border-2 p-12">
        <el-icon><Plus /></el-icon>
      </span>
    </template>
    <template #upload>
      <el-button type="primary">上传</el-button>
    </template>
    <template #tip>
      <div class="el-upload__tip">只能上传jpg/png/svg文件，且不超过1000kb</div>
    </template>
  </component>

  <component
    v-else
    ref="componentRef"
    :is="`el-${attrs.component}`"
    clearable
    v-bind="{ ...attrs }"
  >
    <template v-if="attrs.component === 'radio-group'">
      <component
        v-for="item in attrs.options"
        :is="'el-radio'"
        :key="item.value"
        :value="item.value"
        >{{ item.label }}</component
      >
    </template>
  </component>
</template>
