import { useRouter } from 'vue-router';
import { useColumnConfig, type AnyObject } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
import { ElMessageBox, ElMessage } from 'element-sun';
import { updateFormById } from '../../../api/formDesign';

export function useTableColumnsConfig(
  openFormmDialog: (data: AnyObject) => void,
  isCloudEnv: boolean | undefined,
) {
  const router = useRouter();
  const toggleControl = (row: { formId: string }) => {
    router.push(`/control/${row.formId}`);
  };
  const toggleDesign = (row: { formId: string }) => {
    router.push(`/layout/${row.formId}`);
  };

  return useColumnConfig({
    getData: (t) => [
      {
        prop: 'indexNo',
        editable: false,
        type: 'selection',
      },
      {
        label: t('formDesign.formId', '表单标识'),
        prop: 'formId',
        supportCopyAndTips: true,
        width: 200,
      },
      {
        label: t('formDesign.formName', '表单名称'),
        supportCopyAndTips: true,
        prop: 'formName',
        width: 220,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        width: 140,
        fixed: 'right',
        render: (row: FormDesign.FormInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled={!isCloudEnv}
              before-change={() => {
                return new Promise<void>((resolve, reject) => {
                  ElMessageBox.confirm(
                    t(
                      'switch.ask.title',
                      '您确定要 {{action}} “{{name}}” 吗？',
                      {
                        action:
                          row.enabledFlag === ENABLED_FLAG.YES
                            ? t('global:disabled')
                            : t('global:enabled'),
                        name: row.formName,
                      },
                    ),
                    t('global:tip'),
                    {
                      confirmButtonText: t('global:confirm'),
                      cancelButtonText: t('global:cancel'),
                      type: 'warning',
                    },
                  )
                    .then(async () => {
                      const [, result] = await updateFormById({
                        formId: row.formId,
                        formName: row.formName,
                        formDesc: row.formDesc,
                        enabledFlag:
                          row.enabledFlag === ENABLED_FLAG.YES
                            ? ENABLED_FLAG.NO
                            : ENABLED_FLAG.YES,
                      });
                      if (result?.success) {
                        resolve();
                        ElMessage({
                          type: 'success',
                          message: t(
                            row.enabledFlag === ENABLED_FLAG.YES
                              ? 'global:disabled.success'
                              : 'global:enabled.success',
                          ),
                        });
                        row.enabledFlag =
                          row.enabledFlag === ENABLED_FLAG.YES
                            ? ENABLED_FLAG.NO
                            : ENABLED_FLAG.YES;
                      } else {
                        reject();
                      }
                    })
                    .catch(() => {
                      reject();
                    });
                });
              }}
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('formDesign.formDesc', '表单描述'),
        prop: 'formDesc',
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 210,
        fixed: 'right',
        render: (row: { formId: string; formName: string }) => {
          return (
            <>
              <el-button
                disabled={!isCloudEnv}
                onClick={() =>
                  openFormmDialog({
                    row: { ...row },
                    title: t('formDesign.editTitle', '编辑 {{name}}', {
                      name: row.formName,
                    }),
                  })
                }
                link={true}
                type="primary"
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleControl(row);
                }}
                disabled={!isCloudEnv}
                type="primary"
                link
              >
                {t('formDesign.control', '表单控件')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleDesign(row);
                }}
                type="primary"
                link
              >
                {t('formDesign.layout', '表单设计')}
              </el-button>
            </>
          );
        },
      },
    ],
  });
}
