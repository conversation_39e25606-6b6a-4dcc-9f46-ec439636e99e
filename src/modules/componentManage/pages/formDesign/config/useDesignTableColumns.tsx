import {
  useColumnConfig,
  useAppConfigData,
  MAIN_APP_CONFIG,
  CopyTextWithTooltip,
} from 'sun-biz';
import { Ref } from 'vue';
import {
  CONTROL_TYPE_CODE_NAME,
  DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
  FLAG,
} from '@/utils/constant';
import { getComponentType, getPlaceholder, getExtraProps } from '../utils';
import DynamicComponent from '../components/DynamicComponent.vue';

export function useDesignTableColumns(
  detail: Ref<
    FormDesign.SchemeInfoDetail | undefined,
    FormDesign.SchemeInfoDetail | undefined
  >,
) {
  const dataSetCodes = [
    CONTROL_TYPE_CODE_NAME,
    DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
  ];
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  return useColumnConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 120,
        render: (row: { sort: number }) => <>{row.sort || '--'}</>,
        fixed: 'left',
      },
      {
        label: t('formControl.formCtlNo', '控件编码'),
        prop: 'formCtlNo',
        minWidth: 150,
        fixed: 'left',
        render: (row: { formCtlNo: string; editable: boolean }) => {
          return (
            <CopyTextWithTooltip align={'text-center'} text={row.formCtlNo} />
          );
        },
      },
      {
        label: t('formControl.formCtlName', '控件名称'),
        prop: 'formCtlName',
        minWidth: 150,
        fixed: 'left',
        render: (row: { formCtlName: string; editable: boolean }) => {
          return <>{row.formCtlName}</>;
        },
      },
      {
        label: t('formControl.controlTypeCode', '控件类型'),
        prop: 'controlTypeCode',
        minWidth: 150,
        placeholder: t('global:placeholder.select.template', {
          name: t('formControl.controlTypeCode', '控件类型'),
        }),
        fixed: 'left',
        render: (row: { controlTypeName: string }) => {
          return <>{row.controlTypeName}</>;
        },
      },
      {
        label: t('formControl.dataSearchBizIdTypeCode', '数据检索类型'),
        prop: 'dataSearchBizIdTypeCode',
        minWidth: 150,
        placeholder: t('global:placeholder.select.template', {
          name: t('formControl.dataSearchBizIdTypeCode', '数据检索类型'),
        }),
        render: (row: { dataSearchBizIdTypeDesc: string }) => {
          return <>{row.dataSearchBizIdTypeDesc}</>;
        },
      },
      {
        label: t('formControl.codeSystemNo', '编码体系'),
        prop: 'codeSystemNo',
        minWidth: 150,
        placeholder: t('global:placeholder.select', {
          name: t('formControl.codeSystemNo', '编码体系'),
        }),
        render: (row: { codeSystemName: string }) => {
          return <>{row.codeSystemName}</>;
        },
      },
      {
        label: t('formControl.labelName', '标签名称'),
        prop: 'labelName',
        minWidth: 170,
        render: (
          row: { labelName: string; editable: boolean },
          index: number,
        ) => {
          return row.editable ? (
            <>
              <el-form-item
                style={{ marginBottom: '0' }}
                class="overflow-hidden"
                prop={`tableData.${index}.labelName`}
                rules={[
                  {
                    trigger: 'change',
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.labelName) {
                        callback();
                      } else {
                        callback(
                          t('global:placeholder.input.template', {
                            content: t('formControl.labelName', '标签名称'),
                          }),
                        );
                      }
                    },
                  },
                ]}
              >
                <el-input
                  maxLength={16}
                  v-model={row.labelName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('formControl.labelName', '标签名称'),
                  })}
                ></el-input>
              </el-form-item>
            </>
          ) : (
            <>{row.labelName}</>
          );
        },
      },
      {
        label: t('formControl.label2ndName', '标签辅助名称'),
        prop: 'label2ndName',
        minWidth: 170,
        editable: true,
        render: (row: { label2ndName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={16}
                v-model={row.label2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.label2ndName', '标签辅助名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.label2ndName}</>
          );
        },
      },
      {
        label: t('formControl.labelExtName', '标签扩展名称'),
        prop: 'labelExtName',
        minWidth: 170,
        editable: true,
        render: (row: { labelExtName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={16}
                v-model={row.labelExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.labelExtName', '标签扩展名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.labelExtName}</>
          );
        },
      },
      {
        label: t('formControl.hintMsg', '提示信息'),
        prop: 'hintMsg',
        minWidth: 170,
        editable: true,
        render: (row: { hintMsg: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.hintMsg}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.hintMsg', '提示信息'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.hintMsg}</>
          );
        },
      },
      {
        label: t('formControl.displayWidth', '宽度比例'),
        prop: 'displayWidth',
        minWidth: 130,
        editable: true,
        render: (row: { displayWidth: string; editable: boolean }) => {
          return (
            <el-input-number
              v-model={row.displayWidth}
              controls={false}
              min={1}
              max={Number(detail?.value?.layoutTypeCode || 4)}
              placeholder={t('global:placeholder.input.template', {
                content: '宽度比例',
              })}
            >
              {{
                suffix: () => [<span>/ {detail?.value?.layoutTypeCode}</span>],
              }}
            </el-input-number>
          );
        },
      },
      {
        label: t('formControl.focusAutoDropDownFlag', '聚焦自动下拉标志'),
        prop: 'focusAutoDropDownFlag',
        minWidth: 170,
        editable: true,
        render: (row: { focusAutoDropDownFlag: number; editable: boolean }) => {
          return (
            <el-checkbox
              class="w-full justify-center"
              true-label={FLAG.YES}
              false-label={FLAG.NO}
              v-model={row.focusAutoDropDownFlag}
              size="large"
            />
          );
        },
      },
      {
        label: t('formControl.cursorStayFlag', '光标停留标志'),
        prop: 'cursorStayFlag',
        minWidth: 130,
        editable: true,
        render: (row: { cursorStayFlag: number; editable: boolean }) => {
          return (
            <el-checkbox
              class="w-full justify-center"
              true-label={FLAG.YES}
              false-label={FLAG.NO}
              v-model={row.cursorStayFlag}
              size="large"
            />
          );
        },
      },
      {
        label: t('formControl.displayFlag', '显示标志'),
        prop: 'displayFlag',
        minWidth: 130,
        editable: true,
        render: (row: {
          displayFlag: number;
          allowModifyFlag: number;
          editable: boolean;
        }) => {
          return row.editable ? (
            <el-checkbox
              class="w-full justify-center"
              true-label={FLAG.YES}
              disabled={row.allowModifyFlag === FLAG.NO && !isCloudEnv}
              false-label={FLAG.NO}
              v-model={row.displayFlag}
              size="large"
            />
          ) : (
            <>
              {row.displayFlag === FLAG.YES ? t('global:yes') : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('formControl.mustInputFlag', '必填标志'),
        prop: 'mustInputFlag',
        minWidth: 130,
        editable: true,
        render: (row: {
          mustInputFlag: number;
          allowModifyFlag: number;
          editable: boolean;
        }) => {
          return row.editable ? (
            <>
              <el-checkbox
                disabled={row.allowModifyFlag === FLAG.NO && !isCloudEnv}
                class="w-full justify-center"
                true-label={FLAG.YES}
                false-label={FLAG.NO}
                v-model={row.mustInputFlag}
                size="large"
              />
            </>
          ) : (
            <>
              {row.mustInputFlag === FLAG.YES
                ? t('global:yes')
                : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('formControl.defaultValue', '默认值'),
        prop: 'defaultValue',
        width: 180,
        render: (row: FormDesign.FormControlInfo) => {
          return (
            <DynamicComponent
              {...{
                component: getComponentType(row),
                label: row.labelName,
                onChange: (value: string, name: string) => {
                  row.defaultValueName = name;
                },
                placeholder: getPlaceholder(row, t),
                ...getExtraProps(row),
              }}
              v-model={row['defaultValue']}
            />
          );
        },
      },
    ],
  });
}
