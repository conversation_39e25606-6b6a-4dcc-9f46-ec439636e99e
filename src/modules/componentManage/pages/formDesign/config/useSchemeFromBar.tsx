import { useFormConfig } from 'sun-biz';
import { LAYOUT_TYPE_CODE_NAME, ENABLED_FLAG } from '@/utils/constant';
import { debounce } from '@sun-toolkit/shared';
import { queryMenuListByExample } from '@/modules/system/api/menu.ts';
import { FLAG } from '@/utils/constant';
import { ref, Ref, watch } from 'vue';
export function useSchemeFromBar(
  detail: Ref<
    FormDesign.FormDesignInfo | undefined,
    FormDesign.FormDesignInfo | undefined
  >,
) {
  const menuList = ref<{ value: string; label: string }[]>([]);
  const debounceFetchMenus = debounce(fetchMenuListByExample, 600); //动态查询系统

  watch(
    () => detail.value?.formDesignXMenuList,
    () => {
      menuList.value = (detail.value?.formDesignXMenuList || []).map(
        (item) => ({
          value: item.menuId,
          label: item.menuName,
        }),
      );
    },
  );

  /**
   * 查询菜单列表
   */
  async function fetchMenuListByExample(keyWord: string) {
    const [, result] = await queryMenuListByExample({
      enabledFlag: FLAG.YES,
      pageNumber: 1,
      pageSize: 50,
      keyWord,
    });
    if (result?.success) {
      menuList.value = result.data.map((item) => ({
        value: item.menuId,
        label: item.menuName,
      }));
    }
  }

  //弹窗form
  return useFormConfig({
    dataSetCodes: [LAYOUT_TYPE_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'formDesignName',
        label: t('SchemeFromBar.formDesignName', '方案名称'),
        defaultValue: '',
        triggerModelChange: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('SchemeFromBar.formDesignName', '方案名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('SchemeFromBar.formDesignName', '方案名称'),
            }),
            trigger: 'change',
          },
        ],
      },

      {
        name: 'formDesign2ndName',
        label: t('SchemeFromBar.formDesign2ndName', '方案辅助名称'),
        component: 'input',
        defaultValue: '',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('SchemeFromBar.formDesign2ndName', '方案辅助名称'),
        }),
      },
      {
        name: 'formDesignExtName',
        label: t('SchemeFromBar.formDesignExtName', '方案扩展名称'),
        component: 'input',
        defaultValue: '',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('SchemeFromBar.formDesignExtName', '方案扩展名称'),
        }),
      },
      {
        label: t('SchemeFromBar.layoutTypeCode', '布局类型'),
        name: 'layoutTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('SchemeFromBar.layoutTypeCode', '布局类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('SchemeFromBar.layoutTypeCode', '布局类型'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[LAYOUT_TYPE_CODE_NAME] : [],
        },
      },
      {
        name: 'menuIds',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('schemeFromBar.use.pageElementName', '应用菜单'),
        }),
        label: t('schemeFromBar.use.pageElementName', '应用菜单'),
        extraProps: {
          options: menuList.value,
          remote: true,
          multiple: true,
          collapseTags: true,
          filterable: true,
          'max-collapse-tags': 2,
          remoteMethod: (keyWord: string) => {
            debounceFetchMenus(keyWord);
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        triggerModelChange: true,
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
        component: 'switch',
      },
      {
        name: 'defaultFlag',
        label: t('SchemeFromBar.defaultFlag', '默认标志'),
        triggerModelChange: true,
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
        component: 'switch',
      },
    ],
  });
}
