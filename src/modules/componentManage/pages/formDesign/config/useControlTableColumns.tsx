import { ref, Ref } from 'vue';
import { queryCodeSystemListByExample } from '@/modules/baseConfig/api/code';
import {
  CONTROL_TYPE_CODE_NAME,
  DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
  DATA_SEARCH_BIZ_ID_TYPE_CODE,
  BIZ_ID_TYPE_CODE,
  CONTROL_TYPE_CODE,
  SWITCH_CODE,
  FLAG,
} from '@/utils/constant';
import { ElMessage, type FormInstance } from 'element-sun';
import { TFunction } from 'i18next';
import { addFormControl, updateFormControlById } from '../../../api/formDesign';
import { generateUUID } from '@sun-toolkit/shared';
import {
  DictSelect,
  useColumnConfig,
  type AnyObject,
  CopyTextWithTooltip,
} from 'sun-biz';
import { getComponentType, getPlaceholder, getExtraProps } from '../utils';
import DynamicComponent from '../components/DynamicComponent.vue';
interface FormControlInfo
  extends Partial<Omit<FormDesign.FormControlInfo, 'subFormControlList'>> {
  editable?: boolean;
  subFormControlList?: FormControlInfo[];
  oldParam?: FormControlInfo;
  identification?: string;
}

/**
 * 获取过滤后的tableData
 */
function getNewDataWithoutID(
  tableData: FormControlInfo[],
  identification: string,
) {
  tableData = tableData.filter((item) => {
    // 只在有子项的情况下过滤子项
    if (item.subFormControlList) {
      item.subFormControlList = getNewDataWithoutID(
        item.subFormControlList,
        identification,
      );
    }
    return item.identification !== identification;
  });
  return tableData;
}

/**
 * 二级及以上 新增同级别
 */
function inSertSamelevel(
  tableData: FormControlInfo[],
  row: FormControlInfo,
  DEFAULT_OBJ: object,
) {
  const findIndex = tableData.findIndex(
    (item) => item.formControlId === row.formControlId,
  );
  if (findIndex > -1) {
    tableData.splice(findIndex + 1, 0, {
      ...DEFAULT_OBJ,
      belongGroupElementId: row.belongGroupElementId,
      identification: String(generateUUID()),
    });
    return tableData;
  } else {
    tableData = tableData.map((item) => {
      if (item.subFormControlList) {
        item.subFormControlList = inSertSamelevel(
          item.subFormControlList,
          row,
          DEFAULT_OBJ,
        );
      }
      return {
        ...item,
      };
    });
  }
  return tableData;
}

/**
 *是否展示下拉框
 */
function showDefaultValueSelect(row: FormControlInfo) {
  return (
    row.controlTypeCode === CONTROL_TYPE_CODE.SELECT ||
    row.controlTypeCode === CONTROL_TYPE_CODE.RADIO ||
    row.controlTypeCode === CONTROL_TYPE_CODE.CHECKBOX
  );
}

/**
 *是否可以新增下级
 */

function disabledInsertSubordinate(row: FormControlInfo) {
  return (
    row.controlTypeCode !== CONTROL_TYPE_CODE.TABLE &&
    row.controlTypeCode !== CONTROL_TYPE_CODE.GROUP &&
    row.controlTypeCode !== CONTROL_TYPE_CODE.SET &&
    row.controlTypeCode !== CONTROL_TYPE_CODE.COLLAPSE
  );
}
/**
 *渲染默认值组件
 */
export function renderDefaultValue(
  row: FormControlInfo,
  t: TFunction<'translation', undefined>,
) {
  if (showDefaultValueSelect(row)) {
    return (
      <DictSelect
        {...row}
        label={row.labelNameDisplay || row.labelName || ''}
        v-model={row.defaultValue}
        onChange={(value: string, name: string) => {
          row.defaultValueName = name;
        }}
        disabled={
          row.controlTypeCode !== CONTROL_TYPE_CODE.SELECT ||
          (row.controlTypeCode === CONTROL_TYPE_CODE.SELECT &&
            row.dataSearchBizIdTypeCode ===
              DATA_SEARCH_BIZ_ID_TYPE_CODE.DICT_DATA_SETS &&
            !row.codeSystemNo) ||
          !row.dataSearchBizIdTypeCode
        }
      />
    );
  } else if (row.controlTypeCode === CONTROL_TYPE_CODE.SWITCH) {
    return (
      <el-switch
        v-model={row.defaultValue}
        inline-prompt
        class="w-full justify-center"
        active-value={SWITCH_CODE.YES}
        inactive-value={SWITCH_CODE.NO}
        active-text={t('global:enabled')}
        inactive-text={t('global:disabled')}
      />
    );
  }
  return (
    <el-input
      v-model={row.defaultValue}
      maxLength={row.maxLength}
      disabled={!row.controlTypeCode}
      placeholder={
        !row.controlTypeCode
          ? t('global:placeholder.select.template', {
              name: t('formControl.controlTypeCode', '控件类型'),
            })
          : t('global:placeholder.input.template', {
              content: t('formControl.DefaultValue', '默认值'),
            })
      }
    ></el-input>
  );
}

export function useTableColumnsConfig(
  addControl: (key?: string) => void,
  handleExpandChange: (row: AnyObject, expanded: boolean) => void,
  refreshTableSort: (arr: FormControlInfo[]) => FormControlInfo[],
  tableRef: Ref<
    { formRef: FormInstance } | undefined,
    { formRef: FormInstance } | undefined
  >,
  tableData: Ref<FormControlInfo[], FormControlInfo[]>,
  DEFAULT_OBJ: object,
  formId: string,
) {
  const codeSystemList = ref<Code.ResQueryCodeSystemList[]>([]);
  const querySystemList = async (keyWord?: string) => {
    const [, result] = await queryCodeSystemListByExample({
      pageNumber: 1,
      pageSize: 50,
      keyWord: keyWord,
    });
    if (result?.success) {
      codeSystemList.value = result?.data || [];
    }
  };

  const dataSetCodes = [
    CONTROL_TYPE_CODE_NAME,
    DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
  ];
  return useColumnConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        type: 'selection',
        minWidth: 40,
        selectable: (row) => row.formControlId,
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 120,
        render: (row: { sort: number }) => <>{row.sort || '--'}</>,
        fixed: 'left',
      },
      {
        label: t('formControl.formCtlNo', '控件编码'),
        prop: 'formCtlNo',
        minWidth: 170,
        fixed: 'left',
        required: true,
        render: (row: { formCtlNo: string; editable: boolean }, index) => {
          return row.editable ? (
            <>
              <el-form-item
                style={{ marginBottom: '0' }}
                class="overflow-hidden"
                prop={`tableData.${index}.formCtlNo`}
                rules={[
                  {
                    trigger: 'change',
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.formCtlNo) {
                        callback();
                      } else {
                        callback(
                          t('global:placeholder.input.template', {
                            content: t('formControl.formCtlNo', '控件编码'),
                          }),
                        );
                      }
                    },
                  },
                ]}
              >
                <el-input
                  maxLength={32}
                  v-model={row.formCtlNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('formControl.formCtlNo', '控件编码'),
                  })}
                ></el-input>
              </el-form-item>
            </>
          ) : (
            <CopyTextWithTooltip align={'text-center'} text={row.formCtlNo} />
          );
        },
      },
      {
        label: t('formControl.formCtlName', '控件名称'),
        prop: 'formCtlName',
        minWidth: 170,
        fixed: 'left',
        required: true,
        render: (row: { formCtlName: string; editable: boolean }, index) => {
          return row.editable ? (
            <>
              <el-form-item
                style={{ marginBottom: '0' }}
                class="overflow-hidden"
                prop={`tableData.${index}.formCtlName`}
                rules={[
                  {
                    trigger: 'change',
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.formCtlName) {
                        callback();
                      } else {
                        callback(
                          t('global:placeholder.input.template', {
                            content: t('formControl.formCtlName', '控件名称'),
                          }),
                        );
                      }
                    },
                  },
                ]}
              >
                <el-input
                  maxLength={64}
                  v-model={row.formCtlName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('formControl.formCtlName', '控件名称'),
                  })}
                ></el-input>
              </el-form-item>
            </>
          ) : (
            <>{row.formCtlName}</>
          );
        },
      },
      {
        label: t('formControl.controlTypeCode', '控件类型'),
        prop: 'controlTypeCode',
        minWidth: 150,
        placeholder: t('global:placeholder.select', {
          name: t('formControl.controlTypeCode', '控件类型'),
        }),
        required: true,
        render: (row, index) => {
          const controlTypeList = dataSet?.value?.[CONTROL_TYPE_CODE_NAME];
          return row.editable ? (
            <el-form-item
              style={{ marginBottom: '0' }}
              class="overflow-hidden"
              prop={`tableData.${index}.controlTypeCode`}
              rules={[
                {
                  trigger: 'change',
                  validator: async (
                    rule: string,
                    value: string,
                    callback: (arg0?: string | undefined) => void,
                  ) => {
                    if (row.controlTypeCode) {
                      callback();
                    } else {
                      callback(
                        t('global:placeholder.select.template', {
                          name: t('formControl.controlTypeCode', '控件类型'),
                        }),
                      );
                    }
                  },
                },
              ]}
            >
              <el-select
                v-model={row.controlTypeCode}
                filterable
                placeholder={t('global:placeholder.select.template', {
                  name: t('formControl.controlTypeCode', '控件类型'),
                })}
                onChange={(val: string) => {
                  row.controlTypeName =
                    controlTypeList?.find((item) => item.dataValueNo === val)
                      ?.dataValueNameDisplay || '';
                  row.codeSystemNo = undefined;
                  row.codeSystemName = undefined;
                  row.defaultValue = undefined;
                  row.defaultValueName = undefined;
                  row.dataSearchBizIdTypeCode = undefined;
                  row.dataSearchBizIdTypeDesc = undefined;
                }}
              >
                {controlTypeList?.map((item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ))}
              </el-select>
            </el-form-item>
          ) : (
            <>{row.controlTypeName}</>
          );
        },
      },
      {
        label: t('formControl.labelName', '标签名称'),
        prop: 'labelName',
        minWidth: 170,
        required: true,
        render: (row: { labelName: string; editable: boolean }, index) => {
          return row.editable ? (
            <>
              <el-form-item
                style={{ marginBottom: '0' }}
                class="overflow-hidden"
                prop={`tableData.${index}.labelName`}
                rules={[
                  {
                    trigger: 'change',
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.labelName) {
                        callback();
                      } else {
                        callback(
                          t('global:placeholder.input.template', {
                            content: t('formControl.labelName', '标签名称'),
                          }),
                        );
                      }
                    },
                  },
                ]}
              >
                <el-input
                  maxLength={16}
                  v-model={row.labelName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('formControl.labelName', '标签名称'),
                  })}
                ></el-input>
              </el-form-item>
            </>
          ) : (
            <>{row.labelName}</>
          );
        },
      },
      {
        label: t('formControl.label2ndName', '标签辅助名称'),
        prop: 'label2ndName',
        minWidth: 170,
        editable: true,
        render: (row: { label2ndName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={16}
                v-model={row.label2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.label2ndName', '标签辅助名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.label2ndName}</>
          );
        },
      },
      {
        label: t('formControl.labelExtName', '标签扩展名称'),
        prop: 'labelExtName',
        minWidth: 170,
        editable: true,
        render: (row: { labelExtName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={16}
                v-model={row.labelExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.labelExtName', '标签扩展名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.labelExtName}</>
          );
        },
      },
      {
        label: t('formControl.hintMsg', '提示信息'),
        prop: 'hintMsg',
        minWidth: 170,
        editable: true,
        render: (row: { hintMsg: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.hintMsg}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.hintMsg', '提示信息'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.hintMsg}</>
          );
        },
      },
      {
        label: t('formControl.maxLength', '最大长度'),
        prop: 'maxLength',
        minWidth: 150,
        required: true,
        render: (row: { maxLength: string; editable: boolean }, index) => {
          return row.editable ? (
            <>
              <el-form-item
                style={{ marginBottom: '0' }}
                class="overflow-hidden"
                prop={`tableData.${index}.maxLength`}
                rules={[
                  {
                    trigger: 'change',
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.maxLength) {
                        callback();
                      } else {
                        callback(
                          t('global:placeholder.input.template', {
                            content: t('formControl.maxLength', '最大长度'),
                          }),
                        );
                      }
                    },
                  },
                ]}
              >
                {' '}
                <el-input-number
                  v-model={row.maxLength}
                  controls={false}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('formControl.maxLength', '最大长度'),
                  })}
                ></el-input-number>
              </el-form-item>
            </>
          ) : (
            <>{row.maxLength}</>
          );
        },
      },
      {
        label: t('formControl.displayFlag', '显示标志'),
        prop: 'displayFlag',
        minWidth: 130,
        editable: true,
        render: (row: { displayFlag: number; editable: boolean }) => {
          return row.editable ? (
            <el-checkbox
              class="w-full justify-center"
              true-label={FLAG.YES}
              false-label={FLAG.NO}
              v-model={row.displayFlag}
              size="large"
            />
          ) : (
            <>
              {row.displayFlag === FLAG.YES ? t('global:yes') : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('formControl.allowModifyFlag', '允许自定义配置'),
        prop: 'allowModifyFlag',
        minWidth: 130,
        editable: true,
        render: (row: { allowModifyFlag: number; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-checkbox
                class="w-full justify-center"
                true-label={FLAG.YES}
                false-label={FLAG.NO}
                v-model={row.allowModifyFlag}
                size="large"
              />
            </>
          ) : (
            <>
              {row.allowModifyFlag === FLAG.YES
                ? t('global:yes')
                : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('formControl.mustInputFlag', '必填标志'),
        prop: 'mustInputFlag',
        minWidth: 130,
        editable: true,
        render: (row: { mustInputFlag: number; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-checkbox
                class="w-full justify-center"
                true-label={FLAG.YES}
                false-label={FLAG.NO}
                v-model={row.mustInputFlag}
                size="large"
              />
            </>
          ) : (
            <>
              {row.mustInputFlag === FLAG.YES
                ? t('global:yes')
                : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('formControl.dataSearchBizIdTypeCode', '数据检索类型'),
        prop: 'dataSearchBizIdTypeCode',
        minWidth: 170,
        editable: true,
        placeholder: t('global:placeholder.select', {
          name: t('formControl.dataSearchBizIdTypeCode', '数据检索类型'),
        }),
        render: (row) => {
          const bizIdTypeList =
            dataSet?.value?.[DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME];
          return row.editable ? (
            <el-select
              v-model={row.dataSearchBizIdTypeCode}
              filterable
              placeholder={
                row.controlTypeCode
                  ? showDefaultValueSelect(row)
                    ? t('global:placeholder.select.template', {
                        name: t(
                          'formControl.dataSearchBizIdTypeCode',
                          '数据检索类型',
                        ),
                      })
                    : t(
                        'formControl.dataSearchBizIdTypeCode.message',
                        '控件类型为“下拉框，单选框，复选框”时可维护',
                      )
                  : t('global:placeholder.select', {
                      name: t(
                        'formControl.dataSearchBizIdTypeCode',
                        '数据检索类型',
                      ),
                    })
              }
              disabled={!showDefaultValueSelect(row)}
              onChange={(val: string) => {
                row.codeSystemNo = undefined;
                row.codeSystemName = undefined;
                row.defaultValue = undefined;
                row.defaultValueName = undefined;
                row.dataSearchBizIdTypeDesc =
                  bizIdTypeList?.find((item) => item.dataValueNo === val)
                    ?.dataValueNameDisplay || '';
              }}
            >
              {bizIdTypeList?.map((item) => (
                <el-option
                  key={item.dataValueNo}
                  label={item.dataValueNameDisplay}
                  value={item.dataValueNo}
                />
              ))}
            </el-select>
          ) : (
            <>{row.dataSearchBizIdTypeDesc}</>
          );
        },
      },
      {
        label: t('formControl.codeSystemNo', '编码体系'),
        prop: 'codeSystemNo',
        minWidth: 170,
        editable: true,
        placeholder: t('global:placeholder.select', {
          name: t('formControl.codeSystemNo', '编码体系'),
        }),
        render: (row) => {
          return row.editable ? (
            <el-select
              v-model={row.codeSystemNo}
              filterable
              remote={true}
              disabled={
                row.dataSearchBizIdTypeCode !== BIZ_ID_TYPE_CODE.DICT_DATA_SETS
              }
              remote-method={querySystemList}
              placeholder={
                row.dataSearchBizIdTypeCode !== BIZ_ID_TYPE_CODE.DICT_DATA_SETS
                  ? t('global:placeholder.disabled')
                  : t('global:placeholder.select.template', {
                      name: t('formControl.codeSystemNo', '编码体系'),
                    })
              }
              onChange={(val: string) => {
                row.codeSystemName =
                  codeSystemList.value?.find(
                    (item) => item.codeSystemNo === val,
                  )?.codeSystemName || '';
                row.defaultValue = '';
                row.defaultValueName = '';
              }}
            >
              {codeSystemList.value?.map((item) => (
                <el-option
                  key={item.codeSystemNo}
                  label={item.codeSystemName}
                  value={item.codeSystemNo}
                />
              ))}
              {row.codeSystemName &&
                row.codeSystemNo &&
                !codeSystemList.value.find(
                  (item) => item.codeSystemNo === row.codeSystemNo,
                ) && (
                  <el-option
                    key={row.codeSystemNo}
                    label={row.codeSystemName}
                    value={row.codeSystemNo}
                  />
                )}
            </el-select>
          ) : (
            <>{row.codeSystemName}</>
          );
        },
      },
      {
        label: t('formControl.defaultValue', '默认值'),
        prop: 'defaultValue',
        minWidth: 200,
        editable: true,
        render: (row: FormDesign.FormControlInfo, index) => {
          return row.editable ? (
            <DynamicComponent
              {...{
                component: getComponentType(row),
                label: row.labelName,
                onChange: (value: string, name: string) => {
                  row.defaultValueName = name;
                },
                placeholder: getPlaceholder(row, t),
                ...getExtraProps(row),
              }}
              key={`defaultValue${row.dataSearchBizIdTypeCode}${row.codeSystemNo}${index}`}
              v-model={row['defaultValue']}
            />
          ) : (
            <>
              {row.controlTypeCode === CONTROL_TYPE_CODE.SWITCH
                ? row.defaultValue === SWITCH_CODE.YES
                  ? t('global:on', '开')
                  : t('global:off', '关')
                : row.defaultValueName || row.defaultValue}
            </>
          );
        },
      },
      {
        label: t('formControl.specialMark', '特殊标记'),
        prop: 'specialMark',
        minWidth: 170,
        editable: true,
        render: (row: { specialMark: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={64}
                v-model={row.specialMark}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.specialMark', '特殊标记'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.specialMark}</>
          );
        },
      },
      {
        label: t('formControl.bindingFieldNo', '绑定字段'),
        prop: 'bindingFieldNo',
        minWidth: 170,
        editable: true,
        render: (row: { bindingFieldNo: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                maxLength={32}
                v-model={row.bindingFieldNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('formControl.bindingFieldNo', '绑定字段'),
                })}
              ></el-input>
            </>
          ) : (
            <CopyTextWithTooltip
              align={'text-center'}
              text={row.bindingFieldNo}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 220,
        fixed: 'right',
        render: (row: FormControlInfo, index) => {
          return row.editable ? (
            <>
              <el-button
                onClick={() => {
                  Promise.all(
                    [
                      `tableData.${index}.formCtlNo`,
                      `tableData.${index}.formCtlName`,
                      `tableData.${index}.controlTypeCode`,
                      `tableData.${index}.labelName`,
                      `tableData.${index}.maxLength`,
                    ].map((item) =>
                      tableRef?.value?.formRef?.validateField(item),
                    ),
                  )
                    .then(async () => {
                      const [, result] = row.formControlId
                        ? await updateFormControlById({
                            formId,
                            ...row,
                          } as FormDesign.ReqAddFormControl)
                        : await addFormControl({
                            formId,
                            ...row,
                          } as FormDesign.ReqAddFormControl);
                      if (result?.success) {
                        Reflect.deleteProperty(row, 'oldParam');
                        Reflect.deleteProperty(row, 'identification');
                        if (!row.formControlId) {
                          row.formControlId = (
                            result?.data as { formControlId: string }
                          )?.formControlId;
                        }
                        row.editable = false;
                        ElMessage.success(
                          t(
                            row.formControlId
                              ? 'global:edit.success'
                              : 'global:add.success',
                          ),
                        );
                        // fetchData();
                      }
                    })
                    .catch((error) => {
                      Object.values(error).map((item) => {
                        ((item as []) || []).forEach((cur) => {
                          if ((cur as { message: string })?.message) {
                            ElMessage.error(
                              (cur as { message: string })?.message,
                            );
                          }
                        });
                      });
                    });
                }}
                type="primary"
                link
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                onClick={() => {
                  if (row.oldParam) {
                    row.editable = false;
                    Object.keys(row.oldParam).map((key: string) => {
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-expect-error
                      row[key] = (row?.oldParam || {})[key];
                    });
                  } else {
                    //删除这一项
                    if (row.identification) {
                      tableData.value = getNewDataWithoutID(
                        tableData.value,
                        row.identification,
                      );
                    }
                  }
                }}
                type="primary"
                link
              >
                {t('global:cancel')}
              </el-button>
            </>
          ) : (
            <>
              <el-button
                onClick={() => {
                  row.oldParam = { ...row };
                  row.editable = true;
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => {
                  if (row.belongGroupElementId && row.formControlId) {
                    tableData.value = inSertSamelevel(
                      tableData.value,
                      row,
                      DEFAULT_OBJ,
                    );
                    tableData.value = refreshTableSort(tableData.value);
                  } else {
                    addControl(row.formControlId);
                  }
                }}
                type="primary"
                link
              >
                {t('insert.same.level', '插入同级')}
              </el-button>
              <el-button
                disabled={disabledInsertSubordinate(row)}
                onClick={() => {
                  if (row.subFormControlList) {
                    row.subFormControlList.push({
                      ...DEFAULT_OBJ,
                      belongGroupElementId: row.formControlId,
                      identification: String(generateUUID()),
                    });
                  } else {
                    row.subFormControlList = [
                      {
                        ...DEFAULT_OBJ,
                        belongGroupElementId: row.formControlId,
                        identification: String(generateUUID()),
                      },
                    ];
                  }
                  tableData.value = refreshTableSort(tableData.value);
                  handleExpandChange(row, true);
                }}
                type="primary"
                link
              >
                {t('insert.subordinate', '插入下级')}
              </el-button>
            </>
          );
        },
      },
    ],
  });
}
