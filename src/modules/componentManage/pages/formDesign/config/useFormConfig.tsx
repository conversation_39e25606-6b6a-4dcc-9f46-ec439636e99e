import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
export function useCreateOrEditFormConfig() {
  return useFormConfig({
    getData: (t) => [
      {
        name: 'formName',
        label: t('fromDesign.formName', '表单名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('fromDesign.formName', '表单名称'),
        }),
        isFullWidth: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('fromDesign.formName', '表单名称'),
            }),
            trigger: 'change',
          },
        ],
      },

      {
        name: 'bindingApiUrl',
        label: t('fromDesign.bindingApiUrl', '绑定接口'),
        isFullWidth: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('fromDesign.bindingApiUrl', '绑定接口'),
        }),
      },
      {
        label: t('fromDesign.formDesc', '详细描述'),
        name: 'formDesc',
        component: 'input',
        type: 'textarea',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('fromDesign.formDesc', '表单描述'),
        }),
        extraProps: {
          resize: 'none',
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enableStatus'),
        isFullWidth: true,
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
        component: 'switch',
      },
    ],
  });
}
