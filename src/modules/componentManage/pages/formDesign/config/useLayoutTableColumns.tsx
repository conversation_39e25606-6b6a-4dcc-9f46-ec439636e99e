import { useColumnConfig } from 'sun-biz';
export function useTableColumnsConfig() {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: { sort: number }) => <>{row.sort}</>,
      },
      {
        label: t('global:code'),
        prop: 'paramNo',
        supportCopyAndTips: true,
        minWidth: 130,
      },
      {
        label: t('params.config.paramDesc', '参数描述'),
        prop: 'paramDesc',
        minWidth: 130,
      },
      {
        label: t('params.config.paramCategoryDesc', '参数分类'),
        prop: 'paramCategoryDesc',
        minWidth: 130,
      },
      {
        label: t('params.config.valueTypeDesc', '值类型'),
        prop: 'valueTypeDesc',
        minWidth: 130,
      },
      {
        label: t('params.config.detailDesc', '详细描述'),
        prop: 'detailDesc',
      },
      {
        label: t('params.config.paramInfluenceScopeDesc', '影响范围'),
        prop: 'paramInfluenceScopeDesc',
        minWidth: 130,
      },
      {
        label: t('params.config.paramValue', '配置值'),
        prop: 'paramValue',
        minWidth: 130,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 130,
        fixed: 'right',
        // render: (row: { paramNo: string }) => {
        //   return (
        //     <el-button
        //       onClick={(e: { preventDefault: () => void }) => {
        //         e.preventDefault();
        //       }}
        //       type="primary"
        //       link
        //     >
        //       {t('global:edit')}
        //     </el-button>
        //   );
        // },
      },
    ],
  });
}
