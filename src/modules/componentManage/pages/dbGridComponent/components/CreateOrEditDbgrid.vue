<script lang="ts" setup>
  import { ref, useAttrs, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useDbgridFormConfig } from '../config/useDbgridFormConfig';
  import { useTranslation } from 'i18next-vue';
  import { saveDbgridComponent } from '@/modules/componentManage/api/dbGridComponent';
  import { ProDialog, ProForm } from 'sun-biz';
  import { SelectOptions } from '@/typings/common.ts';

  const { t } = useTranslation();
  const menuListRef = ref<SelectOptions[]>();

  type Props = {
    row?: {
      componentId?: string;
      componentNo?: string;
      componentDesc?: string;
      enabledFlag?: number;
      dbgridComponentSettingList?: DbGridComponent.DbgridComponentSettingInfo[];
    };
    menuList?: SelectOptions[];
  };
  const props = defineProps<Props>();
  const formModel = ref();
  const dialogRef = ref();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const attrs = useAttrs();
  const activeName = ref('');
  const emits = defineEmits<{
    success: [];
  }>();

  const formData = useDbgridFormConfig(
    {
      componentNo: props.row?.componentNo,
      componentDesc: props.row?.componentDesc,
      enabledFlag: props.row?.enabledFlag,
      menuId: props.row?.menuId,
    },
    menuListRef,
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = await saveDbgridComponent({
            ...model,
            componentId: props.row?.componentId,
          } as DbGridComponent.ReqSaveDbgridComponent);
          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                `global:${props.row?.componentId ? 'edit' : 'create'}.success`,
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  function analysisJson(jsonString: string) {
    try {
      const obj = JSON.parse(jsonString);
      return JSON.stringify(obj, null, 4);
    } catch (e) {
      console.log(e);
      return jsonString; // 如果不是有效的 JSON，则返回原始字符串
    }
  }

  watch(
    () => props.row,
    () => {
      if (props.row?.dbgridComponentSettingList?.length) {
        activeName.value =
          props.row?.dbgridComponentSettingList[0]?.dbgridComptSettingId;
      }
      formModel.value = props.row;
      menuListRef.value = props.menuList;
    },
    {
      immediate: true,
    },
  );

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :button-text="attrs['button-text']"
    :confirm-fn="submit"
    :link="attrs.link"
    :title="attrs.title"
    :width="900"
    destroy-on-close
    type="primary"
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="2" :data="formData" />
    <el-tabs
      v-if="
        props.row?.componentId &&
        !!props.row?.dbgridComponentSettingList?.length
      "
      :model-value="activeName"
      class="demo-tabs"
    >
      <el-tab-pane
        v-for="item in props.row?.dbgridComponentSettingList"
        :key="item.dbgridComptSettingId"
        :label="item.bizName || item.influenceScopeDesc"
        :name="item.dbgridComptSettingId"
      >
        <el-scrollbar v-if="props.row?.componentId" class="w-full">
          <pre
            class="max-h-52 overflow-auto whitespace-pre-wrap rounded p-2.5"
            >{{ analysisJson(item.dbgridSettingValue) }}</pre
          >
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </ProDialog>
</template>
<style scoped>
  pre {
    word-wrap: break-word;
    background-color: #f4f4f4;
  }
</style>
