<script lang="tsx" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import { queryDbgridComponentListByExample } from '@/modules/componentManage/api/dbGridComponent';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant.ts';
  import { debounce, downloadFile } from '@sun-toolkit/shared';
  import CreateOrEditDbgrid from './components/CreateOrEditDbgrid.vue';
  import { useDbgridTableColumns } from './config/useDbgridTableColumns';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  import { type AnyObject, ProTable, Title } from 'sun-biz';
  import { queryMenuListByExample } from '@/modules/system/api/menu.ts';
  import { SelectOptions } from '@/typings/common.ts';

  const { t } = useTranslation();

  type DialogData = {
    [key: string]: unknown;
  };
  const menuList = ref<SelectOptions[]>();
  const dmlList = useGetDMLList();
  const createOrEditDbgridRef = ref();
  const tableRef = ref();
  const keyWord = ref<string>('');
  const menuId = ref<string>('');
  const selections = ref<
    (DbGridComponent.DbgridComponentInfo & { componentId: string })[]
  >([]);
  let dialogData = reactive<DialogData>({});

  function onClose() {
    tableRef.value.fetchList(false);
  }

  /**
   * 查询菜单列表
   */
  async function fetchMenuListByExample() {
    let [, result] = await queryMenuListByExample({
      enabledFlag: FLAG.YES,
      pageNumber: -1,
      pageSize: 200,
    });
    if (result?.success) {
      menuList.value = result.data.map((item) => ({
        value: item.menuId,
        label: item.menuName,
      }));
    }
  }

  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_DBGRID_COMPONENT,
      bisIds: selections.value.map((item) => item.componentId),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  async function fetchData(params: Code.PageInfo) {
    let [, result] = await queryDbgridComponentListByExample({
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
      keyWord: keyWord.value,
      menuId: menuId.value,
    });
    if (result?.success) {
      return {
        data: result.data,
        total: result.total,
      };
    }
  }

  function handleEnter() {
    tableRef?.value?.fetchList();
  }

  let inputChange = debounce(handleEnter, 500);

  function selectChange(value: AnyObject[]) {
    selections.value = value as (DbGridComponent.DbgridComponentInfo & {
      componentId: string;
    })[];
  }

  function openDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (dialogData as DialogData)[key] = data[key];
    });
    createOrEditDbgridRef.value.dialogRef.open();
  }

  const columns = useDbgridTableColumns(openDialog, menuList);

  onMounted(() => {
    fetchMenuListByExample();
  });
</script>
<template>
  <div class="p-box flex h-full flex-1 flex-col">
    <Title
      :title="$t('dbgrid.component.settingPage.title', '数据窗体组件')"
      class="mb-4"
    >
    </Title>
    <span class="mb-4 flex justify-between">
      <div>
        <el-input
          v-model="keyWord"
          :placeholder="
            t(
              'dbgrid.component.settingPage.placeholder.keyword',
              '请输入组件编码或组件描述查询',
            )
          "
          :suffix-icon="Search"
          class="mr-5 w-72"
          @input="inputChange"
          @keydown.enter="handleEnter"
        />
        <span>
          菜单：
          <el-select
            v-model="menuId"
            :clearable="true"
            :filterable="true"
            :multiple="false"
            :placeholder="
              t('global:placeholder.select.template', {
                name: t('dbgridColumns.menuId', '菜单'),
              })
            "
            class="mr-5 w-72"
            @change="inputChange"
          >
            <el-option
              v-for="item in menuList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span>
      </div>

      <span>
        <el-button
          class="mr-5"
          type="primary"
          @click="
            () => {
              openDialog({
                title: t('dbGridComponent.addTitle', '新增数据窗体组'),
                row: {},
                menuList,
              });
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
        <el-dropdown @click="clickDropdown">
          <el-button :disabled="!selections.length" type="primary">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template v-if="selections.length && !!dmlList.length" #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in dmlList"
                :key="item.value"
                @click="clickDropdown(item)"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </span>
    <pro-table
      ref="tableRef"
      :columns="columns"
      :fetch-data="fetchData"
      :highlight-current-row="true"
      :pagination="true"
      row-class-name="cursor-pointer"
      row-key="componentId"
      @selection-change="selectChange"
    />
  </div>
  <CreateOrEditDbgrid
    ref="createOrEditDbgridRef"
    v-bind="dialogData"
    @success="onClose"
  />
</template>
