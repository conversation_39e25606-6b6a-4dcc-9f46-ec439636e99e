import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common.ts';

export function useDbgridFormConfig(
  initData?: {
    componentNo?: string;
    componentDesc?: string;
    enabledFlag?: number;
    menuId?: string;
    exportFileFlag?: number;
  },
  menuListRef?: Ref<SelectOptions>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'componentNo',
        label: t('dbgridColumns.componentNo', '组件编码'),
        defaultValue: initData?.componentNo,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('dbgridColumns.componentNo', '组件编码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dbgridColumns.componentNo', '组件编码'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          style: {
            width: '100%',
          },
        },
        span: 2,
      },
      {
        name: 'componentDesc',
        label: t('dbgridColumns.componentDesc', '组件描述'),
        defaultValue: initData?.componentDesc,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('dbgridColumns.componentDesc', '组件描述'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dbgridColumns.componentDesc', '组件描述'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          style: {
            width: '100%',
          },
        },
        span: 2,
      },
      {
        name: 'menuId',
        label: t('dbgridColumns.menuId', '菜单'),
        defaultValue: initData?.menuId,
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('dbgridColumns.menuId', '菜单'),
        }),
        extraProps: {
          clearable: true,
          options: menuListRef.value,
          style: {
            width: '100%',
          },
        },
        span: 2,
      },
      {
        name: 'enabledFlag',
        label: t('global:enableStatus'),
        defaultValue: initData?.enabledFlag ?? ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
        component: 'switch',
      },
      {
        name: 'exportFileFlag',
        label: t('dbgridColumns.exportFileFlag', '导出文件标志'),
        defaultValue: initData?.exportFileFlag ?? ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          'inline-prompt': true,
        },
        component: 'switch',
      },
    ],
  });
  return data;
}
