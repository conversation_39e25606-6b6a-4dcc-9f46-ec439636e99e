import { type AnyObject, useColumnConfig } from 'sun-biz';
import { TFunction } from 'i18next';
import { ElMessage, ElMessageBox } from 'element-sun';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { saveDbgridComponent } from '@/modules/componentManage/api/dbGridComponent';
import { SelectOptions } from '@/typings/common.ts';

export function useDbgridTableColumns(
  openDialog: (data: AnyObject) => void,
  menuList: SelectOptions[],
) {
  // 启用状态切换
  const handleEnableSwitch = async (
    t: TFunction,
    row: DbGridComponent.DbgridComponentInfo,
    type: 'exportFileFlag' | 'enabledFlag',
    actionName: string,
  ) => {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t(
          'switch.ask.title',
          '您确定要将 “{{actionName}}” 修改为 “{{action}}” 吗？',
          {
            action:
              row[type] === ENABLED_FLAG.YES
                ? t('global:disabled')
                : t('global:enabled'),
            actionName,
          },
        ),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          const [, result] = await saveDbgridComponent({
            ...row,
            [type]:
              row[type] === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
          });
          if (result?.success) {
            resolve();
            row[type] =
              row[type] === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES;
            ElMessage({
              type: 'success',
              message: t(
                row[type] === ENABLED_FLAG.YES
                  ? 'global:enabled.success'
                  : 'global:disabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  };
  return useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 40,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('dbgridColumns.componentId', '组件标识'),
        prop: 'componentId',
        minWidth: 130,
        supportCopyAndTips: true,
      },
      {
        label: t('dbgridColumns.componentNo', '组件编码'),
        prop: 'componentNo',
        minWidth: 100,
        supportCopyAndTips: true,
      },
      {
        label: t('dbgridColumns.componentDesc', '组件描述'),
        prop: 'componentDesc',
        minWidth: 200,
      },
      {
        label: t('dbgridColumns.menuName', '菜单'),
        prop: 'menuName',
        minWidth: 120,
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: DbGridComponent.DbgridComponentInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(
                  t,
                  row,
                  'enabledFlag',
                  t('global:enableStatus'),
                )
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('dbgridColumns.exportFileFlag', '导出文件标志'),
        prop: 'exportFileFlag	',
        minWidth: 100,
        render: (row: DbGridComponent.DbgridComponentInfo) => {
          return (
            <el-switch
              modelValue={row.exportFileFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(
                  t,
                  row,
                  'exportFileFlag',
                  t('dbgridColumns.exportFileFlag', '导出文件标志'),
                )
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        fixed: 'right',
        render: (row: { componentDesc: '' }) => {
          return (
            <el-button
              onClick={() =>
                openDialog({
                  row: { ...row },
                  title: t('dbGridComponent.editTitle', '编辑 {{name}}', {
                    name: row.componentDesc,
                  }),
                  menuList: menuList.value,
                })
              }
              link={true}
              type="primary"
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
