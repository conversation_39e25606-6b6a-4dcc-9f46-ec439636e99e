import { ref } from 'vue';
import {
  querySearchTypeListByExample,
  querySearchComponentXMenuByExample,
} from '@/modules/componentManage/api/bizSearch';

export function useSearchType() {
  const loading = ref(false);
  const searchTypeList = ref<BizSearch.SearchTypeItem[]>([]);
  const getSearchTypeList = async (params: BizSearch.SearchTypeReqParams) => {
    loading.value = true;
    const [, res] = await querySearchTypeListByExample(params);
    loading.value = false;
    if (res?.success) {
      searchTypeList.value = res?.data ?? [];
    }
  };
  return {
    loading,
    searchTypeList,
    getSearchTypeList,
  };
}

// 获取菜单
export function useGetMenuList() {
  const loading = ref(false);
  const menuList = ref<BizSearch.SearchComponentXMenuItem[]>([]);
  const getMenuList = async (
    params: BizSearch.SearchComponentXMenuReqParams,
  ) => {
    loading.value = true;
    const [, res] = await querySearchComponentXMenuByExample(params);
    loading.value = false;
    if (res?.success) {
      menuList.value = res?.data;
    }
  };
  return {
    menuList,
    getMenuList,
  };
}
