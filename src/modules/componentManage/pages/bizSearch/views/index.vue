<script lang="ts" setup name="bizSearch">
  import { ref } from 'vue';
  import { Title } from 'sun-biz';
  import { SEARCH_NAME } from '../constant/searchName';
  import bizSearchTable from '@/modules/componentManage/pages/bizSearch/components/bizSearchTable.vue';

  const activeName = ref<SEARCH_NAME>(SEARCH_NAME.COMPONENT); //当前选中的tabName

  // tab切换
  const handleTabChange = (name: SEARCH_NAME) => {
    activeName.value = name;
  };
</script>
<template>
  <div class="flex h-full flex-col">
    <Title :title="$t('bizSearch.component.title', '读卡检索组件')" />
    <!-- tab切换 -->
    <el-tabs
      class="mt-2 h-full"
      @tab-change="handleTabChange"
      v-model="activeName"
    >
      <el-tab-pane
        :label="$t('bizSearch.component.title', '检索组件')"
        :name="SEARCH_NAME.COMPONENT"
        class="h-full"
      >
        <bizSearchTable
          v-if="activeName === SEARCH_NAME.COMPONENT"
          :active-name="activeName"
        />
      </el-tab-pane>
      <el-tab-pane
        class="h-full"
        :label="$t('bizSearch.type.title', '检索方式')"
        :name="SEARCH_NAME.TYPE"
      >
        <bizSearchTable
          v-if="activeName === SEARCH_NAME.TYPE"
          :active-name="activeName"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
