<script setup lang="ts" name="bizSearchComponentMenuOperation">
  import { FLAG } from '@/utils/constant';
  import { useTranslation } from 'i18next-vue';
  import { useGetMenuList } from '@/modules/componentManage/pages/bizSearch/hooks/useSearchType';
  import { useRoute, useRouter } from 'vue-router';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { useSearchTypeTableConfig } from '@/modules/componentManage/pages/bizSearch/config/useTableColumnsConfig';
  import { useMenuBaseInfoFormConfig } from '@/modules/componentManage/pages/bizSearch/config/useFormConfig';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { ref, computed, onMounted, watch } from 'vue';
  import {
    saveaMenuXSearchType,
    queryMenuXSearchTypeByExample,
    querySearchComponentListByExample,
  } from '@/modules/componentManage/api/bizSearch';

  type menuBaseInfoType = {
    hospitalId: string | undefined;
    maxShowItemNum: number | undefined;
    menuId: string | undefined;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const { menuList, getMenuList } = useGetMenuList();

  const loading = ref(false); // 加载状态
  const radioSelect = ref(); //选中的radio
  const componentRow = ref<BizSearch.SearchComponentItem>(); //查询到的组件信息
  const searchTypeInfo = ref<BizSearch.MenuXSearchTypeItem>(); //根据菜单查询到的检索组件方式列表
  const tableData = ref<Partial<BizSearch.MenuXSearchTypeItem>[]>([]); //表格数据
  const baseInfoModel = ref<menuBaseInfoType>({
    hospitalId: undefined,
    maxShowItemNum: 0,
    menuId: undefined,
  }); // 基本信息

  const formRef = ref<{
    ref: FormInstance;
  }>();

  const componentCode = computed(() => route.params.code); // 组件编码
  const componentTitle = computed(() => {
    return t('bizSearch.operation.menu.edit', '按菜单配置');
  });

  /** 查询检索组件 */
  const querySearchComponent = async () => {
    loading.value = true;
    const [, res] = await querySearchComponentListByExample({
      componentCode: componentCode.value as string,
    });
    loading.value = false;
    if (res?.success) {
      componentRow.value = res?.data[0];
      await getMenuList({
        hospitalId: baseInfoModel.value.hospitalId as string,
        componentId: componentRow.value?.componentId,
      });
    }
  };

  // modelChange变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleModelChange = async (data: any) => {
    if (
      data.hospitalId !== baseInfoModel.value.hospitalId &&
      baseInfoModel.value.hospitalId
    ) {
      await getMenuList({
        hospitalId: baseInfoModel.value.hospitalId as string,
        componentId: componentRow.value?.componentId,
      });
    }
    baseInfoModel.value = {
      ...baseInfoModel.value,
      ...data,
    };
  };

  //获取检索方式
  const getSearchTypeList = async () => {
    const [, res] = await queryMenuXSearchTypeByExample({
      componentCode: componentRow.value?.componentCode as string,
      menuId: baseInfoModel.value.menuId,
    });
    if (res?.success) {
      searchTypeInfo.value = res?.data;
      tableData.value = res.data?.bizSearchTypeList ?? [];
      baseInfoModel.value.maxShowItemNum = res.data?.maxShowItemNum ?? 0;
      const obj = tableData.value.find(
        (item) =>
          (
            item as unknown as {
              defaultFlag: FLAG;
            }
          ).defaultFlag,
      );
      if (obj) {
        radioSelect.value = (
          obj as {
            searchComptXSearchTypeId: string;
          }
        ).searchComptXSearchTypeId;
      } else {
        radioSelect.value = (
          tableData.value[0] as {
            searchComptXSearchTypeId: string;
          }
        )?.searchComptXSearchTypeId;
      }
    }
  };

  //radio 选择
  const handleRadioChange = async (val?: string) => {
    if (val) {
      radioSelect.value = val;
    }
    tableData.value = tableData.value.map((item) => {
      (
        item as unknown as {
          defaultFlag: FLAG;
        }
      ).defaultFlag = FLAG.NO;
      if (
        (
          item as unknown as {
            searchComptXSearchTypeId: string;
          }
        ).searchComptXSearchTypeId === radioSelect.value
      ) {
        (
          item as unknown as {
            defaultFlag: FLAG;
          }
        ).defaultFlag = FLAG.YES;
      }
      return item;
    });
  };

  // 保存检索组件
  const saveSearchComponent = async () => {
    await formRef.value?.ref.validate();
    const params = {
      searchComptXMenuId: searchTypeInfo.value?.searchComptXMenuId ?? undefined,
      menuId: baseInfoModel.value?.menuId ?? undefined,
      componentId: searchTypeInfo.value?.componentId ?? undefined,
      maxShowItemNum: baseInfoModel.value?.maxShowItemNum
        ? Number(baseInfoModel.value?.maxShowItemNum)
        : 0,
      hospitalId: baseInfoModel.value?.hospitalId,
      menuXSearchTypeList: tableData.value ?? [],
    };
    const [, res] = await saveaMenuXSearchType(
      params as unknown as BizSearch.SaveMenuXSearchTypeReqParams,
    );
    if (res?.success) {
      router.push('/');
    }
  };

  // 排序
  const handleSortEnd = async (
    data: Partial<BizSearch.MenuXSearchTypeItem>[],
  ) => {
    tableData.value = data.map((item, index) => {
      (
        item as Partial<
          BizSearch.MenuXSearchTypeItem & {
            sort: number;
          }
        >
      ).sort = index + 1;
      return item;
    });
    ElMessage({
      type: 'success',
      message: t('global:modify.sort.success'),
    });
  };

  const menuBaseInfoFormConfig = useMenuBaseInfoFormConfig({
    menuList: menuList,
    getMenuList: getMenuList,
  }); // 基本信息
  const { searchTypeConfig } = useSearchTypeTableConfig({
    isHiddenOperation: true,
    radioSelect: radioSelect,
    handleRadioChange: handleRadioChange,
  }); // 检索方式

  watch(
    () => baseInfoModel.value.menuId,
    async () => {
      await getSearchTypeList();
    },
  );

  onMounted(async () => {
    await querySearchComponent();
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <el-page-header @back="router.push('/')" class="pb-3">
      <template #content>
        <span class="text-base">
          {{ componentTitle }}
        </span>
      </template>
    </el-page-header>
    <Title :title="$t('baseInfo.title', '基本信息')" class="mb-2" />
    <ProForm
      @model-change="handleModelChange"
      v-model="baseInfoModel"
      :data="menuBaseInfoFormConfig"
      ref="formRef"
    />
    <div class="mb-2 flex justify-between">
      <Title :title="$t('bizSearchType.list.title', '检索方式列表')" />
    </div>
    <ProTable
      ref="tableRef"
      :editable="true"
      :draggable="true"
      :data="tableData"
      :loading="loading"
      @drag-end="handleSortEnd"
      row-key="searchComptXSearchTypeId"
      :columns="searchTypeConfig"
    />
    <div class="mt-3.5 text-right">
      <el-button @click="router.push('/')">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" @click="saveSearchComponent">{{
        $t('global:save')
      }}</el-button>
    </div>
  </div>
</template>
