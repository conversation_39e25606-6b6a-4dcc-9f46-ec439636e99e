<script setup lang="ts" name="bizSearchComponentOperation">
  import { FLAG } from '@/utils/constant';
  import { useSearchType } from '@/modules/componentManage/pages/bizSearch/hooks/useSearchType';
  import { useTranslation } from 'i18next-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useBaseInfoFormConfig } from '@/modules/componentManage/pages/bizSearch/config/useFormConfig';
  import { ref, computed, onMounted } from 'vue';
  import { useSearchTypeTableConfig } from '@/modules/componentManage/pages/bizSearch/config/useTableColumnsConfig';
  import { ElMessage, type FormInstance } from 'element-sun';
  import {
    Title,
    ProForm,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
    TableRef,
  } from 'sun-biz';
  import {
    addSearchComponent,
    updateSearchComponentById,
    querySearchComponentListByExample,
  } from '@/modules/componentManage/api/bizSearch';

  type baseInfoType = {
    componentCode: string | undefined;
    maxShowItemNum: number | undefined;
    componentDesc: string | undefined;
    enabledFlag: FLAG;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const { searchTypeList, getSearchTypeList } = useSearchType();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const loading = ref(false); // 加载状态
  const radioSelect = ref(); //选中的radio
  const componentRow = ref(); //查询到的组件信息
  const tableData = ref<Partial<BizSearch.SearchTypeItem>[]>([]); //表格数据
  const baseInfoModel = ref<baseInfoType>({
    componentCode: undefined,
    maxShowItemNum: undefined,
    componentDesc: undefined,
    enabledFlag: FLAG.YES,
  }); // 基本信息

  const formRef = ref<{
    ref: FormInstance;
  }>();
  const tableRef = ref<TableRef>();

  const componentCode = computed(() => route.params.code); // 组件编码
  const isAdd = computed(() => route.params.code === 'add'); // 是否新增
  const componentTitle = computed(() => {
    return route.params.code === 'add'
      ? t('bizSearch.operation.add', '新增检索组件')
      : t('bizSearch.operation.edit', '编辑检索组件');
  });

  /** 查询检索组件 */
  const querySearchComponent = async () => {
    loading.value = true;
    const [, res] = await querySearchComponentListByExample({
      componentCode: componentCode.value as string,
    });
    loading.value = false;
    if (res?.success) {
      componentRow.value = res?.data[0];
      baseInfoModel.value = {
        componentCode: res?.data[0]?.componentCode ?? undefined,
        maxShowItemNum: res?.data[0]?.maxShowItemNum ?? undefined,
        componentDesc: res?.data[0]?.componentDesc ?? undefined,
        enabledFlag: res?.data[0]?.enabledFlag ?? FLAG.YES,
      };
      tableData.value =
        (res?.data[0]
          ?.bizSearchTypeList as Partial<BizSearch.SearchTypeItem>[]) ?? [];
    }
  };

  // 检索组件新增检索方式
  const addSearchTypeFn = async () => {
    if ((tableData.value ?? []).length === 0) {
      addItem({
        editable: true,
        sort: tableData.value?.length + 1,
        enabledFlag: FLAG.YES,
        defaultFlag: FLAG.YES,
      });
    } else {
      addItem({
        editable: true,
        sort: tableData.value?.length + 1,
        enabledFlag: FLAG.YES,
        defaultFlag: FLAG.NO,
      });
    }
    await handleRadioChange();
  };

  //radio 选择
  const handleRadioChange = async (val?: string) => {
    if (val) {
      radioSelect.value = val;
    } else {
      if ((tableData.value ?? []).length === 0) return;
      const obj = tableData.value.find(
        (item: Partial<BizSearch.SearchTypeItem & { defaultFlag: FLAG }>) =>
          item.defaultFlag === FLAG.YES,
      );
      if (obj) {
        radioSelect.value =
          (obj as { searchComptXSearchTypeId: string })
            ?.searchComptXSearchTypeId ?? (obj as { key: string })?.key;
      } else {
        radioSelect.value =
          (
            tableData.value as unknown[] as {
              searchComptXSearchTypeId: string;
            }[]
          )[0]?.searchComptXSearchTypeId ??
          (tableData.value as unknown[] as { key: string }[])[0]?.key;
      }
    }
    tableData.value = tableData.value.map((item) => {
      (
        item as unknown as {
          defaultFlag: FLAG;
        }
      ).defaultFlag = FLAG.NO;
      if (
        ((
          item as unknown as {
            searchComptXSearchTypeId: string;
          }
        ).searchComptXSearchTypeId ??
          (
            item as unknown as {
              key: string;
            }
          ).key) === radioSelect.value
      ) {
        (
          item as unknown as {
            defaultFlag: FLAG;
          }
        ).defaultFlag = FLAG.YES;
      }
      return item;
    });
  };

  // 保存检索组件
  const saveSearchComponent = async () => {
    await formRef.value?.ref.validate();
    const params = {
      componentId: componentRow.value?.componentId ?? undefined,
      componentCode: baseInfoModel.value?.componentCode ?? undefined,
      maxShowItemNum: baseInfoModel.value?.maxShowItemNum ?? undefined,
      componentDesc: baseInfoModel.value?.componentDesc ?? undefined,
      enabledFlag: baseInfoModel.value?.enabledFlag ?? FLAG.YES,
      bizSearchTypeList: tableData.value ?? [],
    };
    const [, res] = await switchSearchComponent(
      params as unknown as
        | BizSearch.AddSearchComponentReqParams
        | BizSearch.UpdateSearchComponentReqParams,
    );
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      router.push('/');
    }
  };

  // 检索组件操作
  const switchSearchComponent = async (
    params:
      | BizSearch.AddSearchComponentReqParams
      | BizSearch.UpdateSearchComponentReqParams,
  ) => {
    if (isAdd.value) {
      return await addSearchComponent(
        params as BizSearch.AddSearchComponentReqParams,
      );
    } else {
      return await updateSearchComponentById(
        params as BizSearch.UpdateSearchComponentReqParams,
      );
    }
  };

  // 排序
  const handleSortEnd = async (data: Partial<BizSearch.SearchTypeItem>[]) => {
    tableData.value = data.map((item, index) => {
      (
        item as Partial<
          BizSearch.SearchTypeItem & {
            sort: number;
          }
        >
      ).sort = index + 1;
      return item;
    });
    ElMessage({
      type: 'success',
      message: t('global:modify.sort.success'),
    });
  };

  const baseInfoFormConfig = useBaseInfoFormConfig({
    isCloudEnv: isCloudEnv as boolean,
  }); // 基本信息
  const { searchTypeConfig, addItem } = useSearchTypeTableConfig({
    tableRef: tableRef,
    data: tableData,
    id: 'searchComptXSearchTypeId',
    radioSelect: radioSelect,
    handleRadioChange: handleRadioChange,
    searchTypeList: searchTypeList,
    getSearchTypeList: getSearchTypeList,
    isHiddenOperation: false,
  }); // 检索方式

  onMounted(async () => {
    if (!isAdd.value) {
      await querySearchComponent();
      await handleRadioChange();
    }
    await getSearchTypeList({
      enabledFlag: FLAG.YES,
    });
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <el-page-header @back="router.push('/')" class="pb-3">
      <template #content>
        <span class="text-base">
          {{ componentTitle }}
        </span>
      </template>
    </el-page-header>
    <Title :title="$t('baseInfo.title', '基本信息')" class="mb-2" />
    <ProForm v-model="baseInfoModel" :data="baseInfoFormConfig" ref="formRef" />
    <div class="mb-2 flex justify-between">
      <Title :title="$t('bizSearchType.list.title', '检索方式列表')" />
      <el-button
        type="primary"
        :disabled="!isCloudEnv"
        @click="addSearchTypeFn"
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      ref="tableRef"
      :editable="true"
      :draggable="true"
      :data="tableData"
      :loading="loading"
      @drag-end="handleSortEnd"
      row-key="searchComptXSearchTypeId"
      :columns="searchTypeConfig"
    />
    <div class="mt-3.5 text-right">
      <el-button @click="router.push('/')">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" @click="saveSearchComponent">{{
        $t('global:save')
      }}</el-button>
    </div>
  </div>
</template>
