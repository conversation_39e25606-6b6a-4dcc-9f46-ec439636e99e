<script setup lang="ts" name="bizSearchTable">
  import { useRouter } from 'vue-router';
  import { SEARCH_NAME } from '../constant/searchName.ts';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from '../config/useSearchConfigData';
  import { useTableColumnsConfig } from '../config/useTableColumnsConfig.tsx';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant.ts';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ref, watch, computed, onMounted } from 'vue';
  import {
    ProForm,
    ProTable,
    DmlButton,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    querySearchTypeListByExample,
    updateSearchTypeEnabledFlagById,
    querySearchComponentListByExample,
    updateSearchComponentEnabledFlagById,
  } from '@/modules/componentManage/api/bizSearch';
  // import {Title} from 'sun-biz';
  import dialogComponent from './dialogComponent.vue';

  const props = defineProps<{
    activeName: SEARCH_NAME;
  }>();

  const router = useRouter();
  const { t } = useTranslation();
  const { isCloudEnv, menuId } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.MENU_ID,
  ]);

  const loading = ref(false); //加载状态
  const rowValue = ref<BizSearch.SearchTypeItem>(); //操作行
  const selectTableData = ref<
    (BizSearch.SearchTypeItem | BizSearch.SearchComponentItem)[]
  >([]); //选中的table数据
  const tableData = ref<
    (BizSearch.SearchTypeItem | BizSearch.SearchComponentItem)[]
  >([]); //当前table数据
  // 检索条件
  const searchModel = ref<
    BizSearch.SearchTypeReqParams | BizSearch.SearchComponentReqParams
  >({
    keyWord: undefined,
  });

  const dialogRef = ref(); //弹窗ref
  const tableRef = ref();

  const activeName = computed(() => props.activeName); //当前选中tabs
  /** 当前table绑定的rowKey */
  const rowKey = computed(() => {
    if (props.activeName === SEARCH_NAME.COMPONENT) {
      return 'componentId';
    } else if (props.activeName === SEARCH_NAME.TYPE) {
      return 'searchTypeId';
    } else {
      return '';
    }
  });
  // DML业务编码
  const bizIdTypeCode = computed(() => {
    return props.activeName === SEARCH_NAME.COMPONENT
      ? BIZ_ID_TYPE_CODE.DICT_SEARCH_COMPONENT
      : BIZ_ID_TYPE_CODE.DICT_SEARCH_TYPE;
  });
  // DML业务来源标识集合
  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      if (props.activeName === SEARCH_NAME.COMPONENT) {
        return (item as BizSearch.SearchComponentItem).componentId;
      }
      if (props.activeName === SEARCH_NAME.TYPE) {
        return (item as BizSearch.SearchTypeItem).searchTypeId;
      }
    });
    return (list as string[]) ?? [];
  });
  // /** table标题 */
  // const title = computed(() => {
  //   switch (props.activeName) {
  //     case SEARCH_NAME.COMPONENT:
  //       return t('search.component.list', '检索组件列表');
  //     case SEARCH_NAME.TYPE:
  //       return t('search.type.list', '检索方式列表');
  //     default:
  //       return '';
  //   }
  // });
  /** 弹窗标题 */
  const dialogTitle = computed(() => {
    if (props.activeName === SEARCH_NAME.TYPE) {
      if ((rowValue.value as BizSearch.SearchTypeItem)?.searchTypeId) {
        return t('search.type.add', '新增检索方式');
      } else {
        return t('search.type.edit', '编辑检索方式');
      }
    } else {
      return '';
    }
  });

  /** 初始化 */
  const init = async () => {
    searchModel.value.keyWord = undefined;
    tableData.value = [];
    selectTableData.value = [];
  };

  /** 获取table数据 */
  const queryTableData = async (
    params:
      | BizSearch.SearchTypeReqParams
      | BizSearch.SearchComponentReqParams = {},
  ) => {
    searchModel.value = { ...searchModel.value, ...params };
    loading.value = true;
    const [, res] = await switchTableData(searchModel.value);
    loading.value = false;
    if (res?.success) {
      tableData.value = res.data ?? [];
    }
  };

  /** table数据接口切换 */
  const switchTableData = async (
    params: BizSearch.SearchTypeReqParams | BizSearch.SearchComponentReqParams,
  ) => {
    switch (props.activeName) {
      case SEARCH_NAME.COMPONENT:
        return await querySearchComponentListByExample(params);
      case SEARCH_NAME.TYPE:
        return await querySearchTypeListByExample(params);
    }
  };

  /** 新增公共方法 */
  const addFn = async () => {
    if (props.activeName === SEARCH_NAME.TYPE) {
      await operationFn({} as BizSearch.SearchTypeItem);
    } else {
      router.push('/bizSearch/operation/add');
    }
  };

  /** 启用禁用 */
  const handleEnableSwitch = async (
    row: BizSearch.SearchTypeItem | BizSearch.SearchComponentItem,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name:
          props.activeName === SEARCH_NAME.COMPONENT
            ? (row as BizSearch.SearchComponentItem).componentName
            : (row as BizSearch.SearchTypeItem).searchTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      let params:
        | BizSearch.UpdateSearchComponentEnabledFlagReqParams
        | BizSearch.UpdateSearchTypeEnabledFlagReqParams = {} as
        | BizSearch.UpdateSearchComponentEnabledFlagReqParams
        | BizSearch.UpdateSearchTypeEnabledFlagReqParams;
      if (props.activeName === SEARCH_NAME.COMPONENT) {
        params = {
          componentId: (row as BizSearch.SearchComponentItem).componentId,
          enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
        };
      } else if (props.activeName === SEARCH_NAME.TYPE) {
        params = {
          searchTypeId: (row as BizSearch.SearchTypeItem).searchTypeId,
          enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
        };
      }
      loading.value = true;
      const [, res] = await switchEnabledFlag(params);
      loading.value = false;
      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  /** 停启用接口调用 */
  const switchEnabledFlag = async (
    params:
      | BizSearch.UpdateSearchComponentEnabledFlagReqParams
      | BizSearch.UpdateSearchTypeEnabledFlagReqParams,
  ) => {
    switch (props.activeName) {
      case SEARCH_NAME.COMPONENT:
        return await updateSearchComponentEnabledFlagById(
          params as BizSearch.UpdateSearchComponentEnabledFlagReqParams,
        );
      case SEARCH_NAME.TYPE:
        return await updateSearchTypeEnabledFlagById(
          params as BizSearch.UpdateSearchTypeEnabledFlagReqParams,
        );
    }
  };
  /** 组件方式编辑操作 */
  const operationFn = async (row: BizSearch.SearchTypeItem) => {
    rowValue.value = row;
    dialogRef.value.open();
  };

  // 选中行设置
  const selectionChange = (
    val: BizSearch.SearchTypeItem[] | BizSearch.SearchComponentItem[],
  ) => {
    selectTableData.value = val;
  };

  const searchFormConfig = useSearchFormConfig({
    queryTableData: queryTableData,
    searchModel: searchModel,
  }); //搜索配置
  const tableColumns = useTableColumnsConfig({
    activeName: activeName,
    isCloudEnv: isCloudEnv,
    handleEnableSwitch: handleEnableSwitch,
    operationFn: operationFn,
    menuId: menuId as string,
  }); //table配置

  watch(
    () => props.activeName,
    async (val: string) => {
      console.log(val, 'val');
      await init();
    },
  );

  onMounted(async () => {
    await init();
    await queryTableData();
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <!-- <Title :title="title" class="mb-3" /> -->
    <div class="flex justify-between">
      <ProForm
        v-model="searchModel"
        layout-mode="inline"
        :data="searchFormConfig"
        :show-search-button="true"
        @model-change="queryTableData"
      />
      <div>
        <DmlButton
          :code="bizIdTypeCode"
          :biz-data="bizData"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button
          class="ml-2.5"
          type="primary"
          @click="addFn"
          :disabled="!isCloudEnv"
          >{{ $t('global:add') }}</el-button
        >
      </div>
    </div>
    <ProTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :row-key="rowKey"
      @selection-change="selectionChange"
    />
    <dialogComponent
      ref="dialogRef"
      :row-value="rowValue"
      :dialog-title="dialogTitle"
      @success="queryTableData"
    />
  </div>
</template>
