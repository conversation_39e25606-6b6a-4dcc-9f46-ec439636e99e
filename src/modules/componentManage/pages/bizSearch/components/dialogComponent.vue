<script setup lang="ts" name="dialogComponent">
  import { FLAG } from '@/utils/constant';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useGetInterface } from '@/hooks/useGetInterface';
  import { type FormInstance } from 'element-sun';
  import { ref, nextTick, computed } from 'vue';
  import { useBizSearchTypeFormConfig } from '../config/useFormConfig';
  import {
    addSearchType,
    updateSearchTypeById,
  } from '@/modules/componentManage/api/bizSearch';
  import {
    ProForm,
    ProDialog,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';

  type FormModelType = {
    searchTypeCode: string | undefined;
    searchTypeName: string | undefined;
    searchType2ndName: string | undefined;
    searchTypeExtName: string | undefined;
    indexTypeCode: string | undefined;
    triggerTypeCode: string | undefined;
    interfaceId: string | undefined;
    enabledFlag: FLAG;
    inputHintContent: string | undefined;
  };

  const emit = defineEmits(['success']);
  const props = defineProps<{
    rowValue: BizSearch.SearchTypeItem | undefined;
    dialogTitle: string;
  }>();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const { interfaceList, getInterfaceList } = useGetInterface();

  const isAdd = computed(() => !props.rowValue?.searchTypeId);
  const orgId = computed(() => currentOrg?.orgId);

  const dialogRef = ref(); //弹窗dialogRef
  const formModelRef = ref<{
    ref: FormInstance;
  }>(); //formRef
  const formModel = ref<FormModelType>({
    searchTypeCode: undefined,
    searchTypeName: undefined,
    searchType2ndName: undefined,
    searchTypeExtName: undefined,
    indexTypeCode: undefined,
    triggerTypeCode: undefined,
    interfaceId: undefined,
    enabledFlag: FLAG.YES,
    inputHintContent: undefined,
  });

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value.open();
      formModel.value = {
        searchTypeCode: rowValue?.searchTypeCode ?? undefined,
        searchTypeName: rowValue?.searchTypeName ?? undefined,
        searchType2ndName: rowValue?.searchType2ndName ?? undefined,
        searchTypeExtName: rowValue?.searchTypeExtName ?? undefined,
        indexTypeCode: rowValue?.indexTypeCode ?? undefined,
        triggerTypeCode: rowValue?.triggerTypeCode ?? undefined,
        interfaceId: rowValue?.interfaceId ?? undefined,
        enabledFlag: rowValue?.enabledFlag ?? FLAG.YES,
        inputHintContent: rowValue?.inputHintContent ?? undefined,
      };
    });
    await getInterfaceList({
      hospitalId: orgId.value,
    });
  };

  // 提交
  const handleConfirmSubmit = async () => {
    await formModelRef.value?.ref.validate();
    const params = {
      ...props.rowValue,
      ...formModel?.value,
    };
    return props.rowValue?.searchTypeId
      ? await updateSearchTypeById(
          params as BizSearch.UpdateSearchTypeReqParams,
        )
      : await addSearchType(params as BizSearch.AddSearchTypeReqParams);
  };

  // form配置
  const searchTypeConfig = useBizSearchTypeFormConfig({
    isAdd: isAdd,
    interfaceList,
    getInterfaceList,
    orgId,
  });

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    class="w-1/2"
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <ProForm
      ref="formModelRef"
      layout-mode="column"
      :column="2"
      v-model="formModel"
      :data="searchTypeConfig"
    />
  </ProDialog>
</template>
