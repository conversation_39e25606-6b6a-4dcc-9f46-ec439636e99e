// import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { useTranslation } from 'i18next-vue';
import { Ref, ComputedRef } from 'vue';
import { InterfaceReqItem, InterfaceReqParams } from '@/api/types';
import {
  ENABLED_FLAG,
  COMPONENT_CODE_NAME,
  INDEX_TYPE_CODE_NAME,
  SEARCH_TYPE_CODE_NAME,
  TRIGGER_TYPE_CODE_NAME,
} from '@/utils/constant';

// 读卡检索组件的详情
export function useBaseInfoFormConfig(options: { isCloudEnv: boolean }) {
  const { isCloudEnv } = options;
  const { t } = useTranslation();
  //数量校验
  const validRules = (
    rule: unknown,
    value: string,
    callback: (error?: Error | undefined) => void,
  ) => {
    if (!value) {
      return callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t(
              'bizSearchComponent.maxShowItemNum.title',
              '最大显示数量',
            ),
          }),
        ),
      );
    }
    if (!(Number.isInteger(Number(value)) && (Number(value) as number) >= 0)) {
      return callback(
        new Error(t('maxShowItemNum.valid', '最大显示数量需为整数')),
      );
    }
    callback();
  };

  return useFormConfig({
    dataSetCodes: [COMPONENT_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'componentCode',
        label: t('bizSearchComponent.componentName.title', '组件名称'),
        labelWidth: 90,
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSearchComponent.componentName.title', '组件名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizSearchComponent.componentName.title', '组件名称'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: !isCloudEnv,
          filterable: true,
          options: dataSet?.value ? dataSet.value[COMPONENT_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'maxShowItemNum',
        labelWidth: 120,
        label: t('bizSearchComponent.maxShowItemNum.title', '最大显示数量'),
        component: 'input',
        type: 'number',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizSearchComponent.maxShowItemNum.title', '最大显示数量'),
        }),
        extraProps: {
          clearable: false,
          disabled: !isCloudEnv,
        },
        rules: [
          {
            required: true,
            validator: validRules,
            trigger: 'change',
          },
        ],
      },
      {
        name: 'componentDesc',
        label: t('bizSearchComponent.componentDesc.title', '组件描述'),
        labelWidth: 90,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizSearchComponent.componentDesc.title', '组件描述'),
        }),
        extraProps: {
          disabled: !isCloudEnv,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bizSearchComponent.componentDesc.title', '组件描述'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        labelWidth: 90,
        component: 'switch',
        extraProps: {
          disabled: !isCloudEnv,
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}

// 读卡检索组件按菜单配置详情form
export function useMenuBaseInfoFormConfig(options: {
  menuList: Ref<BizSearch.SearchComponentXMenuItem[]>;
  getMenuList: (
    params: BizSearch.SearchComponentXMenuReqParams,
  ) => Promise<void>;
}) {
  const { menuList } = options;
  const { t } = useTranslation();
  //数量校验
  const validRules = (
    rule: unknown,
    value: string,
    callback: (error?: Error | undefined) => void,
  ) => {
    if (!value) {
      return callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t(
              'bizSearchComponent.maxShowItemNum.title',
              '最大显示数量',
            ),
          }),
        ),
      );
    }
    if (!(Number.isInteger(Number(value)) && (Number(value) as number) >= 0)) {
      return callback(
        new Error(t('maxShowItemNum.valid', '最大显示数量需为整数')),
      );
    }
    callback();
  };

  return useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('person.belongHospital', '所属医院'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('person.belongHospital', '所属医院'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          clearable: false,
          // onChange: (val: string) => {
          //   hospitalId.value = val;
          // },
        },
      },
      {
        label: t('global:menu'),
        name: 'menuId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:menu'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:menu'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          filterable: true,
          clearable: true,
          options: menuList.value,
          props: {
            label: 'menuNameDisplay',
            value: 'menuId',
          },
        },
      },
      {
        name: 'maxShowItemNum',
        labelWidth: 120,
        label: t('bizSearchComponent.maxShowItemNum.title', '最大显示数量'),
        component: 'input',
        type: 'number',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizSearchComponent.maxShowItemNum.title', '最大显示数量'),
        }),
        extraProps: {
          clearable: false,
        },
        rules: [
          {
            required: true,
            validator: validRules,
            trigger: 'change',
          },
        ],
      },
    ],
  });
}

// 检索方式弹窗config配置
export function useBizSearchTypeFormConfig(options: {
  isAdd: ComputedRef<boolean>;
  orgId: ComputedRef<string | undefined>;
  interfaceList: Ref<InterfaceReqItem[]>;
  getInterfaceList: (params: InterfaceReqParams) => Promise<void>;
}) {
  const { isAdd, orgId, interfaceList, getInterfaceList } = options;
  return useFormConfig({
    dataSetCodes: [
      SEARCH_TYPE_CODE_NAME,
      TRIGGER_TYPE_CODE_NAME,
      INDEX_TYPE_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        name: 'searchTypeCode',
        label: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: !isAdd.value,
          filterable: true,
          options: dataSet?.value ? dataSet.value[SEARCH_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'searchTypeName',
        label: t('bizSearchType.searchTypeName.title', '检索方式名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizSearchType.searchTypeName.title', '检索方式名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bizSearchType.searchTypeName.title', '检索方式名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'searchType2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'searchTypeExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'indexTypeCode',
        label: t('bizSearchType.indexTypeDesc.title', '默认索引方式'),
        component: 'select',
        extraProps: {
          filterable: true,
          options: dataSet?.value ? dataSet.value[INDEX_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSearchType.indexTypeDesc.title', '默认索引方式'),
        }),
      },
      {
        name: 'triggerTypeCode',
        label: t('bizSearchType.triggerTypeCode.title', '触发方式'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSearchType.triggerTypeCode.title', '触发方式'),
        }),
        extraProps: {
          filterable: true,
          options: dataSet?.value ? dataSet.value[TRIGGER_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizSearchType.triggerTypeCode.title', '触发方式'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'interfaceId',
        label: t('bizSearchType.interfaceName.title', '读卡接口'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSearchType.interfaceName.title', '读卡接口'),
        }),
        extraProps: {
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: interfaceList.value,
          remoteMethod: async (keyWord: string) => {
            await getInterfaceList({
              hospitalId: orgId.value,
              keyWord: keyWord,
            });
          },
          props: {
            label: 'interfaceName',
            value: 'interfaceId',
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
      {
        name: 'inputHintContent',
        label: t('bizSearchType.inputHintContent.title', '提示内容'),
        component: 'input',
        type: 'textarea',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('bizSearchType.inputHintContent.title', '提示内容'),
        }),
      },
    ],
  });
}
