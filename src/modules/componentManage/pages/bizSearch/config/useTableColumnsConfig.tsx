import { FLAG } from '@/utils/constant';
import { useRouter } from 'vue-router';
import { SEARCH_NAME } from '../constant/searchName';
import { Ref, ComputedRef } from 'vue';
import { filterSelectData } from '@sun-toolkit/shared';
import {
  useColumnConfig,
  PatientAccess,
  useEditableTable,
  TableRef,
} from 'sun-biz';

// 检索组件、方式table
export function useTableColumnsConfig(options: {
  activeName: ComputedRef<SEARCH_NAME>;
  isCloudEnv: boolean | undefined;
  menuId: string;
  handleEnableSwitch: (
    row: BizSearch.SearchTypeItem | BizSearch.SearchComponentItem,
  ) => Promise<void>;
  operationFn: (row: BizSearch.SearchTypeItem) => Promise<void>;
}) {
  const { activeName, isCloudEnv, menuId, handleEnableSwitch, operationFn } =
    options;
  const router = useRouter();

  return useColumnConfig({
    getData: (t) => {
      return [
        {
          type: 'selection',
          minWidth: 100,
        },
        {
          label: t('bizSearchComponent.componentCode.title', '组件代码'),
          prop: 'componentCode',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
        },
        {
          label: t('bizSearchComponent.componentName.title', '组件名称'),
          prop: 'componentName',
          minWidth: 200,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
        },
        {
          label: t(
            'bizSearchComponent.bizSearchTypeNameDisplay.title',
            '检索方式',
          ),
          prop: 'bizSearchTypeNameDisplay',
          minWidth: 1000,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
          render: (row: BizSearch.SearchComponentItem) => {
            return (
              <>
                <PatientAccess
                  menuId={menuId}
                  code={row.componentCode}
                  style="width:500px"
                />
              </>
            );
            // const list = row.bizSearchTypeList?.filter((item) => {
            //   return item.enabledFlag === FLAG.YES;
            // });
            // const listStr = list.map((item) => item.bizSearchTypeNameDisplay);
            // return <>{listStr.join('|')}</>;
          },
        },
        {
          label: t('bizSearchComponent.maxShowItemNum.title', '最大显示数量'),
          prop: 'maxShowItemNum',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
        },
        {
          label: t('bizSearchComponent.componentDesc.title', '组件描述'),
          prop: 'componentDesc',
          minWidth: 200,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
        },

        {
          label: t('global:sequence'),
          prop: 'indexNo',
          minWidth: 100,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
          prop: 'searchTypeCode',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.searchTypeName.title', '检索方式名称'),
          prop: 'searchTypeName',
          minWidth: 200,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.searchType2ndName.title', '检索方式辅助名称'),
          prop: 'searchType2ndName',
          minWidth: 260,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.searchTypeExtName.title', '检索方式扩展名称'),
          prop: 'searchTypeExtName',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.indexTypeDesc.title', '默认索引方式'),
          prop: 'indexTypeDesc',
          minWidth: 200,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.triggerTypeDesc.title', '触发方式'),
          prop: 'triggerTypeDesc',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.inputHintContent.title', '提示内容'),
          prop: 'inputHintContent',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('bizSearchType.interfaceName.title', '读卡接口'),
          prop: 'interfaceName',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 150,
          render: (row: BizSearch.SearchComponentItem) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                disabled={!isCloudEnv}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.COMPONENT,
          render: (row: BizSearch.SearchComponentItem) => {
            return (
              <div class={'flex items-center justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() =>
                    router.push(`/bizSearch/operation/${row.componentCode}`)
                  }
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() =>
                    router.push(`/bizSearch/menuOperation/${row.componentCode}`)
                  }
                >
                  {t('bizSearch.operation.title', '按菜单配置')}
                </el-button>
              </div>
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          minWidth: 150,
          isHidden: activeName.value !== SEARCH_NAME.TYPE,
          render: (row: BizSearch.SearchComponentItem) => {
            return (
              <el-button
                type="primary"
                link={true}
                onClick={() =>
                  operationFn(row as unknown as BizSearch.SearchTypeItem)
                }
              >
                {t('global:edit')}
              </el-button>
            );
          },
        },
      ];
    },
  });
}

// 检索组件的详情table
export function useSearchTypeTableConfig(options: {
  tableRef?: Ref<TableRef>;
  data?: Ref<Partial<BizSearch.SearchTypeItem>[]>;
  id?: string;
  radioSelect?: Ref<string>;
  handleRadioChange?: (val: string) => Promise<void>;
  searchTypeList?: Ref<BizSearch.SearchTypeItem[]>;
  getSearchTypeList?: (params: BizSearch.SearchTypeReqParams) => Promise<void>;
  isHiddenOperation?: boolean;
}) {
  const {
    tableRef,
    data,
    id,
    radioSelect,
    handleRadioChange,
    searchTypeList,
    getSearchTypeList,
    isHiddenOperation,
  } = options;
  const { addItem, toggleEdit, cancelEdit, delItem } = useEditableTable({
    tableRef: tableRef as Ref<TableRef>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: data as any,
    id: id as string,
  });
  const searchTypeConfig = useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:sequence'),
          prop: 'indexNo',
          minWidth: 100,
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
          prop: 'searchTypeCode',
          minWidth: 150,
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizSearchType.searchTypeCode.title', '检索方式代码'),
              }),
              trigger: 'blur',
            },
          ],
          render: (
            row: BizSearch.SearchTypeItem & {
              editable: boolean;
              bizSearchTypeNameDisplay: string;
            },
          ) => {
            if (row.editable) {
              const filterSelf = data?.value.filter(
                (item) => item.searchTypeCode !== row?.searchTypeCode,
              );
              return (
                <el-select
                  v-model={row.searchTypeCode}
                  remote={true}
                  clearable={true}
                  filterable={true}
                  remote-show-suffix={true}
                  remote-method={(val: string) => {
                    (
                      getSearchTypeList as (
                        params: BizSearch.SearchTypeReqParams,
                      ) => Promise<void>
                    )({
                      keyWord: val,
                      enabledFlag: FLAG.YES,
                    });
                  }}
                  onChange={(val: string) => {
                    const obj = (
                      searchTypeList as Ref<BizSearch.SearchTypeItem[]>
                    ).value.find(
                      (item: BizSearch.SearchTypeItem) =>
                        item.searchTypeCode === val,
                    );
                    row.searchTypeId = obj?.searchTypeId as string;
                    row.searchTypeCode = obj?.searchTypeCode as string;
                    row.bizSearchTypeNameDisplay =
                      obj?.searchTypeNameDisplay as string;
                  }}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizSearchType.searchTypeCode.title',
                      '检索方式代码',
                    ),
                  })}
                >
                  {filterSelectData(
                    (searchTypeList as Ref<BizSearch.SearchTypeItem[]>).value ??
                      [],
                    filterSelf ?? [],
                    'searchTypeCode',
                  ).map((item: BizSearch.SearchTypeItem) => {
                    return (
                      <el-option
                        key={item.searchTypeCode}
                        label={item.searchTypeName}
                        value={item.searchTypeCode}
                      />
                    );
                  })}
                </el-select>
              );
            } else {
              return <>{row.searchTypeCode}</>;
            }
          },
        },
        {
          label: t('bizSearchType.searchTypeName.title', '检索方式名称'),
          prop: 'bizSearchTypeNameDisplay',
          minWidth: 200,
          render: (row: { bizSearchTypeNameDisplay: string }) => {
            return <>{row.bizSearchTypeNameDisplay}</>;
          },
        },
        {
          label: t('bizSearchType.inputHintContent.title', '文本框提示内容'),
          prop: 'inputHintContent',
          minWidth: 200,
          render: (
            row: BizSearch.SearchTypeItem & {
              editable: boolean;
              bizSearchTypeNameDisplay: string;
            },
          ) => {
            // if (row.editable) {
            return (
              <el-input
                v-model={row.inputHintContent}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'bizSearchType.inputHintContent.title',
                    '文本框提示内容',
                  ),
                })}
              ></el-input>
            );
            // } else {
            //   return <>{row?.inputHintContent ?? '--'}</>;
            // }
          },
        },
        {
          label: t('bizSearchType.defaultFlag.title', '默认标志'),
          prop: 'defaultFlag',
          minWidth: 200,
          render: (row: { searchComptXSearchTypeId: string; key: string }) => {
            return (
              <el-radio
                v-model={(radioSelect as Ref<string>).value}
                value={row.searchComptXSearchTypeId ?? row.key}
                onChange={() =>
                  (handleRadioChange as (val: string) => Promise<void>)(
                    row.searchComptXSearchTypeId ?? row.key,
                  )
                }
              ></el-radio>
            );
          },
        },
        {
          label: t('global:enabledFlag'),
          prop: isHiddenOperation ? 'vsEnabledFlag' : 'enabledFlag',
          minWidth: 150,
          render: (row: { vsEnabledFlag: number; enabledFlag: number }) => {
            return (
              <el-switch
                modelValue={
                  isHiddenOperation ? row.vsEnabledFlag : row.enabledFlag
                }
                inline-prompt
                activeValue={FLAG.YES}
                inactiveValue={FLAG.NO}
                onChange={(val: number) => {
                  if (isHiddenOperation) {
                    row.vsEnabledFlag = val;
                  } else {
                    row.enabledFlag = val;
                  }
                }}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          isHidden: isHiddenOperation,
          minWidth: 150,
          render: (
            row: { editable: boolean; searchComptXSearchTypeId: string },
            $index: number,
          ) => {
            if (row.editable) {
              return (
                <div class={'flex items-center justify-around'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                </div>
              );
            } else {
              if (!row?.searchComptXSearchTypeId) {
                return (
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:delete')}
                  </el-button>
                );
              } else {
                return <></>;
              }
            }
          },
        },
      ];
    },
  });
  return { searchTypeConfig, addItem };
}
