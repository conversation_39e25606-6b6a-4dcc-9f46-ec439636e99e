import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(options: {
  queryTableData: (
    row: BizSearch.SearchTypeReqParams | BizSearch.SearchComponentReqParams,
  ) => Promise<void>;
  searchModel: Ref<
    BizSearch.SearchTypeReqParams | BizSearch.SearchComponentReqParams
  >;
}) {
  const { queryTableData, searchModel } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-72',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryTableData(searchModel.value);
              e.preventDefault();
            }
          },
        },
      },
    ],
  });
  return data;
}
