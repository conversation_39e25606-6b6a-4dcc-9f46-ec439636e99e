import { Ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { useRouter } from 'vue-router';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
export function useTableColumnsConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<Banner.BannerReqItem[]>;
  formList: Ref<FormDesign.FormInfo[]>;
  getFormList: (params: { keyWord: string }) => Promise<void>;
  handleAddBanner: (
    row: Banner.AddBannerReqParams,
    index: number,
  ) => Promise<void>;
  handleUpdateBanner: (
    row: Banner.UpdateBannerReqParams,
    index: number,
  ) => Promise<void>;
}) {
  const {
    id,
    data,
    tableRef,
    formList,
    getFormList,
    handleAddBanner,
    handleUpdateBanner,
  } = options;
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (Banner.BannerReqItem & { editable: boolean })[]
    >,
    id,
  });
  const router = useRouter();

  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'indexNo',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: Banner.BannerReqItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('bannerManage.componentId', '组件标识'),
        prop: 'componentId',
        minWidth: 150,
      },
      {
        label: t('bannerManage.componentDesc', '组件描述'),
        prop: 'componentDesc',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bannerManage.componentDesc', '组件描述'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Banner.BannerReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.componentDesc}
                placeholder={t('global:placeholder.input.template', {
                  content: t('bannerManage.componentDesc', '组件描述'),
                })}
              />
            );
          } else {
            return <>{row.componentDesc}</>;
          }
        },
      },
      {
        label: t('bannerManage.formName', '对应表单'),
        prop: 'formId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bannerManage.formName', '对应表单'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Banner.BannerReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.formId}
                remote={true}
                filterable={true}
                remote-method={async (val: string) => {
                  await getFormList({
                    keyWord: val,
                  });
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t('bannerManage.formName', '对应表单'),
                })}
                onChange={(val: string) => {
                  const obj = formList.value?.find(
                    (formItem) => formItem.formId === val,
                  );
                  row.formName = (obj?.formName ?? '') as string;
                }}
              >
                {formList.value?.map((item) => (
                  <el-option
                    key={item.formId}
                    label={item.formName}
                    value={item.formId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.formName}</>;
          }
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: Banner.BannerReqItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled={!row.editable}
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              onChange={() => {
                if (row.enabledFlag === FLAG.YES) {
                  row.enabledFlag = FLAG.NO;
                } else {
                  row.enabledFlag = FLAG.YES;
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (
          row: Banner.BannerReqItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={async () => {
                  if (row.componentId) {
                    await handleUpdateBanner(row, $index);
                  } else {
                    await handleAddBanner(row, $index);
                  }
                }}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:operation')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => router.push(`/design/${row.formId}`)}
              >
                {t('design', '设计')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem };
}
