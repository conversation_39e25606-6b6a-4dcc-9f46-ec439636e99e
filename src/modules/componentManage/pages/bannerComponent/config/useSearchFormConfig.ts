import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(options: {
  queryBannerList: (params: Banner.BannerReqParams) => Promise<void>;
}) {
  const { queryBannerList } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryBannerList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}
