<script setup lang="ts" name="bannerComponentIndex">
  import { queryFormByExample } from '@/modules/componentManage/api/formDesign.ts';
  import { useSearchFormConfig } from '../config/useSearchFormConfig.ts';
  import { useTableColumnsConfig } from '../config/useTableColumnsConfig.tsx';
  import { FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { ref, Ref, onMounted, computed } from 'vue';
  import {
    addBanner,
    updateBannerById,
    queryBannerListByExample,
  } from '@/modules/componentManage/api/banner.ts';
  import { Title, ProForm, ProTable, DmlButton } from 'sun-biz';
  type searchFormType = {
    enabledFlag?: FLAG;
    keyWord?: string;
  };

  const loading = ref<boolean>(false); // 加载状态
  const searchFormModel = ref<searchFormType>({
    enabledFlag: FLAG.ALL,
    keyWord: undefined,
  }); // 检索条件数据
  const tableData = ref<Banner.BannerReqItem[]>([]); // 表格数据
  const formList = ref<FormDesign.FormInfo[]>([]); //表单数据
  const selectTableData = ref<Banner.BannerReqItem[]>([]); //选中的table数据

  const tableColumnsRef = ref();

  const bizData = computed(() => {
    const list = selectTableData.value?.map((item) => {
      return item.componentId;
    });
    return list ?? [];
  });

  // 查询banner列表
  const queryBannerList = async (params: Banner.BannerReqParams = {}) => {
    searchFormModel.value = {
      ...searchFormModel.value,
      ...params,
    };
    loading.value = true;
    const [, res] = await queryBannerListByExample({
      ...searchFormModel.value,
      enabledFlag:
        searchFormModel.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchFormModel.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      tableData.value = res.data ?? [];
    }
  };

  // 保存新增数据
  const handleAddBanner = async (
    row: Banner.AddBannerReqParams,
    index: number,
  ) => {
    const isValid = await tableColumnsRef?.value?.validateRow(index);
    if (!isValid) return;
    const [, res] = await addBanner({ ...row });
    if (res?.success) {
      await queryBannerList();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };

  // 修改数据
  const handleUpdateBanner = async (
    row: Banner.UpdateBannerReqParams,
    index: number,
  ) => {
    const isValid = await tableColumnsRef?.value?.validateRow(index);
    if (!isValid) return;
    const [, res] = await updateBannerById({ ...row });
    if (res?.success) {
      await queryBannerList();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };

  // 获取表单数据
  const getFormList = async (params: { keyWord?: string } = {}) => {
    const [, res] = await queryFormByExample({
      enabledFlag: FLAG.YES,
      ...params,
    });
    if (res?.success) {
      formList.value = res.data ?? [];
    }
  };

  const addItemFn = () => {
    addItem({
      editable: true,
      enabledFlag: FLAG.YES,
    } as unknown as Banner.BannerReqItem & { editable: boolean });
  };

  // 选中行设置
  const selectionChange = (val: Banner.BannerReqItem[]) => {
    selectTableData.value = val;
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryBannerList: queryBannerList,
  });
  // 表格配置数据
  const { tableColumns, addItem } = useTableColumnsConfig({
    id: 'componentId',
    data: tableData,
    formList: formList as Ref<FormDesign.FormInfo[]>,
    tableRef: tableColumnsRef,
    getFormList: getFormList,
    handleAddBanner: handleAddBanner,
    handleUpdateBanner: handleUpdateBanner,
  });

  onMounted(async () => {
    await queryBannerList();
    await getFormList();
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <Title class="mb-2" :title="$t('banner.list', 'Banner列表')" />
    <div class="flex justify-between">
      <ProForm
        ref="searchFormRef"
        layout-mode="inline"
        :data="searchConfig"
        :show-search-button="true"
        v-model="searchFormModel"
        @model-change="queryBannerList"
      />
      <div>
        <DmlButton
          :code="BIZ_ID_TYPE_CODE.DICT_BANNER_COMPONENT"
          :biz-data="bizData"
          @success="
            () => {
              tableColumnsRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button type="primary" class="ml-2.5" @click="addItemFn">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <ProTable
      ref="tableColumnsRef"
      :editable="true"
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      row-key="componentId"
      @selection-change="selectionChange"
    />
  </div>
</template>
