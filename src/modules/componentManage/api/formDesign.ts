import { dictRequest } from '@sun-toolkit/request';
/**
 * [1-10091-1]根据条件查询表单列表
 * @param params
 * @returns
 */
export const queryFormByExample = (params: {
  keyWord?: string;
  formId?: string | undefined;
  enabledFlag?: number;
}) => {
  return dictRequest<
    FormDesign.FormInfo[],
    {
      keyWord?: string;
      formId?: string | undefined;
      enabledFlag?: number;
    }
  >('/formdesign/queryFormByExample', params);
};

/**
 * [1-10092-1]新增表单
 * @param params
 * @returns
 */
export const addForm = (params: FormDesign.AddFormReq) => {
  return dictRequest<string>('/formdesign/addForm', params);
};

/**
 * [1-10093-1]根据标识修改表单信息
 * @param params
 * @returns
 */
export const updateFormById = (params: FormDesign.UpdateFormReq) => {
  return dictRequest<boolean>('/formdesign/updateFormById', params);
};

/**
 * [1-10094-1]新增表单控件
 * @param params
 * @returns
 */
export const addFormControl = (params: FormDesign.ReqAddFormControl) => {
  return dictRequest<{ formControlId: string }>(
    '/formdesign/addFormControl',
    params,
  );
};

/**
 * [1-10095-1]根据标识编辑表单控件
 * @param params
 * @returns
 */
export const updateFormControlById = (params: FormDesign.ReqAddFormControl) => {
  return dictRequest<boolean>('/formdesign/updateFormControlById', params);
};

/**
 * [1-10096-1]修改表单控件排序
 * @param params
 * @returns
 */
export const updateFormControlSortByIds = (params: {
  formControlSortList: { formControlId: string; sort: number }[];
}) => {
  return dictRequest<boolean>('/formdesign/updateFormControlSortByIds', params);
};

/**
 * [1-10097-1]根据条件查询表单设计方案列表
 * @param params
 * @returns
 */
export const queryFormDesignListByExample = (
  params: FormDesign.ReqQueryFormDesignList,
) => {
  return dictRequest<FormDesign.FormDesignInfo[]>(
    '/formdesign/queryFormDesignListByExample',
    params,
  );
};

/**
 * [1-10098-1]根据条件查询表单设计内容
 * @param params
 * @returns
 */
export const queryFormDesignDetailByExample = (params: {
  keyWord?: string;
  formDesignId: string;
}) => {
  return dictRequest<{
    formDesignDetailList: FormDesign.FormDesignDetailInfo[];
  }>('/formdesign/queryFormDesignDetailByExample', params);
};

/**
 * [1-10099-1]保存表单设计内容
 * @param params
 * @returns
 */
export const saveFormDesignDetail = (
  params: FormDesign.SaveFormDesignDetailReq,
) => {
  return dictRequest<Origin.BizSourceInfo[]>(
    '/formdesign/saveFormDesignDetail',
    params,
  );
};

/**
 * [1-10103-1]新增表单设计方案
 * @param params
 * @returns
 */
export const addFormDesign = (params: FormDesign.ReqAddFormDesign) => {
  return dictRequest<{
    formDesignId: string;
  }>('/formdesign/addFormDesign', params);
};
