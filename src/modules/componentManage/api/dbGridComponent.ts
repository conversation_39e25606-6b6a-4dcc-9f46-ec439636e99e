import { dictRequest, basicRequest } from '@sun-toolkit/request';

/**
 * [1-10150-1]	保存数据窗体组件
 * @param params
 * @returns
 */
export const saveDbgridComponent = (
  params: DbGridComponent.ReqSaveDbgridComponent,
) => {
  return dictRequest<string>('/dbgridcomponent/saveDbgridComponent', params);
};

/**
 * [1-10148-1]根据条件查询数据窗体组件列表（定义态）
 * @param params
 * @returns
 */
export const queryDbgridComponentListByExample = (
  params: DbGridComponent.ReqQueryDbgridComponentList,
) => {
  return dictRequest<
    DbGridComponent.DbgridComponentInfo,
    DbGridComponent.ReqQueryDbgridComponentList
  >('/dbgridcomponent/queryDbgridComponentListByExample', params);
};

/**
 * [1-10149-1]根据条件查询数据窗体组件（业务态）
 * @param params
 * @returns
 */
export const queryDbgridComponentByExample = (params: {
  componentNo: string;
}) => {
  return dictRequest<DbGridComponent.DbgridComponent>(
    '/dbgridcomponent/queryDbgridComponentByExample',
    params,
  );
};

/**
 * [1-10151-1]保存数据窗体组件的配置
 * @param params
 * @returns
 */
export const saveDbgridComponentSetting = (
  params: DbGridComponent.ReqSaveDbgridComponentSetting,
) => {
  return dictRequest<DbGridComponent.DbgridComponent>(
    '/dbgridcomponent/saveDbgridComponentSetting',
    params,
  );
};

/**
 * [4-10090-1]根据数据源导出文件
 * @param params
 * @returns
 */
export const exportData2File = (params: {
  fileTypeCode: string;
  dataSource: string;
}) => {
  return basicRequest<{
    fileString: string;
  }>('/untils/exportData2File', params);
};
