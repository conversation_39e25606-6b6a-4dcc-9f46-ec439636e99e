import { dictRequest } from '@sun-toolkit/request';
// import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10131-1]根据条件查询BANNER组件
 * @param params
 * @returns
 */
export const queryBannerListByExample = (params: Banner.BannerReqParams) => {
  return dictRequest<Banner.BannerReqItem[]>(
    '/banner/queryBannerListByExample',
    params,
  );
};

/**
 * [1-10132-1]新增BANNER组件
 * @param params
 * @returns
 */
export const addBanner = (params: Banner.AddBannerReqParams) => {
  return dictRequest<Banner.AddBannerReqItem>('/banner/addBanner', params);
};

/**
 * [1-10133-1]根据标识修改BANNER组件
 * @param params
 * @returns
 */
export const updateBannerById = (params: Banner.UpdateBannerReqParams) => {
  return dictRequest('/banner/updateBannerById', params);
};
