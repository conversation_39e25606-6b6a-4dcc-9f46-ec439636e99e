import { dictRequest } from '@sun-toolkit/request';
// import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10106-1]根据条件查询检索方式列表
 * @param params
 * @returns
 */
export const querySearchTypeListByExample = (
  params: BizSearch.SearchTypeReqParams,
) => {
  return dictRequest<BizSearch.SearchTypeItem[]>(
    '/searchcomponent/querySearchTypeListByExample',
    params,
  );
};

/**
 * [1-10107-1] 新增检索方式
 * @param params
 * @returns
 */
export const addSearchType = (params: BizSearch.AddSearchTypeReqParams) => {
  return dictRequest<BizSearch.AddSearchTypeItem>(
    '/searchcomponent/addSearchType',
    params,
  );
};

/**
 * [1-10108-1] 根据标识修改检索方式
 * @param params
 * @returns
 */
export const updateSearchTypeById = (
  params: BizSearch.UpdateSearchTypeReqParams,
) => {
  return dictRequest('/searchcomponent/updateSearchTypeById', params);
};

/**
 * [1-10109-1] 根据标识停启用检索方式
 * @param params
 * @returns
 */
export const updateSearchTypeEnabledFlagById = (
  params: BizSearch.UpdateSearchTypeEnabledFlagReqParams,
) => {
  return dictRequest(
    '/searchcomponent/updateSearchTypeEnabledFlagById',
    params,
  );
};

/**
 * [1-10110-1]根据条件查询读卡检索组件列表
 * @param params
 * @returns
 */
export const querySearchComponentListByExample = (
  params: BizSearch.SearchComponentReqParams,
) => {
  return dictRequest<BizSearch.SearchComponentItem[]>(
    '/searchcomponent/querySearchComponentListByExample',
    params,
  );
};

/**
 * [1-10111-1] 新增读卡检索组件
 * @param params
 * @returns
 */
export const addSearchComponent = (
  params: BizSearch.AddSearchComponentReqParams,
) => {
  return dictRequest<BizSearch.AddSearchComponentItem>(
    '/searchcomponent/addSearchComponent',
    params,
  );
};

/**
 * [1-10112-1] 根据标识修改读卡检索组件
 * @param params
 * @returns
 */
export const updateSearchComponentById = (
  params: BizSearch.UpdateSearchComponentReqParams,
) => {
  return dictRequest('/searchcomponent/updateSearchComponentById', params);
};

/**
 * [1-10113-1] 根据标识停启用读卡检索组件
 * @param params
 * @returns
 */
export const updateSearchComponentEnabledFlagById = (
  params: BizSearch.UpdateSearchComponentEnabledFlagReqParams,
) => {
  return dictRequest(
    '/searchcomponent/updateSearchComponentEnabledFlagById',
    params,
  );
};

/**
 * [1-10114-1] 保存菜单的检索方式
 * @param params
 * @returns
 */
export const saveaMenuXSearchType = (
  params: BizSearch.SaveMenuXSearchTypeReqParams,
) => {
  return dictRequest('/searchcomponent/saveaMenuXSearchType', params);
};

/**
 * [1-10115-1] 根据条件查询菜单的检索方式列表
 * @param params
 * @returns
 */
export const queryMenuXSearchTypeByExample = (
  params: BizSearch.MenuXSearchTypeReqParams,
) => {
  return dictRequest<BizSearch.MenuXSearchTypeItem>(
    '/searchcomponent/queryMenuXSearchTypeByExample',
    params,
  );
};

/**
 * [1-10116-1] 根据条件查询菜单与读卡检索组件关系
 * @param params
 * @returns
 */
export const querySearchComponentXMenuByExample = (
  params: BizSearch.SearchComponentXMenuReqParams,
) => {
  return dictRequest<BizSearch.SearchComponentXMenuItem[]>(
    '/bizsearchcomponent/querySearchComponentXMenuByExample',
    params,
  );
};
