<script setup lang="ts" name="storageRack">
  import { ref, nextTick } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { CirclePlus, Remove } from '@element-sun/icons-vue';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useStorageRackTableConfig } from './config/useTableConfig';
  import { useStorageRackSearchFormConfig } from './config/useFormConfig';
  import { queryStorageListByExample } from '@modules/system/api/org';
  import {
    queryStorageRackListByExample,
    updateStorageRackEnabledFlagById,
  } from '@modules/drug/api/storageRack';
  import { generateUUID } from '@sun-toolkit/shared';

  type StorageRackTableItem = StorageRack.StorageRackItem & {
    completeStorageRackName: string;
    completeStorageRackId: string;
    editable: boolean;
    isExpand: boolean;
    level: number;
    parentStorageRackList?: string;
    parentStorageRackName?: string;
    key?: string;
    subStorageRackList?: StorageRackTableItem[];
  };

  const { t } = useTranslation();
  const searchParams = ref<StorageRack.SearchStorageRackParams>({
    hospitalId: '',
    commodityStorageId: '',
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const loading = ref(false);
  const storageList = ref<SelectOptions[]>([]); // 库房列表
  const storageRackList = ref<StorageRackTableItem[]>([]); // 货位列表
  const storageRackTableRef = ref();
  const backupStorageRackListMap = ref<{ [key: string]: StorageRackTableItem }>(
    {},
  ); // 备份旧的货位列表map

  // 查询库房数据
  const queryStorageList = async (data: Org.queryStorageParams) => {
    const [, res] = await queryStorageListByExample(data);
    if (res?.success) {
      if (!res.data?.length) {
        nextTick(() => {
          searchParams.value.commodityStorageId = '';
          storageList.value = [];
          storageRackList.value = [];
          backupStorageRackListMap.value = {};
        });
      } else {
        storageList.value = res.data.map((item: Org.Storage) => ({
          label: item.storageNameDisplay,
          value: item.commodityStorageId,
        }));
        nextTick(() => {
          searchParams.value.commodityStorageId =
            res.data[0].commodityStorageId;
          queryStorageRackList();
        });
      }
    }
  };

  // 处理货位列表数据
  const handleFormatData = (
    level: number,
    data?: StorageRackTableItem[],
    parentStorageRackId?: string,
    parentStorageRackName?: string,
    completeStorageRackId?: string,
    completeStorageRackName?: string,
  ) => {
    if (!data?.length) {
      return [];
    }
    const list: StorageRackTableItem[] = data.map((item) => {
      const storageRackItem = {
        ...item,
        editable: false,
        isExpand:
          backupStorageRackListMap.value[item.storageRackId]?.isExpand || false,
        level,
        key: item.storageRackId,
        parentStorageRackList: parentStorageRackId,
        parentStorageRackName,
        completeStorageRackId: completeStorageRackId
          ? `${completeStorageRackId}-${item.storageRackId}`
          : item.storageRackId,
        completeStorageRackName: completeStorageRackName
          ? `${completeStorageRackName}-${item.storageRackName}`
          : item.storageRackName,
      };
      const subStorageRackList = handleFormatData(
        level + 1,
        storageRackItem.subStorageRackList,
        storageRackItem.storageRackId,
        storageRackItem.storageRackName,
        storageRackItem.completeStorageRackId,
        storageRackItem.completeStorageRackName,
      );
      return {
        ...storageRackItem,
        subStorageRackList,
      };
    });
    return list;
  };

  // 查询货位列表
  const queryStorageRackList = async (
    data?: StorageRack.SearchStorageRackParams,
    userBackup?: boolean | undefined,
  ) => {
    loading.value = true;
    // 备份当前的listMap，新数据渲染时保持展开的结构
    if (!!userBackup && storageRackList.value?.length) {
      const map: { [key: string]: StorageRackTableItem } = {};
      storageRackList.value.forEach((item) => {
        if (item.storageRackId) {
          map[item.storageRackId] = item;
        }
      });
      backupStorageRackListMap.value = map;
    } else {
      backupStorageRackListMap.value = {};
    }
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
      storageIdTypeCode: BIZ_ID_TYPE_CODE.DICT_ORGANIZATION,
    };
    const [, res] = await queryStorageRackListByExample(params);
    loading.value = false;
    if (res?.success) {
      const formatList = handleFormatData(1, res.data);
      const list: StorageRackTableItem[] = [];
      const setStorageRackListValue = (data: StorageRackTableItem[]) => {
        if (!data.length) {
          return;
        }
        data.forEach((item) => {
          list.push(item);
          if (item.isExpand && item.subStorageRackList?.length) {
            setStorageRackListValue(item.subStorageRackList);
          }
        });
      };
      setStorageRackListValue(formatList);
      storageRackList.value = list;
    }
  };

  const handleEnableSwitch = (row: StorageRackTableItem) => {
    if (!row.storageRackId) {
      row.enabledFlag =
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES;
      return true;
    }
    ElMessageBox.confirm(
      row.enabledFlag === ENABLED_FLAG.YES &&
        row.subStorageRackList &&
        row.subStorageRackList?.length > 0 &&
        row.subStorageRackList.some((item) => !!item.enabledFlag)
        ? t(
            'storageRack.ask.title',
            '当前货位存在启用的下级货位，是否一同停用?',
          )
        : t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
            action:
              row.enabledFlag === ENABLED_FLAG.YES
                ? t('global:disabled')
                : t('global:enabled'),
            name: row.storageRackName,
          }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        storageRackId: row.storageRackId,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateStorageRackEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        queryStorageRackList({}, true);
      }
    });
    return true;
  };

  const canUpsertTableRow = () => {
    const isEditing = storageRackList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的货位信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  // 新增下级货位
  const handleAddSubStorageRack = (
    row: StorageRackTableItem,
    index: number,
  ) => {
    if (!canUpsertTableRow()) return;
    const item = {
      storageIdTypeCode: BIZ_ID_TYPE_CODE.DICT_ORGANIZATION,
      enabledFlag: ENABLED_FLAG.YES,
      storageRackId: '',
      storageRackName: '',
      commodityStorageId: row.commodityStorageId,
      editable: true,
      isExpand: false,
      level: (row.level || 0) + 1,
      parentStorageRackList: row.storageRackId,
      completeStorageRackId: `${row.completeStorageRackId}-`,
      completeStorageRackName: '',
      key: generateUUID(),
    };
    if (row.subStorageRackList?.length) {
      row.subStorageRackList.push(item);
    } else {
      row.subStorageRackList = [item];
    }
    if (!row.isExpand) {
      row.isExpand = true;
      storageRackList.value.splice(
        index,
        1,
        ...[row, ...row.subStorageRackList],
      );
    } else {
      storageRackList.value.splice(
        index,
        1 + row.subStorageRackList.length - 1,
        ...[row, ...row.subStorageRackList],
      );
    }
  };

  // 新增根目录货位
  const onAddClick = () => {
    if (!canUpsertTableRow()) return;
    addItem({
      storageIdTypeCode: BIZ_ID_TYPE_CODE.DICT_ORGANIZATION,
      enabledFlag: ENABLED_FLAG.YES,
      storageRackName: '',
      commodityStorageId: searchParams.value.commodityStorageId as string,
      editable: true,
      level: 1,
      completeStorageRackId: '',
      completeStorageRackName: '',
      isExpand: false,
      subStorageRackList: [],
    } as unknown as StorageRackTableItem);
    nextTick(() => {
      const row = storageRackTableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${storageRackList.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };

  // 展开行
  const handleExpand = (data: StorageRackTableItem, index: number) => {
    storageRackList.value.splice(
      index,
      1,
      ...[
        { ...data, isExpand: true },
        ...(data.subStorageRackList as StorageRackTableItem[]),
      ],
    );
  };

  // 处理折叠行下层货位列表
  const retractFn = (tableItem?: StorageRackTableItem) => {
    if (!tableItem?.subStorageRackList?.length) return [];
    const arr: StorageRackTableItem[] = tableItem.subStorageRackList.map(
      (item) => {
        return {
          ...item,
          isExpand: false,
          subStorageRackList: retractFn(item as StorageRackTableItem),
        };
      },
    ) as StorageRackTableItem[];
    return arr;
  };

  // 折叠行
  const handleRetract = (data: StorageRackTableItem, index: number) => {
    const retractStep = storageRackList.value.filter((item) =>
      item.completeStorageRackId.includes(data.storageRackId),
    ).length;
    data.subStorageRackList = retractFn(data);
    storageRackList.value.splice(index, retractStep as number, {
      ...data,
      isExpand: false,
    });
  };

  // 编辑
  const handleEdit = (data: StorageRackTableItem) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(data);
  };

  // 取消编辑
  const handleCancelEdit = (data: StorageRackTableItem, index: number) => {
    if (data.level === 1 || data.storageRackId) {
      cancelEdit(data, index);
      return;
    }
    const parentIndex = storageRackList.value.findIndex(
      (item) => item.storageRackId === data.parentStorageRackList,
    );
    if (parentIndex >= 0) {
      const parent = storageRackList.value[parentIndex];
      parent.subStorageRackList = parent?.subStorageRackList?.filter(
        (item: StorageRackTableItem) => item.key !== data.key,
      );
      if (!parent.subStorageRackList?.length) {
        parent.isExpand = false;
      }
      storageRackList.value.splice(
        parentIndex,
        1 + (parent.subStorageRackList?.length || 0) + 1,
        ...[parent, ...(parent.subStorageRackList || [])],
      );
    }
  };

  const searchConfig = useStorageRackSearchFormConfig(
    storageList,
    queryStorageList,
  );

  const { storageRackTableConfig, addItem, toggleEdit, cancelEdit } =
    useStorageRackTableConfig(
      storageRackTableRef,
      storageRackList,
      queryStorageRackList,
      handleEnableSwitch,
      handleAddSubStorageRack,
      handleExpand,
      handleRetract,
      handleEdit,
      handleCancelEdit,
    );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('storageRack.list.title', '库房货位列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="
            (data: StorageRack.SearchStorageRackParams) =>
              queryStorageRackList(data, false)
          "
        />
      </div>
      <el-button class="mr-2" type="primary" @click="onAddClick">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="storageRackTableRef"
      row-key="storageRackId"
      editable
      :loading="loading"
      :data="storageRackList"
      :columns="storageRackTableConfig"
    >
      <template #customizeExpandHeader>
        <div class="flex items-center justify-around">
          <el-icon><CirclePlus /></el-icon>
          <el-icon><Remove /></el-icon>
        </div>
      </template>
    </ProTable>
  </div>
</template>
