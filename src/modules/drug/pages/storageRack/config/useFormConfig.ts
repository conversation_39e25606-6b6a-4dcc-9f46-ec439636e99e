import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';

export function useStorageRackSearchFormConfig(
  storageList: Ref<SelectOptions[]>,
  queryStorageList: (data: Org.queryStorageParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        placeholder: t('global:placeholder.keyword'),
        extraProps: {
          clearable: false,
          className: 'w-52',
          onChange: (val: string) => {
            queryStorageList({ hospitalId: val });
          },
        },
      },
      {
        label: t('storageRack.search.form.commodityStorageId', '所属库房'),
        name: 'commodityStorageId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('storageRack.search.form.commodityStorageId', '所属库房'),
        }),
        extraProps: {
          clearable: false,
          filterable: true,
          className: 'w-52',
          options: storageList.value,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
    ],
  });
  return data;
}
