import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10259-1]根据条件查询库房货位列表
 * @param params
 * @returns
 */
export const queryStorageRackListByExample = (
  params: StorageRack.SearchStorageRackParams,
) => {
  return dictRequest<StorageRack.StorageRackItem[]>(
    '/storagerack/queryStorageRackListByExample',
    params,
  );
};

/**
 * [1-10263-1] 根据标识停启用货位
 * @param params
 * @returns
 */
export const updateStorageRackEnabledFlagById = (params: {
  storageRackId: string;
  enabledFlag: number;
}) => {
  return dictRequest('/storagerack/updateStorageRackEnabledFlagById', params);
};

/**
 * [1-10261-1] 新增货位
 * @param params
 * @returns
 */
export const addStorageRack = (params: StorageRack.UpsertStorageRackParams) => {
  return dictRequest<{ storageRackId: string }>(
    '/storagerack/addStorageRack',
    params,
  );
};

/**
 * [1-10262-1] 根据标识修改货位信息
 * @param params
 * @returns
 */
export const updateStorageRackById = (
  params: StorageRack.UpsertStorageRackParams,
) => {
  return dictRequest('/storagerack/updateStorageRackById', params);
};
