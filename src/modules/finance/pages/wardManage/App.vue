<script setup lang="ts" name="wardManage">
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { ENABLED_FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { useWardTableConfig } from './config/useTableConfig.tsx';
  import { useWardSearchFormConfig } from './config/useFormConfig.ts';
  import {
    updateOrgEnabledFlag,
    queryWardListByExample,
  } from '@modules/system/api/org';
  import WardUpsertDialog from '@/modules/finance/pages/wardManage/components/WardUpsertDialog.vue';
  import DeptXWardListDialog from '@/modules/finance/pages/wardManage/components/DeptXWardListDialog.vue';
  import { queryDepartmentListByExample } from '@modules/system/api/org';

  const { t } = useTranslation();
  const searchParams = ref<Org.queryWardParams>({
    hospitalId: '',
    enabledFlag: ENABLED_FLAG.ALL,
    keyWord: '',
    deptId: '',
  });
  const loading = ref(false);
  const wardList = ref<Org.Item[]>([]);
  const wardUpsertParams = ref<Partial<Org.Item>>();
  const wardUpsertDialogRef = ref();
  const wardUpsertDialogMode = ref('');
  const wardTableRef = ref();
  const deptListDialogRef = ref();
  const deptListDialogData = ref<Partial<Org.Item>>();

  const queryWardListData = async (data?: Org.queryWardParams) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryWardListByExample(params);
    loading.value = false;
    if (res?.success) {
      wardList.value = res.data || [];
    }
    //修改后重新查询对应科室，初始化查询对应科室（对应科室可从启用改为禁用只查询启用对应科室）
    await getAListOfDepartments();
  };

  const onOpenWardDialog = (mode: string, data?: Org.Item) => {
    wardUpsertDialogMode.value = mode;
    if (mode === 'add') {
      wardUpsertParams.value = {
        enabledFlag: ENABLED_FLAG.YES,
        orgTypeCode: ORG_TYPE_CODE.AREA,
      };
    } else if (mode === 'edit') {
      wardUpsertParams.value = {
        ...data,
      };
    }
    wardUpsertDialogRef.value.dialogRef.open();
  };

  const onOpenDeptDialog = (data: Org.Item) => {
    deptListDialogData.value = {
      ...data,
    };
    deptListDialogRef.value.open();
  };

  const handleEnableSwitch = async (row: Org.Item) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.orgName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await updateOrgEnabledFlag({
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
        orgId: row.orgId,
      });
      if (res?.success) {
        await queryWardListData();
      }
    });
  };
  //科室列表
  const deptList = ref<Org.Item[]>([]);
  //获取对应科室
  const getAListOfDepartments = async () => {
    const params = {
      hospitalId: searchParams.value.hospitalId,
      enabledFlag: ENABLED_FLAG.YES,
    };
    const [, res] = await queryDepartmentListByExample(params);
    deptList.value = res?.data || [];
  };

  // queryWardListData();
  const searchConfig = useWardSearchFormConfig(queryWardListData, deptList);
  const tableColumnsConfig = useWardTableConfig(
    handleEnableSwitch,
    onOpenWardDialog,
    onOpenDeptDialog,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('wardManage.list.title', '病区列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          :show-search-button="true"
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryWardListData"
        />
      </div>
      <el-button class="mr-2" type="primary" @click="onOpenWardDialog('add')">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="wardTableRef"
      row-key="orgId"
      :data="wardList"
      :loading="loading"
      :columns="tableColumnsConfig"
    />
    <WardUpsertDialog
      ref="wardUpsertDialogRef"
      :mode="wardUpsertDialogMode"
      :data="wardUpsertParams"
      :hospital-id="searchParams.hospitalId"
      @success="queryWardListData"
    />
    <DeptXWardListDialog
      ref="deptListDialogRef"
      :data="deptListDialogData"
      :search-hospital-id="searchParams.hospitalId"
      :dept-list="deptList"
    />
  </div>
</template>
