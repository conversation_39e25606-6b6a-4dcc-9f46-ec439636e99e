import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';

export function useWardTableConfig(
  handleEnableSwitch: (data: Org.Item) => void,
  onOpenWardDialog: (mode: string, data: Org.Item) => void,
  onOpenDeptDialog: (data: Org.Item) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('wardManage.table.orgName', '病区名称'),
        prop: 'orgName',
        minWidth: 140,
      },
      {
        label: t('global:secondName', '辅助名称'),
        prop: 'org2ndName',
        minWidth: 130,
      },
      {
        label: t('global:thirdName', '扩展名称'),
        prop: 'orgExtName',
        minWidth: 130,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: Org.Item) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('wardManage.table.orgDesc', '简介'),
        prop: 'orgDesc',
        minWidth: 220,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: Org.Item) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenWardDialog('edit', row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenDeptDialog(row)}
              >
                {t('wardManage.table.correspondingDepartment', '对应科室')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}

type DeptXWardEditItem = Partial<DeptXWard.deptXWardInfo> & {
  editable: boolean;
};

export function useDeptTableColumnConfig(
  tableRef: Ref<TableRef>,
  data: Ref<DeptXWardEditItem[]>,
  deptList: Ref<Org.Item[]>,
  handleEnableSwitch: (data: DeptXWard.deptXWardInfo) => void,
  handleSave: (data: DeptXWardEditItem) => void,
) {
  const { toggleEdit, cancelEdit, addItem, validateItem } = useEditableTable({
    tableRef,
    data,
    id: 'deptXWardId',
  });

  const deptListTableColumnsConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('wardManage.deptTable.wardName', '科室名称'),
        prop: 'deptId',
        minWidth: 270,
        required: true,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('wardManage.deptTable.deptId', '科室'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: DeptXWardEditItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.deptId}
                  filterable={true}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('wardManage.deptTable.wardId', '科室'),
                  })}
                  onChange={(val: string) => {
                    const item = deptList.value.find(
                      (dept) => dept.orgId === val,
                    );
                    row.deptName = item?.orgName as string;
                  }}
                >
                  {deptList.value?.map((item) => (
                    <el-option
                      key={item.orgId}
                      label={item.orgName}
                      value={item.orgId}
                      disabled={data.value.some(
                        (dataItem) => dataItem.deptId === item.orgId,
                      )}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.deptName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: DeptXWard.deptXWardInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'interfaceId',
        width: 130,
        render: (row: DeptXWardEditItem, $index: number) => {
          return (
            <>
              {row.editable && (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleSave(row)}
                  >
                    {t('global:save')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    addItem,
    toggleEdit,
    validateItem,
    deptListTableColumnsConfig,
  };
}
