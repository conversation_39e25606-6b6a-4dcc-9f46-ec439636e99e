import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';

export function useWardSearchFormConfig(
  queryWardListData: (data?: Org.queryWardParams) => void,
  deptList: Ref<Org.Item[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        extraProps: {
          className: 'w-40',
          clearable: false,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'deptId',
        label: t('wardManage.deptTypeDesc', '对应科室'),
        component: 'select',
        placeholder: t('wardManage.deptTypeDesc', '请选择对应科室'),
        triggerModelChange: true,
        extraProps: {
          options: deptList.value,
          className: 'w-60',
          props: {
            value: 'orgId',
            label: 'orgName',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryWardListData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryWardListData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useWardUpsertFormConfig(disabled: Ref<boolean>) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('wardManage.form.orgNo', '病区编码'),
        name: 'orgNo',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('wardManage.form.orgNo', '病区编码'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('wardManage.form.orgNo', '病区编码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('wardManage.form.orgName', '病区名称'),
        name: 'orgName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('wardManage.form.orgName', '病区名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('wardManage.form.orgName', '病区名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:secondName', '辅助名称'),
        name: 'org2ndName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:secondName', '辅助名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('global:thirdName', '扩展名称'),
        name: 'orgExtName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:thirdName', '扩展名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        isFullWidth: true,
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'orgDesc',
        isFullWidth: true,
        label: t('wardManage.form.orgDesc', '简介'),
        component: 'input',
        type: 'textarea',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('wardManage.form.orgDesc', '简介'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
    ],
  });
}
