<script setup lang="ts" name="DeptListDialog">
  import { ref, nextTick, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ProTable, ProDialog } from 'sun-biz';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    addDeptXWard,
    queryDeptXWardByExample,
    deleteDeptXWardEnabledFlagById,
  } from '@modules/finance/api/deptXWard';
  import { useDeptTableColumnConfig } from '../config/useTableConfig.tsx';

  const props = defineProps<{
    data: Partial<Org.Item> | undefined;
    searchHospitalId: string | undefined;
    deptList: Org.Item[] | undefined;
  }>();
  const { t } = useTranslation();
  const wardFormData = ref<Partial<Org.Item>>();
  const hospitalId = ref('');
  const dialogRef = ref();
  const deptXWardListTableLoading = ref(false);
  const deptXWardListTableRef = ref();
  const deptXWardList = ref<
    (DeptXWard.deptXWardInfo & {
      editable: boolean;
    })[]
  >([]);
  const deptListData = ref<Org.Item[]>([]);

  watch(
    () => props.searchHospitalId,
    () => {
      if (props.searchHospitalId) {
        hospitalId.value = props.searchHospitalId;
      }
    },
    {
      immediate: true,
    },
  );

  watch(
    () => props.data,
    () => {
      wardFormData.value = props.data;
    },
    { immediate: true, deep: true },
  );

  watch(
    () => props.deptList,
    () => {
      deptListData.value = props.deptList || [];
    },
    { immediate: true, deep: true },
  );

  // 获取病区对应科室列表
  const queryDeptXWardList = async () => {
    deptXWardListTableLoading.value = true;
    const params = {
      wardId: wardFormData.value?.orgId,
      hospitalId: hospitalId.value,
    };
    const [, res] = await queryDeptXWardByExample(params);
    deptXWardListTableLoading.value = false;
    if (res?.success) {
      deptXWardList.value = res.data || [];
    }
  };

  const handleEnableSwitch = async (row: DeptXWard.deptXWardInfo) => {
    if (!row.deptXWardId) {
      row.enabledFlag =
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES;
      return;
    }
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.deptName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        deptXWardId: row.deptXWardId,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await deleteDeptXWardEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
      }
    });
  };

  const handleSave = async (
    data: Partial<DeptXWard.deptXWardInfo> & {
      editable: boolean;
    },
  ) => {
    const isValid = await validateItem(data);
    if (!isValid) {
      return;
    }
    const params = {
      deptId: data.deptId,
      enabledFlag: data.enabledFlag,
      hospitalId: hospitalId.value,
      wardId: wardFormData.value?.orgId,
    };
    const [, res] = await addDeptXWard(params);
    if (res?.success) {
      toggleEdit(data);
      ElMessage.success(t('global:add.success'));
      await queryDeptXWardList();
    }
  };

  const { addItem, toggleEdit, validateItem, deptListTableColumnsConfig } =
    useDeptTableColumnConfig(
      deptXWardListTableRef,
      deptXWardList,
      deptListData,
      handleEnableSwitch,
      handleSave,
    );

  const onAddClick = () => {
    addItem({
      editable: true,
      enabledFlag: ENABLED_FLAG.YES,
      wardId: wardFormData.value!.orgId as string,
      hospitalId: hospitalId.value,
    });
  };

  const onCloseClick = () => {
    dialogRef.value.close();
  };

  const openDialog = () => {
    dialogRef.value.open();
    nextTick(() => {
      queryDeptXWardList();
    });
  };

  defineExpose({ dialogRef, open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${$t('wardManage.deptListDialog.title', '对应科室维护')}${wardFormData?.orgName ? `（${wardFormData.orgName}）` : ''}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :include-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="mb-2 text-right">
      <el-button type="primary" @click="onAddClick">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="deptXWardListTableRef"
      row-key="deptXWardId"
      :editable="true"
      :data="deptXWardList"
      :loading="deptXWardListTableLoading"
      :columns="deptListTableColumnsConfig"
    />
    <div class="mt-4 text-right">
      <el-button @click="onCloseClick">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
