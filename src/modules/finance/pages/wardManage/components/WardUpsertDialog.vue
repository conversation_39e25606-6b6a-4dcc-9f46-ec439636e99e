<script setup lang="ts" name="WardUpsertDialog">
  import { ref, watch, computed } from 'vue';
  import { ProForm, ProDialog } from 'sun-biz';
  import type { FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { addOrg, updateOrgItem } from '@modules/system/api/org';
  import { useWardUpsertFormConfig } from '../config/useFormConfig';

  const props = defineProps<{
    mode: string;
    hospitalId: string | undefined;
    data: Partial<Org.Item> | undefined;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: Org.Item;
  }>();
  const parentOrgId = computed(() => props.hospitalId);
  const dialogRef = ref();
  const disabled = ref(false);
  const dialogForm = ref<Partial<Org.Item> | undefined>({});
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props.data,
    () => {
      disabled.value = props.mode === 'view';
      dialogForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          params.parentOrgId = params.parentOrgId || parentOrgId.value;
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addOrg(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await updateOrgItem(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };
  const formConfig = useWardUpsertFormConfig(disabled);
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${$t(`global:${props.mode}`)}${$t('wardManage.name', '病区')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="3"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
