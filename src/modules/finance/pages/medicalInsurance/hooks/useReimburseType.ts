import { ref } from 'vue';
import { queryReimburseTypeListByExample } from '@/modules/finance/api/reimburseType';

/** 获取报销类型 */
export function useReimburseType() {
  const loading = ref<boolean>(false);
  const reimburseTypeList = ref<ReimburseType.ReimburseTypeReqItem[]>([]);
  const getReimburseType = async (
    params: ReimburseType.ReimburseTypeReqQuery,
  ) => {
    loading.value = true;
    const [, res] = await queryReimburseTypeListByExample(params);
    loading.value = false;
    if (res?.success) {
      reimburseTypeList.value = res.data ?? [];
    }
  };
  return {
    loading,
    reimburseTypeList,
    getReimburseType,
  };
}
