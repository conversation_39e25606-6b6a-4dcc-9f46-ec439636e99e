<script setup lang="ts" name="dialogComponent">
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { useReimburseType } from '../hooks/useReimburseType';
  import { saveMedInsurance } from '@modules/finance/api/medicalInsurance';
  import { type FormInstance } from 'element-sun';
  import { useMedInsuranceConfig } from '../config/useFormConfigData';
  import { ref, nextTick, computed } from 'vue';
  import { ProForm, ProDialog } from 'sun-biz';

  type FormModelType = {
    medInsuranceNo: string | undefined;
    medInsuranceName: string | undefined;
    spellNo: string | undefined;
    wbNo: string | undefined;
    medInsurance2ndName: string | undefined;
    medInsuranceExtName: string | undefined;
    medInsuranceCode: string | undefined;
    enabledFlag: ENABLED_FLAG;
    reimburseTypeIds: string[];
  };

  const emit = defineEmits(['success']);
  const props = defineProps<{
    rowValue: MedInsurance.MedInsuranceReqItem | undefined;
    dialogTitle: string;
    hospitalId: string;
    disabledStatus: boolean;
  }>();

  const { getReimburseType, reimburseTypeList } = useReimburseType();

  const rowDisabledStatus = computed(() => props.disabledStatus);
  const hospitalId = computed(() => props.hospitalId);

  const dialogRef = ref(); //弹窗dialogRef
  const formModelRef = ref<{
    ref: FormInstance;
  }>(); //formRef
  const formModel = ref<FormModelType>({
    medInsuranceNo: undefined,
    medInsuranceName: undefined,
    spellNo: undefined,
    wbNo: undefined,
    medInsurance2ndName: undefined,
    medInsuranceExtName: undefined,
    medInsuranceCode: undefined,
    enabledFlag: ENABLED_FLAG.YES,
    reimburseTypeIds: [],
  });

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value.open();
      formModel.value = {
        medInsuranceNo: rowValue?.medInsuranceNo ?? undefined,
        medInsuranceName: rowValue?.medInsuranceName ?? undefined,
        spellNo: rowValue?.spellNo ?? undefined,
        wbNo: rowValue?.wbNo ?? undefined,
        medInsurance2ndName: rowValue?.medInsurance2ndName ?? undefined,
        medInsuranceExtName: rowValue?.medInsuranceExtName ?? undefined,
        medInsuranceCode: rowValue?.medInsuranceCode ?? undefined,
        enabledFlag: (rowValue?.enabledFlag ?? ENABLED_FLAG.YES) as number,
        reimburseTypeIds:
          rowValue?.reimburseTypeList?.map((item) => item.reimburseTypeId) ??
          [],
      };
    });
    await getReimburseType({
      hospitalId: hospitalId.value,
      enabledFlag: ENABLED_FLAG.YES,
    });
  };

  // 提交
  const handleConfirmSubmit = async () => {
    await formModelRef.value?.ref.validate();
    const params = {
      ...props.rowValue,
      hospitalId: hospitalId.value,
      ...formModel?.value,
    };
    return await saveMedInsurance(params);
  };

  // form配置
  const medInsuranceConfig = useMedInsuranceConfig({
    disabledStatus: rowDisabledStatus,
    hospitalId,
    getReimburseType,
    reimburseTypeList,
  });

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <ProForm
      ref="formModelRef"
      layout-mode="column"
      :column="3"
      v-model="formModel"
      :data="medInsuranceConfig"
    />
  </ProDialog>
</template>
