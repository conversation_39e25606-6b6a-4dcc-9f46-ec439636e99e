<script setup lang="ts" name="menuCard">
  import { ref, nextTick, onMounted, reactive } from 'vue';
  import { Title } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import Sortable from 'sortablejs';
  import { Close } from '@element-sun/icons-vue';
  import { type FormRules } from 'element-sun';

  const emits = defineEmits(['remove', 'removeMed', 'addDictMed']);
  const { cardItem, menuList, medList } = defineProps<{
    cardItem: MedInsurance.MenuXMedInsuranceReqItems;
    menuList: (Menu.MenuInfo & {
      menuGroupId: string;
      menuGroupName: string;
    })[];
    medList: MedInsurance.MedInsuranceReqItem[];
  }>();

  const { t } = useTranslation();

  const formRef = ref();
  const medListRef = ref();
  const medScrollCardRef = ref();

  const formModel = ref<
    MedInsurance.MenuXMedInsuranceReqItems & {
      editable: boolean;
    }
  >(
    cardItem as MedInsurance.MenuXMedInsuranceReqItems & {
      editable: boolean;
    },
  );
  const filteredMenuList = ref(menuList);

  const rules = reactive<
    FormRules<{
      menuId: string;
    }>
  >({
    menuId: [
      {
        required: true,
        message: t('global:placeholder.select.template', {
          name: t('sysXMenu.menuName', '菜单'),
        }),
        trigger: ['blur', 'change'],
      },
    ],
  });

  /** 菜单过滤方法 */
  const filterMethod = (val: string) => {
    filteredMenuList.value = menuList.filter((item) =>
      String(
        (
          item as Menu.MenuInfo & {
            menuGroupId: string;
            menuGroupName: string;
          }
        ).menuGroupName,
      )
        .toLowerCase()
        .includes(val.toLowerCase()),
    );
  };

  /** 过滤医保费别，去除重复的 */
  const getFilteredMedList = (
    currentItem: MedInsurance.DictMedInsuranceReqItems,
  ) => {
    // 获取已选择的医保费别ID列表（排除当前项）
    const selectedMedIds =
      formModel.value.dictMedInsuranceList
        ?.filter((item) => item !== currentItem && item.medInsuranceId)
        .map((item) => item.medInsuranceId) || [];

    // 过滤掉已选择的医保费别，但保留当前项
    return medList.filter(
      (med) => !selectedMedIds.includes(med.medInsuranceId),
    );
  };

  /** 移除当前的card */
  const removeCard = async () => {
    emits('remove', formModel.value);
  };

  /** 移除当前的card下边的医保费别 */
  const removeCardMed = async (index: number) => {
    emits('removeMed', formModel.value, index);
  };

  /** 变更医保费别 */
  const changeMed = async (val: string) => {
    const medObj = medList?.find((item) => item.medInsuranceId === val);

    const obj = formModel.value.dictMedInsuranceList?.find(
      (item) => item.medInsuranceId === val,
    );

    (obj as MedInsurance.DictMedInsuranceReqItems).medInsuranceName =
      medObj?.medInsuranceNameDisplay as string;
  };

  /** 新增费别 */
  const addDictMed = async () => {
    emits('addDictMed', formModel.value);
  };

  /** 初始化拖拽排序 */
  const initSortable = () => {
    if (!medListRef.value) return;

    Sortable.create(medListRef.value, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      handle: '.line-item', // 可以通过整个行项目拖动
      onEnd: (evt) => {
        // 获取拖拽前后的位置
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex) return;

        // 更新数据顺序
        const itemToMove = formModel.value.dictMedInsuranceList?.splice(
          oldIndex as number,
          1,
        )[0];
        formModel.value.dictMedInsuranceList?.splice(
          newIndex as number,
          0,
          itemToMove as MedInsurance.DictMedInsuranceReqItems,
        );
      },
    });
  };

  onMounted(() => {
    nextTick(() => {
      initSortable();
    });
  });

  defineExpose({
    medScrollCardRef,
    formRef,
  });
</script>
<template>
  <el-form
    ref="formRef"
    :model="formModel"
    :rules="rules"
    @keydown.enter="(e: Event) => e.preventDefault()"
  >
    <el-card body-class="relative p-2">
      <div class="flex items-center justify-between">
        <el-form-item prop="menuId" class="mb-0">
          <Title>
            <template #title>
              <span v-if="!formModel?.editable">{{ formModel.menuName }}</span>
              <el-select
                v-else
                class="w-40"
                v-model="formModel.menuId"
                filterable
                :filter-method="filterMethod"
                :placeholder="
                  $t('global:placeholder.select.template', {
                    name: $t('menu', '菜单'),
                  })
                "
              >
                <el-option
                  v-for="(item, index) in filteredMenuList"
                  :key="`${item.menuId}-${index}`"
                  :label="item.menuNameDisplay"
                  :value="item.menuId"
                >
                </el-option>
              </el-select>
            </template>
          </Title>
        </el-form-item>
        <el-icon class="cursor-pointer font-bold" @click="removeCard">
          <Close />
        </el-icon>
      </div>
      <el-form-item prop="dictMedInsuranceList" class="mb-0 mt-2">
        <div class="h-56 w-full">
          <el-scrollbar
            ref="medScrollCardRef"
            max-height="18.2rem"
            class="w-full"
            v-if="(formModel.dictMedInsuranceList ?? [])?.length > 0"
          >
            <div ref="medListRef" class="flex w-full flex-col gap-2">
              <div
                class="line-item flex w-full cursor-move items-center justify-between p-2"
                v-for="(item, index) in formModel.dictMedInsuranceList"
                :key="item.medInsuranceId"
              >
                <div class="flex items-center gap-2">
                  <span>{{ index + 1 }}</span>
                  <span v-if="item.medInsuranceId">
                    {{ item.medInsuranceName }}
                  </span>
                  <el-select
                    v-else
                    clearable
                    filterable
                    class="w-36"
                    @change="changeMed"
                    v-model="item.medInsuranceId"
                    :placeholder="
                      $t('global:placeholder.select.template', {
                        name: $t('medInsurance', '医保费别'),
                      })
                    "
                  >
                    <el-option
                      v-for="im in getFilteredMedList(item)"
                      :key="im.medInsuranceId"
                      :label="im.medInsuranceNameDisplay"
                      :value="im.medInsuranceId"
                    ></el-option>
                  </el-select>
                </div>
                <el-icon
                  class="cursor-pointer font-bold"
                  @click="removeCardMed(index)"
                >
                  <Close />
                </el-icon>
              </div>
            </div>
          </el-scrollbar>

          <div v-else class="flex flex-col items-center justify-center">
            <el-empty :description="$t('global:noData')" class="p-0"></el-empty>
          </div>
        </div>
      </el-form-item>
      <el-button @click="addDictMed" class="mt-2 w-full">
        {{ $t('global:add') }}
      </el-button>
    </el-card>
  </el-form>
</template>

<style lang="scss" scoped>
  .line-item {
    background: var(--el-color-primary-light-9);
    border-radius: 4px;
  }

  .sortable-ghost {
    background: var(--el-color-primary-light-7) !important;
    opacity: 0.5;
  }

  .sortable-chosen {
    background: var(--el-color-primary-light-8);
  }

  .sortable-drag {
    opacity: 0.8;
  }
</style>
