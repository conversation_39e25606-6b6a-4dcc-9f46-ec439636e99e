import { useFormConfig } from 'sun-biz';
import { Ref, ComputedRef } from 'vue';
import { MED_INSURANCE_CODE_NAME, ENABLED_FLAG } from '@/utils/constant';
export function useMedInsuranceConfig(options: {
  disabledStatus: ComputedRef<boolean>;
  hospitalId: ComputedRef<string>;
  reimburseTypeList: Ref<ReimburseType.ReimburseTypeReqItem[]>;
  getReimburseType: (
    params: ReimburseType.ReimburseTypeReqQuery,
  ) => Promise<void>;
}) {
  const { disabledStatus, hospitalId, reimburseTypeList, getReimburseType } =
    options;

  return useFormConfig({
    dataSetCodes: [MED_INSURANCE_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'medInsuranceNo',
        label: t('global:code'),
        component: 'input',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'medInsuranceName',
        label: t('global:name'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
      },
      {
        name: 'medInsurance2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:secondName'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
      },
      {
        name: 'medInsuranceExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:thirdName'),
            }),
        extraProps: {
          disabled: disabledStatus.value,
        },
      },
      {
        name: 'medInsuranceCode',
        label: t('medInsurance.table.codeNo', '代码'),
        component: 'select',
        placeholder: disabledStatus.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('medInsurance.table.codeNo', '代码'),
            }),
        extraProps: {
          filterable: true,
          disabled: disabledStatus.value,
          options: data?.value ? data.value[MED_INSURANCE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'reimburseTypeIds',
        label: t('medInsurance.reimburseType', '报销类型'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medInsurance.reimburseType', '报销类型'),
        }),
        extraProps: {
          disabled: disabledStatus.value,
          filterable: true,
          multiple: true,
          remote: true,
          remoteMethod: (keyWord: string) =>
            getReimburseType({
              keyWord,
              enabledFlag: ENABLED_FLAG.YES,
              hospitalId: hospitalId.value,
            }),
          options: reimburseTypeList.value,
          props: {
            label: 'reimburseTypeNameDisplay',
            value: 'reimburseTypeId',
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabledStatus.value,
        },
      },
    ],
  });
}
