import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(options: {
  queryMedInsuranceData: (
    params: MedInsurance.MedInsuranceReqQuery,
  ) => Promise<void>;
}) {
  const { queryMedInsuranceData } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        className: 'mb-0',
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        className: 'mb-0',
        extraProps: {
          className: 'w-32',
          clearable: false,
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'ml-0.5 mb-0',
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryMedInsuranceData({
                keyWord: (e.target as HTMLInputElement).value,
              } as unknown as MedInsurance.MedInsuranceReqQuery);
            }
          },
        },
      },
    ],
  });
  return data;
}
