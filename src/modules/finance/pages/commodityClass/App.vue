<script setup lang="ts" name="commodityClass">
  import { ref, computed } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { SelectOptions } from '@/typings/common.ts';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useCommodityCategoryTableConfig } from './config/useTableConfig.tsx';
  import { useCommodityCategorySearchFormConfig } from './config/useFormConfig.ts';
  import {
    Title,
    ProForm,
    ProTable,
    useFetchDataset,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    queryCommodityCategoryListByExample,
    updateCommodityCategoryEnabledFlagById,
    updateCommodityCategorySortByIds,
  } from '@modules/finance/api/comodityCategory';
  import CommodityCategoryUpsertDialog from '@/modules/finance/pages/commodityClass/components/CommodityCategoryUpsertDialog.vue';

  const { t } = useTranslation();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const searchParams = ref<ComodityCategory.QueryParams>({
    hospitalId: currentOrg?.orgId || '',
    commodityCategoryWayCode: '',
    enabledFlag: ENABLED_FLAG.ALL,
    keyWord: '',
  });
  const loading = ref(false);
  const draggableFlag = ref(true); //是否拖拽
  const commodityCategoryList = ref<ComodityCategory.ComodityCategoryInfo[]>(
    [],
  );
  const commodityCategorySelectData = ref<
    (SelectOptions & { commodityCategoryWayCode: string })[]
  >([]);
  const commodityCategoryUpsertParams =
    ref<ComodityCategory.ComodityCategoryUpsertParams>({});
  const commodityCategoryUpsertDialogRef = ref();
  const dataSetList = useFetchDataset([
    CodeSystemType.COMMODITY_CATEGORY_WAY_CODE,
    CodeSystemType.COMMODITY_CLASS_CODE,
  ]);
  const searchCommodityCategoryWayCode = computed(
    () => searchParams?.value?.commodityCategoryWayCode || '',
  );
  const commodityCategoryWayCodeData = computed(() =>
    (
      dataSetList?.value?.[CodeSystemType.COMMODITY_CATEGORY_WAY_CODE] || []
    ).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  const commodityClassCodeData = computed(() =>
    (dataSetList?.value?.[CodeSystemType.COMMODITY_CLASS_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  const queryCommodityCategoryData = async (
    data?: ComodityCategory.QueryParams,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      commodityCategoryWayCode:
        searchParams.value.commodityCategoryWayCode || undefined,
      keyWord: searchParams.value.keyWord || undefined,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryCommodityCategoryListByExample(params);
    loading.value = false;
    if (res?.success) {
      res.data?.sort(
        (
          a: ComodityCategory.ComodityCategoryInfo,
          b: ComodityCategory.ComodityCategoryInfo,
        ) => {
          return a.sort - b.sort;
        },
      );
      commodityCategoryList.value = res.data || [];
      draggableFlag.value = !!params.commodityCategoryWayCode;
      if (
        !searchParams.value.commodityCategoryWayCode &&
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL &&
        !searchParams.value.keyWord
      ) {
        commodityCategorySelectData.value = (res.data || []).map(
          (item: ComodityCategory.ComodityCategoryInfo) => ({
            label: item?.commodityCategoryName,
            value: item?.commodityCategoryId,
            commodityCategoryWayCode: item?.commodityCategoryWayCode,
          }),
        );
      }
    }
  };

  const onOpenCommodityCategoryDialog = (
    data?: ComodityCategory.ComodityCategoryInfo,
  ) => {
    if (data) {
      const {
        commodityCategoryId,
        commodityClassCode,
        commodityCategoryName,
        commodityCategory2ndName,
        commodityCategoryExtName,
        enabledFlag,
        spellNo,
        wbNo,
        hospitalId,
        commodityCategoryMapId,
        outCommodityCategoryId,
        inCommodityCategoryId,
        accCommodityCategoryId,
        fncCommodityCategoryId,
        mrCommodityCategoryId,
        commodityCategoryWayCode,
      } = data;
      commodityCategoryUpsertParams.value = {
        commodityCategoryId,
        commodityClassCode,
        commodityCategoryName,
        commodityCategory2ndName,
        commodityCategoryExtName,
        enabledFlag,
        spellNo,
        wbNo,
        hospitalId,
        commodityCategoryMapId,
        outCommodityCategoryId,
        inCommodityCategoryId,
        accCommodityCategoryId,
        fncCommodityCategoryId,
        mrCommodityCategoryId,
        commodityCategoryWayCode,
      };
    } else {
      commodityCategoryUpsertParams.value = { enabledFlag: ENABLED_FLAG.YES };
    }
    commodityCategoryUpsertDialogRef.value.dialogRef.open();
  };

  /** 拖拽排序 */
  const handleSortEnd = async (
    list: ComodityCategory.ComodityCategoryInfo[],
  ) => {
    const updateCommodityCategorySortList = list.map((item, index) => ({
      commodityCategoryId: item.commodityCategoryId,
      sort: index + 1,
    }));
    await updateCommodityCategorySortByIds({ updateCommodityCategorySortList });
    ElMessage({
      type: 'success',
      message: t('global:modify.sort.success'),
    });
    await queryCommodityCategoryData();
  };

  /** 启用状态切换 */
  const handleEnableSwitch = async (
    row: ComodityCategory.ComodityCategoryInfo,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.commodityCategoryName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        commodityCategoryId: row.commodityCategoryId,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateCommodityCategoryEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryCommodityCategoryData();
      }
    });
  };

  const searchConfig = useCommodityCategorySearchFormConfig(
    queryCommodityCategoryData,
    commodityCategoryWayCodeData,
  );
  const tableColumnsConfig = useCommodityCategoryTableConfig(
    searchCommodityCategoryWayCode,
    handleEnableSwitch,
    onOpenCommodityCategoryDialog,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('commodityCategory.list.title', '商品费用分类列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryCommodityCategoryData"
        />
      </div>
      <div>
        <el-button
          class="mr-2"
          type="primary"
          @click="onOpenCommodityCategoryDialog()"
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <ProTable
      row-key="commodityCategoryId"
      :loading="loading"
      :draggable="draggableFlag"
      :data="commodityCategoryList"
      :columns="tableColumnsConfig"
      @drag-end="handleSortEnd"
    />
  </div>
  <CommodityCategoryUpsertDialog
    ref="commodityCategoryUpsertDialogRef"
    :hospital-id="searchParams.hospitalId"
    :data="commodityCategoryUpsertParams"
    :commodity-category-way-code-data="commodityCategoryWayCodeData"
    :commodity-category-select-data="commodityCategorySelectData"
    :commodity-class-code-data="commodityClassCodeData"
    @success="queryCommodityCategoryData"
  />
</template>
