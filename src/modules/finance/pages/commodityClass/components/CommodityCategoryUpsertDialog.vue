<script setup lang="ts" name="CommodityCategoryUpsertDialog">
  import { ref, watch, computed } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ProForm, ProDialog } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  import {
    addCommodityCategory,
    updateCommodityCategoryById,
  } from '@modules/finance/api/comodityCategory';
  import { useCommodityCategoryUpsertFormConfig } from '../config/useFormConfig.ts';

  const props = defineProps<{
    hospitalId: string | undefined;
    data: ComodityCategory.ComodityCategoryUpsertParams;
    commodityCategoryWayCodeData: SelectOptions[];
    commodityCategorySelectData: (SelectOptions & {
      commodityCategoryWayCode: string;
    })[];
    commodityClassCodeData: SelectOptions[];
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: ComodityCategory.ComodityCategoryUpsertParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const commodityCategoryForm =
    ref<ComodityCategory.ComodityCategoryUpsertParams>();
  const isEdit = ref(false);
  const dialogCommodityCategorySelectData = ref<
    (SelectOptions & { commodityCategoryWayCode: string })[]
  >([]);
  const dialogCommodityCategoryWayCodeData = ref<SelectOptions[]>([]);
  const dialogCommodityClassCodeData = ref<SelectOptions[]>([]);
  const commodityCategoryFormWayCode = computed(
    () => commodityCategoryForm?.value?.commodityCategoryWayCode || '',
  );
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props,
    () => {
      isEdit.value = !!props.data.commodityCategoryId;
      commodityCategoryForm.value = cloneDeep(props.data);
      dialogCommodityCategorySelectData.value =
        props.commodityCategorySelectData;
      dialogCommodityCategoryWayCodeData.value =
        props.commodityCategoryWayCodeData;
      dialogCommodityClassCodeData.value = props.commodityClassCodeData;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...commodityCategoryForm.value,
            ...formRef?.value?.model,
            hospitalId: props.hospitalId,
          };
          let isSuccess = false;
          if (params.commodityCategoryId) {
            const [, res] = await updateCommodityCategoryById(params);
            isSuccess = res?.success;
          } else {
            const [, res] = await addCommodityCategory(params);
            isSuccess = res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                params.commodityCategoryId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const formConfig = useCommodityCategoryUpsertFormConfig(
    isEdit,
    commodityCategoryFormWayCode,
    dialogCommodityCategoryWayCodeData,
    dialogCommodityCategorySelectData,
    dialogCommodityClassCodeData,
  );
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${commodityCategoryForm && commodityCategoryForm.commodityCategoryId ? $t('global:edit') : $t('global:add')}${$t('commodityCategory.name', '商品费用分类')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="onConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="commodityCategoryForm"
      :column="2"
      :data="formConfig"
    />
  </ProDialog>
</template>
