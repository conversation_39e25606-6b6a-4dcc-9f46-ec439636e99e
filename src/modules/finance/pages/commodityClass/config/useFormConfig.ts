import { Ref } from 'vue';
import { ENABLED_FLAG, COMMODITY_CATEGORY_WAY_CODE } from '@/utils/constant';
import { SelectOptions } from '@/typings/common.ts';
import { useFormConfig } from 'sun-biz';

export function useCommodityCategorySearchFormConfig(
  queryCommodityCategoryData: (data?: ComodityCategory.QueryParams) => void,
  commodityCategoryWayCodeData: Ref<SelectOptions[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
      },
      {
        label: t(
          'commodityCategory.search.commodityCategoryWayCode',
          '分类方式',
        ),
        name: 'commodityCategoryWayCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'commodityCategory.search.commodityCategoryWayCode',
            '分类方式',
          ),
        }),
        extraProps: {
          options: commodityCategoryWayCodeData.value,
          className: 'w-60',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryCommodityCategoryData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryCommodityCategoryData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useCommodityCategoryUpsertFormConfig(
  isEdit: Ref<boolean>,
  commodityCategoryFormWayCode: Ref<string> | undefined,
  commodityCategoryWayCodeData: Ref<SelectOptions[]>,
  commodityCategorySelectData: Ref<
    (SelectOptions & { commodityCategoryWayCode: string })[]
  >,
  commodityClassCodeSelectData: Ref<SelectOptions[]>,
) {
  return useFormConfig({
    getData: (t) => {
      const data = [
        {
          label: t(
            'commodityCategory.form.commodityCategoryWayCode',
            '分类方式',
          ),
          name: 'commodityCategoryWayCode',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t(
              'commodityCategory.form.commodityCategoryWayCode',
              '分类方式',
            ),
          }),
          extraProps: {
            disabled: isEdit.value,
            options: commodityCategoryWayCodeData.value,
          },
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t(
                  'commodityCategory.form.commodityCategoryWayCode',
                  '分类方式',
                ),
              }),
              trigger: ['change', 'blur'],
            },
          ],
        },
        {
          label: t('commodityCategory.form.commodityCategoryName', '分类名称'),
          name: 'commodityCategoryName',
          autoConvertSpellNoAndWbNo: true,
          isFullWidth: true,
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t(
              'commodityCategory.form.commodityCategoryName',
              '分类名称',
            ),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t(
                  'commodityCategory.form.commodityCategoryName',
                  '分类名称',
                ),
              }),
              trigger: ['change', 'blur'],
            },
          ],
        },
        {
          label: t('global:secondName', '辅助名称'),
          name: 'commodityCategory2ndName',
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:secondName', '辅助名称'),
          }),
        },
        {
          label: t('global:thirdName', '扩展名称'),
          name: 'commodityCategoryExtName',
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:thirdName', '扩展名称'),
          }),
        },
        {
          name: 'spellNo',
          label: t('global:spellNo'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:spellNo'),
          }),
        },
        {
          name: 'wbNo',
          label: t('global:wbNo'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:wbNo'),
          }),
        },
        {
          label: t('commodityCategory.form.commodityClassCode', '商品大类'),
          name: 'commodityClassCode',
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('commodityCategory.form.commodityClassCode', '商品大类'),
          }),
          extraProps: {
            options: commodityClassCodeSelectData.value,
          },
        },
        {
          name: 'enabledFlag',
          label: t('global:enabledFlag'),
          component: 'switch',
          extraProps: {
            'inline-prompt': true,
            'active-value': ENABLED_FLAG.YES,
            'inactive-value': ENABLED_FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
          },
        },
        {
          label: t(
            'commodityCategory.form.outCommodityCategoryId',
            '门诊发票分类',
          ),
          name: 'outCommodityCategoryId',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t(
              'commodityCategory.form.outCommodityCategoryId',
              '门诊发票分类',
            ),
          }),
          extraProps: {
            options: commodityCategorySelectData.value.filter(
              (item) =>
                item.commodityCategoryWayCode ===
                COMMODITY_CATEGORY_WAY_CODE.OUTPATIENT_INVOICE,
            ),
          },
        },
        {
          label: t(
            'commodityCategory.form.inCommodityCategoryId',
            '住院发票分类',
          ),
          name: 'inCommodityCategoryId',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t(
              'commodityCategory.form.inCommodityCategoryId',
              '住院发票分类',
            ),
          }),
          extraProps: {
            options: commodityCategorySelectData.value.filter(
              (item) =>
                item.commodityCategoryWayCode ===
                COMMODITY_CATEGORY_WAY_CODE.INPATIENT_INVOICE,
            ),
          },
        },
        {
          label: t('commodityCategory.form.accCommodityCategoryId', '会计分类'),
          name: 'accCommodityCategoryId',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t(
              'commodityCategory.form.accCommodityCategoryId',
              '会计分类',
            ),
          }),
          extraProps: {
            options: commodityCategorySelectData.value.filter(
              (item) =>
                item.commodityCategoryWayCode ===
                COMMODITY_CATEGORY_WAY_CODE.ACCOUNTING,
            ),
          },
        },
        {
          label: t('commodityCategory.form.fncCommodityCategoryId', '财务分类'),
          name: 'fncCommodityCategoryId',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t(
              'commodityCategory.form.fncCommodityCategoryId',
              '财务分类',
            ),
          }),
          extraProps: {
            options: commodityCategorySelectData.value.filter(
              (item) =>
                item.commodityCategoryWayCode ===
                COMMODITY_CATEGORY_WAY_CODE.FINANCE,
            ),
          },
        },
        {
          label: t('commodityCategory.form.mrCommodityCategoryId', '病案分类'),
          name: 'mrCommodityCategoryId',
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('commodityCategory.form.mrCommodityCategoryId', '病案分类'),
          }),
          extraProps: {
            options: commodityCategorySelectData.value.filter(
              (item) =>
                item.commodityCategoryWayCode ===
                COMMODITY_CATEGORY_WAY_CODE.MEDICAL_RECORD,
            ),
          },
        },
      ];
      if (
        commodityCategoryFormWayCode?.value !==
        COMMODITY_CATEGORY_WAY_CODE.BASIC
      ) {
        data.splice(8, 5);
      }
      return data;
    },
  });
}
