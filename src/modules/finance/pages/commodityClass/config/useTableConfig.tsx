import { Ref } from 'vue';
import { ENABLED_FLAG, COMMODITY_CATEGORY_WAY_CODE } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

export function useCommodityCategoryTableConfig(
  searchCommodityCategoryWayCode: Ref<string> | undefined,
  handleEnableSwitch: (data: ComodityCategory.ComodityCategoryInfo) => void,
  onOpenCommodityCategoryDialog: (
    data?: ComodityCategory.ComodityCategoryInfo,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 60,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t(
            'commodityCategory.commodityCategoryTable.commodityCategoryWayDesc',
            '分类方式',
          ),
          prop: 'commodityCategoryWayDesc',
          minWidth: 120,
        },
        {
          label: t(
            'commodityCategory.commodityCategoryTable.commodityCategoryName',
            '费用分类名称',
          ),
          prop: 'commodityCategoryName',
          minWidth: 140,
        },
        {
          label: t('global:secondName', '辅助名称'),
          prop: 'commodityCategory2ndName',
          minWidth: 150,
        },
        {
          label: t('global:thirdName', '扩展名称'),
          prop: 'commodityCategoryExtName',
          minWidth: 150,
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 100,
          render: (row: ComodityCategory.ComodityCategoryInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          minWidth: 80,
          render: (row: ComodityCategory.ComodityCategoryInfo) => {
            return (
              <div class={'flex justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => onOpenCommodityCategoryDialog(row)}
                >
                  {t('global:edit')}
                </el-button>
              </div>
            );
          },
        },
      ];
      if (
        searchCommodityCategoryWayCode?.value ===
        COMMODITY_CATEGORY_WAY_CODE.BASIC
      ) {
        data.splice(
          5,
          0,
          ...[
            {
              label: t(
                'commodityCategory.commodityCategoryTable.outCommodityCategoryName',
                '门诊发票分类',
              ),
              prop: 'outCommodityCategoryName',
              minWidth: 140,
            },
            {
              label: t(
                'commodityCategory.commodityCategoryTable.inCommodityCategoryName',
                '住院发票分类',
              ),
              prop: 'inCommodityCategoryName',
              minWidth: 140,
            },
            {
              label: t(
                'commodityCategory.commodityCategoryTable.accCommodityCategoryName',
                '会计分类',
              ),
              prop: 'accCommodityCategoryName',
              minWidth: 140,
            },
            {
              label: t(
                'commodityCategory.commodityCategoryTable.fncCommodityCategoryId',
                '财务分类',
              ),
              prop: 'fncCommodityCategoryName',
              minWidth: 140,
            },
            {
              label: t(
                'commodityCategory.commodityCategoryTable.mrCommodityCategoryName',
                '病案分类',
              ),
              prop: 'mrCommodityCategoryName',
              minWidth: 140,
            },
          ],
        );
      }
      return data;
    },
  });
}
