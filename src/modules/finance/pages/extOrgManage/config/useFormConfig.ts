import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';

export function useExtOrgSearchFormConfig(
  orgTypeCodeData: Ref<SelectOptions[]>,
  queryOrgData: (data?: Org.queryReqParams & { orgTypeCode?: string }) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'orgTypeCode',
        label: t('extOrgManage.search.orgTypeCode', '组织类型'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('extOrgManage.search.orgTypeCode', '组织类型'),
        }),
        triggerModelChange: true,
        extraProps: {
          filterable: true,
          clearable: false,
          options: orgTypeCodeData.value,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryOrgData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryOrgData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useExtOrgUpsertFormConfig(disabled: Ref<boolean>) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('extOrgManage.form.orgNo', '组织编码'),
        name: 'orgNo',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('extOrgManage.form.orgNo', '组织编码'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('extOrgManage.form.orgNo', '组织编码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('extOrgManage.form.orgName', '组织名称'),
        name: 'orgName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('extOrgManage.form.orgName', '组织名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('extOrgManage.form.orgName', '组织名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:secondName', '辅助名称'),
        name: 'org2ndName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:secondName', '辅助名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('global:thirdName', '扩展名称'),
        name: 'orgExtName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:thirdName', '扩展名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        isFullWidth: true,
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'orgDesc',
        isFullWidth: true,
        label: t('extOrgManage.form.orgDesc', '组织简介'),
        component: 'input',
        type: 'textarea',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('extOrgManage.form.orgDesc', '组织简介'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
    ],
  });
}
