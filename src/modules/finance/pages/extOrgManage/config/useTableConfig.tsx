import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

export function useExtOrgTableConfig(
  handleEnableSwitch: (data: Org.Item) => void,
  onOpenOrgDialog: (mode: string, data: Org.Item) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('extOrgManage.table.orgNo', '组织编码'),
        prop: 'orgNo',
        minWidth: 140,
      },
      {
        label: t('extOrgManage.table.orgName', '组织名称'),
        prop: 'orgName',
        minWidth: 140,
      },
      {
        label: t('global:secondName', '辅助名称'),
        prop: 'org2ndName',
        minWidth: 130,
      },
      {
        label: t('global:thirdName', '扩展名称'),
        prop: 'orgExtName',
        minWidth: 130,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: Org.Item) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('extOrgManage.table.orgTypeDesc', '组织类型'),
        prop: 'orgTypeDesc',
        minWidth: 100,
      },
      {
        label: t('extOrgManage.table.orgDesc', '组织简介'),
        prop: 'orgDesc',
        minWidth: 220,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        render: (row: Org.Item) => {
          return (
            <el-button
              type="primary"
              link={true}
              onClick={() => onOpenOrgDialog('edit', row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
