<script setup lang="ts" name="extOrgManage">
  import { ref, computed, onMounted } from 'vue';
  import { ENABLED_FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { Title, ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useTranslation } from 'i18next-vue';
  import { ElMessageBox } from 'element-sun';
  import { useExtOrgTableConfig } from './config/useTableConfig.tsx';
  import { useExtOrgSearchFormConfig } from './config/useFormConfig.ts';
  import {
    queryOrgListByExampleFlat,
    updateOrgEnabledFlag,
  } from '@modules/system/api/org';
  import OrgUpsertDialog from '@/modules/finance/pages/extOrgManage/components/OrgUpsertDialog.vue';

  type ExtOrgQueryParams = Org.queryReqFlatPageParams & {
    orgTypeCode?: string;
  };

  const { t } = useTranslation();
  const searchParams = ref<ExtOrgQueryParams>({
    orgTypeCode: ORG_TYPE_CODE.WORK_UNIT,
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
    pageNumber: 1,
    pageSize: 25,
  });
  const loading = ref(false);
  const orgList = ref<Org.Item[]>([]);
  const orgUpsertParams = ref<Org.Item>();
  const orgUpsertDialogRef = ref();
  const orgUpsertDialogMode = ref('');
  const orgTableRef = ref();
  const total = ref(0);

  const dataSetList = useFetchDataset([CodeSystemType.ORG_TYPE_CODE]);
  // 只使用 5工作单位、6记账单位、7生产厂家、8供货单位
  const orgTypeCodeData = computed(() =>
    (dataSetList?.value?.[CodeSystemType.ORG_TYPE_CODE] || [])
      .filter((item) =>
        [
          ORG_TYPE_CODE.WORK_UNIT,
          ORG_TYPE_CODE.ACCOUNTING_UNIT,
          ORG_TYPE_CODE.MANUFACTURER,
          ORG_TYPE_CODE.SUPPLIER,
        ].includes(item?.dataValueNo),
      )
      .map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      })),
  );

  async function queryOrgData(data?: ExtOrgQueryParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
      orgTypeCodes: searchParams.value.orgTypeCode
        ? [searchParams.value.orgTypeCode]
        : undefined,
      orgTypeCode: undefined,
    };
    const [, res] = await queryOrgListByExampleFlat(params);
    loading.value = false;
    if (res?.success) {
      orgList.value = res.data || [];
      total.value = res.total || 0;
    }
  }

  const onOpenOrgDialog = (mode: string, data?: Org.Item) => {
    orgUpsertDialogMode.value = mode;
    if (mode === 'add') {
      orgUpsertParams.value = {
        enabledFlag: ENABLED_FLAG.YES,
        orgTypeCode: searchParams.value.orgTypeCode,
      } as Org.Item;
    } else if (mode === 'edit' && data) {
      orgUpsertParams.value = {
        ...data,
      };
    }
    orgUpsertDialogRef.value.dialogRef.open();
  };

  const handleEnableSwitch = async (row: Org.Item) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.orgName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await updateOrgEnabledFlag({
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
        orgId: row.orgId,
      });
      if (res?.success) {
        await queryOrgData();
      }
    });
  };

  const searchConfig = useExtOrgSearchFormConfig(orgTypeCodeData, queryOrgData);
  const tableColumnsConfig = useExtOrgTableConfig(
    handleEnableSwitch,
    onOpenOrgDialog,
  );

  onMounted(() => {
    queryOrgData();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('extOrgManage.list.title', '外部组织列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :show-search-button="true"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryOrgData"
        />
      </div>
      <el-button class="mr-2" type="primary" @click="onOpenOrgDialog('add')">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="orgTableRef"
      row-key="orgId"
      :data="orgList"
      :loading="loading"
      :columns="tableColumnsConfig"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      @current-page-change="
        (val: number) => {
          queryOrgData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryOrgData({
            pageSize: val,
          });
        }
      "
    />
  </div>
  <OrgUpsertDialog
    ref="orgUpsertDialogRef"
    :mode="orgUpsertDialogMode"
    :data="orgUpsertParams"
    @success="queryOrgData"
  />
</template>
