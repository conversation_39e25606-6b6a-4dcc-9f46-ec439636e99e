<script setup lang="ts" name="operationDialog">
  import { FLAG } from '@/utils/constant.ts';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ElMessage } from 'element-sun';
  import { useGetPayWay } from '../hooks/useGetPayWay.ts';
  import { useTranslation } from 'i18next-vue';
  import { useGetInterface } from '@/hooks/useGetInterface';
  import { useDialogFormConfig } from '../config/useFormConfig.tsx';
  import { ref, nextTick, computed } from 'vue';
  import { addPayWay, updatePayWayById } from '@/modules/finance/api/payWay.ts';
  import { useDialogTableColumnsConfig } from '../config/useTableColumnsConfig.tsx';
  import { Title, ProDialog, ProForm, ProTable, TableRef } from 'sun-biz';

  const props = defineProps<{
    rowValue: PayWay.PayWayReqItem;
    dialogTitle: string;
  }>();

  export type DialogFormModel = {
    payWayNo: string | undefined;
    payWayName: string | undefined;
    payWay2ndName: string | undefined;
    payWayExtName: string | undefined;
    payWayTypeCode: string | undefined;
    payWayCode: string | undefined;
    needAccountInfoFlag: FLAG;
    interfaceId: string | undefined;
    scanPayFlag: FLAG;
    autoGetPayAmtFlag: FLAG;
    needAccountUnitFlag: FLAG;
    originalReturnMaxDay: number;
    enabledFlag: FLAG;
    spellNo: string | undefined;
    wbNo: string | undefined;
  };

  const emit = defineEmits(['success']);
  const { payWayList, getPayWayList } = useGetPayWay();
  const { interfaceList, getInterfaceList } = useGetInterface();
  const { t } = useTranslation();

  const loading = ref<boolean>(false); // 加载状态
  const dialogFormModel = ref<DialogFormModel>(); // 检索条件数据
  const tableData = ref<
    (PayWay.RefundPayWayReqItem & {
      editable: boolean;
    })[]
  >([]); // 表格数据

  const dialogRef = ref();
  const dialogFormRef = ref();
  const tableColumnsRef = ref<TableRef>();

  const operationType = computed(() => {
    return props.rowValue?.payWayId ? 'update' : 'add';
  });

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value.open();
      dialogFormModel.value = {
        payWayNo: rowValue?.payWayNo ?? undefined,
        payWayName: rowValue?.payWayName ?? undefined,
        payWay2ndName: rowValue?.payWay2ndName ?? undefined,
        payWayExtName: rowValue?.payWayExtName ?? undefined,
        payWayTypeCode: rowValue?.payWayTypeCode ?? undefined,
        payWayCode: rowValue?.payWayCode ?? undefined,
        needAccountInfoFlag: rowValue?.needAccountInfoFlag ?? FLAG.NO,
        interfaceId: rowValue?.interfaceId ?? undefined,
        scanPayFlag: rowValue?.scanPayFlag ?? FLAG.NO,
        autoGetPayAmtFlag: rowValue?.autoGetPayAmtFlag ?? FLAG.NO,
        needAccountUnitFlag: rowValue?.needAccountUnitFlag ?? FLAG.NO,
        originalReturnMaxDay: rowValue?.originalReturnMaxDay ?? -1,
        enabledFlag: rowValue?.enabledFlag ?? FLAG.YES,
        spellNo: rowValue?.spellNo ?? undefined,
        wbNo: rowValue?.wbNo ?? undefined,
      };
      tableData.value = (rowValue?.refundPayWayList ??
        []) as unknown as (PayWay.RefundPayWayReqItem & {
        editable: boolean;
      })[];
    });
    await getPayWayList({
      enabledFlag: FLAG.YES,
    });
  };

  // 提交支付方式
  const handleConfirmSubmit = async () => {
    await Promise.all([
      dialogFormRef.value?.ref.validate(),
      tableColumnsRef.value?.formRef.validate(),
    ]);
    const obj = (tableData.value ?? []).find((item) => item.editable === true);
    if (obj) {
      ElMessage.error(t('notSave.refundPayWay', '存在未保存的可退支付方式'));
      return [Error('error')];
    }
    tableData.value.map((item, index) => {
      item.sort = index + 1;
      return item;
    });
    if (operationType.value === 'add') {
      return await addPayWay({
        ...dialogFormModel.value,
        refundPayWayList: tableData.value,
      } as PayWay.AddPayWayReqParams);
    } else {
      return await updatePayWayById({
        payWayId: props.rowValue?.payWayId,
        sort: props.rowValue?.sort,
        ...dialogFormModel.value,
        refundPayWayList: tableData.value,
      } as PayWay.UpdatePayWayReqParams);
    }
  };

  // 检索条件配置数据
  const dialogFormConfig = useDialogFormConfig({
    operationType: operationType,
    dialogFormModel: dialogFormModel,
    getInterfaceList: getInterfaceList,
    interfaceList: interfaceList,
  });
  // 表格配置数据
  const { tableColumns, addItem } = useDialogTableColumnsConfig({
    tableRef: tableColumnsRef,
    data: tableData,
    getPayWayList: getPayWayList,
    payWayList: payWayList,
  });

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    class="w-3/4"
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <div class="flex h-full flex-col">
      <ProForm
        ref="dialogFormRef"
        v-model="dialogFormModel"
        :data="dialogFormConfig"
        :column="4"
      />
      <div class="mb-2 flex justify-between">
        <Title :title="$t('payWay.back.list', '可退支付方式列表')" />
        <el-button
          type="primary"
          @click="
            () =>
              addItem({
                editable: true,
              })
          "
          >{{ $t('global:add') }}</el-button
        >
      </div>
      <ProTable
        ref="tableColumnsRef"
        :columns="tableColumns"
        :data="tableData"
        :loading="loading"
        :editable="true"
        row-key="rowKey"
      />
    </div>
  </ProDialog>
</template>
