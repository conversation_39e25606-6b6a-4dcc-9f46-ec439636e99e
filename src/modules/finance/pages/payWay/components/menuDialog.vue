<script lang="ts" name="menuDialog" setup>
  import { FLAG } from '@/utils/constant';
  import { ProDialog, Title } from 'sun-biz';
  import { useMenuList } from '../hooks/useMenuList';
  import { useTranslation } from 'i18next-vue';
  import { nextTick, reactive, ref } from 'vue';
  import { filterSelectData, generateUUID } from '@sun-toolkit/shared';
  import { ElMessage, ElMessageBox, type FormInstance } from 'element-sun';
  import {
    queryMenuPayWayListByExample,
    queryPayWayListByExample,
    saveMenuPayWay,
  } from '@/modules/finance/api/payWay';
  import MenuCard from './menuCard.vue';

  const props = defineProps<{
    dialogTitle?: string;
  }>();

  const { t } = useTranslation();
  const emit = defineEmits(['success']);
  const { getMenuList, menuFilterList } = useMenuList();

  // const menu = ref([]); //当前查询的菜单Id
  const menuPayWayList = ref<PayWay.MenuPayWayReqItem[]>([]); //菜单支付方式列表
  const payWayList = ref<PayWay.PayWayReqItem[]>([]);
  const cascaderProps = ref({
    label: 'menuNameDisplay',
    value: 'menuId',
    children: 'subSysXMenuList',
  }); //菜单树形结构

  const dialogRef = ref();
  const scrollBarRef = ref();
  const scrollCardRef = ref();
  const menuCardRef = reactive([]);
  // 打开弹窗
  const openDialog = async () => {
    nextTick(async () => {
      dialogRef.value.open();
    });
    await getMenuPayWayList();
    await getPayWayList();
    await getMenuList();
  };

  // 获取菜单卡片配置
  const getMenuPayWayList = async (params: PayWay.MenuPayWayReqParams = {}) => {
    const [, res] = await queryMenuPayWayListByExample(params);
    if (res?.success) {
      menuPayWayList.value = res.data ?? [];
    }
  };

  // 获取支付方式
  const getPayWayList = async (params: PayWay.PayWayReqParams = {}) => {
    const [, res] = await queryPayWayListByExample({
      enabledFlag: FLAG.YES,
      ...params,
    });
    if (res?.success) {
      payWayList.value = res.data ?? [];
    }
  };

  // 添加菜单项
  const addCard = async () => {
    menuPayWayList.value.push({
      key: generateUUID(),
      editable: true,
      defaultMultiPayFlag: FLAG.NO,
      maxDisplayCount: 0,
      menuPayWayDtList: [],
    } as unknown as PayWay.MenuPayWayReqItem);
    // 等待 DOM 更新
    nextTick(() => {
      // 获取滚动容器并设置滚动到底部
      const scrollbarDiv = scrollCardRef.value as HTMLDivElement;
      const scrollbar = scrollBarRef.value.wrapRef as HTMLDivElement;
      if (scrollbarDiv) {
        scrollbar.style.scrollBehavior = 'smooth';
        scrollbarDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
        scrollbar.scrollTop = scrollbarDiv.scrollHeight;
      }
    });
  };

  // 检索菜单
  // const handleChangeMenu = async (value: string) => {
  //   await getMenuPayWayList({
  //     menuId: value,
  //   });
  // };

  // 提交
  const handleConfirmSubmit = async () => {
    let flag: boolean = false;
    await Promise.all(
      menuCardRef.map((ref) =>
        (
          ref as {
            formRef: FormInstance;
          }
        )?.formRef?.validate?.(),
      ),
    );
    menuPayWayList.value.forEach((item) => {
      if (item.menuPayWayDtList.length === 0) {
        flag = true;
        ElMessage.error(
          t('payWay.add.validate', '存在支付方式为空的菜单，请新增支付方式'),
        );
        return [new Error('error')];
      }
      item.menuPayWayDtList.map((im, ix) => {
        im.sort = ix + 1;
        if (im.payWayId === undefined) {
          flag = true;
          ElMessage.error(
            t(
              'payWay.select.validate',
              '存在未选择支付方式的菜单，请选择支付方式',
            ),
          );
          return [new Error('error')];
        }
        return im;
      });
    });
    if (!flag) {
      return await saveMenuPayWay({
        menuPayWayMtList: menuPayWayList.value,
      } as unknown as PayWay.PayWaySaveMenuReqParams);
    }
  };

  // 移除菜单项
  const handleRemove = (item: PayWay.MenuPayWayReqItem) => {
    const cardItemIndex = menuPayWayList.value.findIndex((i) =>
      i.menuId
        ? i.menuId === item.menuId
        : (
            i as unknown as {
              key: string;
            }
          ).key ===
          (
            item as unknown as {
              key: string;
            }
          ).key,
    );

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:remove'),
        name: item.menuNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(() => {
      menuPayWayList.value.splice(cardItemIndex, 1);
      ElMessage.success(t('remove.success', '移除成功'));
    });
  };

  // 移除菜单下边的支付方式
  const handleRemoveMenuPayWay = (obj: {
    cardItem: PayWay.MenuPayWayReqItem;
    index: number;
  }) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} {{name}} 吗？', {
        action: t('global:remove'),
        name: obj.cardItem.menuPayWayDtList[obj.index].payWayName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(() => {
      obj.cardItem.menuPayWayDtList.splice(obj.index, 1);
      ElMessage.success(t('remove.success', '移除成功'));
    });
  };

  // 添加菜单下边的支付方式
  const addMenuPayWay = async (cardItem: PayWay.MenuPayWayReqItem) => {
    const cardItemIndex = menuPayWayList.value.findIndex((i) =>
      i.menuId
        ? i.menuId === cardItem.menuId
        : (
            i as unknown as {
              key: string;
            }
          ).key ===
          (
            cardItem as unknown as {
              key: string;
            }
          ).key,
    );
    (
      (menuPayWayList.value[cardItemIndex]?.menuPayWayDtList ??
        []) as unknown as {
        key: string;
        defaultFlag: FLAG;
        editable: boolean;
      }[]
    ).push({
      editable: true,
      defaultFlag: FLAG.NO,
      key: generateUUID(),
    });
  };

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          menuPayWayList = [];
          done();
        });
      }
    "
    :close-on-click-modal="false"
    :confirm-fn="
      () => handleConfirmSubmit() as unknown as Promise<[never, unknown]>
    "
    :destroy-on-close="true"
    :title="props.dialogTitle ?? ''"
    class="w-4/5"
    @success="() => emit('success')"
  >
    <div class="flex justify-between">
      <Title :title="t('select.menu', '选择菜单')" />
      <!-- <el-form-item :label="$t('system.menu', '系统菜单')">
        <el-select
          v-model="menu"
          :clearable="true"
          :filterable="true"
          :placeholder="$t('global:placeholder.select')"
          class="w-60"
          @change="handleChangeMenu"
        >
          <el-option
            v-for="item in menuFilterList"
            :key="item.menuGroupId"
            :label="(item as any).menuGroupName"
            :value="item.menuGroupId"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-button type="primary" @click="addCard"
        >{{ $t('global:add') }}
      </el-button>
    </div>
    <div class="overflow-y-auto">
      <el-scrollbar ref="scrollBarRef" height="53vh">
        <div ref="scrollCardRef" class="grid grid-cols-3 gap-4 pt-2">
          <MenuCard
            v-for="(item, index) in menuPayWayList"
            :key="item.menuId ?? (item as any).key"
            :ref="(el) => ((menuCardRef as any)[index] = el)"
            :card-item="item"
            :cascader-props="cascaderProps"
            :menu-filter-list="
              filterSelectData(
                menuFilterList,
                menuPayWayList.filter((im) => im.menuId !== item.menuId),
                'menuId',
              )
            "
            :pay-way-list="payWayList"
            @remove="handleRemove"
            @remove-menu-pay-way="handleRemoveMenuPayWay"
            @add-menu-pay-way="addMenuPayWay"
          />
        </div>
      </el-scrollbar>
    </div>
  </ProDialog>
</template>
