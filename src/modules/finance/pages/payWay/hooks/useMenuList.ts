import { ref, computed } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryMenuStructListByExample } from '@/modules/system/api/menu';

export function extractMenu(sourceArray: Menu.SystemInfo[]) {
  const result: Menu.MenuInfo[] = [];
  const seenIds = new Set();

  sourceArray.forEach((itm) => {
    itm.sysXMenuList?.forEach((item) => {
      if (item.menuFlag === FLAG.YES && !seenIds.has(item.menuId)) {
        seenIds.add(item.menuId);
        result.push(item);
      } else if (item.menuFlag === FLAG.NO && item.subSysXMenuList) {
        item.subSysXMenuList.forEach((im) => {
          if (im.menuFlag === FLAG.YES && !seenIds.has(im.menuId)) {
            seenIds.add(im.menuId);
            result.push({
              menuGroupId: im.menuId,
              menuGroupName:
                // itm.sysNameDisplay + '/' + item.menuName + '/' + im.menuName,
                im.menuNameDisplay,
              ...im,
            } as unknown as Menu.MenuInfo);
          }
        });
      }
    });
  });

  // 从result中过滤掉menuGroupId和menuGroupName都不存在的项
  const filteredResult = (
    result as (Menu.MenuInfo & {
      menuGroupId: string;
      menuGroupName: string;
    })[]
  ).filter(
    (
      item: Menu.MenuInfo & {
        menuGroupId: string;
        menuGroupName: string;
      },
    ) => item.menuGroupId && item.menuGroupName,
  );

  return filteredResult;
}

export function useMenuList() {
  const loading = ref<boolean>(false);
  const menuList = ref<Menu.SystemInfo[]>([]);
  const menuFilterList = computed(() => {
    // const filterMenuList = menuList.value.filter(
    //   (item) => item.sysId === '1611936373814706176',
    // );
    // return extractMenu(filterMenuList[0]?.sysXMenuList ?? []);
    return extractMenu(menuList.value);
  });
  const getMenuList = async (
    params: {
      sysId?: string;
      enabledFlag?: 0 | 1;
      userId?: string;
    } = {},
  ) => {
    loading.value = true;
    const [, res] = await queryMenuStructListByExample({
      ...params,
      enabledFlag: FLAG.YES,
    });
    loading.value = false;
    if (res?.success) {
      menuList.value = res.data ?? [];
    }
  };

  return {
    menuList,
    getMenuList,
    loading,
    menuFilterList,
  };
}
