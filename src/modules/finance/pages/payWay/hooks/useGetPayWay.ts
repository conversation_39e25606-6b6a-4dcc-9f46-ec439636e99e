import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryPayWayListByExample } from '@/modules/finance/api/payWay.ts';
export function useGetPayWay() {
  const loading = ref<boolean>(false);
  const payWayList = ref<PayWay.PayWayReqItem[]>([]);
  const result = ref();
  const getPayWayList = async (params: PayWay.PayWayReqParams) => {
    loading.value = true;
    const [, res] = await queryPayWayListByExample({
      ...params,
      enabledFlag:
        params.enabledFlag === FLAG.ALL ? undefined : params.enabledFlag,
    });
    loading.value = false;
    result.value = res;
    if (res?.success) {
      payWayList.value = res?.data ?? [];
    }
  };
  return {
    loading,
    payWayList,
    getPayWayList,
    result,
  };
}
