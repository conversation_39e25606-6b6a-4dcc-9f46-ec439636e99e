import { Ref } from 'vue';
import { FLAG } from '@/utils/constant.ts';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { filterSelectData } from '@sun-toolkit/shared';
export function useTableColumnsConfig(options: {
  handleEnableSwitch: (row: PayWay.PayWayReqItem) => Promise<void>;
  operationFn: (row: PayWay.PayWayReqItem) => Promise<void>;
}) {
  const { handleEnableSwitch, operationFn } = options;
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('payWay.payWayNo', '支付方式编码'),
        prop: 'payWayNo',
        minWidth: 120,
      },
      {
        label: t('payWay.payWayName', '支付方式名称'),
        prop: 'payWayName',
        minWidth: 150,
      },
      {
        label: t('payWay.payWay2ndName', '支付方式辅助名称'),
        prop: 'payWay2ndName',
        minWidth: 150,
      },
      {
        label: t('payWay.payWayExtName', '支付方式扩展名称'),
        prop: 'payWayExtName',
        minWidth: 150,
      },
      {
        label: t('payWay.interfaceName', '支付接口'),
        prop: 'interfaceName',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: PayWay.PayWayReqItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: PayWay.PayWayReqItem) => {
          return (
            <el-button
              type="primary"
              link={true}
              onClick={() => operationFn(row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
  return { tableColumns };
}

export function useDialogTableColumnsConfig(options: {
  tableRef: Ref<TableRef, TableRef>;
  data: Ref<
    (PayWay.RefundPayWayReqItem & {
      editable: boolean;
    })[]
  >;
  getPayWayList: (params: PayWay.PayWayReqParams) => Promise<void>;
  payWayList: Ref<PayWay.PayWayReqItem[]>;
}) {
  const { tableRef, data, getPayWayList, payWayList } = options;
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'rowKey',
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        width: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('payWay.refundPayWay', '退款方式'),
        prop: 'refundPayWayId',
        minWidth: 100,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('payWay.refundPayWay', '退款方式'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: PayWay.RefundPayWayReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable === true) {
            return (
              <el-select
                v-model={row.refundPayWayId}
                placeholder={t('global:placeholder.select.template', {
                  name: t('payWay.refundPayWay', '退款方式'),
                })}
                onChange={(val: string) => {
                  row.refundPayWayId = val;
                  const obj = payWayList.value.find(
                    (item) => item.payWayId === row.refundPayWayId,
                  );
                  row.refundPayWayName = obj?.payWayNameDisplay;
                }}
                filterable={true}
                remote={true}
                remote-method={async (val: string) => {
                  await getPayWayList({
                    enabledFlag: FLAG.YES,
                    keyWord: val,
                  });
                }}
              >
                {filterSelectData(
                  payWayList.value,
                  data.value.filter(
                    (item) => item.refundPayWayId !== row.refundPayWayId,
                  ),
                  'refundPayWayId',
                  'payWayId',
                )?.map((item: PayWay.PayWayReqItem) => (
                  <el-option
                    key={item?.payWayId}
                    label={item?.payWayNameDisplay}
                    value={item?.payWayId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.refundPayWayName}</>;
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 150,
        render: (
          row: PayWay.RefundPayWayReqItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => delItem($index)}
                >
                  {t('global:remove')}
                </el-button>
              )}
            </>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem };
}
