import { WarningFilled } from '@element-sun/icons-vue';
import { useFormConfig } from 'sun-biz';
import { useTranslation } from 'i18next-vue';
import { DialogFormModel } from '../components/operationDialog.vue';
import { ComputedRef, Ref } from 'vue';
import { FLAG, PAY_WAY_CODE_NAME } from '@/utils/constant';
import { InterfaceReqItem, InterfaceReqParams } from '@/api/types';
export function useDialogFormConfig(options: {
  dialogFormModel: Ref<DialogFormModel | undefined>;
  operationType: ComputedRef<string>;
  getInterfaceList: (params: InterfaceReqParams) => Promise<void>;
  interfaceList: Ref<InterfaceReqItem[]>;
}) {
  const { operationType, dialogFormModel, getInterfaceList, interfaceList } =
    options;
  const { t } = useTranslation();
  // 原路退费周期校验
  const returnDayValidator = (
    rule: unknown,
    value: number | string,
    callback: (error?: Error | undefined) => void,
  ) => {
    if (!value && value !== 0) {
      return callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('payWay.originalReturnMaxDay', '原路退费周期'),
          }),
        ),
      );
    }
    const reg = /^[+-]?\d+$/; // 匹配正负整数
    if (!reg.test(value as string)) {
      callback(
        new Error(
          t('payWay.originalReturnMaxDay.valid', '原路退费周期需为整数'),
        ),
      );
    } else {
      callback();
    }
  };

  const data = useFormConfig({
    dataSetCodes: [PAY_WAY_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'payWayNo',
        label: t('global:code'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:code'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: operationType.value === 'update' ? true : false,
        },
      },
      {
        name: 'payWayName',
        label: t('global:name'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'payWay2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'payWayExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'payWayCode',
        label: t('payWay.payWayCode', '支付方式代码'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('payWay.payWayCode', '支付方式代码'),
        }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[PAY_WAY_CODE_NAME] : [],
        },
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        name: 'originalReturnMaxDay',
        label: t('payWay.originalReturnMaxDay', '原路退费周期'),
        rules: [
          {
            required: true,
            validator: returnDayValidator,
            trigger: ['blur', 'change'],
          },
        ],
        render: () => {
          return (
            <div class="flex w-full items-center gap-x-2">
              <el-input
                class="flex w-full flex-1"
                clearable={true}
                modelValue={dialogFormModel.value?.originalReturnMaxDay}
                placeholder={t('global:placeholder.input.template', {
                  content: t('payWay.originalReturnMaxDay', '原路退费周期'),
                })}
                onUpdate:modelValue={(value: number) => {
                  console.log(value);

                  (
                    dialogFormModel.value as DialogFormModel
                  ).originalReturnMaxDay = value;
                }}
              />
              <div>{t('day', '天')}</div>
              <el-tooltip
                effect="dark"
                content={t(
                  'payWay.originalReturnMaxDay.tip',
                  '<0始终支持原路退，0始终不支持原路退，>0退费周期内可原路退',
                )}
                placement="top-end"
              >
                <el-icon size={18}>
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </div>
          );
        },
      },
      {
        name: 'interfaceId',
        label: t('payWay.interfaceId', '支付接口'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('payWay.interfaceId', '支付接口'),
        }),
        extraProps: {
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: interfaceList.value ?? [],
          remoteMethod: async (keyWord: string) => {
            await getInterfaceList({
              keyWord: keyWord,
            });
          },
          props: {
            label: 'interfaceName',
            value: 'interfaceId',
          },
        },
      },
      {
        name: 'needAccountInfoFlag',
        label: t('payWay.needAccountInfoFlag', '需要银行信息'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
      {
        name: 'scanPayFlag',
        label: t('payWay.scanPayFlag', 'HIS扫码标志'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
      {
        name: 'autoGetPayAmtFlag',
        label: t('payWay.autoGetPayAmtFlag', '自动获取金额'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
      {
        name: 'needAccountUnitFlag',
        label: t('payWay.needAccountUnitFlag', '需要记账单位'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
    ],
  });
  return data;
}
