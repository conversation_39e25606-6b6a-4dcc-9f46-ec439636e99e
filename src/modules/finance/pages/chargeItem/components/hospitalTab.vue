<script setup lang="ts" name="hospitalTab">
  import { FLAG } from '@/utils/constant';
  import { ref, computed } from 'vue';
  import { SelectOptions } from '@/typings/common';
  import { usePriceTableConfig } from '@/modules/finance/pages/chargeItem/config/useTableConfig';
  import { type FormInstance } from 'element-sun';
  import { dayjs } from '@sun-toolkit/shared';
  import { useDetailHospitalFormConfig } from '@/modules/finance/pages/chargeItem/config/useDetailFormConfig';
  import { ProForm, ProTable, TableRef } from 'sun-biz';
  export type hospitalFormItemType = {
    enabledFlag: FLAG;
    commodityCategoryId: string | undefined;
    encounterTypeCodes: string[];
    outCommodityCategoryId: string | undefined;
    inCommodityCategoryId: string | undefined;
    accCommodityCategoryId: string | undefined;
    fncCommodityCategoryId: string | undefined;
    mrCommodityCategoryId: string | undefined;
  };

  type Props = {
    hospitalFormItem: ChargeItem.HospitalChargeItemInfo;
    comodityCategoryOptions: SelectOptions[];
  };
  const props = withDefaults(defineProps<Props>(), {
    hospitalFormItem: () => ({}) as ChargeItem.HospitalChargeItemInfo,
    comodityCategoryOptions: () => [],
  });
  const emit = defineEmits(['modelChange', 'changeCategory']);

  const hospitalFormItem = computed(() => props.hospitalFormItem); //当前医院的收费信息
  const comodityCategoryOptions = computed(() => props.comodityCategoryOptions); //项目类别options

  //医院项目表单
  const hospitalFormRef = ref<{
    ref: FormInstance;
  }>();
  // 当前医院收费项目的tableRef
  const priceTableRef = ref<TableRef>();

  // 医院收费项目表格配置
  const { priceTableConfig, addItem } = usePriceTableConfig({
    tableRef: priceTableRef,
    data: hospitalFormItem.value
      .commodityPriceList as (ChargeItem.CommodityPriceInfo & {
      editable: boolean;
    })[],
  });

  // 收费项目分类的同步
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleModelChange = async (data: any) => {
    emit('modelChange', data);
  };

  const changeCategoryData = async (options: {
    keyWord: string;
    code: string;
  }) => {
    emit('changeCategory', options);
  };

  // 新增收费项目价格
  const handleAddPrice = async () => {
    addItem({
      editable: true,
      enabledFlag: FLAG.YES,
      startAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      endAt: '2099-12-31 23:59:59',
    } as ChargeItem.CommodityPriceInfo & {
      editable: boolean;
    });
  };

  // 医院收费项目表单配置
  const hospitalFormConfig = useDetailHospitalFormConfig(
    changeCategoryData,
    comodityCategoryOptions,
  );

  defineExpose({ hospitalFormRef, priceTableRef });
</script>
<template>
  <div class="flex h-full flex-col">
    <ProForm
      ref="hospitalFormRef"
      :label-width="110"
      v-model="hospitalFormItem as hospitalFormItemType"
      :data="hospitalFormConfig"
      @model-change="handleModelChange"
    />
    <div class="flex items-center justify-between pb-3">
      <div>价格</div>
      <el-button type="primary" @click="handleAddPrice">新增价格</el-button>
    </div>
    <ProTable
      ref="priceTableRef"
      row-key="commodityId"
      :editable="true"
      :data="hospitalFormItem.commodityPriceList"
      :columns="priceTableConfig"
    />
  </div>
</template>
