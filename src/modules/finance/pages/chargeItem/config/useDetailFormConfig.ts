import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';
import { useFormConfig } from 'sun-biz';
import {
  FLAG,
  ENCOUNTER_TYPE_CODE_NAME,
  COMMODITY_CATEGORY_WAY_CODE,
} from '@/utils/constant';

// 详情基本信息
export function useDetailBaseInfoFormConfig(
  unitOptions: Ref<SelectOptions[]>,
  getUnitList: (params: Unit.QueryParams) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'commodityNo',
        label: t('global:code'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:code'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'commodityName',
        label: t('global:name'),
        autoConvertSpellNoAndWbNo: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'commodity2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'commodityExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'commoditySpec',
        label: t('chargeItem.commoditySpec.project.title', '项目规格'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('chargeItem.commoditySpec.project.title', '项目规格'),
        }),
      },
      {
        name: 'unitId',
        label: t('chargeItem.unitId.title', '计价单位'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.unitId.title', '计价单位'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteMethod: (keyWord: string) => {
            getUnitList({
              keyWord: keyWord,
            } as Unit.QueryParams);
          },
          options: unitOptions.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('chargeItem.unitName.title', '计价单位'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        name: 'chargeItemConnotation',
        label: t('chargeItem.chargeItemConnotation.title', '项目内涵'),
        component: 'input',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('chargeItem.chargeItemConnotation.title', '项目内涵'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('chargeItem.chargeItemConnotation.title', '项目内涵'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'memo',
        label: t('chargeItem.memo.title', '备注'),
        type: 'textarea',
        isFullWidth: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('chargeItem.memo.title', '备注'),
        }),
      },
    ],
  });
  return data;
}

// 医院的项目form
export function useDetailHospitalFormConfig(
  changeCategoryData: (params: {
    keyWord: string;
    code: string;
  }) => Promise<void>,
  comodityCategoryOptions: Ref<SelectOptions[]>,
) {
  const data = useFormConfig({
    dataSetCodes: [ENCOUNTER_TYPE_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'commodityCategoryId',
        label: t('chargeItem.commodityCategoryName.title', '费用分类'),
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.commodityCategoryName.title', '费用分类'),
        }),
        extraProps: {
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.BASIC,
            }),
          filterable: true,
          options: comodityCategoryOptions.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('chargeItem.commodityCategoryName.title', '费用分类'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'outCommodityCategoryId',
        label: t('chargeItem.outCommodityCategoryName.title', '门诊发票分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.outCommodityCategoryName.title', '门诊发票分类'),
        }),
        extraProps: {
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.OUTPATIENT_INVOICE,
            }),
          filterable: true,
          options: comodityCategoryOptions.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'chargeItem.outCommodityCategoryName.title',
                '门诊发票分类',
              ),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'inCommodityCategoryId',
        label: t('chargeItem.inCommodityCategoryName.title', '住院发票分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.inCommodityCategoryName.title', '住院发票分类'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.INPATIENT_INVOICE,
            }),
          options: comodityCategoryOptions.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'chargeItem.inCommodityCategoryName.title',
                '住院发票分类',
              ),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'accCommodityCategoryId',
        label: t('chargeItem.accCommodityCategoryName.title', '会计分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.accCommodityCategoryName.title', '会计分类'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.ACCOUNTING,
            }),
          options: comodityCategoryOptions.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('chargeItem.accCommodityCategoryName.title', '会计分类'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'fncCommodityCategoryId',
        label: t('chargeItem.fncCommodityCategoryName.title', '财务分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.fncCommodityCategoryName.title', '财务分类'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.FINANCE,
            }),
          options: comodityCategoryOptions.value,
        },
      },
      {
        name: 'mrCommodityCategoryId',
        label: t('chargeItem.mrCommodityCategoryName.title', '病案分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.mrCommodityCategoryName.title', '病案分类'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          remoteMethod: (keyWord: string) =>
            changeCategoryData({
              keyWord: keyWord,
              code: COMMODITY_CATEGORY_WAY_CODE.MEDICAL_RECORD,
            }),
          options: comodityCategoryOptions.value,
        },
      },
      {
        name: 'encounterTypeCodes',
        label: t('chargeItem.encounterTypeCodes.title', '使用范围'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('chargeItem.encounterTypeCodes.title', '使用范围'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('chargeItem.encounterTypeCodes.title', '使用范围'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          multiple: true,
          filterable: true,
          // 此处暂时只留门诊和住院（后续需要其他也可以放开）
          options: data?.value
            ? data.value[ENCOUNTER_TYPE_CODE_NAME].filter(
                (item) => item.dataValueNo === '1' || item.dataValueNo === '2',
              )
            : [],
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
    ],
  });
  return data;
}
