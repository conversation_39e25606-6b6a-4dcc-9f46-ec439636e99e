import { Ref } from 'vue';
import { ORG_TYPE_CODE } from '@/typings/common';
import { useFormConfig } from 'sun-biz';

export function useSearchFormConfig(
  hospitalId: Ref<string>,
  orgTypeCode: string | undefined,
  queryChargeItemList: (
    params: Partial<ChargeItem.QueryParams>,
  ) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.keyword'),
        extraProps: {
          hospitalId: hospitalId.value,
          clearable: orgTypeCode === ORG_TYPE_CODE.GROUP ? true : false,
          className: 'w-52',
          onChange: (val: string) => {
            hospitalId.value = val;
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        className: 'w-52',
        // triggerModelChange: true,
        placeholder: t('global:placeholder.keyword'),
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryChargeItemList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}
