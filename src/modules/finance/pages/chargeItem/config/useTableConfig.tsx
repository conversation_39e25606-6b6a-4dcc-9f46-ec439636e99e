/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRouter } from 'vue-router';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { ORG_TYPE_CODE } from '@/typings/common';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { formatDecimalNumber, dayjs } from '@sun-toolkit/shared';
import { Ref, ComputedRef, ref } from 'vue';

function tableHeaders(
  tableData: Ref<ChargeItem.ChargeItemInfo[]>,
  orgTypeCode: ComputedRef<string | undefined>,
  handleEnableSwitch: (
    row: ChargeItem.ChargeItemInfo & ChargeItem.HospitalChargeItemInfo,
    index: number,
  ) => void,
  hospitalList: any[] | undefined,
  t: any,
) {
  if (orgTypeCode.value === ORG_TYPE_CODE.HOSPITAL)
    return [
      {
        label: t('chargeItem.commodityCategoryName.title', '费用分类'),
        prop: 'commodityCategoryName',
        minWidth: 100,
      },
      {
        label: t('chargeItem.latestPrice.title', '价格'),
        prop: 'latestPrice',
        minWidth: 120,
        autoFormatterNumber: true,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: ChargeItem.ChargeItemInfo & ChargeItem.HospitalChargeItemInfo,
          $index: number,
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row, $index)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
    ];
  if (orgTypeCode.value === ORG_TYPE_CODE.GROUP) {
    if ((hospitalList ?? []).length > 0) {
      const tableArr: any[] = [];
      hospitalList?.map((item: { orgName: string }, index: number) => {
        tableArr.push({
          label: t('chargeItem.hospital.title', item.orgName),
          minWidth: 100,
          _children: [
            {
              label: t('chargeItem.commodityCategoryName.title', '费用分类'),
              prop: 'commodityCategoryName' + index,
              minWidth: 100,
            },
            {
              label: t('chargeItem.latestPrice.title', '价格'),
              prop: 'latestPrice' + index,
              minWidth: 120,
              autoFormatterNumber: true,
            },
            {
              label: t('global:enabledFlag'),
              prop: 'enabledFlag' + index,
              minWidth: 150,
              render: (
                row: ChargeItem.ChargeItemInfo &
                  ChargeItem.HospitalChargeItemInfo,
              ) => {
                return (
                  <el-switch
                    modelValue={(row as any)['enabledFlag' + index]}
                    inline-prompt
                    active-value={ENABLED_FLAG.YES}
                    inactive-value={ENABLED_FLAG.NO}
                    before-change={() => handleEnableSwitch(row, index)}
                    active-text={t('global:enabled')}
                    inactive-text={t('global:disabled')}
                  />
                );
              },
            },
          ],
        });
        return tableArr;
      });
      return tableArr;
    }
    return [];
  }
  return [];
}

export function useTableConfig(
  tableData: Ref<ChargeItem.ChargeItemInfo[]>,
  orgTypeCode: ComputedRef<string | undefined>,
  hospitalList: any[] | undefined,
  handleEnableSwitch: (
    row: ChargeItem.ChargeItemInfo & ChargeItem.HospitalChargeItemInfo,
    index: number,
  ) => Promise<void>,
  hospitalId: Ref<string>,
) {
  const router = useRouter();

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('chargeItem.commodityNo.title', '项目编码'),
        prop: 'commodityNo',
        minWidth: 100,
      },
      {
        label: t('chargeItem.commodityName.title', '项目名称'),
        prop: 'commodityName',
        minWidth: 120,
      },
      {
        label: t('chargeItem.commodity2ndName.title', '辅助项目名称'),
        prop: 'commodity2ndName',
        minWidth: 150,
      },
      {
        label: t('chargeItem.commodityExtName.title', '拓展项目名称'),
        prop: 'commodityExtName',
        minWidth: 150,
      },
      {
        label: t('chargeItem.commoditySpec.title', '规格'),
        prop: 'commoditySpec',
        minWidth: 100,
      },
      {
        label: t('chargeItem.unitName.title', '计价单位'),
        prop: 'unitName',
        minWidth: 100,
      },
      ...tableHeaders(
        tableData,
        orgTypeCode,
        handleEnableSwitch,
        hospitalList,
        t,
      ),
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (row: ChargeItem.ChargeItemInfo) => {
          return (
            <el-button
              type="primary"
              link={true}
              onClick={() => {
                router.push({
                  path: `/detail/${hospitalId.value}`,
                  query: {
                    id: row.commodityId,
                  },
                });
              }}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

// 详情页价格table
export function usePriceTableConfig(options: {
  tableRef: Ref<TableRef, TableRef>;
  data: (ChargeItem.CommodityPriceInfo & { editable: boolean })[];
}) {
  const { t } = useTranslation();
  const { tableRef, data } = options;
  const tempData = ref(data);
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data: tempData,
    id: 'commodityPriceId',
  });
  // 价格的自定义校验
  const priceValidate = (
    rule: unknown,
    value: string,
    callback: (data?: Error | undefined) => void,
  ) => {
    if (value && Number(value) < 0) {
      callback(new Error(t('price.valid', '价格不能小于零')));
    } else if (!value) {
      callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('price', '价格'),
          }),
        ),
      );
    } else {
      callback();
    }
  };
  const priceTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('price', '价格'),
        prop: 'price',
        minWidth: 150,
        editable: true,
        rules: [
          {
            validator: priceValidate,
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ChargeItem.CommodityPriceInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  type="number"
                  v-model={row.price}
                  clearable={false}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('price', '价格'),
                  })}
                />
              ) : (
                <>{formatDecimalNumber(row.price) ?? '--'}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('effectiveTime', '生效时间'),
        prop: 'startAt',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('effectiveTime', '生效时间'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: ChargeItem.CommodityPriceInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.startAt}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t('effectiveTime', '生效时间'),
                  })}
                  default-value={new Date()}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.endAt &&
                      dayjs(time).isAfter(dayjs(row.endAt), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.startAt}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('failureTime', '失效时间'),
        prop: 'endAt',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('failureTime', '失效时间'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: ChargeItem.CommodityPriceInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.endAt}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t('failureTime', '失效时间'),
                  })}
                  default-value={new Date()}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.startAt &&
                      dayjs(time).isBefore(dayjs(row.startAt), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.endAt}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        editable: true,
        minWidth: 150,
        render: (
          row: ChargeItem.ChargeItemInfo & ChargeItem.HospitalChargeItemInfo,
        ) => {
          return (
            <div class={'w-full'}>
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                active-text={t('global:enabled')}
                onChange={(val: number) => {
                  row.enabledFlag = val;
                }}
                inactive-text={t('global:disabled')}
              />
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (
          row: ChargeItem.CommodityPriceInfo & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => {
                      row.price = Number(row.price);
                      toggleEdit(row);
                    }}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => toggleEdit(row)}
                >
                  {t('global:edit')}
                </el-button>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    priceTableConfig,
    addItem,
  };
}
