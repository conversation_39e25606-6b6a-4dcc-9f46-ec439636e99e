import { ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { queryUnitListByExample } from '@/modules/finance/api/unit';
import { queryCommodityCategoryListByExample } from '@/modules/finance/api/comodityCategory';

// 费用分类Options
export const useComodityCategory = () => {
  const comodityCategoryOptions = ref<SelectOptions[]>([]);
  const commodityCategoryList = ref<ComodityCategory.ComodityCategoryInfo[]>(
    [],
  );
  const getCommodityCategoryList = async (
    params: ComodityCategory.QueryParams,
  ) => {
    const [, res] = await queryCommodityCategoryListByExample({ ...params });
    if (res?.data) {
      commodityCategoryList.value = res.data;
      comodityCategoryOptions.value = (res.data ?? [])?.map(
        (item: ComodityCategory.ComodityCategoryInfo) => ({
          ...item,
          label: item.commodityCategoryName,
          value: item.commodityCategoryId,
        }),
      );
    }
  };
  return {
    comodityCategoryOptions,
    commodityCategoryList,
    getCommodityCategoryList,
  };
};

// 计价单位Options
export const useUnit = () => {
  const unitOptions = ref<SelectOptions[]>([]);
  const unitList = ref<Unit.UnitInfo[]>([]);
  const getUnitList = async (params?: Unit.QueryParams) => {
    const [, res] = await queryUnitListByExample({
      pageNumber: 1,
      pageSize: 100,
      ...params,
    });
    if (res?.data) {
      unitList.value = res.data;
      unitOptions.value = (res.data ?? [])?.map((item: Unit.UnitInfo) => ({
        label: item.unitName,
        value: item.unitId,
      }));
    }
  };
  return {
    unitOptions,
    unitList,
    getUnitList,
  };
};
