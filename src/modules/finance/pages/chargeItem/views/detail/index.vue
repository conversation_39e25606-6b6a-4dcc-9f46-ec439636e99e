<script lang="ts" name="chargeItemDetail" setup>
  import { Plus } from '@element-sun/icons-vue';
  import { useTranslation } from 'i18next-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { useDetailBaseInfoFormConfig } from '@/modules/finance/pages/chargeItem/config/useDetailFormConfig';
  import { computed, ref, onMounted, nextTick, watch } from 'vue';
  import { type FormInstance, ElMessageBox, ElMessage } from 'element-sun';
  import { useAppConfigData, MAIN_APP_CONFIG, Title, ProForm } from 'sun-biz';
  import {
    addChargeItem,
    updateChargeItemById,
    queryChargeItemListByExample,
  } from '@/modules/finance/api/chargeItem';
  import {
    useComodityCategory,
    useUnit,
  } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import hospitalTab from '@/modules/finance/pages/chargeItem/components/hospitalTab.vue';

  type baseInfoFormType = {
    producedByOrgId: string | undefined;
    commodityId: string | undefined;
    commodityNo: string | undefined;
    commodityName: string | undefined;
    commodity2ndName: string | undefined;
    commodityExtName: string | undefined;
    commoditySpec: string | undefined;
    unitId: string[] | undefined;
    spellNo: string | undefined;
    wbNo: string | undefined;
    chargeItemConnotation: string | undefined;
    memo: string | undefined;
  };
  type hospitalItem = {
    orgId: string;
    orgName: string;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const { unitOptions, getUnitList } = useUnit(); //计价单位options
  //获取当前登录的组织和医院list
  const { hospitalList } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.HOSPITAL_LIST,
  ]);
  // 费用分类options
  const {
    comodityCategoryOptions,
    commodityCategoryList,
    getCommodityCategoryList,
  } = useComodityCategory();

  const loading = ref(false); //加载状态
  const commodityId = ref(route.query.id); //商品id add为新增
  const hospitalId = ref(route.params.hospitalId); //医院id
  const chargeItemDetailData = ref<ChargeItem.ChargeItemInfo>(); //收费项目详情
  const baseInfoFormModel = ref<baseInfoFormType>(); //基本信息表单
  const hospitalSelectList = ref<hospitalItem[]>([]); //下拉选择的医院
  const submitLoading = ref(false); // 提交loading

  const baseInfoFormRef = ref<{
    ref: FormInstance;
  }>(); //baseInfo表单组件
  const hospitalTabRef = ref(); //医院表单和表格组件

  // 标题
  const componentTitle = computed(() =>
    commodityId.value === 'add'
      ? t('chargeItem.title.add', '新增收费项目')
      : t('chargeItem.title.edit', '编辑收费项目'),
  );
  // 判断当前传的orgId是医院还是集团
  const orgTypeCode = computed(() => {
    const org = hospitalList?.find(
      (item) => item.orgId === route.params.hospitalId,
    );
    if (org) {
      return org.orgTypeCode;
    } else {
      return ORG_TYPE_CODE.GROUP;
    }
  });

  /**
   * 页面逻辑方法
   */
  // 初始化时获取收费项目详情
  const getChargeItemDetail = async () => {
    // add不获取详情数据
    if (commodityId.value === 'add') {
      if (orgTypeCode.value === ORG_TYPE_CODE.GROUP) {
        chargeItemDetailData.value = {
          producedByOrgId: '',
          commodityId: '',
          commodityNo: '',
          commodityName: '',
          commodity2ndName: '',
          commodityExtName: '',
          commoditySpec: '',
          unitId: undefined,
          spellNo: '',
          wbNo: '',
          chargeItemConnotation: '',
          memo: '',
          hospitalChargeItemList: [],
        } as unknown as ChargeItem.ChargeItemInfo;
      } else if (orgTypeCode.value === ORG_TYPE_CODE.HOSPITAL) {
        chargeItemDetailData.value = {
          producedByOrgId: undefined,
          commodityId: '',
          commodityNo: '',
          commodityName: '',
          commodity2ndName: '',
          commodityExtName: '',
          commoditySpec: '',
          unitId: undefined,
          spellNo: '',
          wbNo: '',
          chargeItemConnotation: '',
          memo: '',
          hospitalChargeItemList: [
            {
              hospitalId: hospitalId.value as string,
              hospitalName: (hospitalList ?? [])[0].orgName,
              enabledFlag: FLAG.YES,
              commodityCategoryId: '',
              encounterTypeCodes: [],
              outCommodityCategoryId: '',
              inCommodityCategoryId: '',
              accCommodityCategoryId: '',
              fncCommodityCategoryId: '',
              mrCommodityCategoryId: '',
              commodityPriceList: [],
            },
          ],
        } as unknown as ChargeItem.ChargeItemInfo;
      }
      baseInfoFormModel.value = {
        ...chargeItemDetailData.value,
      } as unknown as baseInfoFormType;
      return;
    }

    // 获取详情数据
    loading.value = true;
    const [, res] = await queryChargeItemListByExample({
      commodityId: commodityId.value as string,
      pageNumber: 1,
      pageSize: 1,
    });
    loading.value = false;
    if (res?.success) {
      chargeItemDetailData.value = res?.data[0];
      hospitalId.value = (chargeItemDetailData.value.hospitalChargeItemList ??
        [])[0].hospitalId;
      // 基础信息表单
      baseInfoFormModel.value = {
        ...{
          producedByOrgId: undefined,
          commodityId: undefined,
          commodityNo: '',
          commodityName: '',
          commodity2ndName: '',
          commodityExtName: '',
          commoditySpec: '',
          unitId: [],
          spellNo: '',
          wbNo: '',
          chargeItemConnotation: '',
          memo: '',
        },
        ...chargeItemDetailData.value,
      } as unknown as baseInfoFormType;
    }
  };

  // 选择新增医院
  const handleAddHospital = async () => {
    // 过滤掉已选择过的
    hospitalSelectList.value = hospitalList?.filter(
      (itemA) =>
        !chargeItemDetailData.value?.hospitalChargeItemList?.some(
          (itemB: ChargeItem.HospitalChargeItemInfo) =>
            itemB.hospitalId === itemA.orgId,
        ),
    ) as hospitalItem[];
  };

  // 新增医院
  const handleSelectHospital = async (item: hospitalItem) => {
    (chargeItemDetailData.value?.hospitalChargeItemList ?? []).push({
      hospitalId: item.orgId,
      hospitalName: item.orgName,
      enabledFlag: FLAG.YES,
      commodityCategoryId: '',
      encounterTypeCodes: [],
      outCommodityCategoryId: '',
      inCommodityCategoryId: '',
      accCommodityCategoryId: '',
      fncCommodityCategoryId: '',
      mrCommodityCategoryId: '',
      commodityPriceList: [],
    } as unknown as ChargeItem.HospitalChargeItemInfo);
    hospitalId.value = item.orgId;
  };

  //移除医院
  const handleTabRemove = async (targetName: string) => {
    let index: number;
    let name: string;
    index =
      chargeItemDetailData.value?.hospitalChargeItemList?.findIndex(
        (item: ChargeItem.HospitalChargeItemInfo) =>
          item.hospitalId === targetName,
      ) ?? -1;
    if (index > 0) {
      name = (chargeItemDetailData.value?.hospitalChargeItemList ?? [])[
        index - 1
      ].hospitalId;
    } else if (
      index === 0 &&
      (chargeItemDetailData.value?.hospitalChargeItemList ?? []).length > 1
    ) {
      name = (chargeItemDetailData.value?.hospitalChargeItemList ?? [])[
        index + 1
      ].hospitalId;
    } else {
      name = route.params.hospitalId as string;
    }
    chargeItemDetailData.value?.hospitalChargeItemList?.splice(index, 1);
    hospitalId.value = name;
  };

  // 费用分类同步
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleHospitalModelChange = async (value: any) => {
    if (chargeItemDetailData.value) {
      const updatedList =
        chargeItemDetailData.value?.hospitalChargeItemList?.map((item) => {
          // 未知原因直接赋值赋上会被删掉（盲猜赋值的时候modelChange会初始化一下form数据）
          nextTick(() => {
            if (item.hospitalId === hospitalId.value) {
              const obj = (commodityCategoryList.value ?? []).find(
                (im) => im.commodityCategoryId === value.commodityCategoryId,
              );

              item.commodityCategoryId = item.commodityCategoryId
                ? item.commodityCategoryId
                : (obj?.commodityCategoryId as string);
              item.commodityCategoryName = item.commodityCategoryName
                ? item.commodityCategoryName
                : (obj?.commodityCategoryName as string);
              item.accCommodityCategoryId = item.accCommodityCategoryId
                ? item.accCommodityCategoryId
                : (obj?.accCommodityCategoryId as string);
              item.accCommodityCategoryName = item.accCommodityCategoryName
                ? item.accCommodityCategoryName
                : (obj?.accCommodityCategoryName as string);
              item.fncCommodityCategoryId = item.fncCommodityCategoryId
                ? item.fncCommodityCategoryId
                : obj?.fncCommodityCategoryId;
              item.fncCommodityCategoryName = item.fncCommodityCategoryName
                ? item.fncCommodityCategoryName
                : obj?.fncCommodityCategoryName;
              item.inCommodityCategoryId = item.inCommodityCategoryId
                ? item.inCommodityCategoryId
                : obj?.inCommodityCategoryId;
              item.inCommodityCategoryName = item.inCommodityCategoryName
                ? item.inCommodityCategoryName
                : obj?.inCommodityCategoryName;
              item.mrCommodityCategoryId = item.mrCommodityCategoryId
                ? item.mrCommodityCategoryId
                : obj?.mrCommodityCategoryId;
              item.mrCommodityCategoryName = item.mrCommodityCategoryName
                ? item.mrCommodityCategoryName
                : obj?.mrCommodityCategoryName;
              item.outCommodityCategoryId = item.outCommodityCategoryId
                ? item.outCommodityCategoryId
                : obj?.outCommodityCategoryId;
              item.outCommodityCategoryName = item.outCommodityCategoryName
                ? item.outCommodityCategoryName
                : obj?.outCommodityCategoryName;

              setTimeout(() => {
                comodityCategoryOptions.value.push(
                  ...[
                    {
                      label: (obj?.commodityCategoryName ?? '') as string,
                      value: (obj?.commodityCategoryId ?? '') as string,
                    },
                    {
                      label: (obj?.accCommodityCategoryName ?? '') as string,
                      value: (obj?.accCommodityCategoryId ?? '') as string,
                    },
                    {
                      label: (obj?.fncCommodityCategoryName ?? '') as string,
                      value: (obj?.fncCommodityCategoryId ?? '') as string,
                    },
                    {
                      label: (obj?.inCommodityCategoryName ?? '') as string,
                      value: (obj?.inCommodityCategoryId ?? '') as string,
                    },
                    {
                      label: (obj?.mrCommodityCategoryName ?? '') as string,
                      value: (obj?.mrCommodityCategoryId ?? '') as string,
                    },
                    {
                      label: (obj?.outCommodityCategoryName ?? '') as string,
                      value: (obj?.outCommodityCategoryId ?? '') as string,
                    },
                  ],
                );
              }, 10);
            }
          });
          return item;
        });

      chargeItemDetailData.value.hospitalChargeItemList = updatedList;
    }
  };

  //校验价格list是否为空、
  const checkPriceList = async () => {
    const currentIndex =
      chargeItemDetailData.value?.hospitalChargeItemList?.findIndex(
        (hospital) => hospital.hospitalId === hospitalId.value,
      );
    const currentHosPrice = (chargeItemDetailData.value
      ?.hospitalChargeItemList ?? [])[currentIndex as number]
      ?.commodityPriceList;
    if (!currentHosPrice || currentHosPrice.length === 0) {
      ElMessage.warning(t('priceList.validate.tips', '价格列表不能为空'));
      return false;
    }
    return true;
  };

  // 切换医院
  const handleToggleTipsItem = async (
    item: ChargeItem.HospitalChargeItemInfo,
  ) => {
    const currentIndex =
      chargeItemDetailData.value?.hospitalChargeItemList?.findIndex(
        (hospital) => hospital.hospitalId === hospitalId.value,
      );
    Promise.all([
      hospitalTabRef.value[
        currentIndex as number
      ].hospitalFormRef.ref.validate(),
      hospitalTabRef.value[
        currentIndex as number
      ].priceTableRef.formRef.validate(),
    ])
      .then(async () => {
        const currentHosPrice = (chargeItemDetailData.value
          ?.hospitalChargeItemList ?? [])[currentIndex as number]
          ?.commodityPriceList;

        const flag = await checkPriceList();
        if (!flag) {
          return;
        }
        const currentHosPriceObj = currentHosPrice.find(
          (im) => im.editable === true,
        );
        if (currentHosPriceObj) {
          return ElMessageBox.alert(
            t(
              'hospital.toggleHospitalWarning',
              '有正在编辑中的时段，请先保存编辑状态中的医院',
            ),
            t('global:tip'),
          ).catch((e) => console.log(e));
        } else {
          hospitalId.value = item.hospitalId;
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  // 保存收费项目
  const handleSubmit = async () => {
    submitLoading.value = true;
    const currentHospitalIndex =
      chargeItemDetailData.value?.hospitalChargeItemList?.findIndex((item) => {
        return item.hospitalId === hospitalId.value;
      });

    Promise.all([
      baseInfoFormRef.value?.ref?.validate(),
      hospitalTabRef.value[
        currentHospitalIndex as number
      ].hospitalFormRef?.ref?.validate(),
      hospitalTabRef.value[
        currentHospitalIndex as number
      ].priceTableRef?.formRef?.validate(),
    ])
      .then(async () => {
        submitLoading.value = false;
        const flag = await checkPriceList();
        if (!flag) {
          return;
        }
        await saveChargeItem();
        // router.push('/');
      })
      .catch((error) => {
        submitLoading.value = false;
        console.log(error);
      });
  };

  // 保存
  const saveChargeItem = async () => {
    const params = {
      ...chargeItemDetailData.value,
      ...baseInfoFormModel.value,
    } as unknown as ChargeItem.ChargeItemInfo;

    if (commodityId.value === 'add') {
      const [, res] = await addChargeItem(params);
      if (res?.success) {
        router.push('/');
      }
    } else {
      const [, res] = await updateChargeItemById(params);
      if (res?.success) {
        t('global:modify.success');
        router.push('/');
      }
    }
  };

  // 基本信息表单配置
  const baseInfoFormConfig = useDetailBaseInfoFormConfig(
    unitOptions,
    getUnitList,
  );

  /** 切换不同的商品分类展示不同的list */
  const changeCategory = async (options: { keyWord: string; code: string }) => {
    comodityCategoryOptions.value = [];
    const { keyWord, code } = options;
    await getCommodityCategoryList({
      keyWord: keyWord,
      hospitalId: hospitalId.value as string,
      commodityCategoryWayCode: code,
      enabledFlag: FLAG.YES,
    });
  };

  watch(
    () => hospitalId.value,
    async (newValue) => {
      if (newValue) {
        await getCommodityCategoryList({
          hospitalId: hospitalId.value as string,
          enabledFlag: FLAG.YES,
        });
      }
    },
    {
      immediate: true,
    },
  );

  onMounted(async () => {
    await getChargeItemDetail();
    // 获取计价单位
    await getUnitList({
      keyWord: '',
    } as Unit.QueryParams);
  });
</script>
<template>
  <div class="mr-2.5 flex h-full flex-col">
    <el-page-header @back="router.push('/')" class="pb-3">
      <template #content>
        <span class="text-base">
          {{ componentTitle }}
        </span>
      </template>
    </el-page-header>
    <div class="flex h-full flex-col" v-loading="loading">
      <Title :title="$t('baseInfo', '基本信息')" class="mb-2" />
      <ProForm
        :label-width="110"
        ref="baseInfoFormRef"
        v-model="baseInfoFormModel"
        :data="baseInfoFormConfig"
      />
      <Title :title="$t('hospital.project', '医院的项目')" class="mb-2" />
      <!-- v-if="
          orgTypeCode === ORG_TYPE_CODE.GROUP &&
          chargeItemDetailData.hospitalChargeItemList.length > 0
        " -->
      <el-tabs
        class="hospital-tabs h-full"
        v-model="hospitalId"
        @tab-remove="handleTabRemove"
        :class="orgTypeCode === ORG_TYPE_CODE.GROUP ? '' : 'hide-tabs'"
      >
        <el-tab-pane
          class="h-full"
          :closable="
            !item.hospitalCommodityId && orgTypeCode === ORG_TYPE_CODE.GROUP
          "
          :key="item.hospitalId"
          :name="item.hospitalId"
          v-for="item in chargeItemDetailData?.hospitalChargeItemList"
        >
          <template #label>
            {{ orgTypeCode === ORG_TYPE_CODE.GROUP ? item.hospitalName : '' }}
          </template>
          <template #default>
            <div
              class="h-full"
              @click.stop="
                () => {
                  if (hospitalId === item.hospitalId) {
                    return;
                  }
                  handleToggleTipsItem(item);
                }
              "
            >
              <hospitalTab
                @change-category="changeCategory"
                @model-change="handleHospitalModelChange"
                ref="hospitalTabRef"
                :hospital-form-item="item"
                :comodity-category-options="comodityCategoryOptions"
              />
            </div>
          </template>
        </el-tab-pane>
        <!-- 加号 -->
        <el-tab-pane v-if="orgTypeCode === ORG_TYPE_CODE.GROUP">
          <template #label>
            <el-dropdown
              class="h-full"
              ref="dropdown1"
              trigger="click"
              @command="handleSelectHospital"
            >
              <el-icon @click.stop="handleAddHospital" class="h-full" size="20"
                ><Plus
              /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in hospitalSelectList"
                    :key="item.orgId"
                    :command="item"
                  >
                    {{ item.orgName }}
                  </el-dropdown-item>
                  <div
                    class="mx-2 py-1 text-base"
                    style="font-size: 0.8rem; color: #d5d7de"
                    v-if="hospitalSelectList.length === 0"
                  >
                    {{ $t('notFound.hospital', '暂无可选医院') }}
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="mt-3 text-right">
      <el-button @click="() => router.push('/')">{{
        $t('global:cancel')
      }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
  /* stylelint-disable-next-line */
  .hospital-tabs:deep(.el-tabs__item) {
    padding-left: 0;
  }

  .hide-tabs {
    /* stylelint-disable-next-line */
    :deep(.el-tabs__nav-scroll) {
      display: none;
    }
  }
</style>
