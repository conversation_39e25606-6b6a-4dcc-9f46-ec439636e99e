<script lang="ts" name="chargeItem" setup>
  import { useRouter } from 'vue-router';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import { ref, computed } from 'vue';
  import { ORG_TYPE_CODE } from '@/typings/common';
  import { useTranslation } from 'i18next-vue';
  import { useTableConfig } from '../config/useTableConfig';
  import { useSearchFormConfig } from '../config/useSearchFormConfig';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    Title,
    ProForm,
    ProTable,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    queryChargeItemListByExample,
    updateHospitalChargeItemEnabledFlagById,
  } from '@/modules/finance/api/chargeItem';

  const router = useRouter();
  const { t } = useTranslation();

  const hospitalId = ref<string>(''); // 医院ID
  const loading = ref<boolean>(false); // 加载中
  const chargeItemList = ref<ChargeItem.ChargeItemInfo[]>([]); // 收费项目列表
  const searchModel = ref({
    enabledFlag: ENABLED_FLAG.ALL,
  }); // 查询默认参数
  const queryPageParams = ref({
    pageNumber: 1,
    pageSize: 10,
    total: 0,
  }); // 分页参数

  const { currentOrg, hospitalList } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.HOSPITAL_LIST,
  ]); //当前组织标识和医院list

  const currentOrgTypeCode = computed(() => {
    const arr = (hospitalList ?? [])?.filter(
      (item: { orgId: string }) => item.orgId === hospitalId.value,
    );
    if (arr.length > 0) {
      return arr[0].orgTypeCode;
    } else {
      return currentOrg?.orgTypeCode;
    }
  }); //当前选中的组织类型

  // 修改启用标识
  const handleEnableSwitch = async (
    row: ChargeItem.ChargeItemInfo & ChargeItem.HospitalChargeItemInfo,
    index: number,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.commodityName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        loading.value = true;
        const [, res] = await updateHospitalChargeItemEnabledFlagById({
          hospitalCommodityId:
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (row as any)['hospitalCommodityId' + index] ??
            row.hospitalCommodityId,
          enabledFlag:
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        });
        loading.value = false;
        if (res?.success) {
          row.enabledFlag =
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES;
          ElMessage.success(
            t(
              row.enabledFlag === ENABLED_FLAG.YES
                ? 'global:enabled.success'
                : 'global:disabled.success',
            ),
          );
        }
      })
      .catch(() => {});
  };

  // 查询table列表
  const queryChargeItemList = async (data: Partial<ChargeItem.QueryParams>) => {
    searchModel.value.enabledFlag =
      (data?.enabledFlag as unknown as number) ?? searchModel.value.enabledFlag;
    const params = {
      hospitalId: hospitalId.value,
      pageNumber: queryPageParams.value.pageNumber,
      pageSize: queryPageParams.value.pageSize,
      ...data,
      enabledFlag:
        searchModel.value.enabledFlag === -1
          ? undefined
          : searchModel.value.enabledFlag,
    };
    loading.value = true;
    const [, res] = await queryChargeItemListByExample(
      params as ChargeItem.QueryParams,
    );
    loading.value = false;
    if (res?.success) {
      chargeItemList.value = handleChargeItem(res?.data ?? []);
      queryPageParams.value.total = res?.total;
    }
  };

  // 处理数据
  const handleChargeItem = (data: ChargeItem.ChargeItemInfo[]) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let arr: any = [];
    if (currentOrgTypeCode.value === ORG_TYPE_CODE.HOSPITAL) {
      arr = data.map((item) => ({
        ...item,
        ...(item.hospitalChargeItemList ?? [])[0],
      }));
    }
    if (currentOrgTypeCode.value === ORG_TYPE_CODE.GROUP) {
      hospitalList?.map((item: { orgId: string }, index: number) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        arr = data.map((im: any) => {
          im?.hospitalChargeItemList?.map(
            (hos: {
              hospitalId: string;
              hospitalCommodityId: string;
              commodityCategoryName: string;
              latestPrice: number;
              enabledFlag: number;
            }) => {
              if (hos.hospitalId === item.orgId) {
                im['hospitalCommodityId' + index] = hos.hospitalCommodityId;
                im['commodityCategoryName' + index] = hos.commodityCategoryName;
                im['latestPrice' + index] = hos.latestPrice;
                im['enabledFlag' + index] = hos.enabledFlag;
              } else {
                im['hospitalCommodityId' + index] = null;
                im['commodityCategoryName' + index] = null;
                im['latestPrice' + index] = null;
                im['enabledFlag' + index] = 0;
              }
            },
          );
          return im;
        });
      });
    }
    return arr;
  };

  const searchFormConfig = useSearchFormConfig(
    hospitalId,
    currentOrg?.orgTypeCode,
    queryChargeItemList,
  ); // 查询表单配置
  const tableConfig = useTableConfig(
    chargeItemList,
    currentOrgTypeCode,
    hospitalList,
    handleEnableSwitch,
    hospitalId,
  ); // 表格配置
</script>
<template>
  <div class="flex h-full flex-col">
    <Title :title="$t('chargeItem.title', '收费项目')" class="mb-5" />
    <div class="flex justify-between">
      <!-- 搜索框 -->
      <ProForm
        v-model="searchModel"
        layout-mode="inline"
        :data="searchFormConfig"
        :show-search-button="true"
        @model-change="queryChargeItemList"
      />
      <!-- 新增 -->
      <el-button
        type="primary"
        @click="
          router.push({
            path: `/detail/${hospitalId}`,
            query: {
              id: 'add',
            },
          })
        "
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <!-- 分页表格 -->
    <ProTable
      :data="chargeItemList"
      row-key="commodityId"
      :page-info="{
        total: queryPageParams.total,
        pageNumber: queryPageParams.pageNumber,
        pageSize: queryPageParams.pageSize,
      }"
      :columns="tableConfig"
      :pagination="true"
      :loading="loading"
      @current-page-change="
        (val: number) => {
          queryPageParams.pageNumber = val;
          queryChargeItemList({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryPageParams.pageSize = val;
          queryChargeItemList({
            pageSize: val,
          });
        }
      "
    />
  </div>
</template>
