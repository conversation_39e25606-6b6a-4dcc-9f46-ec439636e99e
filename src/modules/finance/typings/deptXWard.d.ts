declare namespace DeptXWard {
  interface QueryParams {
    deptId?: string;
    wardId?: string;
    enabledFlag?: number;
    hospitalId?: string;
  }
  interface deptXWardInfo {
    deptXWardId: string;
    deptId: string;
    deptName: string;
    wardId: string;
    wardName: string;
    enabledFlag: number;
    hospitalId: string;
  }
  interface addDeptXWardParams {
    deptId?: string;
    wardId?: string;
    enabledFlag?: number;
    hospitalId?: string;
  }
}
