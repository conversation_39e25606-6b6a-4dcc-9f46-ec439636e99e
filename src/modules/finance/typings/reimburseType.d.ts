declare namespace ReimburseType {
  interface ReimburseTypeReqQuery {
    keyWord?: string;
    enabledFlag?: number;
    hospitalId: string;
  }
  interface ReimburseTypeReqItem {
    reimburseTypeId: string;
    reimburseTypeName: string;
    reimburseType2ndName?: string;
    reimburseTypeExtName?: string;
    reimburseTypeNameDisplay: string;
    enabledFlag: number;
    sort: number;
    interfaceId?: string;
    interfaceName?: string;
  }
  interface SaveReimburseTypeReqQuery {
    reimburseTypeId?: string;
    reimburseTypeName: string;
    reimburseType2ndName?: string;
    reimburseTypeExtName?: string;
    reimburseTypeNameDisplay: string;
    enabledFlag: number;
    sort: number;
    interfaceId?: string;
    hospitalId: string;
  }
  interface SaveReimburseTypeReqItem {
    reimburseTypeId: string;
  }
}
