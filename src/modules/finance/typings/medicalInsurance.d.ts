declare namespace MedInsurance {
  interface MedInsuranceReqQuery {
    hospitalId: string;
    keyWord?: string;
    enabledFlag?: number;
    dictEncResAccesserIds?: string;
  }

  interface MedInsuranceReqItem {
    medInsuranceId: string;
    medInsuranceCode?: string;
    medInsuranceNo: string;
    medInsuranceName: string;
    medInsurance2ndName?: string;
    medInsuranceExtName?: string;
    medInsuranceNameDisplay: string;
    enabledFlag: number;
    sort: number;
    spellNo?: string;
    wbNo?: string;
    hospitalId: string;
    reimburseTypeList?: {
      reimburseTypeId: string;
      reimburseTypeName: string;
      reimburseType2ndName?: string;
      reimburseTypeExtName?: string;
      reimburseTypeNameDisplay: string;
      enabledFlag: number;
      sort: number;
      medInsuranceId: string;
      medInsuranceName: string;
      interfaceId?: string;
      interfaceName: string;
    }[];
  }

  interface ReimburseTypeReqItem {
    reimburseTypeId: string;
    reimburseTypeName: string;
    reimburseType2ndName?: string;
    reimburseTypeExtName?: string;
    reimburseTypeNameDisplay: string;
    enabledFlag: number;
    sort: number;
    medInsuranceId: string;
    medInsuranceName: string;
    interfaceId?: string;
    interfaceName: string;
  }

  interface SaveMedInsuranceReqQuery {
    medInsuranceId?: string;
    medInsuranceCode?: string;
    medInsuranceNo: string;
    medInsuranceName: string;
    medInsurance2ndName?: string;
    medInsuranceExtName?: string;
    enabledFlag: number;
    spellNo?: string;
    wbNo?: string;
    hospitalId: string;
    reimburseTypeIds: string[];
  }

  interface SaveMedInsuranceReqItem {
    medInsuranceId: string;
  }

  interface UpdateMedInsuranceSortReqQuery {
    medInsuranceId: string;
    sort: number;
  }

  interface SysMenuMedInsuranceReqQuery {
    keyWord?: string;
    enabledFlag?: number;
    medInsuranceIds?: string[];
    hospitalId: string;
    menuId: string;
  }

  interface SysMenuMedInsuranceReqItem {
    medInsuranceId: string;
    medInsuranceCode?: string;
    medInsuranceNo: string;
    medInsuranceName: string;
    medInsurance2ndName?: string;
    medInsuranceExtName?: string;
    medInsuranceNameDisplay: string;
    enabledFlag: number;
    sort: number;
    spellNo?: string;
    wbNo?: string;
    hospitalId: string;
    reimburseTypeList?: {
      reimburseTypeId: string;
      reimburseTypeName: string;
      reimburseType2ndName?: string;
      reimburseTypeExtName?: string;
      reimburseTypeNameDisplay: string;
      enabledFlag: number;
      sort: number;
      medInsuranceId: string;
      medInsuranceName: string;
      interfaceId?: string;
      interfaceName?: string;
    }[];
  }

  /** 根据条件查询菜单的医保费别列表（定义态） 1-10407-1 入参*/
  interface MenuXMedInsuranceReqParams {
    menuIds?: string[];
  }

  /** 根据条件查询菜单的医保费别列表（定义态） 1-10407-1 出参*/
  interface MenuXMedInsuranceReqItems {
    menuId: string;
    menuName: string;
    dictMedInsuranceList?: {
      menuXMedInsuranceId: string;
      medInsuranceId: string;
      medInsuranceCode?: string;
      medInsuranceNo: string;
      medInsuranceName: string;
      sort: number;
    }[];
  }

  interface DictMedInsuranceReqItems {
    menuXMedInsuranceId: string;
    medInsuranceId: string;
    medInsuranceCode?: string;
    medInsuranceNo: string;
    medInsuranceName: string;
    sort: number;
  }

  /** 保存菜单的医保费别 1-10406-1 入参*/
  interface SaveMenuXMedInsuranceReqParams {
    menuXMedInsuranceList: {
      menuId: string;
      dictMedInsuranceList?: {
        menuXMedInsuranceId?: string;
        medInsuranceId: string;
        sort: number;
      }[];
    }[];
  }
}
