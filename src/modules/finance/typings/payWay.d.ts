declare namespace PayWay {
  interface PayWayReqParams {
    keyWord?: string;
    enabledFlag?: number;
  }

  interface PayWayReqItem {
    payWayId: string;
    payWayNo: string;
    payWayName: string;
    payWay2ndName?: string;
    payWayExtName?: string;
    payWayNameDisplay: string;
    payWayTypeCode?: string;
    payWayTypeDesc?: string;
    payWayCode?: string;
    payWayDesc?: string;
    needAccountInfoFlag: number;
    interfaceId?: string;
    interfaceName?: string;
    scanPayFlag: number;
    autoGetPayAmtFlag: number;
    needAccountUnitFlag: number;
    originalReturnMaxDay: number;
    enabledFlag: number;
    sort: number;
    spellNo?: string;
    wbNo?: string;
    refundPayWayList?: {
      refundPayWayId: string;
      refundPayWayName: string;
      sort: number;
    }[];
  }

  interface AddPayWayReqParams {
    payWayNo: string;
    payWayName: string;
    payWay2ndName?: string;
    payWayExtName?: string;
    payWayTypeCode?: string;
    payWayCode?: string;
    needAccountInfoFlag: number;
    interfaceId?: string;
    scanPayFlag: number;
    autoGetPayAmtFlag: number;
    needAccountUnitFlag: number;
    originalReturnMaxDay: number;
    enabledFlag: number;
    spellNo: string;
    wbNo: string;
    refundPayWayList?: {
      refundPayWayId: string;
      sort: number;
    }[];
  }

  interface AddPayWayReqItem {
    payWayId: string;
  }

  interface UpdatePayWayReqParams {
    payWayId: string;
    payWayNo: string;
    payWayName: string;
    payWay2ndName?: string;
    payWayExtName?: string;
    payWayTypeCode?: string;
    payWayCode?: string;
    needAccountInfoFlag: number;
    interfaceId?: string;
    scanPayFlag: number;
    autoGetPayAmtFlag: number;
    needAccountUnitFlag: number;
    originalReturnMaxDay: number;
    enabledFlag: number;
    spellNo?: string;
    wbNo?: string;
    refundPayWayList?: {
      refundPayWayId: string;
      sort: number;
    }[];
  }

  interface RefundPayWayReqItem {
    refundPayWayId?: string;
    refundPayWayName?: string;
    sort?: number;
  }

  interface UpdatePayWayEnabledReqParams {
    payWayId: string;
    enabledFlag: number;
  }

  interface UpdatePayWaySortReqParams {
    payWaySortList: {
      payWayId: string;
      sort: number;
    }[];
  }

  interface MenuPayWayReqParams {
    menuId?: string;
    enabledFlag?: number;
  }

  interface MenuPayWayReqItem {
    menuPayWayMtId: string;
    menuId: string;
    menuNameDisplay: string;
    maxDisplayCount: number;
    defaultMultiPayFlag: number;
    menuPayWayDtList: {
      menuPayWayDtId: string;
      payWayId: string;
      payWayName: string;
      enabledFlag: number;
      defaultFlag: number;
      sort: number;
    }[];
  }

  interface MenuPayWayDtListItem {
    menuPayWayDtId: string;
    payWayId: string;
    payWayName: string;
    enabledFlag: number;
    defaultFlag: number;
    defaultHoverFlag?: boolean;
    editable?: boolean;
    sort: number;
  }

  interface PayWayForBizReqParams {
    keyWord?: string;
    enabledFlag?: number;
    menuId?: string;
  }

  interface PayWayForBizReqItem {
    maxDisplayCount: number;
    defaultMultiPayFlag: number;
    payWayList?: PayWayReqItem[];
  }

  interface PayWaySaveMenuReqParams {
    menuPayWayMtList: {
      menuPayWayMtId?: string;
      menuId: string;
      maxDisplayCount: number;
      defaultMultiPayFlag: number;
      menuPayWayDtList: {
        menuPayWayDtId?: string;
        payWayId: string;
        enabledFlag: number;
        defaultFlag: number;
        sort: number;
      }[];
    }[];
  }

  interface PayWaySaveMenuReqItem {
    menuPayWayMtId: string;
  }
}
