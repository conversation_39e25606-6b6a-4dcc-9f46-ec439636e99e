declare namespace ChargeItem {
  interface QueryParams {
    keyWord?: string;
    hospitalId?: string;
    commodityId?: string;
    enabledFlag?: string;
    pageNumber: number;
    pageSize: number;
  }

  interface ChargeItemInfo {
    commodityId: string;
    commodityNo?: string;
    commodityName: string;
    commodity2ndName?: string;
    commodityExtName?: string;
    commodityNameDisplay: string;
    commoditySpec?: string;
    unitId: string;
    unitName: string;
    spellNo?: string;
    wbNo?: string;
    memo?: string;
    producedByOrgId?: string;
    producedByOrgName?: string;
    chargeItemConnotation?: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    hospitalChargeItemList?: HospitalChargeItemInfo[];
  }

  interface HospitalChargeItemInfo {
    hospitalCommodityId: string;
    hospitalId: string;
    hospitalName: string;
    commodityCategoryId: string;
    commodityCategoryName: string;
    outCommodityCategoryId?: string;
    outCommodityCategoryName?: string;
    inCommodityCategoryId?: string;
    inCommodityCategoryName?: string;
    accCommodityCategoryId?: string;
    accCommodityCategoryName?: string;
    fncCommodityCategoryId?: string;
    fncCommodityCategoryName?: string;
    mrCommodityCategoryId?: string;
    mrCommodityCategoryName?: string;
    enabledFlag: number;
    encounterTypeCodes: string[];
    latestPrice: number;
    commodityPriceList: CommodityPriceInfo[];
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
  }

  interface CommodityPriceInfo {
    [x: string]: boolean;
    commodityPriceId: string;
    price: number;
    startAt: string;
    endAt?: string;
    enabledFlag: number;
  }

  interface QueryParamsEnabeldFlag {
    hospitalCommodityId: string;
    enabledFlag: number;
  }

  interface QueryParamsHospitalChargeItem {
    pageNumber: number;
    pageSize: number;
    keyWord?: string;
    hospitalId: string;
    encounterTypeCode: string;
    priceAt: string;
  }

  interface HospitalChargeItemInfoItem {
    hospitalCommodityId: string;
    commodityCategoryId: string;
    commodityCategoryName: string;
    outCommodityCategoryId?: string;
    outCommodityCategoryName?: string;
    inCommodityCategoryId?: string;
    inCommodityCategoryName?: string;
    accCommodityCategoryId?: string;
    accCommodityCategoryName?: string;
    fncCommodityCategoryId?: string;
    fncCommodityCategoryName?: string;
    mrCommodityCategoryId?: string;
    mrCommodityCategoryName?: string;
    commodityId: string;
    commodityNo?: string;
    commodityName: string;
    commodity2ndName?: string;
    commodityExtName?: string;
    commodityNameDisplay: string;
    commoditySpec?: string;
    unitId: string;
    unitName: string;
    spellNo?: string;
    wbNo?: string;
    memo?: string;
    producedByOrgId?: string;
    producedByOrgName?: string;
    chargeItemConnotation?: string;
    enabledFlag: number;
    commodityPriceId: string;
    price: number | null | string;
    startAt: string;
    endAt?: string;
  }
}
