declare namespace ComodityCategory {
  interface QueryParams {
    keyWord?: string;
    hospitalId?: string;
    enabledFlag?: number;
    commodityCategoryWayCode?: string;
  }
  interface ComodityCategoryInfo {
    commodityCategoryId: string;
    commodityClassCode?: string;
    commodityClassDesc?: string;
    commodityCategoryName: string;
    commodityCategory2ndName?: string;
    commodityCategoryExtName?: string;
    commodityCategoryNameDisplay: string;
    commodityCategoryWayCode: string;
    commodityCategoryWayDesc: string;
    enabledFlag: number;
    spellNo?: string;
    wbNo?: string;
    hospitalId: string;
    sort: number;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    commodityCategoryMapId?: string;
    outCommodityCategoryId?: string;
    outCommodityCategoryName?: string;
    inCommodityCategoryId?: string;
    inCommodityCategoryName?: string;
    accCommodityCategoryId?: string;
    accCommodityCategoryName?: string;
    fncCommodityCategoryId?: string;
    fncCommodityCategoryName?: string;
    mrCommodityCategoryId?: string;
    mrCommodityCategoryName?: string;
  }

  interface ComodityCategoryUpsertParams {
    commodityCategoryId?: string;
    commodityClassCode?: string;
    commodityCategoryName?: string;
    commodityCategory2ndName?: string;
    commodityCategoryExtName?: string;
    commodityCategoryWayCode?: string;
    enabledFlag?: number;
    spellNo?: string;
    wbNo?: string;
    hospitalId?: string;
    commodityCategoryMapId?: string;
    outCommodityCategoryId?: string;
    inCommodityCategoryId?: string;
    accCommodityCategoryId?: string;
    fncCommodityCategoryId?: string;
    mrCommodityCategoryId?: string;
  }
}
