import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10166-1]根据条件查询科室病区对应关系列表
 * @param params
 * @returns
 */
export const queryDeptXWardByExample = (params: DeptXWard.QueryParams) => {
  return dictRequest<DeptXWard.deptXWardInfo[]>(
    '/deptxward/queryDeptXWardByExample',
    params,
  );
};

/**
 * [1-10167-1]新增科室病区对应关系
 * @param params
 * @returns
 */
export const addDeptXWard = (params: DeptXWard.addDeptXWardParams) => {
  return dictRequest<{ deptXWardId: string }>(
    '/deptxward/addDeptXWard',
    params,
  );
};

/**
 * [1-10168-1]根据标识停启用科室病区对应关系
 * @param params
 * @returns
 */
export const deleteDeptXWardEnabledFlagById = (params: {
  deptXWardId: string;
  enabledFlag: number;
}) => {
  return dictRequest('/deptxward/deleteDeptXWardEnabledFlagById', params);
};
