import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10124-1]根据条件查询支付方式列表（定义态）
 * @param params
 * @returns
 */
export const queryPayWayListByExample = (params: PayWay.PayWayReqParams) => {
  return dictRequest<PayWay.PayWayReqItem[]>(
    '/payway/queryPayWayListByExample',
    params,
  );
};

/**
 * [1-10125-1]新增支付方式
 * @param params
 * @returns
 */
export const addPayWay = (params: PayWay.AddPayWayReqParams) => {
  return dictRequest<PayWay.AddPayWayReqItem[]>('/payway/addPayWay', params, {
    successMsg: translation('global:add.success'),
  });
};

/**
 * [1-10126-1]根据标识修改支付方式
 * @param params
 * @returns
 */
export const updatePayWayById = (params: PayWay.UpdatePayWayReqParams) => {
  return dictRequest('/payway/updatePayWayById', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [1-10127-1]根据标识停启用支付方式
 * @param params
 * @returns
 */
export const updatePayWayEnabledFlagById = (
  params: PayWay.UpdatePayWayEnabledReqParams,
) => {
  return dictRequest('/payway/updatePayWayEnabledFlagById', params);
};

/**
 * [1-10128-1]根据标识修改支付方式排序
 * @param params
 * @returns
 */
export const updatePayWaySortByIds = (
  params: PayWay.UpdatePayWaySortReqParams,
) => {
  return dictRequest('/payway/updatePayWaySortByIds', params, {
    successMsg: translation('global:modify.sort.success'),
  });
};

/**
 * [1-10129-1]根据条件查询菜单的支付方式（定义态）
 * @param params
 * @returns
 */
export const queryMenuPayWayListByExample = (
  params: PayWay.MenuPayWayReqParams,
) => {
  return dictRequest<PayWay.MenuPayWayReqItem[]>(
    '/payway/queryMenuPayWayListByExample',
    params,
  );
};

/**
 * [1-10130-1]根据条件查询支付方式列表（业务态）
 * @param params
 * @returns
 */
export const queryPayWayListByExampleForBiz = (
  params: PayWay.PayWayForBizReqParams,
) => {
  return dictRequest<PayWay.PayWayForBizReqItem>(
    '/payway/queryPayWayListByExampleForBiz',
    params,
  );
};

/**
 * [1-10173-1]保存菜单的支付方式
 * @param params
 * @returns
 */
export const saveMenuPayWay = (params: PayWay.PayWaySaveMenuReqParams) => {
  return dictRequest<PayWay.PayWaySaveMenuReqItem>(
    '/payway/saveMenuPayWay',
    params,
  );
};
