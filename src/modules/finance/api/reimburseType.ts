import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10152-1] 根据条件查询报销类型列表
 * @param params
 * @returns
 */
export const queryReimburseTypeListByExample = (
  params: ReimburseType.ReimburseTypeReqQuery,
) => {
  return dictRequest<ReimburseType.ReimburseTypeReqItem[]>(
    '/reimbursetype/queryReimburseTypeListByExample',
    params,
  );
};

/**
 * [1-10153-1] 保存报销类型
 * @param params
 * @returns
 */
export const saveReimburseType = (
  params: ReimburseType.SaveReimburseTypeReqQuery,
) => {
  return dictRequest<ReimburseType.SaveReimburseTypeReqItem>(
    '/reimbursetype/saveReimburseType',
    params,
  );
};
