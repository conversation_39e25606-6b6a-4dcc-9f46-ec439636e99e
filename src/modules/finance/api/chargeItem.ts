import { dictRequest } from '@sun-toolkit/request';
// import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10048-1]根据条件查询收费项目列表
 * @param params
 * @returns
 */
export const queryChargeItemListByExample = (
  params: ChargeItem.QueryParams,
) => {
  return dictRequest<ChargeItem.ChargeItemInfo, ChargeItem.QueryParams>(
    '/chargeItem/queryChargeItemListByExample',
    params,
  );
};

/**
 * [1-10049-1]新增收费项目
 * @param params
 * @returns
 */
export const addChargeItem = (params: ChargeItem.ChargeItemInfo) => {
  return dictRequest<ChargeItem.ChargeItemInfo[]>(
    '/chargeItem/addChargeItem',
    params,
  );
};

/**
 * [1-10050-1]根据标识修改收费项目
 * @param params
 * @returns
 */
export const updateChargeItemById = (params: ChargeItem.ChargeItemInfo) => {
  return dictRequest<ChargeItem.ChargeItemInfo[]>(
    '/chargeItem/updateChargeItemById',
    params,
  );
};

/**
 * [1-10051-1]根据标识停启用医院的收费项目
 * @param params
 * @returns
 */
export const updateHospitalChargeItemEnabledFlagById = (
  params: ChargeItem.QueryParamsEnabeldFlag,
) => {
  return dictRequest<ChargeItem.ChargeItemInfo[]>(
    '/chargeItem/updateHospitalChargeItemEnabledFlagById',
    params,
  );
};

/**
 * [1-10147-1]根据条件查询医院的收费项目列表(业务态)
 * @param params
 * @returns
 */
export const queryHospitalChargeItemListByExample = (
  params: ChargeItem.QueryParamsHospitalChargeItem,
) => {
  return dictRequest<
    ChargeItem.HospitalChargeItemInfoItem,
    ChargeItem.QueryParamsHospitalChargeItem
  >('/chargeItem/queryHospitalChargeItemListByExample', params);
};
