import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10086-1]根据条件查询费别列表
 * @param params
 * @returns
 */
export const queryMedInsuranceByExample = (
  params: MedInsurance.MedInsuranceReqQuery,
) => {
  return dictRequest<MedInsurance.MedInsuranceReqItem[]>(
    '/medinsurance/queryMedInsuranceByExample',
    params,
  );
};

/**
 * [1-10087-1]	保存费别信息
 * @param params
 * @returns
 */
export const saveMedInsurance = (
  params: MedInsurance.SaveMedInsuranceReqQuery,
) => {
  return dictRequest<MedInsurance.SaveMedInsuranceReqItem>(
    '/medinsurance/saveMedInsurance',
    params,
  );
};

/**
 * [1-10088-1]根据标识修改费别排序
 * @param params
 * @returns
 */
export const updateMedInsuranceSortByIds = (params: {
  dictMedInsuranceList: MedInsurance.UpdateMedInsuranceSortReqQuery[];
}) => {
  return dictRequest('/medinsurance/updateMedInsuranceSortByIds', params, {
    successMsg: translation('global:modify.sort.success'),
  });
};

/**
 * [1-10160-1]根据条件查询菜单的医保费别列表
 * @param params
 * @returns
 */
export const querySysMenuMedInsuranceByExample = (
  params: MedInsurance.SysMenuMedInsuranceReqQuery,
) => {
  return dictRequest<MedInsurance.SysMenuMedInsuranceReqItem[]>(
    '/medinsurance/querySysMenuMedInsuranceByExample',
    params,
  );
};

/**
 * [1-10407-1]根据条件查询菜单的医保费别列表（定义态）
 * @param params
 * @returns
 */
export const queryMenuXMedInsuranceByExample = (
  params: MedInsurance.MenuXMedInsuranceReqParams,
) => {
  return dictRequest<MedInsurance.MenuXMedInsuranceReqItems[]>(
    '/medinsurance/queryMenuXMedInsuranceByExample',
    params,
  );
};

/**
 * [1-10406-1]保存菜单的医保费别
 * @param params
 * @returns
 */
export const saveMenuXMedInsurance = (
  params: MedInsurance.SaveMenuXMedInsuranceReqParams,
) => {
  return dictRequest('/medinsurance/saveMenuXMedInsurance', params);
};
