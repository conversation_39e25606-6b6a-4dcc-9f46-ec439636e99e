import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10043-1]根据条件查询商品费用分类列表
 * @param params
 * @returns
 */
export const queryCommodityCategoryListByExample = (
  params: ComodityCategory.QueryParams,
) => {
  return dictRequest<ComodityCategory.ComodityCategoryInfo[]>(
    '/commoditycategory/queryCommodityCategoryListByExample',
    params,
  );
};

/**
 * [1-10044-1]新增商品费用分类
 * @param params
 * @returns
 */
export const addCommodityCategory = (
  params: ComodityCategory.ComodityCategoryUpsertParams,
) => {
  return dictRequest<{ commodityCategoryId: string }>(
    '/commoditycategory/addCommodityCategory',
    params,
  );
};

/**
 * [1-10045-1]根据标识修改商品费用分类
 * @param params
 * @returns
 */
export const updateCommodityCategoryById = (
  params: ComodityCategory.ComodityCategoryUpsertParams,
) => {
  return dictRequest('/commoditycategory/updateCommodityCategoryById', params);
};

/**
 * [1-10046-1]根据标识停启用商品费用分类
 * @param params
 * @returns
 */
export const updateCommodityCategoryEnabledFlagById = (params: {
  commodityCategoryId: string;
  enabledFlag: number;
}) => {
  return dictRequest(
    '/commoditycategory/updateCommodityCategoryEnabledFlagById',
    params,
  );
};

/**
 * [1-10047-1]根据标识修改商品费用分类排序
 * @param data
 * @returns
 */
export const updateCommodityCategorySortByIds = (params: {
  dataSetSortList: {
    commodityCategoryId: string;
    sort: number;
  }[];
}) => {
  return dictRequest(
    '/commoditycategory/updateCommodityCategorySortByIds',
    params,
  );
};
