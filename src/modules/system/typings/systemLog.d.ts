declare namespace SystemLog {
  /** [94-10001-1]保存日志 入参 */
  interface SaveLogReqParams {
    logTypeCode: string;
    clientTraceId?: string;
    traceId?: string;
    spanId?: string;
    parentSpanId?: string;
    logGenerateAt: string;
    logAbstract?: string;
    exceptionFlag: number;
    logWriterTypeCode: string;
    logWriterName: string;
    userLoginId?: string;
    clientIpAddress?: string;
    serverIpAddress?: string;
    userId: string;
    expiredAt?: string;
    className?: string;
    methodName?: string;
    method?: string;
    invokeUseTime?: number;
    reqParam?: string;
    resParam?: string;
    logDetail?: string;
  }

  /** [94-10001-1]保存日志 出参 */
  interface SaveLogReqItem {
    appLogId: string;
  }

  /** [94-10002-1]根据条件查询日志列表 入参 */
  interface LogReqParams {
    pageNumber: number;
    pageSize: number;
    userId?: string;
    logGenerateBeginAt?: string; //同时
    logGenerateEndAt?: string; //同时
    clientTraceId?: string;
    traceId?: string;
    invokeUseTimeLower?: number;
    clientIpAddress?: string;
    serverIpAddress?: string;
    logTypeCode?: string;
    logWriterTypeCode?: string;
    logWriterName?: string;
    logKeyword?: string;
  }

  /** [94-10002-1]根据条件查询日志列表 出参 */
  interface LogReqItem {
    appLogId: string;
    logTypeCode: string;
    logTypeDesc: string;
    clientTraceId?: string;
    traceId?: string;
    spanId?: string;
    parentSpanId?: string;
    logGenerateAt: string;
    logAbstract: string;
    exceptionFlag: number;
    logWriterTypeCode: string;
    logWriterTypeDesc: string;
    logWriterName: string;
    userLoginId?: string;
    clientIpAddress?: string;
    serverIpAddress?: string;
    userId: string;
    userName?: string;
    expiredAt?: string;
    className?: string;
    methodName?: string;
    method?: string;
    invokeUseTime?: number;
    isSubLog?: boolean;
    subLogList?: LogReqItem[];
  }

  /** [94-10003-1]根据条件清空日志 入参 */
  //   interface DeleteLogReqParams {}

  /** [94-10003-1]根据条件清空日志 出参 */
  //   interface DeleteLogReqItem {}

  /** [94-10004-1]根据条件下载日志 入参 */
  interface DownloadLogReqParams {
    userId?: string;
    logGenerateBeginAt?: string; //同时
    logGenerateEndAt?: string; //同时
    clientTraceId?: string;
    traceId?: string;
    invokeUseTimeLower?: number;
    clientIpAddress?: string;
    serverIpAddress?: string;
    logTypeCode?: string;
    logWriterTypeCode?: string;
    logWriterName?: string;
    logKeyword?: string;
  }

  /** [94-10004-1]根据条件下载日志 出参 */
  interface DownloadLogReqItem {
    logFile: string;
  }

  /** [94-10005-1]根据标识查询日志详情 入参 */
  interface LogDetailReqParams {
    appLogId: string;
  }

  /** [94-10005-1]根据标识查询日志详情 出参 */
  interface LogDetailReqItem {
    appLogId: string;
    logTypeCode: string;
    logTypeDesc: string;
    clientTraceId?: string;
    traceId?: string;
    spanId?: string;
    parentSpanId?: string;
    logGenerateAt: string;
    logAbstract: string;
    exceptionFlag: number;
    logWriterTypeCode: string;
    logWriterTypeDesc: string;
    logWriterName: string;
    userLoginId?: string;
    clientIpAddress?: string;
    serverIpAddress?: string;
    userId: string;
    userName: string;
    expiredAt?: string;
    className?: string;
    methodName?: string;
    method?: string;
    invokeUseTime?: string;
    reqParam?: string;
    resParam?: string;
    logDetail?: string;
    isSubLog?: boolean;
  }

  /** [94-10006-1]保存日志() 入参 */
  interface SaveLogForClientReqParams {
    logTypeCode: string;
    clientTraceId?: string;
    traceId?: string;
    logGenerateAt: string;
    logAbstract?: string;
    exceptionFlag: number;
    logWriterTypeCode: string;
    logWriterName: string;
    userLoginId?: string;
    clientIpAddress?: string;
    serverIpAddress?: string;
    userId: string;
    expiredAt?: string;
    className?: string;
    methodName?: string;
    method?: string;
    invokeUseTime?: number;
    reqParam?: string;
    resParam?: string;
    logDetail?: string;
  }

  /** [94-10006-1]保存日志() 出参 */
  interface SaveLogForClientReqItem {
    appLogId: string;
  }
}
