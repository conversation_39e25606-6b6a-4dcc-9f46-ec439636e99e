declare namespace Menu {
  /**
   * 分支信息
   */
  interface CodeBranchInfo {
    codeRepositoryBranchId: string;
    codeBranchId: string;
    codeBranchName: string;
  }
  /**
   * 代码仓库信息
   */
  interface CodeRepositoryInfo {
    systemDefId: string;
    codeRepositoryId: string;
    codeRepositoryName: string;
    codeRepositoryDesc: string;
    codeRepositoryTypeCode: string;
    codeRepositoryTypeCodeDesc: string;
    sort: number;
    enabledFlag: number;
    codeBranchList: CodeBranchInfo[];
  }

  interface PageElementInfo {
    pageElementId: string;
    pageElementNo: string;
    pageElementName: string;
    defaultAllowUseFlag: number;
    enabledFlag: number;
    sort: number;
    spellNo?: string;
    wbNo?: string;
  }

  interface MenuInfo {
    menuId: string;
    sysId: string;
    menuName: string;
    menu2ndName: string;
    menuExtName: string;
    menuNameDisplay: string;
    url: string;
    groupName?: string;
    sysName: string;
    label?: string;
    iconUri: string;
    menuSourceCode: string;
    menuSourceDesc: string;
    enabledFlag: number;
    spellNo: string;
    wbNo: string;
    createdOrgLocationId: number;
    createdOrgLocationName: string;
    createdUserId: number;
    createdUserName: string;
    createdAt: string;
    modifiedOrgLocationId: number;
    modifiedOrgLocationName: string;
    modifiedUserId: number;
    modifiedUserName: string;
    parentSysId: string;
    parentSysXMenuId?: string;
    modifiedAt: string;
    sysXMenuId: string;
    menuFlag: 0 | 1;
    edit?: boolean;
    identification: string;
    pageElementList: PageElementInfo[];
    subSysXMenuList: MenuInfo[];
  }

  interface SystemInfo {
    accessFlag: number;
    sysId: string;
    groupName: string;
    label?: string;
    enabledFlag: number;
    sysName: string;
    sys2ndName: string;
    sysExtName: string;
    sysNameDisplay: string;
    url: string;
    iconUri: string;
    sort: number;
    spellNo: string;
    wbNo: string;
    createdOrgLocationId: number;
    createdOrgLocationName: string;
    createdUserId: number;
    createdUserName: string;
    createdAt: string;
    modifiedOrgLocationId: number;
    modifiedOrgLocationName: string;
    modifiedUserId: number;
    modifiedUserName: string;
    modifiedAt: string;
    parentSysXMenuId?: string;
    sysXMenuList?: MenuInfo[];
    identification?: string;
    label?: string;
    subSysXMenuList: MenuInfo[];
    codeRepositoryList?: CodeRepositoryInfo[];
    devGroupCode: string;
    devGroupCodeDesc: string;
  }

  type MixSystemMenuElement = Partial<SystemInfo> &
    Partial<MenuInfo> &
    Partial<ElementInfo> & {
      sysSpellNo?: string;
      sysWbNo?: string;
      groupSpellNo?: string;
      groupWbNo?: string;
      menuSpellNo?: string;
      menuWbNo?: string;
    };

  interface ResAddSystemParams {
    enabledFlag: 0 | 1;
    sort: number;
    sysName: string;
    sys2ndName: string;
    sysExtName: string;
    url: string;
    iconUri: string;
    spellNo: string;
    wbNo: string;
    devGroupCode?: string;
    accessFlag: number;
  }

  interface ResUpdateSystemParams extends ResAddSystemParams {
    sysId: string;
  }

  interface ResAddMenuParams {
    enabledFlag: 0 | 1;
    sysId: string;
    menuName: string;
    menu2ndName: string;
    menuExtName: string;
    url: string;
    iconUri: string;
    menuSourceCode: string;
    enabledFlag: string;
    spellNo: string;
    wbNo: string;
    assignTo?: string;
  }

  interface ResUpdateMenuParams extends ResAddMenuParams {
    menuId: string;
  }

  interface ResAddMenuStructParams {
    sysId?: string;
    menuId: string;
    enabledFlag: 0 | 1;
    menuName: string;
    menu2ndName: string;
    menuExtName: string;
    menuFlag?: 0 | 1;
    spellNo: string;
    wbNo: string;
    parentSysXMenuId?: string;
  }

  interface ResUpdateMenuStructByIdParams {
    sysXMenuId: string;
    menuName: string;
    menu2ndName?: string;
    menuExtName?: string;
    spellNo?: string;
    wbNo?: string;
  }

  interface ElementInfo {
    pageElementId: string;
    menuId: string;
    menuName: string;
    pageElementNo: string;
    pageElementName: string;
    defaultAllowUseFlag: number;
    enabledFlag: number;
    sort: number;
    spellNo?: string;
    wbNo?: string;
  }

  type AddElement = Omit<ElementInfo, 'pageElementId'>;

  interface UpdateElement extends AddElement {
    pageElementId: string;
  }

  interface HospitalXSysInfo {
    hospitalXSysId: string;
    hospitalId: string;
    hospitalNameDisplay: string;
    sysId: string;
    sysNameDisplay: string;
    sort: number;
    spellNo: string;
    wbNo: string;
  }

  interface systemType {
    value: string;
    label?: string;
  }
}
