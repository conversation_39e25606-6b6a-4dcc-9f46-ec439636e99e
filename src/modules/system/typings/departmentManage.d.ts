declare namespace DepartmentManage {
  interface RequestParameters {
    keyWord?: string; //关键字
    enabledFlag: -1 | 1 | 0 | undefined; //启用标志
    hospitalId: string; //所属医院标识
  }

  interface getExampleTree {
    deptTypeDesc: string; //科室类型
    orgNo: string; //科室编码
    orgName: string; //科室名称
    orgNameDisplay: string; //科室英文名称
    orgDesc: string; //简介
    enabledFlag: number; //启用标识
    orgId: string; //组织标识
    addSubordinates?: boolean;
    orgXContactList?: {
      contactNo?: string;
      contactTypeCode?: string;
      contactTypeCodeDesc?: string;
    }[];
  }

  interface RequestParametersUpdateOrgEnabledFlagById {
    orgId: string; //组织标识
    enabledFlag: number; //启用标识
  }

  interface HandleAddOrgItem {
    parentOrgId: row.orgId;
    parentOrgName: string;
    tenantId: string;
    orgTypeCode: string;
    disabledOrgType: boolean;
    parentOrgTypeCode: string;
  }
}
