declare namespace MsChargeSetting {
  interface SearchParams {
    hospitalId?: string;
    enabledFlag?: number;
  }

  interface ServiceList {
    msNo: string;
    msName: string;
    msTypeDesc: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    billingSettings: number;
    startAt: string; // 开始日期时间
    endAt: string;
    existsEnableChargeSettingFlag: number;
    msId: string; // 医疗服务标识
    msChargeTypeCode: string; // 医疗服务计费方式代码
    msTypeCode: string;
  }

  interface Query {
    pageNumber: number;
    pageSize: number;
    tota?: number;
  }

  interface SettingListReqParam {
    msIds: string[]; // 服务标识集合
    hospitalId: string; // 医院标识
    onlyQueryUsingFlag: number; // 仅查使用中标志
    enabledFlag?: number; // 启用标志
  }

  // 根据条件查询服务的计费对象列表入参
  interface MsChargeObjectReqParam {
    msId: string; // 服务标识
    msChargeTypeCode?: string; // 医疗服务计费方式代码
  }

  interface IUpdateEnabledFlagReqParam {
    msChargeTypeSettingId: string; // 医疗服务计费方式设置标识
    enabledFlag: number; // 启用标志
  }

  // 支持的医疗服务计费方式列表项目
  interface MsChargeTypeSupported {
    msChargeTypeCode: string; // 医疗服务计费方式代码
    msChargeTypeDesc: string; // 医疗服务计费方式描述
    msChargeObjectList?: MsChargeObject[]; // 支持的医疗服务计费方式列表
  }

  // 支持的医疗服务计费方式列表项
  interface MsChargeObject {
    msChargeObjectName: string; // 医疗服务计费对象名称
    msChargeObjectId: string; // 医疗服务计费对象标识
    msChargeObjectTypeCode: string; // 医疗服务计费对象类别代码
    msChargeObjectTypeDesc: string; // 医疗服务计费对象类别描述
  }

  interface RequestData {
    msChargeTypeCode: string;
    msChargeObjectTypeCode: string;
    msChargeObjectId: string;
  }

  interface MsChargeTypeSettingList {
    startAt: string;
    state: string;
    msId: string;
    csTypeCode: string;
    msChargeObjectName?: string;
    msChargeTypeDtList: MsChargeTypeDtList[];
    msChargeObjectId?: string;
    msChargeObjectTypeCode?: string;
  }

  // 医疗服务计费方式设置列表项
  interface MsChargeTypeSetting {
    msChargeTypeSettingId: string; // 医疗服务计费方式设置标识
    msChargeTypeCode: string; // 医疗服务计费方式代码
    msChargeTypeDesc: string; // 医疗服务计费方式描述
    msId: string; // 医疗服务标识
    startAt: string; // 开始日期时间
    endAt: string; // 结束日期时间
    usingFlag: number; // 使用中标志
    enabledFlag: number; // 启用标志
    msChargeTypeDtList?: MsChargeTypeDt[]; // 医疗服务计费方式明细列表
  }

  // 医疗服务计费方式明细列表项
  interface MsChargeTypeDt {
    msChargeTypeDtId: string; // 医疗服务计费方式明细标识
    msChargeObjectTypeCode: string; // 医疗服务计费对象类别代码
    msChargeObjectTypeDesc: string; // 医疗服务计费对象类别描述
    msChargeObjectId: string; // 医疗服务计费对象标识
    msChargeObjectName: string; // 医疗服务计费对象名称
    rangeLowerNum?: number; // 区间下限数量
    rangeUpperNum?: number; // 区间上限数量
    msChargeDtList?: MsChargeDt[]; // 医疗服务计费明细列表
  }

  // 医疗服务计费明细列表项
  interface MsChargeDt {
    msChargeDtId: string; // 医疗服务计费明细标识
    hospitalCommodityId: string; // 医院商品标识
    commodityId: string; // 商品标识
    commodityTypeCode: string; // 商品类型代码
    commodityTypeDesc: string; // 商品类型描述
    commodityNo?: string; // 商品编码
    commodityNameDisplay: string; // 商品名称（语言环境）
    commodityPurchasePrice: number; // 商品进价
    price: string; // 销售价格
    commoditySpec?: string; // 商品规格
    unitId: string; // 单位标识
    unitName: string; // 单位名称（语言环境）
    num: number; // 数量
  }

  interface ChargeItemList {
    hospitalId: string;
    priceAt?: string;
    encounterTypeCode?: string;
    enabledFlag?: number;
  }

  interface ListOfPaidDrugs {
    commodityCategoryName?: string;
    commodityNo?: string;
    commodityNameDisplay?: string;
    price?: number;
    unitName?: string;
    commodityId?: string;
    hospitalCommodityId?: string;
  }

  interface MsChargeTypeDtList extends ListOfPaidDrugs {
    msChargeObjectName?: string;
    msName?: string;
    editable?: boolean;
    quantity?: string;
    msChargeObjectTypeCode?: string;
    productType?: string;
    num?: number;
  }

  // 医疗服务计费方式明细项
  interface msChargeTypeDtItem {
    msChargeObjectTypeCode: string; // 医疗服务计费对象类别代码
    msChargeObjectId: string; // 医疗服务计费对象标识
    rangeLowerNum?: number; // 区间下限数量
    rangeUpperNum?: number; // 区间上限数量
    // 医疗服务计费明细列表
    msChargeDtList: {
      hospitalCommodityId: string; // 医院商品标识
      commodityId: string; // 商品标识
      num: number; // 数量
    }[];
  }

  // 保存接口入参类型
  interface MsChargeSaveReqParam
    extends Pick<
      MsChargeSetting.ServiceList,
      'msChargeTypeCode' | 'startAt' | 'msId'
    > {
    // 医疗服务计费方式明细列表
    msChargeTypeDtList?: msChargeTypeDtItem[];
  }

  // 新计费设置：计费方式表单
  interface MsChargeSelectForm {
    csTypeCode: string;
    startAt: string;
    state: 0 | 1;
  }

  interface MsChargeTableItem {
    containAddButton?: boolean; // 是否是添加区间按钮
    editable?: boolean; // 行是否可编辑
    msChargeTypeCode?: string;
    msChargeTypeDesc?: string;
    msChargeObjectId: string;
    msChargeObjectName: string;
    msChargeObjectTypeCode: string; // 计费对象编码
    msChargeObjectTypeDesc: string; // 计费对象名称
    rangeLowerNum?: number; // 区间下限
    rangeUpperNum?: number; // 区间上限
    commodityNo?: string; // 费用编码
    commodityId?: string; // 商品标识
    hospitalCommodityId?: string; // 医院商品标识
    commodityNameDisplay?: string; // 费用名称
    price?: string; // 单价
    unitName?: string; // 单位
    num?: number; // 数量
  }

  interface MsChargeSettingOperation {
    insert: (row: MsChargeSetting.MsChargeTableItem, index: number) => void; // 插入
    edit: (row: MsChargeSetting.MsChargeTableItem) => void; // 编辑
    remove: (index: number) => void; // 移除
    addRange: (row: MsChargeSetting.MsChargeTableItem, index: number) => void; // 添加区间
  }

  interface IChargeObjectMapValue {
    start: number;
    end: number;
    list: MsChargeSetting.MsChargeTableItem[];
    [key: number]: { start: number; end: number };
  }

  interface IServiecInfoHospitalIdMsId {
    hospitalId: string; // 医院标识
    msId: string; // 服务标识集合
    msNo: string; // 医疗服务编码
    msName: string; // 医疗服务名称
  }
}
