import { Ref } from 'vue';
import type { OrgInfo } from './useOrgColumnConfig';
import { useFormConfig } from 'sun-biz';
import Upload from '@/components/Upload/index.vue';
import { ORG_TYPE_CODE } from '@/utils/constant';

export type OrgEnvSetting = {
  applicationTitle?: string;
  copyrightDesc?: string;
  backgroundPic?: string;
  logo?: string;
};

/** 环境配置信息 */
export function useOrgEnvConfig(
  orgInfo: Ref<OrgInfo>,
  orgEnvSetting: Ref<OrgEnvSetting>,
) {
  return useFormConfig({
    getData: (t) => {
      if (
        orgInfo.value.orgTypeCode !== ORG_TYPE_CODE.HOSPITAL &&
        orgInfo.value.orgTypeCode !== ORG_TYPE_CODE.GROUP
      ) {
        return [];
      }
      return [
        {
          name: 'applicationTitle',
          label: t('org.appTitle', '程序标题'),
          component: 'input',
        },
        {
          name: 'copyrightDesc',
          label: t('org.copyrightDesc', '版权描述'),
          component: 'input',
        },
        {
          name: 'backgroundPic',
          label: t('org.bgPic', '背景图片'),
          isFullWidth: true,
          render: () => (
            <Upload
              desc={t('org.bgPic', '背景图片')}
              v-model={orgEnvSetting.value.backgroundPic}
            />
          ),
        },
        {
          name: 'logo',
          label: t('org.logo', 'LOGO'),
          isFullWidth: true,
          render: () => (
            <Upload
              max-size={50}
              desc={t('org.logo', 'LOGO')}
              v-model={orgEnvSetting.value.logo}
            />
          ),
        },
      ];
    },
  });
}
