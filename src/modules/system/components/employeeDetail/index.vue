<script setup lang="ts" name="employeeDetail">
  import { useTranslation } from 'i18next-vue';
  import { useGetWard } from './hooks/useGetWard';
  import { useGetDepartment } from './hooks/useGetDepartment';
  import { ElMessageBox, type FormInstance } from 'element-sun';
  import { queryOrgAndHospitalList } from '@/modules/system/api/org';
  import { useFetchParamsConfig } from '@/hooks/useFetchParamsConfig';
  import { useEmployeeInfoConfig } from './config/useEmployeeInfoConfig';
  import { addEmployee, updateEmployee } from '@modules/system/api/employee';
  import {
    ref,
    watch,
    computed,
    nextTick,
    onMounted,
    shallowRef,
    watchEffect,
    onBeforeMount,
  } from 'vue';
  import {
    Title,
    ProForm,
    ProTable,
    TableRef,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    USER_JOB_CODE,
    CONTACT_TYPE_CODE,
    DEFAULT_PASSWORD_PARAM_NO,
  } from '@/utils/constant';
  import {
    useRoleColumnConfig,
    useConcatNoColumnConfig,
    usePerCertificateColumnConfig,
  } from './config/useEmployeeColumnConfig';

  interface RoleSetting {
    orgId: string;
    orgNameDisPlay: string;
    userRolesList: string[];
    editable: boolean;
    loginOrgLocationList: string[];
    bizUnitIds: string[];
  }

  type EmployeeInfo = Omit<
    Employee.Item,
    | 'userRoleList'
    | 'loginOrgLocationList'
    | 'perCertificateList'
    | 'bizUnitIds'
  >;

  const props = defineProps<{
    isAdd?: boolean;
    disabled?: boolean;
    isPersonalInfo?: boolean;
    detailData: {
      employeeData: Employee.Item;
      userInfo: Employee.UserInfo;
      employeeInfo: EmployeeInfo;
    };
  }>();

  const emit = defineEmits(['back']);

  const { t } = useTranslation();
  const { wardList, getWardList } = useGetWard();
  const { departmentList, getDepartmentList } = useGetDepartment();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const submitLoading = ref(false);
  const contactNoRef = ref<TableRef>();
  const perCertificateRef = ref<TableRef>();
  const orgList = shallowRef<Org.Item[]>([]);
  const employeeFormRef = ref<{
    ref: FormInstance;
    model: Employee.Item;
    getItemRef: (name: string) => HTMLInputElement | null;
  }>();
  const userFormRef = ref<{
    ref: FormInstance;
    model: Employee.Item;
  }>();

  const isAdd = computed(() => props.isAdd ?? false);
  const isDisabled = computed(() => props.disabled ?? false);

  // 职工信息
  const employeeData = ref<Employee.Item>();
  const employeeInfo = ref<EmployeeInfo>({
    userStatusCode: '1',
  } as EmployeeInfo);
  const userInfo = ref<Employee.UserInfo>({} as Employee.UserInfo);

  // 医院设置列表
  const hospitalSetting = ref<RoleSetting[]>([]);
  // 联系号码
  const perContactData = ref<Employee.ContactNoItem[]>([]);
  // 证件信息
  const perCertificateData = ref<Employee.CertificateItem[]>([]);

  // 保存
  const handleSubmit = async () => {
    if (!employeeFormRef.value || !userFormRef.value) return;
    // 校验职工基本信息
    const isEmployeeValid = await employeeFormRef.value.ref.validate();
    // 校验用户信息
    const isUserValid = await userFormRef.value.ref.validate();
    if (!isEmployeeValid || !isUserValid) return;
    // 编辑状态 校验证件信息
    if (!isAdd.value) {
      await perCertificateRef?.value?.formRef?.validate();
    }
    const submitFn = isAdd.value ? addEmployee : updateEmployee;
    const { certificateTypeCode, certificateNo, contactNo, ...restInfo } =
      employeeFormRef.value.model;
    // 证件
    const perCertificateList = isAdd.value
      ? [
          {
            certificateTypeCode,
            certificateNo,
          },
        ]
      : perCertificateData.value;
    const perContactList = isAdd.value
      ? contactNo
        ? [
            {
              contactTypeCode: CONTACT_TYPE_CODE.MOBILE,
              contactNo,
            },
          ]
        : undefined
      : perContactData.value.map((item: Employee.ContactNoItem) => ({
          contactTypeCode: item.contactTypeCode,
          contactNo: item.contactNo,
        }));
    // 校验证件信息非编辑状态
    if (
      !isAdd.value &&
      perCertificateData.value.find(
        (item: Employee.CertificateItem) => item.editable,
      )
    ) {
      await ElMessageBox.confirm(
        t('global:tip.edit.submit.template', {
          name: t('people.certificateInfo', '证件信息'),
        }),
        t('global:tip'),
      );
      perCertificateData.value.forEach((item: Employee.CertificateItem) => {
        item.editable = false;
      });
    }
    // 校验联系方式非编辑状态
    if (
      !isAdd.value &&
      perContactData.value.find((item: Employee.ContactNoItem) => item.editable)
    ) {
      await ElMessageBox.confirm(
        t('employee.contact.tip', '联系方式存在编辑中的数据，请先确认！'),
        t('global:tip'),
      );
      return;
      // perContactData.value.forEach((item: Employee.ContactNoItem) => {
      //   item.editable = false;
      // });
    }
    submitLoading.value = true;
    const [, res] = await submitFn({
      ...(employeeData.value || {}),
      ...restInfo,
      ...employeeInfo.value,
      ...userInfo.value,
      // 证件信息
      perCertificateList,
      // 联系方式
      perContactList,
      // 角色
      userRoles: hospitalSetting.value
        .flatMap((item) => item.userRolesList)
        ?.filter((id) => !!id),
      // 服务院区
      loginOrgLocationList: hospitalSetting.value
        .flatMap((item) => item.loginOrgLocationList)
        ?.filter((id) => !!id)
        ?.map((id, index) => ({
          orgLocationId: id,
          sort: index + 1,
        })),
      bizUnitIds: hospitalSetting.value
        .flatMap((item) => item.bizUnitIds)
        ?.filter((id) => !!id),
    });
    submitLoading.value = false;
    if (res?.data) {
      showDefaultPassword();
    }
  };

  /**添加证件 */
  const addPerCertificate = () => {
    addCertificateItem({
      perCertificateId: '',
      certificateNo: '',
      certificateTypeCode: '',
      editable: true,
    });
  };

  /**添加联系方式  */
  const addPerContactNo = () => {
    addContactNoItem({
      contactTypeCode: '',
      contactNo: '',
      perContactId: '',
      editable: true,
    });
  };

  const showDefaultPassword = () => {
    ElMessageBox.alert(
      t(
        'createEmployeeSuccessTip',
        `新建成功，默认密码: ${defaultPassword?.[DEFAULT_PASSWORD_PARAM_NO]?.[0]?.paramValue ?? '--'}`,
      ),
      t('global:tip'),
      {
        confirmButtonText: t('hasKnow', '知道了'),
        callback: () => {
          emit('back');
        },
      },
    );
  };

  /** 切换岗位 */
  async function changeRole(role: string) {
    if (role === USER_JOB_CODE.NURSE) {
      hospitalSetting.value = hospitalSetting.value.map((item) => {
        item.bizUnitIds = [];
        return item;
      });
    }
  }

  watch(
    () => userInfo.value.userJobCode,
    async (newValue, oldValue) => {
      if (oldValue === USER_JOB_CODE.NURSE) {
        hospitalSetting.value = hospitalSetting.value.map((item) => {
          item.bizUnitIds = [];
          return item;
        });
      }
    },
  );

  watch(
    () => props.detailData,
    async (newValue) => {
      employeeData.value = newValue.employeeData;
      employeeInfo.value = newValue.employeeInfo;
      userInfo.value = newValue.userInfo;
    },
    {
      immediate: true,
      deep: true,
    },
  );

  watchEffect(() => {
    if (orgList.value?.length > 0) {
      hospitalSetting.value = orgList.value.map((item) => ({
        ...item,
        orgId: item?.orgId || '',
        editable: true,
        userRolesList:
          employeeData.value?.userRoleList
            ?.filter((role) => role.hospitalId === item.orgId && role?.roleId)
            ?.map((item) => item.roleId) || [],
        loginOrgLocationList:
          employeeData.value?.loginOrgLocationList
            ?.filter((location) => location?.orgId === item.orgId)
            ?.map((item) => item.orgLocationId) || [],
        bizUnitIds:
          employeeData.value?.bizUnitList
            ?.filter((bizUnit) => bizUnit.hospitalId === item.orgId)
            ?.map((item) => item.bizUnitId) || [],
      }));
    }
    // 同步证件信息
    if (employeeData.value?.perCertificateList) {
      perCertificateData.value = employeeData.value.perCertificateList ?? [];
    }
    // 同步练习方式
    if (employeeData.value?.perContactList) {
      perContactData.value = employeeData.value.perContactList ?? [];
    }
  });

  onBeforeMount(async () => {
    const [, res] = await queryOrgAndHospitalList();
    if (res?.data) {
      orgList.value = res.data ?? [];
    }
  });
  onMounted(() => {
    if (isAdd.value) {
      nextTick(() => {
        const empNoRef = employeeFormRef?.value?.getItemRef('empNo');
        if (empNoRef) {
          empNoRef.focus();
        }
      });
    }
  });

  const defaultPassword = useFetchParamsConfig({
    hospitalId: currentOrg?.orgId as string,
    paramNos: [DEFAULT_PASSWORD_PARAM_NO],
  });
  const config = useEmployeeInfoConfig({
    isAdd: isAdd.value,
    employeeData: employeeData,
    employRef: employeeFormRef,
    userInfo,
    employeeInfo,
    changeRole,
    isDisabled,
  });
  const roleColumn = useRoleColumnConfig({
    userInfo,
    employeeInfo,
    departmentList,
    getDepartmentList,
    wardList,
    getWardList,
    isDisabled,
  });
  const { columnConfig: perCertificateColumn, addItem: addCertificateItem } =
    usePerCertificateColumnConfig({
      tableRef: perCertificateRef,
      data: perCertificateData,
    });
  const { columnConfig: contactNoColumn, addItem: addContactNoItem } =
    useConcatNoColumnConfig({
      tableRef: contactNoRef,
      data: perContactData,
    });
</script>

<template>
  <div
    class="mb-2 h-full overflow-auto"
    :style="{
      height: isPersonalInfo ? 'calc(100% - 2.5rem)' : 'calc(100% - 5rem)',
    }"
  >
    <el-scrollbar view-class="mr-2.5 mb-4">
      <Title
        :title="$t('employee.baseInfo', '职工信息')"
        :class="isPersonalInfo ? 'mb-3' : 'my-3'"
      />
      <ProForm
        ref="employeeFormRef"
        v-model="employeeInfo"
        :use-item-refs="true"
        :data="config.employeeInfo"
      />

      <template v-if="!isAdd && employeeData">
        <ProTable
          ref="perCertificateRef"
          :columns="perCertificateColumn"
          :editable="true"
          :data="employeeData.perCertificateList"
        >
        </ProTable>
        <el-button
          class="rounded-0 w-full border-t-0"
          style="border-top-width: 0; border-radius: 0"
          icon="Plus"
          @click="addPerCertificate"
          >{{ $t('global:add') }}</el-button
        >
        <ProTable
          class="mt-2"
          ref="contactNoRef"
          :columns="contactNoColumn"
          :editable="true"
          :data="employeeData.perContactList"
        >
        </ProTable>
        <el-button
          class="rounded-0 w-full border-t-0"
          style="border-top-width: 0; border-radius: 0"
          icon="Plus"
          @click="addPerContactNo"
          >{{ $t('global:add') }}</el-button
        >
      </template>
      <Title :title="$t('user.baseInfo', '用户信息')" class="my-3" />
      <ProForm ref="userFormRef" v-model="userInfo" :data="config.userInfo" />

      <Title
        :title="$t('user.role.relationship', '角色与服务关系')"
        class="my-3"
      />
      <ProTable
        :columns="roleColumn"
        :editable="true"
        :data="hospitalSetting"
      />
    </el-scrollbar>
  </div>
  <div class="mr-2.5 text-right">
    <el-button @click="emit('back')" v-show="!isPersonalInfo">
      {{ $t('global:cancel') }}
    </el-button>
    <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
      {{ $t('global:save') }}
    </el-button>
  </div>
</template>
