import { Ref, ref, ComputedRef } from 'vue';
import { validatePhone, validateID } from '@sun-toolkit/shared';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import {
  ORG_TYPE_CODE,
  USER_JOB_CODE,
  CERTIFICATE_TYPE_CODE,
  CERTIFICATE_TYPE_CODE_NAME,
  CONTACT_TYPE_CODE,
  CONTACT_TYPE_CODE_NAME,
} from '@/utils/constant';

export function useRoleColumnConfig(options: {
  isDisabled: ComputedRef<boolean>;
  employeeInfo: Ref<Employee.Item, Employee.Item>;
  userInfo: Ref<Employee.UserInfo>;
  wardList: Ref<Org.Item[]>;
  departmentList: Ref<Org.Item[]>;
  getWardList: (params: Org.queryWardParams) => Promise<void>;
  getDepartmentList: (params: Org.queryDepartmentParams) => Promise<void>;
}) {
  const {
    userInfo,
    employeeInfo,
    wardList,
    departmentList,
    getWardList,
    getDepartmentList,
    isDisabled,
  } = options;
  const loading = ref<boolean>(false);
  const getBizUnitList = (employeeInfo: Ref<Employee.Item>, orgId: string) => {
    return (employeeInfo.value?.bizUnitList ?? [])
      .filter((item) => item.hospitalId === orgId)
      .map((item) => ({
        ...item,
        orgId: item.bizUnitId,
        orgNameDisplay: item.bizUnitName,
      }));
  };

  const getOrgList = (
    userJobCode: string,
    wardList: Ref<Org.Item[]>,
    departmentList: Ref<Org.Item[]>,
  ) => {
    return userJobCode === USER_JOB_CODE.NURSE
      ? wardList.value
      : departmentList.value;
  };

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber', '序号'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('group&hospital', '集团&医院'),
        minWidth: 120,
        prop: 'orgName',
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('group&hospital', '集团&医院'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: { orgName: string }) => <>{row.orgName}</>,
      },

      {
        label: t('working.hospital.zone', '服务院区'),
        prop: 'loginOrgLocationList',
        minWidth: 250,
        editable: true,
        render: (row: {
          orgTypeCode: string;
          loginOrgLocationList: string[];
          orgLocationList: Org.LocationItem[];
          editable: boolean;
        }) => {
          return (
            <div style="height: 34px" class="w-full">
              <el-select
                v-model={row.loginOrgLocationList}
                multiple
                filterable
                collapse-tags
                clearable={false}
                collapse-tags-tooltip
                disabled={isDisabled.value}
                placeholder={t('global:placeholder.select.template', {
                  name: t('working.hospital.zone', '服务院区'),
                })}
              >
                {(row?.orgLocationList ?? [])?.map((item) => (
                  <el-option
                    key={item.orgLocationId}
                    label={item.orgLocationName}
                    value={item.orgLocationId}
                  />
                ))}
              </el-select>
            </div>
          );
        },
      },
      {
        label:
          userInfo.value.userJobCode === USER_JOB_CODE.NURSE
            ? t('working.Ward.zone', '服务病区')
            : t('working.Department.zone', '服务科室'),
        prop: 'loginOrgLocationList',
        minWidth: 250,
        editable: true,
        render: (row: {
          orgTypeCode: string;
          orgId: string;
          bizUnitList: Employee.BizUnitItem[];
          bizUnitIds: string[];
          editable: boolean;
        }) => {
          const arr =
            getOrgList(userInfo.value.userJobCode, wardList, departmentList) ??
            [];
          const bizUnitListArr = getBizUnitList(employeeInfo, row.orgId) ?? [];

          return row.orgTypeCode === ORG_TYPE_CODE.GROUP ? (
            <div class={'flex-1 justify-center'}>--</div>
          ) : (
            <div style="height: 34px" class="w-full">
              <el-select
                loading={loading.value}
                v-model={row.bizUnitIds}
                remote
                multiple
                filterable
                collapse-tags
                clearable={true}
                remote-show-suffix
                collapse-tags-tooltip
                disabled={isDisabled.value}
                remote-method={async (keyWord: string) => {
                  if (userInfo.value.userJobCode === USER_JOB_CODE.NURSE) {
                    loading.value = true;
                    await getWardList({
                      keyWord,
                      hospitalId: row?.orgId,
                    });
                    loading.value = false;
                  } else {
                    loading.value = true;
                    await getDepartmentList({
                      keyWord,
                      hospitalId: row?.orgId,
                    });
                    loading.value = false;
                  }
                }}
                placeholder={t('global:placeholder.select.template', {
                  name:
                    userInfo.value.userJobCode === USER_JOB_CODE.NURSE
                      ? t('working.Ward.zone', '服务病区')
                      : t('working.Department.zone', '服务科室'),
                })}
              >
                {(arr.length > 0
                  ? arr
                  : (bizUnitListArr as (Employee.BizUnitItem & {
                      orgId: string;
                      orgNameDisplay: string;
                    })[])
                ).map((item) => (
                  <el-option
                    key={item.orgId}
                    label={item.orgNameDisplay}
                    value={item.orgId}
                  ></el-option>
                ))}
              </el-select>
            </div>
          );
        },
      },
      {
        label: t('role.roleName', '角色'),
        prop: 'userRolesList',
        minWidth: 320,
        editable: true,
        render: (row: {
          orgTypeCode: string;
          userRolesList: string[];
          roleList: { roleId: string; roleName: string }[];
        }) => {
          return row.orgTypeCode === ORG_TYPE_CODE.GROUP ? (
            <div class={'flex-1 justify-center'}>--</div>
          ) : (
            <>
              <el-select
                v-model={row.userRolesList}
                multiple
                filterable
                disabled={isDisabled.value}
                placeholder={t('global:placeholder.select.template', {
                  name: t('role.roleName', '角色'),
                })}
              >
                {(row?.roleList ?? [])?.map((item) => (
                  <el-option
                    key={item.roleId}
                    label={item.roleName}
                    value={item.roleId}
                  />
                ))}
              </el-select>
            </>
          );
        },
      },
    ],
  });
}

export function usePerCertificateColumnConfig(options: {
  tableRef: Ref<TableRef>;
  data: Ref<Employee.CertificateItem[]>;
}) {
  const { tableRef, data } = options;
  const { toggleEdit, addItem, cancelEdit } = useEditableTable({
    tableRef,
    data,
    id: 'perCertificateId',
  });
  const dataSetCodes = [CERTIFICATE_TYPE_CODE_NAME];

  const columnConfig = useColumnConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        label: t('person.certificateType', '证件类型'),
        prop: 'certificateTypeCode',
        minWidth: 120,
        editable: true,
        placeholder: t('global:placeholder.select', {
          name: t('person.certificateType', '证件类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select'),
            trigger: 'blur',
          },
        ],
        render: (row: Employee.CertificateItem) => {
          const certificateTypeList =
            dataSet?.value?.[CERTIFICATE_TYPE_CODE_NAME];
          return row.editable ? (
            <el-select
              v-model={row.certificateTypeCode}
              filterable
              placeholder={t('global:placeholder.select.template', {
                name: t('person.certificateType', '证件类型'),
              })}
              onChange={(val: string) => {
                row.certificateTypeDesc =
                  certificateTypeList?.find((item) => item.dataValueNo === val)
                    ?.dataValueNameDisplay || '';
              }}
            >
              {(certificateTypeList ?? [])?.map((item) => (
                <el-option
                  key={item.dataValueNo}
                  label={item.dataValueNameDisplay}
                  value={item.dataValueNo}
                />
              ))}
            </el-select>
          ) : (
            <>{row.certificateTypeDesc}</>
          );
        },
      },
      {
        label: t('person.certificateNo', '证件号码'),
        prop: 'certificateNo',
        minWidth: 140,
        editable: true,
        rules: (row: Employee.CertificateItem) => [
          {
            validator: (
              rule: unknown,
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (!value) {
                callback(
                  new Error(
                    t('global:placeholder.input.template', {
                      content: t('person.certificateNo', '证件号码'),
                    }),
                  ),
                );
              }
              if (row.certificateTypeCode === CERTIFICATE_TYPE_CODE.ID_CARD) {
                validateID(rule, value, callback);
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        render: (row: Employee.CertificateItem) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.certificateNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('person.certificateNo', '证件号码'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.certificateNo}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: Employee.CertificateItem, index: number) => {
          return row.editable ? (
            <>
              <el-button
                onClick={() => cancelEdit(row, index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
            </>
          );
        },
      },
    ],
  });

  return {
    columnConfig,
    addItem,
  };
}

export function useConcatNoColumnConfig(options: {
  tableRef: Ref<TableRef>;
  data: Ref<Employee.ContactNoItem[]>;
}) {
  const { tableRef, data } = options;
  const { toggleEdit, addItem, cancelEdit } = useEditableTable({
    tableRef,
    data,
    id: 'perContactId',
  });
  const dataSetCodes = [CONTACT_TYPE_CODE_NAME];

  const columnConfig = useColumnConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        label: t('person.contactType', '联系方式'),
        prop: 'contactTypeCode',
        minWidth: 120,
        editable: true,
        placeholder: t('global:placeholder.select', {
          name: t('person.contactType', '联系方式'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select', {
              name: t('person.contactType', '联系方式'),
            }),
            trigger: 'blur',
          },
        ],
        render: (row: Employee.ContactNoItem) => {
          const contactTypeList = dataSet?.value?.[CONTACT_TYPE_CODE_NAME];
          return row.editable ? (
            <el-select
              v-model={row.contactTypeCode}
              filterable
              placeholder={t('global:placeholder.select.template', {
                name: t('person.contactType', '联系方式'),
              })}
              onChange={(val: string) => {
                row.contactTypeDesc =
                  contactTypeList?.find((item) => item.dataValueNo === val)
                    ?.dataValueNameDisplay || '';
              }}
            >
              {(contactTypeList ?? [])?.map((item) => (
                <el-option
                  key={item.dataValueNo}
                  label={item.dataValueNameDisplay}
                  value={item.dataValueNo}
                />
              ))}
            </el-select>
          ) : (
            <>{row.contactTypeDesc}</>
          );
        },
      },
      {
        label: t('person.contactNo', '联系号码'),
        prop: 'contactNo',
        minWidth: 140,
        editable: true,
        rules: (row: Employee.ContactNoItem) => [
          {
            validator: (
              rule: unknown,
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (!value) {
                callback(
                  new Error(
                    t('global:placeholder.input.template', {
                      content: t('person.contactNo', '联系号码'),
                    }),
                  ),
                );
              }
              if (row.contactTypeCode === CONTACT_TYPE_CODE.MOBILE) {
                validatePhone(rule, value, callback);
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        render: (row: Employee.ContactNoItem) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.contactNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('person.contactNo', '联系号码'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.contactNo}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: Employee.ContactNoItem, index: number) => {
          return row.editable ? (
            <>
              <el-button
                onClick={() => cancelEdit(row, index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <el-button
              onClick={(e: { preventDefault: () => void }) => {
                e.preventDefault();
                toggleEdit(row);
              }}
              type="primary"
              link
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });

  return {
    columnConfig,
    addItem,
  };
}
