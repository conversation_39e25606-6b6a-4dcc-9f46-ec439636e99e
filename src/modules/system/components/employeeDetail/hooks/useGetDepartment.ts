import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryDepartmentListByExample } from '@/modules/system/api/org';

export function useGetDepartment() {
  const loading = ref<boolean>(false);
  const departmentList = ref<Org.Item[]>([]);
  const getDepartmentList = async (params: Org.queryDepartmentParams) => {
    loading.value = true;
    const [, res] = await queryDepartmentListByExample({
      enabledFlag: FLAG.YES,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      departmentList.value = res?.data ?? [];
    }
  };
  return {
    loading,
    departmentList,
    getDepartmentList,
  };
}
