import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryWardListByExample } from '@/modules/system/api/org';

export function useGetWard() {
  const loading = ref<boolean>(false);
  const wardList = ref<Org.Item[]>([]);
  const getWardList = async (params: Org.queryWardParams) => {
    loading.value = true;
    const [, res] = await queryWardListByExample({
      enabledFlag: FLAG.YES,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      wardList.value = res?.data ?? [];
    }
  };
  return {
    loading,
    wardList,
    getWardList,
  };
}
