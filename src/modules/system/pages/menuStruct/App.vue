<script setup lang="ts" name="orgManage">
  import MenuList from './components/MenuList.vue';
  import DetailInfo from './components/DetailInfo.vue';
  import { ref } from 'vue';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';

  let detail = ref<Menu.MixSystemMenuElement>({});
  const treeRef = ref();

  const dmlList = useGetDMLList();

  function changeDetail(info: Menu.MixSystemMenuElement, keep: boolean = true) {
    detail.value = {
      ...(keep ? detail.value : {}),
      ...info,
    };
  }

  function refreshTree(type: boolean) {
    treeRef?.value?.fetchMenuStructListByExample(type);
  }
</script>

<template>
  <ul class="flex size-full">
    <MenuList
      class="p-box"
      :menu-list="dmlList"
      :change-detail="changeDetail"
      ref="treeRef"
    />
    <DetailInfo
      class="p-box"
      :detail="detail"
      :change-detail="changeDetail"
      :refresh-tree="refreshTree"
    />
  </ul>
</template>
