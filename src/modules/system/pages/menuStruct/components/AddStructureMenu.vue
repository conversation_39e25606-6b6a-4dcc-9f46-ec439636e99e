<script setup lang="tsx">
  import { ref, computed, watch } from 'vue';
  import { CodeSystemType } from '@/typings/codeManage';
  import {
    addMenuStruct,
    queryMenuListByExample,
    querySystemListByExample,
  } from '../../../api/menu';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  import {
    ProForm,
    useFetchDataset,
    convertToWbNo,
    convertToSpellNo,
    ProDialog,
  } from 'sun-biz';
  const { t } = useTranslation();
  const modelValue = ref<{ [key in string]: unknown }>({});
  type Props = {
    sysId: string;
    parentSysXMenuId?: string;
  };

  const props = withDefaults(defineProps<Props>(), {
    parentSysXMenuId: undefined,
  });
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const menus = ref<Menu.MenuInfo[]>();
  const systems = ref<Menu.SystemInfo[]>([]);
  const curSysId = ref('');
  const queryValue = ref('');
  const dialogRef = ref();
  const emits = defineEmits<{
    success: [boolean];
  }>();

  function getBaseInfoData(
    props: Partial<Menu.ResUpdateMenuParams>,
    menus: Menu.MenuInfo[] = [],
    menuSources: SelectOptions[] = [],
  ) {
    return [
      {
        name: 'menuId',
        label: t('addStructureMenu.menu.reference', '菜单引用'),
        span: 24,
        defaultValue: '',
        placeholder: t(
          'addStructureMenu.menuReference.placeholder',
          '请选择引用的菜单',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addStructureMenu.menuReference.placeholder',
              '请选择引用的菜单',
            ),
            trigger: modelValue.value.menuId ? ['blur', 'change'] : ['blur'],
          },
        ],
        render: () => {
          return (
            <div class={'flex w-full'}>
              <el-select
                placeholder={t(
                  'addStructureMenu.system.input.placeholder',
                  '请选择系统',
                )}
                clearable={false}
                v-model={curSysId.value}
                onChange={(value: string) => {
                  fetchMenuListByExample(value);
                  formRef?.value?.ref.resetFields();
                }}
                class="w-36"
              >
                {systems.value.map((item) => (
                  <el-option label={item.sysName} value={item.sysId} />
                ))}
              </el-select>
              <el-select
                filterable={true}
                v-model={modelValue.value.menuId}
                filterMethod={(value: string) => {
                  queryValue.value = value;
                }}
                onChange={(value: string) => {
                  let found = menus.find((item) => item.menuId === value);
                  if (found) {
                    modelValue.value = {
                      ...modelValue.value,
                      menuName: found.menuName,
                      menu2ndName: found.menu2ndName,
                      url: found.url,
                      spellNo:
                        found.spellNo || convertToSpellNo(found.spellNo || ''),
                      wbNo: found.wbNo || convertToWbNo(found.wbNo || ''),
                      menuExtName: found.menuExtName,
                      menuSourceCode: found.menuSourceCode,
                      enabledFlag: found.enabledFlag,
                    };
                  }
                }}
                class="flex-1"
                placeholder={t(
                  'addStructureMenu.menuReference.placeholder',
                  '请选择引用的菜单',
                )}
              >
                {menus
                  .filter((cur) => {
                    return (
                      cur.enabledFlag === ENABLED_FLAG.YES &&
                      ((cur.menuNameDisplay || '').includes(queryValue.value) ||
                        (cur.spellNo || '')
                          .toLocaleUpperCase()
                          .includes(
                            (queryValue.value || '').toLocaleUpperCase(),
                          ) ||
                        (cur.wbNo || '')
                          .toLocaleUpperCase()
                          .includes(
                            (queryValue.value || '').toLocaleUpperCase(),
                          ))
                    );
                  })
                  .map((item) => (
                    <el-option
                      label={item.menuNameDisplay}
                      value={item.menuId}
                    />
                  ))}
              </el-select>
            </div>
          );
        },
      },
      {
        name: 'menuName',
        label: t('addStructureMenu.menuName', '菜单名称'),
        span: 24,
        defaultValue: props.menuName || '',
        autoConvertSpellNoAndWbNo: true,
        component: 'input',
        placeholder: t(
          'addStructureMenu.menuName.placeholder',
          '请输入菜单名称',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addStructureMenu.menuName.placeholder',
              '请输入菜单名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menu2ndName',
        label: t('addStructureMenu.menu2ndName', '菜单辅助名称'),
        span: 24,
        defaultValue: props.menu2ndName || '',
        component: 'input',
        placeholder: t(
          'addStructureMenu.menu2ndName.placeholder',
          '请输入菜单辅助名称',
        ),
      },
      {
        name: 'menuExtName',
        label: t('addStructureMenu.menuExtName', '菜单扩展名称'),
        span: 24,
        defaultValue: props.menuExtName || '',
        component: 'input',
        placeholder: t(
          'addStructureMenu.menuExtName.placeholder',
          '请输入菜单扩展名称',
        ),
      },
      {
        name: 'spellNo',
        component: 'input',
        placeholder: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
        defaultValue: props.spellNo || '',
        span: 24,
        label: t('addStructureMenu.spellNo', '拼音码'),
      },
      {
        name: 'wbNo',
        component: 'input',
        placeholder: t('addStructureMenu.wbNo.placeholder', '请输入五笔码'),
        defaultValue: props.wbNo || '',
        span: 24,
        label: t('global:wbNo'),
      },
      {
        name: 'url',
        component: 'input',
        placeholder: t('addStructureMenu.url.placeholder', '请输入菜单地址'),
        defaultValue: props.url || '',
        span: 24,
        label: t('addStructureMenu.url', '菜单地址'),
        rules: [
          {
            required: true,
            message: t('addStructureMenu.url.placeholder', '请输入菜单地址'),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: true,
        },
      },
      {
        name: 'menuSourceCode',
        component: 'select',
        placeholder: t(
          'addStructureMenu.menuSourceCode.placeholder',
          '请选择菜单来源',
        ),
        defaultValue: props.menuSourceCode || '',
        span: 24,
        label: t('addStructureMenu.menuSourceCode', '菜单来源'),
        rules: [
          {
            required: true,
            message: t(
              'addStructureMenu.menuSourceCode.placeholder',
              '请选择菜单来源',
            ),
            trigger: 'change',
          },
        ],
        extraProps: {
          options: menuSources,
          disabled: true,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addStructureMenu.enabledFlag.placeholder',
          '请输入启用状态',
        ),
        span: 24,
        label: t('addStructureMenu.enabledFlag', '启用状态'),
        defaultValue: props.enabledFlag,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          disabled: true,
        },
      },
    ];
  }

  let baseInfoDescData = computed(() => {
    return getBaseInfoData(props, menus.value, menuSources.value);
  });

  watch(
    () => props.sysId,
    () => {
      if (props.sysId) {
        fetchMenuListByExample(props.sysId);
      }
      fetchSystemListByExample();
      curSysId.value = props.sysId;
    },
    {
      immediate: true,
    },
  );

  /**
   * 查询菜单
   */
  async function fetchMenuListByExample(sysId: string) {
    let [, result] = await queryMenuListByExample({
      sysIds: [sysId],
    });
    if (result?.success) {
      menus.value = result.data;
    }
  }

  /**
   * 查询系统
   */
  async function fetchSystemListByExample() {
    let [, result] = await querySystemListByExample({});
    if (result?.success) {
      systems.value = result.data;
    }
  }

  const dataSetList = useFetchDataset([CodeSystemType.MENU_SOURCE_CODE]);

  const menuSources = computed(() =>
    (dataSetList?.value?.[CodeSystemType.MENU_SOURCE_CODE] || []).map(
      (item) => {
        return {
          value: item.dataValueNo,
          label: item.dataValueCnName,
        };
      },
    ),
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = await addMenuStruct({
            ...(model as unknown as Menu.ResAddMenuStructParams),
            sysId: props.sysId,
            menuFlag: 1,
            parentSysXMenuId: props.parentSysXMenuId,
          });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t('global:create.success'),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="800"
    ref="dialogRef"
    :before-close="
      (cb: () => void) => {
         modelValue = {};
        cb && cb();
      }
    "
    :include-button="false"
    :title="$t('structure.AddStructureMenu.buttonText', '新增菜单')"
    :button-text="$t('structure.AddStructureMenu.buttonText', '新增菜单')"
    destroy-on-close
    @success="emits('success', true)"
  >
    <ProForm
      v-model="modelValue"
      ref="formRef"
      :column="1"
      :data="baseInfoDescData"
    />
  </ProDialog>
</template>
