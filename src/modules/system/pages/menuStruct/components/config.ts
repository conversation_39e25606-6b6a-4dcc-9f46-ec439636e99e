import { TFunction, Namespace } from 'i18next';
import { MENU_AND_GROUP_FLAG } from '../constant.ts';
import { ENABLED_FLAG } from '@/utils/constant';
import { FLAG } from '@/utils/constant.ts';

function getDefaultValue(value: string = '', edit: boolean = false) {
  return edit !== true ? value || '--' : value;
}

export function getBaseInfoData(
  initData: Menu.MixSystemMenuElement,
  t: TFunction<Namespace, undefined>,
) {
  if (initData?.menuFlag === MENU_AND_GROUP_FLAG.MENU) {
    return [
      {
        name: 'sysXMenuId',
        label: t('structure.form.sysXMenuId', '标识'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.sysXMenuId, initData.edit),
        component: 'text',
      },
      {
        label: t('global:name'),
        name: 'menuName',
        autoConvertSpellNoAndWbNo: true,
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menuName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.menuName.message', '请输入名称'),
        rules: [
          {
            required: initData.edit ? true : false,
            message: t('AddStructureGroup.menuName.message', '请输入名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menu2ndName',
        label: t('global:secondName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menu2ndName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.menu2ndName.placeholder',
          '请输入辅助名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.menu2ndName.placeholder',
              '请输入辅助名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menuExtName',
        label: t('global:thirdName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menuExtName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.menuExtName.placeholder',
          '请输入扩展名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.menuExtName.placeholder',
              '请输入扩展名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('addStructureMenu.spellNo', '拼音码'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.spellNo, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
        rules: [
          {
            required: false,
            message: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.wbNo, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'url',
        label: t('manage.table.url', '地址'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.url, initData.edit),
        component: 'text',
      },
      {
        name: 'createdAt',
        label: t('manage.table.createdAt', '创建时间'),
        defaultValue: getDefaultValue(initData.createdAt, initData.edit),
        component: 'text',
      },
      {
        name: 'createdUserName',
        label: t('manage.table.createdUserName', '创建人'),
        defaultValue: getDefaultValue(initData.createdUserName, initData.edit),
        component: 'text',
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        label: t('manage.enabledFlag', '启用标识'),
        defaultValue: initData.enabledFlag,
        extraProps: {
          disabled: !initData.edit,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'inline-prompt': true,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        name: 'menuSourceDesc',
        label: t('structure.basic.menuSourceDesc', '来源'),
        defaultValue: getDefaultValue(initData.menuSourceDesc, initData.edit),
        component: 'text',
      },
    ];
  } else if (initData?.menuFlag === MENU_AND_GROUP_FLAG.GROUP) {
    return [
      {
        name: 'sysXMenuId',
        label: t('structure.form.sysXMenuId', '标识'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.sysXMenuId, initData.edit),
        component: 'text',
      },
      {
        name: 'menuName',
        label: t('global:name'),
        autoConvertSpellNoAndWbNo: true,
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menuName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.menuName.message', '请输入名称'),
        rules: [
          {
            required: initData.edit ? true : false,
            message: t('AddStructureGroup.menuName.message', '请输入名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menu2ndName',
        label: t('global:secondName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menu2ndName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.menu2ndName.placeholder',
          '请输入辅助名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.menu2ndName.placeholder',
              '请输入辅助名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menuExtName',
        label: t('global:thirdName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.menu2ndName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.menuExtName.placeholder',
          '请输入扩展名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.menuExtName.placeholder',
              '请输入扩展名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('addStructureMenu.spellNo', '拼音码'),
        yAndTips: true,
        defaultValue: getDefaultValue(initData.spellNo, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
        rules: [
          {
            required: false,
            message: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.wbNo, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'createdUserName',
        label: t('manage.table.createdUserName', '创建人'),
        defaultValue: getDefaultValue(initData.createdUserName, initData.edit),
        component: 'text',
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        label: t('manage.enabledFlag', '启用标识'),
        defaultValue: initData.enabledFlag,
        extraProps: {
          disabled: !initData.edit,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'inline-prompt': true,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        name: 'createdAt',
        label: t('manage.table.createdAt', '创建时间'),
        defaultValue: getDefaultValue(initData.createdAt, initData.edit),
        component: 'text',
      },
    ];
  } else {
    return [
      {
        name: 'sysId',
        label: t('structure.form.sysXMenuId', '标识'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.sysId, initData?.edit),
        component: 'text',
      },
      {
        name: 'sysName',
        label: t('global:name'),
        autoConvertSpellNoAndWbNo: true,
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.sysName, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.menuName.message', '请输入名称'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.menuName.message', '请输入名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'sys2ndName',
        label: t('global:secondName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.sys2ndName, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.menu2ndName.placeholder',
          '请输入辅助名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.menu2ndName.placeholder',
              '请输入辅助名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'sysExtName',
        label: t('global:thirdName'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData.sysExtName, initData.edit),
        component: initData.edit ? 'input' : 'text',
        placeholder: t(
          'addOrEditMenu.sysExtName.placeholder',
          '请输入扩展名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'addOrEditMenu.sysExtName.placeholder',
              '请输入扩展名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('addStructureMenu.spellNo', '拼音码'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.spellNo, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
        rules: [
          {
            required: false,
            message: t('addStructureMenu.spellNo.placeholder', '请输入拼音码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.wbNo, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'url',
        label: t('manage.table.url', '地址'),
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.url, initData?.edit),
        component: 'text',
        placeholder: t('manage.url.placeholder', '请输入地址'),
        rules: [
          {
            required: false,
            message: t('manage.url.placeholder', '请输入地址'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'createdAt',
        label: t('manage.table.createdAt', '创建时间'),
        defaultValue: getDefaultValue(initData?.createdAt, initData?.edit),
        component: 'text',
      },
      {
        name: 'createdUserName',
        label: t('manage.table.createdUserName', '创建人'),
        defaultValue: getDefaultValue(
          initData?.createdUserName,
          initData?.edit,
        ),
        component: 'text',
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'text',
        defaultValue:
          initData.enabledFlag === ENABLED_FLAG.YES
            ? t('global:enabled')
            : t('global:disabled'),
      },
    ];
  }
}
