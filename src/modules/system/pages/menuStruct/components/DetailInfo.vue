<script setup lang="tsx">
  import { getBaseInfoData } from './config.ts';
  import { computed, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { updateMenuStructById } from '../../../api/menu';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { MENU_AND_GROUP_FLAG } from '../constant.ts';
  import { Title, ProForm } from 'sun-biz';

  const { t } = useTranslation();
  type Props = {
    detail: Menu.MixSystemMenuElement;
    changeDetail: (info: Menu.MixSystemMenuElement) => void;
    refreshTree: (type: boolean) => void;
  };
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const props = defineProps<Props>();

  function getTitle() {
    if (props.detail?.menuFlag === MENU_AND_GROUP_FLAG.MENU) {
      return t('structure.menuInfo.title', '应用菜单信息');
    } else if (props.detail?.menuFlag === MENU_AND_GROUP_FLAG.GROUP) {
      return t('structure.groupInfo.title', '应用分组信息');
    } else {
      return t('structure.systemInfo.title', '应用系统信息');
    }
  }

  /**
   * 修改
   */
  function editClick() {
    props.changeDetail({ edit: true });
  }

  /**
   * 保存
   */

  function saveClick() {
    // edit.value = false;
    formRef?.value?.ref.validate(async (valid) => {
      let model = JSON.parse(JSON.stringify(formRef?.value?.model));
      if (valid) {
        let [, result] = await updateMenuStructById({
          ...model,
          sysXMenuId: props.detail?.sysXMenuId,
        } as Menu.ResUpdateMenuStructByIdParams);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:save.success'),
          });
          props.refreshTree(true);
        }
      }
    });
  }

  /**
   * 取消
   */
  function cancelClick() {
    props.changeDetail({ edit: false });
  }

  const baseInfoDescData = computed(() => {
    return getBaseInfoData(props.detail, t);
  });

  // 你的脚本代码
</script>
<template>
  <li
    class="flex w-full flex-auto flex-col justify-between overflow-hidden pl-4 pr-2"
  >
    <span>
      <Title :title="getTitle()" class="mb-4"> </Title>
      <ProForm
        v-if="detail?.identification"
        ref="formRef"
        :column="4"
        :with-colon="true"
        :key="`${detail?.identification}_${detail?.edit ? '0' : '1'}`"
        :data="baseInfoDescData"
      />
    </span>
    <span
      class="flex justify-end"
      v-if="
        props.detail?.menuFlag === MENU_AND_GROUP_FLAG.MENU ||
        props.detail?.menuFlag === MENU_AND_GROUP_FLAG.GROUP
      "
    >
      <el-button v-if="props.detail?.edit" @click="cancelClick">{{
        $t('global:cancel', '取消')
      }}</el-button>
      <el-button v-if="props.detail?.edit" type="primary" @click="saveClick">
        {{ $t('global:save', '保存') }}
      </el-button>
      <el-button v-else type="primary" @click="editClick">
        {{ $t('generel.edit', '编辑') }}</el-button
      >
    </span>
  </li>
</template>
<style scoped>
  /* 你的样式代码 */
</style>
