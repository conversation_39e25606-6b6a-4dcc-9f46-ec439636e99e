<script setup lang="ts" name="orgManage">
  import { ref, onMounted, computed, nextTick } from 'vue';
  import { FLAG } from '@/utils/constant';
  import {
    queryMenuStructListByExample,
    deleteMenuStructById,
    updateSortByIds,
    updateSystemSortByIds,
  } from '../../../api/menu';
  import AddStructureGroup from './AddStructureGroup.vue';
  import AddStructureMenu from './AddStructureMenu.vue';
  import { ElMessageBox, ElMessage, ElTree } from 'element-sun';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import { MENU_AND_GROUP_FLAG } from '../constant';

  const { t } = useTranslation();
  type Props = {
    changeDetail: (node: Menu.MixSystemMenuElement, keep?: boolean) => void;
    menuList: { value: string; label: string }[];
  };

  type DropNode = {
    level: number;
    data: Menu.MenuInfo;
  };

  const props = defineProps<Props>();
  const refreshIndex = ref<number>(0);
  let state = ref<Menu.MixSystemMenuElement>({});
  const loading = ref(false);
  const sysId = ref('');
  const parentSysXMenuId = ref('');
  const addStructureGroupRef = ref();
  const addStructureMenuRef = ref();
  const treeRef = ref();
  const showSystemSelection = ref(false); //系统选择
  const systemSelectsTheResult = ref('all'); //系统下拉选择结果
  const listOfSystems = ref<Menu.systemType[]>([
    {
      value: 'all',
      label: '全部系统',
    },
  ]); //储存系统列表
  let systemList = ref<Menu.SystemInfo[]>([]);
  const filterText = ref(''); // 绑定输入框
  const FIRST_LEVEL = 1;
  onMounted(() => {
    fetchMenuStructListByExample();
  });

  /**
   * 查询菜单结果
   */
  const defaultCheckedKeys = ref<[string | undefined]>([
    state.value?.identification,
  ]);

  async function fetchMenuStructListByExample(type = false) {
    loading.value = true;
    listOfSystems.value = [
      {
        value: 'all',
        label: '全部系统',
      },
    ];
    let [, result] = await queryMenuStructListByExample({
      accessFlag: FLAG.YES,
    });
    if (!type) {
      loading.value = false;
    }
    let dataTypeAll = JSON.parse(
      JSON.stringify(treeRef.value.getCheckedKeys()),
    );
    if (result?.success) {
      systemList.value = result.data.map((item) => {
        //判断当前是否为启用标识
        if (item.enabledFlag === 1) {
          listOfSystems.value.push({
            value: item.sysId,
            label: item.sysName,
          });
        }
        return {
          ...item,
          label: item.sysName,
          identification: item.sysId,
          subSysXMenuList: (item.sysXMenuList || []).map((cur) => ({
            ...cur,
            label: cur.menuName,
            parentSysId: item.sysId,
            sysName: item.sysName,
            sysSpellNo: item.spellNo,
            sysWbNo: item.wbNo,
            identification: cur.sysXMenuId,
            subSysXMenuList: (cur.subSysXMenuList || []).map((obj) => ({
              ...obj,
              label: obj.menuName,
              parentSysId: item.sysId,
              groupName: cur.menuName,
              groupSpellNo: cur.spellNo,
              groupWbNo: cur.wbNo,
              sysSpellNo: item.spellNo,
              sysWbNo: item.wbNo,
              sysName: item.sysName,
              parentSysXMenuId: cur.sysXMenuId,
              identification: obj.sysXMenuId,
            })),
          })),
        };
      });
      let info: Menu.MixSystemMenuElement = {};
      let identification = state.value?.identification;
      if (!identification) {
        info = systemList.value[0];
      } else {
        systemList.value.forEach((item: Menu.SystemInfo) => {
          if (item.identification === identification) {
            info = item;
          }
          item.subSysXMenuList?.forEach((cur: Menu.MenuInfo) => {
            if (cur?.identification === identification) {
              info = cur;
            } else {
              cur.subSysXMenuList?.forEach((obj: Menu.MenuInfo) => {
                if (obj?.identification === identification) {
                  info = obj;
                }
              });
            }
          });
        });
      }
      handleNodeClick(
        {
          ...(info || systemList.value[0] || {}),
          edit: false,
        },
        true,
      );
      if (type) {
        defaultCheckedKeys.value = dataTypeAll;
        nextTick(() => {
          retrieveTheList({ value: systemSelectsTheResult.value });
          loading.value = false;
        });
      }
    }
  }

  /**
   * 点击菜单栏获取详情
   */
  const handleNodeClick = (
    node: Menu.MixSystemMenuElement,
    chageSelect: boolean = false,
  ) => {
    props.changeDetail(node, false);
    state.value = node;
    if (chageSelect) {
      refreshIndex.value = refreshIndex.value + 1;
    }
  };

  /**
   * 点击DML下载
   */
  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_SYS_X_MENU,
      bisIds: [
        ...treeRef.value!.getHalfCheckedNodes(),
        ...treeRef.value!.getCheckedNodes(),
      ]
        .filter(
          (item) =>
            item.menuFlag === MENU_AND_GROUP_FLAG.GROUP ||
            item.menuFlag === MENU_AND_GROUP_FLAG.MENU,
        )
        .map((item) => item.identification),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      treeRef.value!.setCheckedKeys([], false);
    }
  }

  //检索列表
  function retrieveTheList(item: Menu.systemType) {
    //同时传入关键字和选择的系统
    treeRef?.value?.filter({
      systemSelectsTheResult: item.value,
      filterText: filterText.value,
    });
    systemSelectsTheResult.value = item.value;
  }

  //显示
  const systemSelectsTheResultVakye = computed(() => {
    return listOfSystems.value.find((item) => {
      return item.value === systemSelectsTheResult.value;
    });
  });

  /**
   * 编辑分组或者菜单
   */
  function editGroupOrMenu(data: Menu.MenuInfo) {
    if (data.menuFlag === MENU_AND_GROUP_FLAG.GROUP) {
      props.changeDetail({ edit: true });
    }
    if (data.menuFlag === MENU_AND_GROUP_FLAG.MENU) {
      props.changeDetail({ edit: true });
    }
  }

  /**
   * 删除分组或者菜单
   */
  function deleteGroupOrMenu(info: Menu.MenuInfo) {
    ElMessageBox.confirm(
      t('menu.delete.ask.title', '您确定要删除{{type}} “{{name}}”吗', {
        type:
          info.menuFlag === MENU_AND_GROUP_FLAG.GROUP
            ? t('struct.group', '分组')
            : t('struct.menu', '菜单'),
        name: info.menuName,
      }),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        let [, result] = await deleteMenuStructById({
          sysXMenuId: info.sysXMenuId,
        });
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          state.value.identification = '';
          fetchMenuStructListByExample(true);
        }
      })
      .catch(() => {});
  }

  // 根据用户输入过滤树节点
  const filterTree = () => {
    //同时传入关键字和选择的系统
    treeRef?.value?.filter({
      systemSelectsTheResult: systemSelectsTheResult.value,
      filterText: filterText.value,
    });
  };

  function isIncludes(curString: string = '', value: string = '') {
    return curString.toLocaleUpperCase().includes(value.toLocaleUpperCase());
  }

  // 定义节点的过滤规则
  const filterNode = (
    value: {
      systemSelectsTheResult: string;
      filterText: string;
    },
    data: Menu.MixSystemMenuElement,
  ) => {
    //判断是否查找到关键字
    const isKeywords =
      (data?.label || '').includes(value.filterText) ||
      (data?.groupName || '').includes(value.filterText) ||
      (data?.sysName || '').includes(value.filterText) ||
      isIncludes(data?.spellNo, value.filterText) ||
      isIncludes(data?.wbNo, value.filterText) ||
      isIncludes(data?.sysSpellNo, value.filterText) ||
      isIncludes(data?.sysWbNo, value.filterText) ||
      isIncludes(data?.groupSpellNo, value.filterText) ||
      isIncludes(data?.groupWbNo, value.filterText);
    //修改需求是否显示系统和子项需要同时满足选中的系统及查询的关键字
    if (value.systemSelectsTheResult === 'all' && !value.filterText) {
      //若选择所有系统且没有输入关键字时则显示所有
      return true;
    } else if (value.systemSelectsTheResult === 'all' && value.filterText) {
      //若选择所有系统并存在关键字时
      return isKeywords; // 过滤条件：节点标签包含输入值
    } else if (value.systemSelectsTheResult !== 'all' && !value.filterText) {
      //若选择系统且不存在关键字时
      return (
        data.sysId === value.systemSelectsTheResult ||
        data.parentSysId === value.systemSelectsTheResult
      );
    } else {
      //若选择系统且存在关键字时
      return (
        isKeywords &&
        (data.sysId === value.systemSelectsTheResult ||
          data.parentSysId === value.systemSelectsTheResult)
      );
    }
  };

  function showDmlButton() {
    if (!treeRef.value) {
      return false;
    }
    return !!treeRef.value!.getCheckedKeys(false).length;
  }

  let inputChange = debounce(filterTree, 700);

  function allowDrop(
    draggingNode: { level: number; data: Menu.MixSystemMenuElement },
    dropNode: { level: number; data: Menu.MixSystemMenuElement },
    type: string,
  ) {
    //不允许跨系统
    if (
      (draggingNode.data.sysId || draggingNode.data?.parentSysId) !==
      (dropNode.data.sysId || dropNode.data?.parentSysId)
    ) {
      //系统同等级可以换位置
      if (
        draggingNode.level === FIRST_LEVEL &&
        dropNode.level === FIRST_LEVEL
      ) {
        return type === 'prev' || type === 'next';
      }
      return false;
    }

    //不允许跨分组了
    // if (draggingNode.level - dropNode.level === 1) {
    //   return type === 'inner';
    // }
    // 只允许在同级放置，且控制插入后的位置
    if (draggingNode.level !== dropNode.level) {
      return false;
    }
    return type === 'prev' || type === 'next';
  }

  // 辅助函数：查找节点的父节点
  function findParentNode(
    data: Menu.MenuInfo,
  ): Menu.MenuInfo | Menu.SystemInfo | undefined {
    let result: Menu.MenuInfo | Menu.SystemInfo | undefined = undefined;
    if (data.parentSysXMenuId && data.parentSysId) {
      let found = systemList.value.find(
        (item) => item.sysId === data.parentSysId,
      );
      if (found) {
        result = found.subSysXMenuList.find(
          (cur) => cur.sysXMenuId === data.parentSysXMenuId,
        );
      }
    } else {
      result = systemList.value.find((item) => item.sysId === data.parentSysId);
    }
    return result;
  }

  async function handleDrop(draggingNode: DropNode, dropNode: DropNode) {
    if (draggingNode.level === dropNode.level && dropNode.level !== 1) {
      // 找到拖拽节点的父节点
      const parentNode = findParentNode(draggingNode.data);
      let [, result] = await updateSortByIds({
        sysXMenuList:
          parentNode?.subSysXMenuList.map((item, index) => ({
            sysXMenuId: item.identification,
            sort: index + 1,
          })) || [],
      });
      if (result?.success) {
        ElMessage({
          type: 'success',
          message: t('global:modify.sort.success'),
        });
      }
    } else if (
      draggingNode.level === dropNode.level &&
      dropNode.level === FIRST_LEVEL
    ) {
      let [, result] = await updateSystemSortByIds({
        sysSortList: systemList.value.map((item, index) => ({
          sysId: item.sysId,
          sort: index + 1,
        })),
      });
      if (result?.success) {
        ElMessage({
          type: 'success',
          message: t('global:modify.sort.success'),
        });
      }
    }

    // 在这里实现节点数据的重新排列

    // 拖拽了系统

    //拖拽了
  }

  function openAddStructureGroupDialog(value: string) {
    sysId.value = value;
    addStructureGroupRef?.value?.dialogRef?.open();
  }

  function openAddStructureMenuDialog(value: string, menuId: string) {
    sysId.value = value;
    parentSysXMenuId.value = menuId;
    addStructureMenuRef?.value?.dialogRef?.open();
  }

  defineExpose({
    fetchMenuStructListByExample,
  });
</script>

<template>
  <li
    class="flex h-full basis-[248px] flex-col border-r border-solid border-slate-300 pl-2 pr-2 pt-2"
  >
    <div class="mb-4 flex">
      <el-dropdown>
        <el-button
          type="primary"
          class="mr-2"
          @click="showSystemSelection = true"
        >
          {{ systemSelectsTheResultVakye?.label }}
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </el-button>
        <template #dropdown v-if="showSystemSelection">
          <el-dropdown-menu>
            <el-dropdown-item
              @click="retrieveTheList(item)"
              :key="item.value"
              v-for="item in listOfSystems"
              >{{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-input
        v-model="filterText"
        class="mr-2 min-w-44"
        :placeholder="$t('structure.searchInput.placeholder', '输入关键字查询')"
        @input="inputChange"
        clearable
      />
      <el-dropdown>
        <el-button type="primary" :disabled="!showDmlButton()">
          DML
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </el-button>
        <template #dropdown v-if="showDmlButton()">
          <el-dropdown-menu>
            <el-dropdown-item
              @click="clickDropdown(item)"
              :key="item.value"
              v-for="item in props.menuList"
              >{{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-scrollbar class="flex-1">
      <el-tree
        v-loading="loading"
        ref="treeRef"
        node-key="identification"
        :key="refreshIndex"
        :highlight-current="true"
        :default-checked-keys="defaultCheckedKeys"
        :data="systemList"
        :filter-node-method="filterNode"
        :props="{
          label: 'label',
          children: 'subSysXMenuList',
        }"
        @node-click="
          (node: Menu.MixSystemMenuElement) => {
            handleNodeClick(node);
          }
        "
        draggable
        :allow-drop="allowDrop"
        @node-drop="handleDrop"
        :expand-on-click-node="false"
        default-expand-all
        show-checkbox
      >
        <template #default="{ node, data }">
          <span class="flex w-full justify-between">
            <span :title="`${data?.label} ${data?.identification}`">
              <span
                :class="
                  data.enabledFlag === FLAG.NO
                    ? '!important cursor-not-allowed text-gray-500'
                    : ''
                "
                >{{ node.label }}</span
              >
              <el-tag
                class="ml-1"
                v-if="data.menuFlag === MENU_AND_GROUP_FLAG.GROUP"
                type="warning"
                >{{ $t('structure.grouping.tag', '分组') }}</el-tag
              >
              <el-tag
                class="ml-1"
                v-else-if="data.menuFlag === MENU_AND_GROUP_FLAG.MENU"
                type="success"
              >
                {{ $t('structure.menu.tag', '菜单') }}
              </el-tag>
              <el-tag class="ml-1" v-else>{{
                $t('structure.system.tag', '系统')
              }}</el-tag>
            </span>
            <span v-if="data.identification === state?.identification">
              <el-dropdown placement="bottom-end" class="mr-2">
                <el-icon class="mt-1"
                  ><MoreFilled class="origin-center rotate-90"
                /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :disabled="data.enabledFlag === FLAG.NO"
                      v-if="
                        state?.sysId ||
                        state?.menuFlag === MENU_AND_GROUP_FLAG.GROUP
                      "
                      @click="
                        () => {
                          openAddStructureGroupDialog(
                            data.sysId || data.parentSysId,
                          );
                        }
                      "
                    >
                      {{
                        $t('structure.AddStructureMenu.buttonText', '新增分组')
                      }}</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="
                        state?.sysId ||
                        state?.menuFlag === MENU_AND_GROUP_FLAG.GROUP
                      "
                      :disabled="data.enabledFlag === FLAG.NO"
                      @click="
                        () => {
                          openAddStructureMenuDialog(
                            data.sysId || data.parentSysId,
                            data.sysXMenuId,
                          );
                        }
                      "
                    >
                      {{
                        $t('structure.AddStructureMenu.buttonText', '新增菜单')
                      }}</el-dropdown-item
                    >

                    <el-dropdown-item
                      v-if="
                        state?.menuFlag === MENU_AND_GROUP_FLAG.GROUP ||
                        state?.menuFlag === MENU_AND_GROUP_FLAG.MENU
                      "
                      @click="
                        () => {
                          editGroupOrMenu(node?.data);
                        }
                      "
                    >
                      {{ $t('global:edit') }}</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="
                        (state?.menuFlag === MENU_AND_GROUP_FLAG.GROUP &&
                          !node?.data?.subSysXMenuList.length) ||
                        state?.menuFlag === MENU_AND_GROUP_FLAG.MENU
                      "
                      @click="
                        () => {
                          deleteGroupOrMenu(node?.data);
                        }
                      "
                    >
                      {{ $t('global:delete') }}
                    </el-dropdown-item>
                    <el-popover
                      placement="right"
                      :width="350"
                      trigger="hover"
                      effect="light"
                      v-if="
                        state?.menuFlag === MENU_AND_GROUP_FLAG.GROUP &&
                        !!node?.data?.subSysXMenuList.length
                      "
                      :content="
                        $t(
                          'menu.list.disableDeleteTips',
                          '当前分组存在菜单，请全部删除后再执行此操作',
                        )
                      "
                    >
                      <template #reference>
                        <el-button
                          disabled
                          link
                          class="pl-4"
                          style="padding-left: 1rem"
                        >
                          {{ $t('global:delete') }}
                        </el-button>
                      </template>
                    </el-popover>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </span>
        </template>
        <template #empty>
          <el-empty :image-size="100" />
        </template>
      </el-tree>
    </el-scrollbar>
  </li>
  <AddStructureGroup
    ref="addStructureGroupRef"
    :sys-id="sysId"
    @success="fetchMenuStructListByExample"
  />
  <AddStructureMenu
    ref="addStructureMenuRef"
    :sys-id="sysId"
    :parent-sys-x-menu-id="parentSysXMenuId"
    @success="fetchMenuStructListByExample"
  />
</template>
