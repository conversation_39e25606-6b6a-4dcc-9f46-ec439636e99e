<script setup lang="ts">
  import { ref, computed } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { addMenuStruct } from '../../../api/menu';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProDialog } from 'sun-biz';
  const { t } = useTranslation();
  function getBaseInfoData() {
    return [
      {
        name: 'menuName',
        label: t('AddStructureGroup.create.menuName', '分组名称'),
        autoConvertSpellNoAndWbNo: true,
        defaultValue: '',
        component: 'input',
        placeholder: t('AddStructureGroup.menuName.placeholder', '请输入名称'),
        rules: [
          {
            required: true,
            message: t('AddStructureGroup.menuName.message', '请输入名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menu2ndName',
        label: t('AddStructureGroup.create.menu2ndName', '分组辅助名称'),
        defaultValue: '',
        component: 'input',
        placeholder: t(
          'AddStructureGroup.menu2ndName.placeholder',
          '请输入分组辅助名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'AddStructureGroup.menu2ndName.message',
              '请输入分组辅助名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menuExtName',
        label: t('AddStructureGroup.create.menuExtName', '分组扩展名称'),
        defaultValue: '',
        component: 'input',
        placeholder: t(
          'AddStructureGroup.menuExtName.placeholder',
          '请输入分组扩展名称',
        ),
        rules: [
          {
            required: false,
            message: t(
              'AddStructureGroup.menuExtName.message',
              '请输入分组扩展名称',
            ),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        component: 'input',
        placeholder: t('AddStructureGroup.spellNo.placeholder', '请输入拼音码'),
        defaultValue: '',
        label: t('global:spellNo'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.spellNo.message', '请输入拼音码'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'wbNo',
        component: 'input',
        placeholder: t('AddStructureGroup.wbNo.placeholder', '请输入五笔码'),
        defaultValue: '',
        label: t('global:wbNo'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.wbNo.message', '请输入五笔码'),
            trigger: 'change',
          },
        ],
      },
    ];
  }

  export type Props = {
    sysId: string;
  };

  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [boolean];
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
    open: () => void;
  }>();
  const dialogRef = ref();
  const baseInfoDescData = computed(() => getBaseInfoData());
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = await addMenuStruct({
            ...(model as unknown as Menu.ResAddMenuStructParams),
            menuFlag: 0,
            sysId: props.sysId,
          });
          if (result?.success) {
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="800"
    ref="dialogRef"
    :include-button="false"
    :title="$t('structure.AddStructureMenu.buttonText', '新增分组')"
    :button-text="$t('structure.AddStructureMenu.buttonText', '新增分组')"
    destroy-on-close
    @success="emits('success', true)"
  >
    <ProForm ref="formRef" :column="1" :data="baseInfoDescData" />
  </ProDialog>
</template>
