import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
// import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { queryFlatOrgList } from '@/api/common';

export function useGetOrgFlat() {
  const loading = ref<boolean>(false);
  const flatOrgList = ref<Org.FlatOrgReqItem[]>([]);

  const getFlatOrgList = async (params?: Org.FlatOrgReqParams) => {
    const defaultParams = {
      pageNumber: 1,
      pageSize: 100,
      enabledFlag: FLAG.YES,
    };
    loading.value = true;

    const [, res] = await queryFlatOrgList({
      ...defaultParams,
      ...params,
    });

    loading.value = false;
    if (res?.success) {
      flatOrgList.value = res?.data ?? [];
    }
  };

  return { loading, flatOrgList, getFlatOrgList };
}
