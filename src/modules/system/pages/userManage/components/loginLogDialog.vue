<script setup lang="ts" name="loginLogDialog">
  import { ref, nextTick } from 'vue';
  import { queryUserLoginRecordByUserId } from '@/modules/system/api/user';
  import { useSearchLoginRecordFormConfig } from '../config/useSearchFormConfig';
  import { useLoginRecordTableConfig } from '../config/useLoginRecordTableConfig';
  import { ProDialog, ProForm, ProTable } from 'sun-biz';
  import { UserReqItem } from '@/api/types';
  import { dayjs } from 'element-sun';
  import { formatDateString } from '@sun-toolkit/shared';

  type dialogFormModelType = {
    userNo: string;
    userNameDisplay: string;
    loginTime: string[];
  };

  const dialogRef = ref();
  const dialogFormModel = ref<dialogFormModelType>({
    userNo: '',
    userNameDisplay: '',
    loginTime: [],
  });
  const loading = ref(false);
  const rowValue = ref<UserReqItem>();
  const tableList = ref<User.LoginRecordReqItem[]>([]);
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: 25,
    total: 0,
  });

  const resetPageInfo = async () => {
    pageInfo.value = { pageNumber: 1, pageSize: 25, total: 0 };
  };

  /** 打开日志弹窗 */
  const openDialog = async (row?: UserReqItem) => {
    rowValue.value = row;
    await initFormModel();
    nextTick(() => {
      dialogRef.value.open();
    });

    await fetchData();
  };

  /** 初始化formModel */
  const initFormModel = async () => {
    dialogFormModel.value.userNo = rowValue.value?.userNo ?? '';
    dialogFormModel.value.userNameDisplay =
      rowValue.value?.userNameDisplay ?? '';
    dialogFormModel.value.loginTime = [
      dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ];

    await resetPageInfo();
  };

  /** 检索 */
  const modelChange = async (data: { dateTime: string }) => {
    // resetPageInfo();
    dialogFormModel.value = {
      ...dialogFormModel.value,
      ...data,
    };

    await resetPageInfo();
    await fetchData();
  };

  /** 获取数据 */
  const fetchData = async () => {
    loading.value = true;

    const [, res] = await queryUserLoginRecordByUserId({
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
      userId: rowValue.value?.userId ?? '',
      loginInBeginAt:
        dialogFormModel.value.loginTime &&
        dialogFormModel.value.loginTime.length > 0
          ? formatDateString(dialogFormModel.value.loginTime[0], 'start')
          : undefined,
      loginInEndAt:
        dialogFormModel.value.loginTime &&
        dialogFormModel.value.loginTime.length > 0
          ? formatDateString(dialogFormModel.value.loginTime[1], 'end')
          : undefined,
    });

    loading.value = false;
    if (res?.success) {
      tableList.value = res?.data ?? [];
      pageInfo.value.total = res?.total;
    }
  };

  const formConfig = useSearchLoginRecordFormConfig();
  const tableColumns = useLoginRecordTableConfig();

  defineExpose({
    open: openDialog,
  });
</script>

<template>
  <ProDialog
    class="w-4/5"
    ref="dialogRef"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :include-footer="false"
    :title="$t('login.record', '登录记录')"
    destroy-on-close
  >
    <ProForm
      ref="formRef"
      layout-mode="inline"
      class="flex flex-wrap"
      v-model="dialogFormModel"
      :data="formConfig"
      label-suffix=" :"
      @model-change="modelChange"
    >
      <el-button type="primary" @click="fetchData">{{
        $t('global:query')
      }}</el-button></ProForm
    >
    <div class="flex h-[60vh] flex-col overflow-hidden">
      <ProTable
        ref="loginRecordTableRef"
        :columns="tableColumns"
        :data="tableList"
        :loading="loading"
        row-key="userId"
        :page-info="{
          total: pageInfo.total,
          pageNumber: pageInfo.pageNumber,
          pageSize: pageInfo.pageSize,
        }"
        :pagination="true"
        @current-page-change="
          (val: number) => {
            pageInfo.pageNumber = val;
            fetchData();
          }
        "
        @size-page-change="
          (val: number) => {
            pageInfo.pageSize = val;
            fetchData();
          }
        "
      />
    </div>
  </ProDialog>
</template>
