import { useFormConfig } from 'sun-biz';
import { Ref, ComputedRef } from 'vue';
import { baseInfoExtendType, baseInfoType } from '../views/detail.vue';
import {
  USER_TYPE_CODE_NAME,
  USER_TYPE_CODE,
  PROFESSION_TYPE_CODE,
  CERTIFICATE_TYPE_CODE_NAME,
  CERTIFICATE_TYPE_CODE,
  CONTACT_TYPE_CODE,
  CONTACT_TYPE_CODE_NAME,
  GENDER_CODE_NAME,
} from '@/utils/constant';
import { validateID, validatePhone } from '@sun-toolkit/shared';

/** 头部基本信息 */
export function useBaseInfoFormConfig(options: {
  disabled: ComputedRef<boolean>;
  baseInfoModel: Ref<baseInfoType | undefined>;
}) {
  const { disabled, baseInfoModel } = options;

  const data = useFormConfig({
    dataSetCodes: [USER_TYPE_CODE_NAME, GENDER_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('user.userNo', '用户编码'),
        name: 'userNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('user.userNo', '用户编码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('user.userNo', '用户编码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.userName', '用户名称'),
        name: 'userName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('user.userName', '用户名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('user.userName', '用户名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.user2ndName', '辅助名称'),
        name: 'user2ndName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('user.user2ndName', '辅助名称'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.userExtName', '扩展名称'),
        name: 'userExtName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('user.userExtName', '扩展名称'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.userTypeCode', '用户类型'),
        name: 'userTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('user.userTypeCode', '用户类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('user.userTypeCode', '用户类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: false,
          disabled: disabled.value,
          options: dataSet?.value ? dataSet.value?.[USER_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('gender', '性别'),
        name: 'genderCode',
        component: 'select',
        isHidden:
          baseInfoModel.value?.userTypeCode !== USER_TYPE_CODE.NATURAL_PERSON,
        placeholder: t('global:placeholder.select.template', {
          name: t('gender', '性别'),
        }),
        extraProps: {
          disabled: disabled.value,
          options: dataSet?.value ? dataSet.value?.[GENDER_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        rules: [
          {
            required:
              baseInfoModel.value?.userTypeCode !==
              USER_TYPE_CODE.NATURAL_PERSON
                ? false
                : true,
            message: t('global:placeholder.select.template', {
              name: t('gender', '性别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:spellNo'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('global:wbNo'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
    ],
  });
  return data;
}

/** 基本信息扩展 */
export function useBaseInfoExtendFormConfig(options: {
  isAdd: ComputedRef<boolean>;
  disabled: ComputedRef<boolean>;
  baseInfoExtendModel: Ref<baseInfoExtendType>;
  validatePeopleInfo: (params: People.ReqParams) => Promise<void>;
  validateCertificateNo: () => Promise<void>;
  validateContactNo: () => Promise<void>;
}) {
  const {
    isAdd,
    disabled,
    baseInfoExtendModel,
    validatePeopleInfo,
    validateCertificateNo,
    validateContactNo,
  } = options;

  const data = useFormConfig({
    dataSetCodes: [
      CERTIFICATE_TYPE_CODE_NAME,
      CONTACT_TYPE_CODE_NAME,
      PROFESSION_TYPE_CODE,
    ],
    getData: (t, dataSet) => [
      {
        label: t('user.certificateTypeCode', '证件类型'),
        name: 'certificateTypeCode',
        component: 'select',
        isHidden: !isAdd.value,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.certificateType', '证件类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('user.certificateTypeCode', '证件类型'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
          onChange: validateCertificateNo,
          options: dataSet?.value
            ? dataSet.value?.[CERTIFICATE_TYPE_CODE_NAME]
            : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('person.certificateNo', '证件号码'),
        name: 'certificateNo',
        component: 'input',
        isHidden: !isAdd.value,
        placeholder: t('global:placeholder.input.template', {
          content: t('person.certificateNo', '证件号码'),
        }),
        extraProps: {
          disabled: disabled.value,
          onChange: async (val: string) => {
            if (baseInfoExtendModel?.value?.certificateTypeCode && val) {
              await validatePeopleInfo({
                personInfoTypes: ['4'],
                certificateNo: val,
                certificateTypeCode:
                  baseInfoExtendModel?.value?.certificateTypeCode,
              });
            }
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('person.certificateNo', '证件号码'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            /** 检验身份证号码 */
            validator: (
              rule: { filed: string },
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (
                baseInfoExtendModel.value.certificateTypeCode ===
                CERTIFICATE_TYPE_CODE.ID_CARD
              ) {
                validateID(rule, value, (error?: Error) => {
                  if (error) {
                    callback(error);
                  } else {
                    callback();
                    // baseInfoModel.value.genderCode = getSexCode(value);
                  }
                });
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: t('user.titleCode', '职称'),
        name: 'titleCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('user.titleCode', '职称'),
        }),
        extraProps: {
          disabled: disabled.value,
          filterable: true,
          options: dataSet?.value?.[PROFESSION_TYPE_CODE] || [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('user.contactTypeCode', '联系方式'),
        name: 'contactTypeCode',
        component: 'select',
        isHidden: !isAdd.value,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.contactTypeCode', '联系方式'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('user.contactTypeCode', '联系方式'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
          onChange: validateContactNo,
          options: dataSet?.value
            ? dataSet.value?.[CONTACT_TYPE_CODE_NAME]
            : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('user.contactNo', '联系号码'),
        name: 'contactNo',
        component: 'input',
        isHidden: !isAdd.value,
        placeholder: t('global:placeholder.input.template', {
          content: t('user.contactNo', '联系号码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('user.contactNo', '联系号码'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            /** 检验手机号 */
            validator: (
              rule: { filed: string },
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (
                baseInfoExtendModel.value.contactTypeCode ===
                CONTACT_TYPE_CODE.MOBILE
              ) {
                validatePhone(rule, value, (error?: Error) => {
                  if (error) {
                    callback(error);
                  } else {
                    callback();
                  }
                });
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.personSimpleDesc', '个人简介'),
        name: 'personSimpleDesc',
        type: 'textarea',
        component: 'input',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('user.personSimpleDesc', '个人简介'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.person2ndSimpleDesc', '辅助简介'),
        name: 'person2ndSimpleDesc',
        type: 'textarea',
        component: 'input',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('user.person2ndSimpleDesc', '辅助简介'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('user.personExtSimpleDesc', '扩展简介'),
        name: 'personExtSimpleDesc',
        type: 'textarea',
        component: 'input',
        isFullWidth: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('user.personExtSimpleDesc', '扩展简介'),
        }),
        extraProps: {
          disabled: disabled.value,
        },
      },
    ],
  });
  return data;
}
