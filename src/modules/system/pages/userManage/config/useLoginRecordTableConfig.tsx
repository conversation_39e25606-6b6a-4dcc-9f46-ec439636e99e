import { useColumnConfig } from 'sun-biz';

export function useLoginRecordTableConfig() {
  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('login.loginInAt', '登录时间'),
        prop: 'loginInAt',
        minWidth: 220,
      },
      {
        label: t('login.loginOutAt', '退出时间'),
        prop: 'loginOutAt',
        minWidth: 220,
      },
      {
        label: t('login.loginInIp', '登录IP'),
        prop: 'loginInIp',
        minWidth: 220,
      },
      {
        label: t('login.loginInMac', '登录MAC'),
        prop: 'loginInMac',
        minWidth: 220,
      },
      {
        label: t('login.loginOrgNameDisplay', '登录组织'),
        prop: 'loginOrgNameDisplay',
        minWidth: 220,
      },
      {
        label: t('login.loginOrgLocationNameDisplay', '登录组织位置'),
        prop: 'loginOrgLocationNameDisplay',
        minWidth: 220,
      },
      {
        label: t('login.loginAddrDetail', '登录详细地址'),
        prop: 'loginAddrDetail',
        minWidth: 220,
      },
    ],
  });
  return data;
}
