<script setup lang="ts" name="UserManageDetail">
  import { UserReqItem } from '@/api/types';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { queryUserList } from '@/api/common';
  import { useTranslation } from 'i18next-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useGetUserInfo } from '@/hooks/useGetUserList';
  import { useFetchParamsConfig } from '@/hooks/useFetchParamsConfig';
  import { useGetWard } from '@/modules/system/components/employeeDetail/hooks/useGetWard';
  import { useGetDepartment } from '@/modules/system/components/employeeDetail/hooks/useGetDepartment';
  import { Ref, ref, computed, onMounted, nextTick } from 'vue';
  import { queryOrgAndHospitalList } from '@/modules/system/api/org';
  import { queryPersonListByExample } from '@/modules/system/api/people';
  import { useUserInfoFormConfig } from '../config/useUserInfoFormConfig';
  import { useUserInfoTableConfig } from '../config/useUserInfoTableConfig';
  import { updateUserById, addUser } from '@/modules/system/api/user';
  import {
    useCertificateTableConfig,
    useContactNoTableConfig,
  } from '../config/useBaseInfoTableConfig';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import {
    FLAG,
    USER_JOB_CODE,
    USER_TYPE_CODE,
    CONTACT_TYPE_CODE,
    CERTIFICATE_TYPE_CODE,
    DEFAULT_PASSWORD_PARAM_NO,
  } from '@/utils/constant';
  import {
    useBaseInfoFormConfig,
    useBaseInfoExtendFormConfig,
  } from '../config/useBaseInfoFormConfig';

  /** 基本信息type */
  export type baseInfoType = {
    userNo?: string;
    userName?: string;
    user2ndName?: string;
    userExtName?: string;
    userTypeCode?: string;
    genderCode?: string;
    spellNo?: string;
    wbNo?: string;
  };

  /** 基本信息扩展type */
  export type baseInfoExtendType = {
    certificateTypeCode?: string;
    certificateNo?: string;
    titleCode?: string;
    contactTypeCode?: string;
    contactNo?: string;
    personSimpleDesc?: string;
    person2ndSimpleDesc?: string;
    personExtSimpleDesc?: string;
  };

  /** 用户信息type */
  export type userInfoType = {
    userJobCode?: string;
    paySumTypeCode?: string;
    paySumBelongUserId?: string;
    invoiceAgentUserId?: string;
    enabledFlag?: number;
  };

  /** 用户与服务关系type */
  export type userRelationType = {
    orgId: string;
    orgNameDisplay: string;
    userRolesList: string[];
    editable: boolean;
    loginOrgLocationList: string[];
    bizUnitList: string[];
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const {
    loading: userLoading,
    userList,
    getUserList: queryUserListData,
  } = useGetUserInfo();
  const { wardList, getWardList } = useGetWard();
  const { departmentList, getDepartmentList } = useGetDepartment();

  /** 基本信息ref */
  const baseInfoRef = ref();
  /** 基本信息扩展ref */
  const baseInfoExtendRef = ref();
  /** 证件ref */
  const perCertificateRef = ref();
  /** 联系方式ref */
  const contactNoRef = ref();
  /** 用户信息ref */
  const userFormRef = ref();

  /** 初始加载loading */
  const loading = ref(false);
  /** 确定按钮loading */
  const submitLoading = ref(false);
  /** 获取科室或者病区的loading */
  const bizUnitLoading = ref(false);
  /** 当前用户信息 */
  const userInfo = ref<UserReqItem>();
  /** 基本信息formModel */
  const baseInfoModel = ref<baseInfoType>();
  /** 基本信息扩展formModel */
  const baseInfoExtendModel = ref<baseInfoExtendType>();
  /** 证件tableData */
  const perCertificateList = ref<Employee.CertificateItem[]>([]);
  /** 联系方式tableData */
  const perContactList = ref<Employee.ContactNoItem[]>([]);
  /** 用户信息formModel */
  const userInfoModel = ref<userInfoType>({
    userJobCode: undefined,
  });
  /** 用户与服务信息formModel */
  const userRelationList = ref<userRelationType[]>();
  /** 当前登录租户的院区list */
  const orgList = ref<Org.Item[]>([]);
  /** 当前用户的服务业务单元 */
  const bizUnitList = ref<UserReqItem['bizUnitList']>([]);
  const recordOldUserRelationList = ref<userRelationType[]>([]);
  const bizUnitListData = ref<{ bizUnitId: string; bizUnitName: string }[]>();

  /** 编辑过来的用户id */
  const userId = computed(() => route.params?.userId);
  /** 当前绑定医院 */
  const orgId = computed(() => route.params?.hospitalId as string);
  /** status=1详情 其他是新增或者编辑 */
  const disabled = computed(() => route.query?.status === '1');
  /** 是否新增 */
  const isAdd = computed(() => {
    if (route.params?.userId) return false;
    return true;
  });

  const defaultPassword = useFetchParamsConfig({
    hospitalId: orgId.value as string,
    paramNos: [DEFAULT_PASSWORD_PARAM_NO],
  });

  /** 初始化数据 */
  const initData = async () => {
    loading.value = true;

    if (!isAdd.value && userId.value) {
      await fetchData();
    }
    await getUserList();
    await getHospitalList();

    const userInfoData = cloneDeep(userInfo.value);

    /** 基本信息 */
    baseInfoModel.value = {
      userNo: userInfoData?.userNo ?? undefined,
      userName: userInfoData?.userNameDisplay ?? undefined,
      user2ndName: userInfoData?.user2ndName ?? undefined,
      userExtName: userInfoData?.userExtName ?? undefined,
      userTypeCode: userInfoData?.userTypeCode ?? USER_TYPE_CODE.NATURAL_PERSON,
      genderCode: userInfoData?.genderCode ?? undefined,
      spellNo: userInfoData?.spellNo ?? undefined,
      wbNo: userInfoData?.wbNo ?? undefined,
    };

    /** 基本信息扩展 */
    baseInfoExtendModel.value = {
      certificateTypeCode: isAdd.value
        ? CERTIFICATE_TYPE_CODE.ID_CARD
        : undefined,
      certificateNo: undefined,
      titleCode: userInfoData?.titleCode ?? undefined,
      contactTypeCode: isAdd.value ? CONTACT_TYPE_CODE.MOBILE : undefined,
      contactNo: undefined,
      personSimpleDesc: undefined,
      person2ndSimpleDesc: undefined,
      personExtSimpleDesc: undefined,
    };

    /** 证件信息 */
    perCertificateList.value = (userInfoData?.perCertificateList ??
      []) as Employee.CertificateItem[];

    /** 联系方式信息 */
    perContactList.value = (userInfoData?.perContactList ??
      []) as Employee.ContactNoItem[];

    /** 用户信息 */
    userInfoModel.value = {
      userJobCode: userInfoData?.userJobCode ?? undefined,
      paySumTypeCode: userInfoData?.paySumTypeCode ?? undefined,
      paySumBelongUserId: userInfoData?.paySumBelongUserId ?? undefined,
      invoiceAgentUserId: userInfoData?.invoiceAgentUserId ?? undefined,
      enabledFlag: userInfoData?.enabledFlag ?? FLAG.YES,
    };

    /** 角色与服务关系 */
    userRelationList.value = (orgList.value?.map((item) => ({
      ...item,
      orgId: item?.orgId || '',
      editable: true,
      userRolesList:
        userInfoData?.userRoleList
          ?.filter((role) => role.hospitalId === item.orgId && role?.roleId)
          ?.map((item) => item.roleId) || [],
      loginOrgLocationList:
        userInfoData?.loginOrgLocationList
          ?.filter((location) => location?.orgId === item.orgId)
          ?.map((item) => item.orgLocationId) || [],
      bizUnitList:
        userInfoData?.bizUnitList
          ?.filter((bizUnit) => bizUnit.hospitalId === item.orgId)
          ?.map((item) => item.bizUnitId) || [],
    })) ?? []) as userRelationType[];

    const arr = cloneDeep(userRelationList.value);
    recordOldUserRelationList.value = arr ?? [];

    bizUnitList.value = userInfoData?.bizUnitList ?? [];

    loading.value = false;
  };

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  /** 获取登录租户的院区 */
  const getHospitalList = async () => {
    const [, res] = await queryOrgAndHospitalList();
    if (res?.success) {
      orgList.value = res.data ?? [];
    }
  };

  /** 获取用户信息详情 */
  const fetchData = async () => {
    const [, res] = await queryUserList({
      pageNumber: 1,
      pageSize: 1,
      userId: userId.value as string,
    });
    if (res?.success) {
      userInfo.value = (res.data ?? [])[0];
    }
  };

  /** 查询是否已经存在 */
  const validatePeopleInfo = async (params: People.ReqParams) => {
    const [, res] = await queryPersonListByExample(params);

    if (res?.success) {
      if (!res.data?.length) return;
      const info = res.data[0];

      ElMessageBox.confirm(
        `${t('people.info.tip.content', '当前证件存在档案信息，是否引用？')}<br/>
                     ${t('global:personName')}: ${info.nameDisplay}`,
        t('people.info.tip', '档案提醒'),
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
        },
      ).then(() => {
        baseInfoModel.value = {
          ...baseInfoModel.value,
          userName: info.nameDisplay,
          user2ndName: info.person2ndName,
          userExtName: info.personExtName,
          genderCode: info.genderCode,
          spellNo: info.spellNo,
          wbNo: info.wbNo,
        };
        // baseInfoExtendModel.value = { ...baseInfoExtendModel.value };
      });
    }
  };

  /** 切换证件类型校验证件号码 */
  const validateCertificateNo = async () => {
    if (baseInfoExtendModel.value?.certificateNo) {
      nextTick(async () => {
        await baseInfoExtendRef.value?.ref?.validateField(['certificateNo']);
      });
    }
  };

  /** 切换联系方式校验手机号码 */
  const validateContactNo = async () => {
    if (baseInfoExtendModel.value?.contactNo) {
      nextTick(async () => {
        await baseInfoExtendRef.value?.ref?.validateField(['contactNo']);
      });
    }
  };

  /** 添加证件 */
  const addPerCertificate = async () => {
    addCertificateItem({
      perCertificateId: '',
      certificateNo: '',
      certificateTypeCode: CERTIFICATE_TYPE_CODE.ID_CARD,
      editable: true,
    });
  };

  /** 添加联系方式 */
  const addPerContactNo = async () => {
    addContactNoItem({
      contactTypeCode: CONTACT_TYPE_CODE.MOBILE,
      contactNo: '',
      perContactId: '',
      editable: true,
    });
  };

  /** 获取用户list */
  const getUserList = async (keyWord?: string) => {
    await queryUserListData({
      keyWord: keyWord,
      hospitalId: orgId.value ?? undefined,
    });
  };

  /** 切换用户岗位 */
  const changeRole = async (role: string) => {
    const currentIsNurse = userInfo.value?.userJobCode === USER_JOB_CODE.NURSE;
    const targetIsNurse = role === USER_JOB_CODE.NURSE;

    // 如果当前角色和目标角色的护士状态一致，恢复之前保存的业务单元
    if (currentIsNurse === targetIsNurse) {
      const arr = cloneDeep(recordOldUserRelationList.value);
      userRelationList.value = userRelationList.value?.map((item) => ({
        ...item,
        bizUnitList:
          (arr?.find((org) => org.orgId === item.orgId) ?? {}).bizUnitList ||
          [],
      }));
    }
    // 如果状态不一致，保存当前业务单元并清空
    else {
      // 保存当前状态
      recordOldUserRelationList.value = cloneDeep(userRelationList.value) ?? [];
      // 清空业务单元
      userRelationList.value = userRelationList.value?.map((item) => ({
        ...item,
        bizUnitList: [],
      }));
    }
  };

  /** 获取病区和科室 */
  const getBizUnitList = async (row: userRelationType, keyWord: string) => {
    bizUnitListData.value = [];
    bizUnitLoading.value = true;
    if (userInfoModel.value?.userJobCode === USER_JOB_CODE.NURSE) {
      await getWardList({
        keyWord: keyWord,
        hospitalId: row.orgId,
      });
      bizUnitListData.value = wardList.value?.map((item) => ({
        bizUnitId: item.orgId,
        bizUnitName: item.orgNameDisplay,
      })) as { bizUnitId: string; bizUnitName: string }[];
    } else {
      await getDepartmentList({
        keyWord: keyWord,
        hospitalId: row.orgId,
      });
      bizUnitListData.value = departmentList.value?.map((item) => ({
        bizUnitId: item.orgId,
        bizUnitName: item.orgNameDisplay,
      })) as { bizUnitId: string; bizUnitName: string }[];
    }
    bizUnitLoading.value = false;
  };

  /** 保存 */
  const handleSubmit = async () => {
    let arr = [
      baseInfoRef.value?.ref?.validate(),
      userFormRef.value?.ref?.validate(),
    ];

    if (baseInfoModel.value?.userTypeCode === USER_TYPE_CODE.NATURAL_PERSON) {
      if (isAdd.value) {
        arr = [...arr, baseInfoExtendRef.value?.ref?.validate()];
      } else {
        arr = [
          ...arr,
          perCertificateRef.value?.formRef?.validate(),
          contactNoRef.value?.formRef?.validate(),
        ];
      }
    }

    await validEditable();
    await Promise.all(arr);

    submitLoading.value = true;

    const params = {
      userId: isAdd.value ? undefined : userId.value,
      personId: isAdd.value ? undefined : userInfo.value?.personId,
      userNo: baseInfoModel.value?.userNo,
      userName: baseInfoModel.value?.userName,
      user2ndName: baseInfoModel.value?.user2ndName,
      userExtName: baseInfoModel.value?.userExtName,
      userTypeCode: baseInfoModel.value?.userTypeCode,
      enabledFlag: userInfoModel.value.enabledFlag,
      spellNo: baseInfoModel.value?.spellNo,
      wbNo: baseInfoModel.value?.wbNo,
      paySumTypeCode: userInfoModel.value?.paySumTypeCode,
      paySumBelongUserId: userInfoModel.value?.paySumBelongUserId,
      invoiceAgentUserId: userInfoModel.value?.invoiceAgentUserId,
      userJobCode: userInfoModel.value?.userJobCode,
      titleCode: baseInfoExtendModel.value?.titleCode,
      personSimpleDesc: baseInfoExtendModel.value?.personSimpleDesc,
      person2ndSimpleDesc: baseInfoExtendModel.value?.person2ndSimpleDesc,
      personExtSimpleDesc: baseInfoExtendModel.value?.personExtSimpleDesc,
      genderCode:
        baseInfoModel.value?.userTypeCode !== USER_TYPE_CODE.MACHINE
          ? baseInfoModel.value?.genderCode
          : undefined,
      perCertificateList:
        baseInfoModel.value?.userTypeCode !== USER_TYPE_CODE.MACHINE
          ? isAdd.value
            ? [
                {
                  certificateTypeCode:
                    baseInfoExtendModel.value?.certificateTypeCode ?? undefined,
                  certificateNo:
                    baseInfoExtendModel.value?.certificateNo ?? undefined,
                },
              ]
            : (perCertificateList.value ?? []).map((item) => ({
                perCertificateId: item.perCertificateId ?? undefined,
                certificateTypeCode: item.certificateTypeCode ?? undefined,
                certificateNo: item.certificateNo ?? undefined,
              }))
          : undefined,
      perContactList:
        baseInfoModel.value?.userTypeCode !== USER_TYPE_CODE.MACHINE
          ? isAdd.value
            ? [
                {
                  contactTypeCode:
                    baseInfoExtendModel.value?.contactTypeCode ?? undefined,
                  contactNo: baseInfoExtendModel.value?.contactNo ?? undefined,
                },
              ]
            : (perContactList.value ?? []).map((item) => ({
                perContactId: item.perContactId ?? undefined,
                contactTypeCode: item.contactTypeCode ?? undefined,
                contactNo: item.contactNo ?? undefined,
              }))
          : undefined,
      loginOrgLocationList: (userRelationList.value ?? [])
        .flatMap((item) => item.loginOrgLocationList)
        ?.filter((id) => !!id)
        ?.map((id, index) => ({
          orgLocationId: id,
          sort: index + 1,
        })),
      userRoles: (userRelationList.value ?? [])
        .flatMap((item) => item.userRolesList)
        ?.filter((id) => !!id),
      bizUnitIds: (userRelationList.value ?? [])
        .flatMap((item) => item.bizUnitList)
        ?.filter((id) => !!id),
    } as User.EditReqParams;

    const [, res] = await submitFn(params);

    submitLoading.value = false;

    if (res?.success) {
      if (isAdd.value) {
        showDefaultPassword();
      } else {
        ElMessage.success(t('global:modify.success'));
        goBack();
      }
    }
  };

  const submitFn = async (params: User.EditReqParams) => {
    if (isAdd.value) {
      return await addUser(params);
    } else {
      return await updateUserById(params);
    }
  };

  /** 保存成功展示密码 */
  const showDefaultPassword = () => {
    ElMessageBox.alert(
      t(
        'createEmployeeSuccessTip',
        `新建成功，默认密码: ${defaultPassword?.[DEFAULT_PASSWORD_PARAM_NO]?.[0]?.paramValue ?? '--'}`,
      ),
      t('global:tip'),
      {
        confirmButtonText: t('hasKnow', '知道了'),
        callback: () => {
          goBack();
        },
      },
    );
  };

  /** 校验证件信息和联系方式信息是否有编辑中的 */
  const validEditable = async () => {
    const certificateObj = (perCertificateList.value ?? []).find(
      (item) => item.editable,
    );
    const contactObj = (perContactList.value ?? []).find(
      (item) => item.editable,
    );
    if (
      !isAdd.value &&
      baseInfoModel.value?.userTypeCode === USER_TYPE_CODE.NATURAL_PERSON
    ) {
      if (certificateObj) {
        ElMessageBox.confirm(
          t('user.certificate.tip', '证件信息存在编辑中的数据，请先确认！'),
          t('global:tip'),
        );
      }
      if (contactObj) {
        await ElMessageBox.confirm(
          t('employee.contact.tip', '联系方式存在编辑中的数据，请先确认！'),
          t('global:tip'),
        );
      }
    }
  };

  const baseInfoConfig = useBaseInfoFormConfig({
    baseInfoModel: baseInfoModel,
    disabled: disabled,
  });

  const baseInfoExtendConfig = useBaseInfoExtendFormConfig({
    isAdd,
    disabled: disabled,
    baseInfoExtendModel: baseInfoExtendModel as Ref<baseInfoExtendType>,
    validatePeopleInfo,
    validateCertificateNo,
    validateContactNo,
  });

  const { addItem: addCertificateItem, data: perCertificateColumn } =
    useCertificateTableConfig({
      disabled: disabled,
      tableRef: perCertificateRef,
      tableData: perCertificateList,
    });
  const { addItem: addContactNoItem, data: contactNoColumn } =
    useContactNoTableConfig({
      disabled: disabled,
      tableRef: contactNoRef,
      tableData: perContactList,
    });

  const userInfoConfig = useUserInfoFormConfig({
    isAdd,
    disabled,
    userList,
    userLoading,
    changeRole,
    getUserList,
  });

  const userRelationConfig = useUserInfoTableConfig({
    bizUnitLoading,
    disabled,
    bizUnitList,
    userInfoModel: userInfoModel as Ref<userInfoType>,
    bizUnitListData,
    getBizUnitList,
  });

  onMounted(async () => {
    await initData();
  });
</script>
<template>
  <!-- 用户管理详情 -->
  <div class="flex size-full flex-col py-2.5 pl-2.5" v-loading="loading">
    <el-page-header @back="goBack" class="mb-2">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('user.add', '新增用户')
              : `${disabled ? $t('detail', '详情') : $t('global:edit')}-${userInfo?.userNameDisplay}`
          }}
        </span>
      </template>
    </el-page-header>

    <div
      class="mb-4 flex size-full flex-col overflow-auto"
      style="height: calc(100% - 5rem)"
    >
      <el-scrollbar view-class="mr-2.5">
        <!-- 基本信息 -->
        <Title :title="$t('user.baseInfo', '基本信息')" class="my-3"></Title>
        <ProForm
          v-model="baseInfoModel"
          ref="baseInfoRef"
          :data="baseInfoConfig"
          :column="4"
        />

        <!-- 基本信息扩展 -->
        <div
          v-if="baseInfoModel?.userTypeCode === USER_TYPE_CODE.NATURAL_PERSON"
          class="mb-2"
        >
          <ProForm
            v-model="baseInfoExtendModel"
            ref="baseInfoExtendRef"
            :data="baseInfoExtendConfig"
            :column="4"
          />

          <!-- 证件table -->
          <template v-if="!isAdd">
            <ProTable
              ref="perCertificateRef"
              row-key="perCertificateId"
              :columns="perCertificateColumn"
              :editable="true"
              :data="perCertificateList"
            >
            </ProTable>
            <el-button
              icon="Plus"
              :disabled="disabled"
              @click="addPerCertificate"
              class="w-full rounded-none border-t-0"
            >
              {{ $t('global:add') }}
            </el-button>

            <!-- 联系方式table -->
            <ProTable
              class="mt-2"
              ref="contactNoRef"
              row-key="perContactId"
              :columns="contactNoColumn"
              :editable="true"
              :data="perContactList"
            >
            </ProTable>
            <el-button
              class="w-full rounded-none border-t-0"
              :disabled="disabled"
              icon="Plus"
              @click="addPerContactNo"
            >
              {{ $t('global:add') }}
            </el-button>
          </template>
        </div>

        <!-- 用户信息 -->
        <Title :title="$t('user.userInfo', '用户信息')" class="mb-3" />
        <ProForm
          :column="4"
          ref="userFormRef"
          v-model="userInfoModel"
          :data="userInfoConfig"
        />

        <!-- 角色与服务关系 -->
        <Title
          :title="$t('user.role.relationship', '角色与服务关系')"
          class="my-3"
        />
        <ProTable
          ref="userRelationRef"
          :columns="userRelationConfig"
          :editable="true"
          :data="userRelationList"
        />
      </el-scrollbar>
    </div>

    <!-- 保存、取消按钮 -->
    <div class="mr-2.5 text-right">
      <el-button @click="goBack">
        {{ $t('global:cancel') }}
      </el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
        v-if="!disabled"
      >
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
