<script setup lang="ts" name="personalInfo">
  import { ref, onMounted } from 'vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import { queryEmployeeListByExample } from '@modules/system/api/employee';
  import PersonalDetail from '@/modules/system/components/employeeDetail/index.vue';

  const { userInfo } = useAppConfigData([MAIN_APP_CONFIG.USER_INFO]);

  type EmployeeInfo = Omit<
    Employee.Item,
    | 'userRoleList'
    | 'loginOrgLocationList'
    | 'perCertificateList'
    | 'bizUnitIds'
  >;

  const loading = ref(false);
  const isExistEmployee = ref(false);

  const detailData = ref<{
    employeeData: Employee.Item;
    userInfo: Employee.UserInfo;
    employeeInfo: EmployeeInfo;
  }>({
    employeeData: {} as Employee.Item,
    userInfo: {} as Employee.UserInfo,
    employeeInfo: {} as EmployeeInfo,
  });

  /** 初始化 */
  const initData = async () => {
    await fetchData();
  };

  /** 获取数据 */
  const fetchData = async () => {
    loading.value = true;
    const [, res] = await queryEmployeeListByExample({
      pageNumber: 0,
      userId: userInfo?.userId,
    });
    loading.value = false;
    if (res?.success) {
      if (res.data && res.data.length > 0) {
        isExistEmployee.value = true;
      } else {
        isExistEmployee.value = false;
        return;
      }
      const infoData = res.data[0];
      detailData.value.employeeData = infoData;
      detailData.value.userInfo = {
        userJobCode: infoData.userJobCode,
        userEnabledFlag: infoData.enabledFlag,
        invoiceAgentUserId: infoData.invoiceAgentUserId,
        paySumTypeCode: infoData.paySumTypeCode,
        paySumBelongUserId: infoData.paySumBelongUserId,
      };
      detailData.value.employeeInfo = { ...infoData };
    }
  };

  onMounted(async () => {
    await initData();
  });
</script>

<template>
  <div class="p-box size-full">
    <PersonalDetail
      :disabled="true"
      :detail-data="detailData"
      :is-personal-info="true"
      v-if="isExistEmployee"
    ></PersonalDetail>
    <el-empty
      v-else
      class="h-full"
      :description="$t('not.exist.employeeData', '未查询到职工信息')"
    ></el-empty>
  </div>
</template>
