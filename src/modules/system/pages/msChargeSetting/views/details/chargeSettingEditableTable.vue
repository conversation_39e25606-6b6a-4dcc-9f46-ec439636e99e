<script setup lang="ts">
  import { ProTable } from 'sun-biz';
  import { computed, useTemplateRef } from 'vue';
  import { FormInstance } from 'element-sun';
  import { FLAG } from '@sun-toolkit/enums';
  import { useChargeSettingEditableTable } from '../../config/useChargeSettingEditableTable';
  import {
    computeChargeObjectMap,
    spanMethodFn,
  } from '../../config/useTableSpanDataConfig';

  const data = defineModel<MsChargeSetting.MsChargeTableItem[]>('data', {
    default: [],
  });
  const chargeItems =
    defineModel<MsChargeSetting.ListOfPaidDrugs[]>('chargeItems');
  /* 计费方式是否包含 区间设置 */
  const containRangeColumn = defineModel<boolean>('containRangeColumn', {
    default: false,
  });
  /**
   * 计费对象Map<msChargeTypeCode, Set<msChargeObjectTypeCode>>
   * 计费对象Map<计费对象类型Code, Set<计费列表项 code>>
   */
  const props = defineProps<{
    msChargetObjectListMap: Map<string, Set<string>>;
  }>();

  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    formRef: FormInstance;
  }>('proTable');
  const operations: MsChargeSetting.MsChargeSettingOperation = {
    insert,
    edit,
    remove,
    addRange,
  };
  const chargeObjectMap = computed(() =>
    computeChargeObjectMap(
      data.value,
      props.msChargetObjectListMap,
      containRangeColumn.value,
    ),
  );
  // 列合并
  const spanMethod = computed(() => {
    return spanMethodFn(
      chargeObjectMap.value,
      data.value,
      containRangeColumn.value,
    );
  });
  const columnsConfig = useChargeSettingEditableTable(
    containRangeColumn,
    chargeItems.value || [],
    operations,
    data,
    chargeObjectMap,
  );

  // 插入
  function insert(row: MsChargeSetting.MsChargeTableItem, index: number) {
    // 校验当前行必填信息
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        let baseInfo: MsChargeSetting.MsChargeTableItem = {
          editable: true,
          msChargeTypeCode: row.msChargeTypeCode,
          msChargeTypeDesc: row.msChargeTypeDesc,
          msChargeObjectId: row.msChargeObjectId,
          msChargeObjectName: row.msChargeObjectName,
          msChargeObjectTypeCode: row.msChargeObjectTypeCode,
          msChargeObjectTypeDesc: row.msChargeObjectTypeDesc,
          num: FLAG.YES, // 数量默认为 1
        };

        // 插入新行
        if (containRangeColumn.value) {
          baseInfo = {
            ...baseInfo,
            rangeLowerNum: row.rangeLowerNum,
            rangeUpperNum: row.rangeUpperNum,
          };
          const { msChargeObjectTypeCode, rangeUpperNum } = row;
          const curChargeObject = chargeObjectMap.value.get(
            msChargeObjectTypeCode,
          )!;
          const { end } = curChargeObject[rangeUpperNum!];
          data.value.splice(end + 1, 0, baseInfo);
        } else {
          data.value.push(baseInfo);
        }

        // 新插入行前边都不可编辑
        data.value.forEach((item, ind) => {
          if (ind <= index) {
            item.editable = false;
          }
        });
      }
    });
  }
  // 编辑
  function edit(row: MsChargeSetting.MsChargeTableItem) {
    row.editable = true;
  }
  // 移除
  function remove(index: number) {
    data.value.splice(index, 1);
  }
  // 新增区间
  function addRange(row: MsChargeSetting.MsChargeTableItem, index: number) {
    // 校验上一个行的区间是否设置(校验上一行的区间设置)
    const lastIndex = index - 1;
    const lastItem = data.value[lastIndex];
    const { rangeUpperNum } = lastItem;
    proTableRef.value?.formRef.validateField(
      // 【tableData.】 是ProTable 组件内部绑定的属性前缀
      [
        `tableData.${lastIndex}.rangeUpperNum`,
        `tableData.${lastIndex}.rangeLowerNum`,
      ],
      (valid) => {
        if (valid) {
          const newRangeNum = rangeUpperNum! + 1;
          const newRangeItem: MsChargeSetting.MsChargeTableItem = {
            editable: true,
            msChargeObjectId: row.msChargeObjectId,
            msChargeObjectName: row.msChargeObjectName,
            msChargeTypeCode: row.msChargeTypeCode,
            msChargeTypeDesc: row.msChargeTypeDesc,
            msChargeObjectTypeCode: row.msChargeObjectTypeCode,
            msChargeObjectTypeDesc: row.msChargeObjectTypeDesc,
            num: FLAG.YES, // 数量默认为 1
            rangeLowerNum: newRangeNum,
            rangeUpperNum: newRangeNum,
          };
          data.value.splice(index, 0, newRangeItem);
        }
      },
    );
  }

  defineExpose({
    proTableRef,
  });
</script>

<template>
  <div>
    <ProTable
      ref="proTable"
      :data="data"
      :columns="columnsConfig"
      :editable="true"
      :span-method="spanMethod"
    ></ProTable>
  </div>
</template>
