<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onBeforeMount, onUnmounted } from 'vue';
  import { Plus } from '@element-sun/icons-vue';
  import { FLAG } from '@sun-toolkit/enums';
  import { Title } from 'sun-biz';
  import {
    queryMsChargeSettingListByExample,
    queryMsChargeObjectListByExample,
  } from '@/modules/system/api/msChargeSetting';
  import ChargeSettingForm from './chargeSettingForm.vue';
  import ChargeSettingList from './chargeSettingList.vue';
  import { SERVICEINFO_HOSPITALID_MSID } from '../../constant';
  import { useSessionStorage } from '../../config/useSessionStorage';

  const router = useRouter();
  // 医疗服务计费方式设置列表
  const msChargeTypeSettingList = ref<MsChargeSetting.MsChargeTypeSetting[]>(
    [],
  );
  const SessionServiceInfo =
    useSessionStorage<MsChargeSetting.IServiecInfoHospitalIdMsId>(
      SERVICEINFO_HOSPITALID_MSID,
    );
  const { hospitalId, msId, msName, msNo } = SessionServiceInfo.value || {};
  // 支持的医疗服务计费方式列表项目
  const msChargeTypeSupported = ref<MsChargeSetting.MsChargeTypeSupported[]>(
    [],
  );
  const addFormDisable = ref(false);
  const isAddForm = ref(false);
  const loading = ref(false);

  // 根据条件查询服务的计费设置列表
  async function queryChargeSettingListOfService() {
    loading.value = true;
    const [, res] = await queryMsChargeSettingListByExample({
      msIds: [msId],
      hospitalId: hospitalId,
      onlyQueryUsingFlag: FLAG.NO,
    });
    if (res?.success) {
      msChargeTypeSettingList.value = res?.data || [];
    }
    loading.value = false;
  }

  // 根据条件查询服务的计费对象列表
  async function queryChargeObjectListOfService() {
    const [, res] = await queryMsChargeObjectListByExample({
      msId: msId,
    });
    if (res?.success) {
      msChargeTypeSupported.value = res?.data || [];
    }
  }

  // 计费设置
  function setCharge() {
    isAddForm.value = true;
    addFormDisable.value = true;
  }

  function goBack() {
    router.push('/');
  }

  // 初始化服务计费设置列表+服务计费对象列表
  function initFun() {
    queryChargeSettingListOfService();
    queryChargeObjectListOfService();
  }

  function clean() {
    isAddForm.value = false;
    addFormDisable.value = false;
    queryChargeSettingListOfService();
  }

  function refresh() {
    queryChargeSettingListOfService();
  }

  onBeforeMount(() => {
    initFun();
  });
  onUnmounted(() => {
    SessionServiceInfo.clear();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      class="mb-3"
      :title="
        $t(
          'msChargeSetting.list.title',
          msName && msNo ? `${msName}（${msNo}）的计费设置` : '计费设置',
        )
      "
    >
      <el-button plain @click="goBack()">
        {{ $t('global:back') }}
      </el-button></Title
    >
    <div class="mb-3">
      <el-button
        type="primary"
        :icon="Plus"
        :disabled="addFormDisable"
        plain
        @click="setCharge"
      >
        {{ $t('system.msChargeSetting.newChargeSetting', '新计费设置') }}
      </el-button>
    </div>
    <!-- 新计费设置区块 -->
    <div v-if="isAddForm">
      <ChargeSettingForm
        :ms-charge-object-list="msChargeTypeSupported"
        :ms-charge-type-setting-list="msChargeTypeSettingList"
        :hospitalid-msid="{ hospitalId, msId }"
        @clean="clean"
      ></ChargeSettingForm>
    </div>
    <!-- 计费设置列表 -->
    <ChargeSettingList
      class="mt-2"
      @refresh="refresh"
      v-loading="loading"
      :list="msChargeTypeSettingList"
      :ms-charge-object-list="msChargeTypeSupported"
    ></ChargeSettingList>
  </div>
</template>
