<script setup lang="ts">
  import { ProTable } from 'sun-biz';
  import { computed, ref, watch } from 'vue';
  import { useChargeSettingListTable } from '../../config/useChargeSettingListTable';
  import {
    computeChargeObjectMap,
    spanMethodFn,
  } from '../../config/useTableSpanDataConfig';

  const data = defineModel<MsChargeSetting.MsChargeTableItem[]>('data', {
    default: [],
  });
  /* 计费方式是否包含 区间设置 */
  const containRangeColumn = defineModel<boolean>('containRangeColumn', {
    default: false,
  });
  /**
   * 计费对象Map<msChargeTypeCode, Set<msChargeObjectTypeCode>>
   * 计费对象Map<计费对象类型Code, Set<计费列表项 code>>
   */
  const props = defineProps<{
    msChargetObjectListMap: Map<string, Set<string>>;
  }>();

  const chargeObjectMap = computed(() =>
    computeChargeObjectMap(
      data.value,
      props.msChargetObjectListMap,
      containRangeColumn.value,
    ),
  );
  // 列合并
  const spanMethod = ref<ReturnType<typeof spanMethodFn>>();

  watch(
    chargeObjectMap,
    () => {
      spanMethod.value = spanMethodFn(
        chargeObjectMap.value,
        data.value,
        containRangeColumn.value,
      );
    },
    {
      immediate: true,
    },
  );

  const columnsConfig = useChargeSettingListTable(containRangeColumn);
</script>

<template>
  <div>
    <ProTable
      :data="data"
      :columns="columnsConfig"
      :editable="true"
      :span-method="spanMethod"
    ></ProTable>
  </div>
</template>
