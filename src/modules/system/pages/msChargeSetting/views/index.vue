<script setup lang="ts">
  import { ref } from 'vue';
  import {
    Title,
    ProForm,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { useServiceListFormConfig } from '../config/useFormConfig.tsx';
  import { useServiceListTableConfig } from '../config/useTableConfig.tsx';
  import { queryClinicalServiceListAndChargeSettingByExample } from '@/modules/system/api/msChargeSetting.ts';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';
  import { useRouter } from 'vue-router';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { SERVICEINFO_HOSPITALID_MSID } from '../constant.ts';
  import { useSessionStorage } from '../config/useSessionStorage.ts';

  const SessionServiceInfo =
    useSessionStorage<MsChargeSetting.IServiecInfoHospitalIdMsId>(
      SERVICEINFO_HOSPITALID_MSID,
    );
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const router = useRouter();
  const searchParams = ref<MsChargeSetting.SearchParams>({
    hospitalId: currentOrg?.orgId || '',
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const serviceList = ref<MsChargeSetting.ServiceList[]>([]); //服务列表
  const loading = ref(true);
  const searchConfig = useServiceListFormConfig();

  //分页配置
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  //根据条件查询临床服务列表及计费设置
  const queryTheListOfClinicalServices = async () => {
    loading.value = true;
    const params = {
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] =
      await queryClinicalServiceListAndChargeSettingByExample(params);
    if (res?.success) {
      serviceList.value = res?.data || [];
      if (res?.total) {
        pageInfo.value.total = res?.total;
      }
    }
    loading.value = false;
  };
  const modelChange = (data: MsChargeSetting.SearchParams) => {
    searchParams.value = {
      ...searchParams.value,
      ...(data ?? {}),
    };
    queryTheListOfClinicalServices();
  };
  //获取分页的方法
  const currentPageChange = (pageNumber: number) => {
    pageInfo.value.pageNumber = pageNumber;
    queryTheListOfClinicalServices();
  };
  //当前多少条数据发生改变
  const sizePageChange = (num: number) => {
    pageInfo.value.pageSize = num;
    queryTheListOfClinicalServices();
  };
  //点击计费设置
  const billingSettings = (row: MsChargeSetting.ServiceList) => {
    SessionServiceInfo.set({
      hospitalId: searchParams.value.hospitalId,
      msId: row.msId,
      msNo: row.msNo,
      msName: row.msName,
    } as MsChargeSetting.IServiecInfoHospitalIdMsId);
    router.push(`/details/${searchParams.value.hospitalId}/${row.msId}`);
  };
  //初始化获取数据
  const initFun = () => {
    queryTheListOfClinicalServices();
  };
  const tableColumnsConfig = useServiceListTableConfig(billingSettings);
  initFun();
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('msChargeSetting.list.title', '服务列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :column="5"
          layout-mode="inline"
          :show-search-button="true"
          @model-change="modelChange"
        ></ProForm>
      </div>
    </div>
    <ProTable
      :loading="loading"
      :columns="tableColumnsConfig"
      :data="serviceList"
      :page-info="pageInfo"
      :pagination="true"
      @current-page-change="currentPageChange"
      @size-page-change="sizePageChange"
    ></ProTable>
  </div>
</template>
