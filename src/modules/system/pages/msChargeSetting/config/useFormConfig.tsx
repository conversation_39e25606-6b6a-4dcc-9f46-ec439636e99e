import { useFormConfig } from 'sun-biz';
import { CS_TYPE_CODE, BILLING_SETTINGS } from '@/utils/constant.ts';
import { ref } from 'vue';
export function useServiceListFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [CS_TYPE_CODE],
    getData: (t, dataSet) => {
      const billingService = ref([
        {
          value: BILLING_SETTINGS.NOT_SET,
          label: t('system.msChargeSetting.notSet', '未设置'),
        },
        {
          value: BILLING_SETTINGS.ALREADY_SET,
          label: t('system.msChargeSetting.alreadySet', '已设置'),
        },
      ]);
      return [
        {
          label: t('system.msChargeSetting.belongHospital', '所属医院'),
          name: 'hospitalId',
          triggerModelChange: true,
          component: 'hospitalSelect',
          extraProps: {
            clearable: false,
            className: 'w-40',
          },
        },
        {
          name: 'csTypeCode',
          label: t(
            'system.msChargeSetting.clinicalServiceTypes',
            '临床服务类型',
          ),
          component: 'select',
          triggerModelChange: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('system.msChargeSetting.clinicalServiceTypes', '临床服务'),
          }),
          extraProps: {
            className: 'w-40',
            options: dataSet?.value ? dataSet.value[CS_TYPE_CODE] : [],
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
        {
          label: t('global:enabledFlag'),
          name: 'enabledFlag',
          component: 'flagSelect',
          triggerModelChange: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('global:enabledFlag'),
          }),
          extraProps: {
            clearable: false,
            className: 'w-40',
          },
        },
        {
          name: 'existsEnableChargeSettingFlag',
          label: t('system.msChargeSetting.billingSettings', '计费设置'),
          component: 'select',
          triggerModelChange: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('system.msChargeSetting.billingSettings', '计费设置'),
          }),
          extraProps: {
            className: 'w-40',
            options: billingService.value,
          },
        },
        {
          name: 'keyWord',
          label: t('global:keyword', '关键字'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('system.msChargeSetting.content', '关键字'),
          }),
          triggerModelChange: true,
          extraProps: {
            className: 'w-40',
          },
        },
      ];
    },
  });
  return data;
}
