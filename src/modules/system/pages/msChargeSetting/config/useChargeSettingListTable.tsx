import { useColumnConfig } from 'sun-biz';
import { Ref } from 'vue';
export function useChargeSettingListTable(
  containRangeColumn: Ref<boolean> /* 当前计费方式是否包含区间设置 */,
) {
  return useColumnConfig({
    getData: (t) => {
      const rangeColumns = [
        // 区间下限
        {
          label: t('system.msChargeSetting.rangeLowerNum', '区间下限'),
          prop: 'rangeLowerNum',
        },
        // 区间上限
        {
          label: t('system.msChargeSetting.rangeUpperNum', '区间上限'),
          prop: 'rangeUpperNum',
        },
      ];
      const baseColumns = [
        // 序号
        {
          label: t('global:indexNo'),
          minWidth: 75,
          type: 'index',
        },
        // 计费对象
        {
          label: t('system.msChargeSetting.msChargeObjectTypeDesc', '计费对象'),
          prop: 'msChargeObjectTypeDesc',
        },
      ];
      const costDetailsColumn = [
        // 费用编码
        {
          label: t('system.msChargeSetting.commodityNo', '费用编码'),
          prop: 'commodityNo',
          minWidth: 80,
        },
        // 费用名称
        {
          label: t('system.msChargeSetting.commodityNameDisplay', '费用名称'),
          prop: 'commodityNameDisplay',
          minWidth: 80,
        },
        // 单价
        {
          label: t('system.msChargeSetting.price', '单价'),
          prop: 'price',
          minWidth: 80,
        },
        // 单位
        {
          label: t('system.msChargeSetting.unitName', '单位'),
          prop: 'unitName',
          minWidth: 80,
        },
        // 数量
        {
          label: t('system.msChargeSetting.num', '数量'),
          prop: 'num',
          minWidth: 80,
        },
        // 操作：插入、编辑、移除
        {
          label: t('global:operation'),
          minWidth: 80,
          render: () => {
            return (
              <>
                <el-button link type="primary" disabled>
                  {t('global:insert')}
                </el-button>
                <el-button link type="primary" disabled>
                  {t('global:edit')}
                </el-button>
                <el-button link type="danger" disabled>
                  {t('global:remove')}
                </el-button>
              </>
            );
          },
        },
      ];

      return containRangeColumn.value
        ? [...baseColumns, ...rangeColumns, ...costDetailsColumn]
        : [...baseColumns, ...costDetailsColumn];
    },
  });
}
