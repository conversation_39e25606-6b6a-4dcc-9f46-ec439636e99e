import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG, BILLING_SETTINGS } from '@/utils/constant';
export function useServiceListTableConfig(
  billingSettings: (row: MsChargeSetting.ServiceList) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 60,
          type: 'index',
        },
        {
          label: t('system.msChargeSetting.msNo', '服务编码'),
          prop: 'msNo',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.msName', '服务名称'),
          prop: 'msName',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.msTypeDesc', '服务类型'),
          prop: 'msTypeDesc',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.spellNo', '拼音码'),
          prop: 'spellNo',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.wbNo', '五笔码'),
          prop: 'wbNo',
          minWidth: 90,
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 90,
          render: (row: MsChargeSetting.ServiceList) => {
            return (
              <div>
                {row.enabledFlag === ENABLED_FLAG.YES
                  ? t('global:enabled')
                  : t('global:disabled')}
              </div>
            );
          },
        },
        {
          label: t('system.msChargeSetting.billingSettings', '计费设置'),
          prop: 'enabledFlag',
          minWidth: 90,
          render: (row: MsChargeSetting.ServiceList) => {
            return (
              <div>
                {row.existsEnableChargeSettingFlag === BILLING_SETTINGS.NOT_SET
                  ? t('system.msChargeSetting.notSet', '未设置')
                  : t('system.msChargeSetting.alreadySet', '已设置')}
              </div>
            );
          },
        },
        {
          label: t('system.msChargeSetting.startAt', '生效时间'),
          prop: 'startAt',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.endAt', '失效时间'),
          prop: 'endAt',
          minWidth: 90,
        },
        {
          label: t('system.msChargeSetting.operate', '操作'),
          minWidth: 100,
          fixed: 'right',
          render: (row: MsChargeSetting.ServiceList) => {
            return (
              <el-button
                link
                type="primary"
                onClick={() => billingSettings(row)}
              >
                {t('system.msChargeSetting.billingSettings', '计费设置')}
              </el-button>
            );
          },
        },
      ];
      return data;
    },
  });
}
