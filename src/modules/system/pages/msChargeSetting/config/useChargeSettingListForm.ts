import { FLAG } from '@sun-toolkit/enums';
import { TFunction } from 'i18next';
import type { IListItem } from '../views/details/chargeSettingList.vue';

/**
 * 计费设置列表表单配置
 * @param t i18n翻译函数
 * @param usingFlag 是否启用开关
 * @param msChargeTypeOptions 计费方式选项
 */
export function useChargeSettingListForm(
  t: TFunction,
  collapseItem: IListItem,
  msChargeTypeOptions: { label: string; value: string }[],
  switchChange: (msChargeTypeSettingId: string, enabledFlag: number) => void,
) {
  return [
    {
      name: 'csTypeCode',
      component: 'select',
      triggerModelChange: true,
      placeholder: t('global:placeholder.select.template', {
        name: t('system.msChargeSetting.billingMethod', '计费方式'),
      }),
      className: 'w-60 mb-0',
      extraProps: {
        disabled: true,
        clearable: false,
        options: msChargeTypeOptions,
      },
    },
    {
      name: 'date',
      label: t('system.msChargeSetting.effectiveDate', '生效日期'),
      component: 'date-picker',
      className: ' pl-20 mb-0',
      extraProps: {
        disabled: true,
        type: 'daterange',
        format: 'YYYY-MM-DD HH:mm:ss',
        'value-format': 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: t('global:rangeSeparator'),
      },
    },
    {
      name: 'enabledFlag',
      label: t('system.msChargeSetting.effectiveDate', '状态'),
      component: 'switch',
      className: 'pl-20 mb-0',
      extraProps: {
        disabled: !collapseItem.usingFlag,
        'inline-prompt': true,
        'active-value': FLAG.YES,
        'inactive-value': FLAG.NO,
        'active-text': t('global:enabled'),
        'inactive-text': t('global:disabled'),
        onChange: (enabledFlag: number) => {
          switchChange(collapseItem.msChargeTypeSettingId, enabledFlag);
        },
      },
    },
  ];
}
