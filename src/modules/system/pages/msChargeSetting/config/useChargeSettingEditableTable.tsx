import { useColumnConfig } from 'sun-biz';
import { NUM_MAX, NUM_MIN, RANGE_NUM_MAX, RANGE_NUM_MIN } from '../constant';
import { Ref } from 'vue';
import { Plus } from '@element-sun/icons-vue';
import { FormItemRule } from 'element-sun';

export function useChargeSettingEditableTable(
  containRangeColumn: Ref<boolean> /* 当前计费方式是否包含区间设置 */,
  chargeItems: MsChargeSetting.ListOfPaidDrugs[] /* 收费项目列表*/,
  operations: MsChargeSetting.MsChargeSettingOperation /* 操作方法 */,
  tableData: Ref<MsChargeSetting.MsChargeTableItem[]> /* 表格数据 */,
  /**
   * Map<计费对象code, { 计费列表开始索引， 计费列表结束索引， 计费列表, 区间1: { start, end }, 区间2： { start, end } }>
   */
  chargeObjectMap: Ref<Map<string, MsChargeSetting.IChargeObjectMapValue>>,
) {
  return useColumnConfig({
    getData: (t) => {
      // 是否可插入(仅最后一行可以插入)
      const insertDisable = (
        row: MsChargeSetting.MsChargeTableItem,
        index: number,
      ) => {
        const { msChargeObjectTypeCode, rangeUpperNum } = row;
        const curChargeObject = chargeObjectMap.value.get(
          msChargeObjectTypeCode,
        ) as MsChargeSetting.IChargeObjectMapValue;
        // 获取当前计费列表的 结束索引 end
        let end = curChargeObject.end;
        if (containRangeColumn.value) {
          // 获取当前区间列表的 结束索引 end
          end = curChargeObject[rangeUpperNum!].end;
        }

        return index !== end;
      };

      // 是否可移除(计费列表长度 > 1, 可移除; 否则，不可移除)
      const removeDisable = (row: MsChargeSetting.MsChargeTableItem) => {
        const { msChargeObjectTypeCode } = row;
        const curChargeObject = chargeObjectMap.value.get(
          msChargeObjectTypeCode,
        ) as MsChargeSetting.IChargeObjectMapValue;
        const { list } = curChargeObject;
        if (containRangeColumn.value) {
          // 计费对象列表最后一行为【增加区间】按钮，需要减去一行
          return list.length - 1 <= 1;
        } else {
          return list.length <= 1;
        }
      };

      const rangeColumns = [
        // 区间下限
        {
          label: t('system.msChargeSetting.rangeLowerNum', '区间下限'),
          prop: 'rangeLowerNum',
          editable: true,
          rules: (row: MsChargeSetting.MsChargeTableItem) => [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('system.msChargeSetting.rangeLowerNum', '区间下限'),
              }),
            },
            {
              validator(
                rule: FormItemRule,
                value: number,
                callback: (error?: string | Error) => void,
              ) {
                // 1. 区间下限必须大于上一行区间上限
                // 2. 且 区间下限必须小于等于区间上限
                const {
                  rangeLowerNum = 0,
                  rangeUpperNum,
                  msChargeObjectTypeCode,
                } = row;
                const curChargeObject = chargeObjectMap.value.get(
                  msChargeObjectTypeCode,
                ) as MsChargeSetting.IChargeObjectMapValue;
                const curRangeStart = curChargeObject[rangeUpperNum!].start; // 本区间开始索引
                const lastRangeItem = tableData.value[curRangeStart - 1]; // 上一区间结束行
                if (lastRangeItem) {
                  const { rangeUpperNum: lastRangeUpperNum = 0 } =
                    lastRangeItem;
                  if (rangeLowerNum <= lastRangeUpperNum) {
                    callback(
                      new Error(
                        t(
                          'system.msChargeSetting.rangeLowerNumErrorCompareWithLast',
                          '区间下限必须大于上一行区间上限',
                        ),
                      ),
                    );
                  } else {
                    callback();
                  }
                } else {
                  // 为计费列表的第一行
                  if (rangeLowerNum > rangeUpperNum!) {
                    callback(
                      new Error(
                        t(
                          'system.msChargeSetting.rangeLowerNumErrorCompareWithCurrent',
                          '区间下限必须小于等于区间上限',
                        ),
                      ),
                    );
                  } else {
                    callback();
                  }
                }
              },
              trigger: 'change',
            },
          ],
          render: (row: MsChargeSetting.MsChargeTableItem, index: number) => {
            if (row.containAddButton) {
              // 渲染【添加区间按钮】
              return (
                <>
                  <el-button
                    class="w-full"
                    icon={Plus}
                    link
                    // 当上一区间上限已经为 区间最大值，则禁止添加区间
                    disabled={
                      tableData.value[index - 1].rangeUpperNum === RANGE_NUM_MAX
                    }
                    onClick={() => operations.addRange(row, index)}
                  >
                    {t('global:add')}
                    {t('system.msChargeSetting.range', '区间')}
                  </el-button>
                </>
              );
            } else {
              const nextItem = tableData.value[index + 1];
              // 下一行存在且不为增加区间按钮
              if (!nextItem.containAddButton) {
                return <>{row.rangeLowerNum}</>;
              }
              return (
                <>
                  <el-input-Number
                    class="mx-auto"
                    controls-position="right"
                    min={RANGE_NUM_MIN}
                    max={RANGE_NUM_MAX}
                    v-model={row.rangeLowerNum}
                    onChange={(val: number) => {
                      const { rangeUpperNum, msChargeObjectTypeCode } = row;
                      const curChargeObject = chargeObjectMap.value.get(
                        msChargeObjectTypeCode,
                      ) as MsChargeSetting.IChargeObjectMapValue;
                      const curRangeStart =
                        curChargeObject[rangeUpperNum!].start; // 本区间开始索引
                      const lastRangeItem = tableData.value[curRangeStart - 1]; // 上一区间结束行
                      if (lastRangeItem) {
                        const { rangeUpperNum: lastRangeUpperNum = 0 } =
                          lastRangeItem;
                        if (val <= lastRangeUpperNum) {
                          // 如果当前区间下限小于等于上一行区间上限，则将当前区间下限设置为上一行区间上限 + 1
                          row.rangeLowerNum = lastRangeUpperNum + 1;
                        }
                      } else {
                        // 为计费列表的第一行
                        if (val > rangeUpperNum!) {
                          // 如果当前区间下限大于等于区间上限，则将当前区间下限设置为区间上限
                          row.rangeLowerNum = rangeUpperNum!;
                        }
                      }
                    }}
                  ></el-input-Number>
                </>
              );
            }
          },
        },
        // 区间上限
        {
          label: t('system.msChargeSetting.rangeUpperNum', '区间上限'),
          prop: 'rangeUpperNum',
          editable: true,
          rules: (row: MsChargeSetting.MsChargeTableItem) => [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('system.msChargeSetting.rangeUpperNum', '区间上限'),
              }),
            },
            {
              validator(
                rule: FormItemRule,
                value: number,
                callback: (error?: string | Error) => void,
              ) {
                // 1. 区间上限必须大于等于区间下限
                const { rangeLowerNum = 0, rangeUpperNum = 0 } = row;
                if (rangeUpperNum < rangeLowerNum) {
                  callback(
                    new Error(
                      t(
                        'system.msChargeSetting.rangeUpperNumErrorCompareWithCurrent',
                        '区间上限必须大于等于区间下限',
                      ),
                    ),
                  );
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          render: (row: MsChargeSetting.MsChargeTableItem, index: number) => {
            const nextItem = tableData.value[index + 1];
            // 下一行存在且不为增加区间按钮
            if (!nextItem.containAddButton) {
              return <>{row.rangeUpperNum}</>;
            }
            return (
              <el-input-Number
                class="mx-auto"
                controls-position="right"
                min={RANGE_NUM_MIN}
                max={RANGE_NUM_MAX}
                v-model={row.rangeUpperNum}
                onChange={(val: number) => {
                  const { rangeLowerNum = 0 } = row;
                  if (val <= rangeLowerNum) {
                    // 如果当前区间上限小于等于区间下限，则将当前区间上限设置为区间下限
                    row.rangeUpperNum = rangeLowerNum;
                  }
                }}
              ></el-input-Number>
            );
          },
        },
      ];
      const baseColumns = [
        // 序号
        {
          label: t('global:indexNo'),
          minWidth: 75,
          type: 'index',
        },
        // 计费对象
        {
          label: t('system.msChargeSetting.msChargeObjectTypeDesc', '计费对象'),
          prop: 'msChargeObjectTypeDesc',
        },
      ];
      const costDetailsColumn = [
        // 费用编码
        {
          label: t('system.msChargeSetting.commodityNo', '费用编码'),
          prop: 'commodityNo',
          minWidth: 80,
        },
        // 费用名称
        {
          label: t('system.msChargeSetting.commodityNameDisplay', '费用名称'),
          prop: 'commodityNameDisplay',
          minWidth: 80,
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.select.template', {
                name: t(
                  'system.msChargeSetting.commodityNameDisplay',
                  '费用名称',
                ),
              }),
            },
          ],
          render: (row: MsChargeSetting.MsChargeTableItem) => {
            /**
             * isSelected 是否已被选
             * @param {String} commodityNo // 费用编码
             * @returns
             */
            const isSelected = (commodityNo: string) => {
              // row.msChargeObjectTypeCode // 计费对象编码
              // 筛选统一个计费对象下的费用明细列表
              const msChargetList = tableData.value.filter(
                (item) =>
                  item.msChargeObjectTypeCode === row.msChargeObjectTypeCode,
              );
              const commoditiesSelected = msChargetList.map(
                (item) => item.commodityNo,
              );
              return commoditiesSelected.includes(commodityNo);
            };

            if (row.editable) {
              return (
                <el-select
                  v-model={row.commodityNo}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'system.msChargeSetting.commodityNameDisplay',
                      '费用名称',
                    ),
                  })}
                  onChange={(val: string) => {
                    const commoditySelected = chargeItems.find(
                      (item) => item.commodityNo === val,
                    );
                    if (commoditySelected) {
                      const {
                        commodityNameDisplay,
                        price,
                        unitName,
                        commodityId,
                        hospitalCommodityId,
                      } = commoditySelected;
                      row.commodityId = commodityId;
                      row.hospitalCommodityId = hospitalCommodityId;
                      row.commodityNameDisplay = commodityNameDisplay;
                      row.price = price as unknown as string;
                      row.unitName = unitName;
                    }
                  }}
                >
                  {chargeItems.map((ci) => (
                    <el-option
                      label={ci.commodityNameDisplay}
                      key={ci.commodityId}
                      value={ci.commodityNo}
                      disabled={isSelected(ci.commodityNo!)}
                    ></el-option>
                  ))}
                </el-select>
              );
            } else {
              return <>{row.commodityNameDisplay}</>;
            }
          },
        },
        // 单价
        {
          label: t('system.msChargeSetting.price', '单价'),
          prop: 'price',
          minWidth: 80,
        },
        // 单位
        {
          label: t('system.msChargeSetting.unitName', '单位'),
          prop: 'unitName',
          minWidth: 80,
        },
        // 数量
        {
          label: t('system.msChargeSetting.num', '数量'),
          prop: 'num',
          minWidth: 80,
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('system.msChargeSetting.num', '数量'),
              }),
            },
          ],
          render: (row: MsChargeSetting.MsChargeTableItem) => {
            if (row.editable) {
              return (
                <el-input-Number
                  class="mx-auto"
                  controls-position="right"
                  min={NUM_MIN}
                  max={NUM_MAX}
                  v-model={row.num}
                ></el-input-Number>
              );
            } else {
              return <>{row.num}</>;
            }
          },
        },
        // 操作：插入、编辑、移除
        {
          label: t('global:operation'),
          minWidth: 80,
          render: (row: MsChargeSetting.MsChargeTableItem, index: number) => {
            return (
              <>
                <el-button
                  link
                  type="primary"
                  disabled={insertDisable(row, index)}
                  onClick={() => operations.insert(row, index)}
                >
                  {' '}
                  {t('global:insert')}{' '}
                </el-button>
                <el-button
                  link
                  type="primary"
                  onClick={() => operations.edit(row)}
                >
                  {' '}
                  {t('global:edit')}{' '}
                </el-button>
                <el-button
                  link
                  type="danger"
                  disabled={removeDisable(row)}
                  onClick={() => operations.remove(index)}
                >
                  {' '}
                  {t('global:remove')}{' '}
                </el-button>
              </>
            );
          },
        },
      ];

      return containRangeColumn.value
        ? [...baseColumns, ...rangeColumns, ...costDetailsColumn]
        : [...baseColumns, ...costDetailsColumn];
    },
  });
}
