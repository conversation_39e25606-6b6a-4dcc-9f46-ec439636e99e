import { useFormConfig } from 'sun-biz';
import { FLAG } from '@sun-toolkit/enums';
import { MS_CHARGE_TYPE_CODE_NAME } from '@/utils/constant.ts';

export function useChargeSettingForm(
  itemDisableStatus = {
    csTypeCode: false,
    startAt: false,
    state: true /* 默认禁止 */,
  },
) {
  return useFormConfig({
    dataSetCodes: [MS_CHARGE_TYPE_CODE_NAME],
    getData: (t, dataSet) => {
      return [
        {
          name: 'csTypeCode',
          component: 'select',
          triggerModelChange: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('system.msChargeSetting.billingMethod', '计费方式'),
          }),
          className: 'w-40 mb-0',
          extraProps: {
            disabled: itemDisableStatus.csTypeCode,
            clearable: false,
            options: dataSet?.value
              ? dataSet.value[MS_CHARGE_TYPE_CODE_NAME]
              : [],
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
        {
          name: 'startAt',
          label: t('system.msChargeSetting.effectiveDate', '生效日期'),
          component: 'datePicker',
          className: 'w-100 mb-0',
          extraProps: {
            disabled: itemDisableStatus.startAt,
            type: 'datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            'value-format': 'YYYY-MM-DD HH:mm:ss',
          },
        },
        {
          name: 'state',
          label: t('system.msChargeSetting.effectiveDate', '状态'),
          component: 'switch',
          className: 'w-100 mb-0',
          extraProps: {
            disabled: itemDisableStatus.state,
            'inline-prompt': true,
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
          },
        },
      ];
    },
  });
}
