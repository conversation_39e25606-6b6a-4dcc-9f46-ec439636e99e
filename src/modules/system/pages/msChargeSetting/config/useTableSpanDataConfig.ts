import { TableColumnCtx } from 'element-sun';
import { COLUMNS_ADD_RANGE_OWNED, SPAN_MERGES_PROPERTY } from '../constant';

interface SpanMethodProps {
  row: MsChargeSetting.MsChargeTableItem;
  column: TableColumnCtx<MsChargeSetting.MsChargeTableItem>;
  rowIndex: number;
  columnIndex: number;
}
/**
 *
 * @param tableData 表格数据
 * @param msChargetObjectListMap 计费对象Map<msChargeTypeCode, Set<msChargeObjectTypeCode>>
 * @param containRangeColumn 包含区间设置
 * @returns
 */
export function computeChargeObjectMap(
  tableData: MsChargeSetting.MsChargeTableItem[],
  msChargetObjectListMap: Map<string, Set<string>>,
  containRangeColumn: boolean = false,
) {
  /**
   * Map<计费对象code, { 计费列表开始索引， 计费列表结束索引， 计费列表, 区间1: { start, end }, 区间2： { start, end } }>
   */
  const map = new Map<string, MsChargeSetting.IChargeObjectMapValue>();
  // 计费方式
  if (!tableData?.length) {
    return map;
  }
  const chargeType = tableData[0].msChargeTypeCode!;
  // 计费对象 code 集合 set(code1, code2, ...)
  const chargeCodeSet = msChargetObjectListMap.get(chargeType);
  if (chargeCodeSet) {
    const codes = chargeCodeSet.values();
    codes.forEach((msChargeObjectTypeCode: string, index: number) => {
      // 获取计费对象对应的 计费列表
      const list = tableData.filter(
        (co) => co.msChargeObjectTypeCode === msChargeObjectTypeCode,
      );
      const start = index * list.length;
      // 若包含【区间】，则计费列表最后一行是【增加区间】行
      const end = start + list.length - 1;

      // 获取计费列表中的 区间列表集合 Set<rangeUpperNum>
      // 获取计费列表中的 区间列表对象 { rangeUpperNum, { start, end } }
      const rangeSetObject: {
        [key: number]: { start: number; end: number };
      } = {};
      if (containRangeColumn) {
        // 区间列表集合 Set<rangeUpperNum>
        const rangeSet = new Set<number>();
        list.forEach((li) => {
          if (!li.containAddButton) {
            rangeSet.add(li.rangeUpperNum!);
          }
        });

        if (rangeSet.size) {
          // rangeSet
          const nums = Array.from(rangeSet);
          //   .values()
          nums.forEach((rangeUpperNum: number, rangeInd: number) => {
            const rangeList = list.filter(
              (li) =>
                li.rangeUpperNum === rangeUpperNum && !li.containAddButton,
            );
            let rangeStart;
            if (rangeInd > 0) {
              // 非第一个区间，则开始索引为上一个区间 end + 1
              rangeStart = rangeSetObject[nums[rangeInd - 1]].end + 1;
            } else {
              // 第一个区间，则开始索引为：计费对象对应的计费列表开始行索引
              rangeStart = start;
            }
            const rangeEnd = rangeStart + rangeList.length - 1;
            rangeSetObject[rangeUpperNum] = {
              start: rangeStart,
              end: rangeEnd,
            };
          });
        }
      }

      if (containRangeColumn) {
        map.set(msChargeObjectTypeCode, {
          start,
          end,
          list,
          ...rangeSetObject,
        });
      } else {
        map.set(msChargeObjectTypeCode, { start, end, list });
      }
    });
  }
  return map;
}

export function spanMethodFn(
  chargeObjectMap: Map<string, MsChargeSetting.IChargeObjectMapValue>,
  tableData: MsChargeSetting.MsChargeTableItem[],
  containRangeColumn: boolean,
) {
  // 列合并
  function spanMethod({
    row,
    column,
    columnIndex,
    rowIndex,
  }: SpanMethodProps): number[] | { rowspan: number; colspan: number } | void {
    const msChargeObjectTypeCode = row.msChargeObjectTypeCode;
    const curChargeObject = chargeObjectMap.get(msChargeObjectTypeCode)!;
    if (!curChargeObject) return;
    const { start, end } = curChargeObject;
    const count = end - start + 1;
    const curRow = tableData[rowIndex]; // 当前行
    if (
      // 计费对象
      column?.property === SPAN_MERGES_PROPERTY.MsChargeObjectTypeDesc ||
      // 列索引为 0 的序号
      columnIndex === 0
    ) {
      // 如果计费对象相同
      if (rowIndex % count === 0) {
        return {
          rowspan: count,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
    // 包含区间设置
    if (containRangeColumn) {
      // 非添加按钮
      if (!curRow.containAddButton) {
        const curRange = curChargeObject[curRow.rangeUpperNum!];
        const { start: rangeStart, end: rangeEnd } = curRange;
        const curRangeLen = rangeEnd - rangeStart + 1;
        const inRangList = rowIndex >= rangeStart && rowIndex <= rangeEnd;
        // 在当前【同一区间】范围内
        if (inRangList) {
          const isRangeProperty =
            column.property === SPAN_MERGES_PROPERTY.RangeLowerNum ||
            column.property === SPAN_MERGES_PROPERTY.RangeUpperNum;

          // 是【区间下限】或 【区间上限】列
          if (isRangeProperty) {
            if ((rowIndex - rangeStart) % curRangeLen === 0) {
              return {
                rowspan: curRangeLen,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
      } else {
        // 是【添加区间】按钮
        if (column.property === SPAN_MERGES_PROPERTY.RangeLowerNum) {
          return [column.no, COLUMNS_ADD_RANGE_OWNED];
        } else {
          return [0, 0];
        }
      }
    }
  }

  return spanMethod;
}
