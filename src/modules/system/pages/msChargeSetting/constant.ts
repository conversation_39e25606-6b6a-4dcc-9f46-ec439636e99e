export const NUM_MIN = 1; // 数量最小值
export const NUM_MAX = 999999; // 数量最大值
export const RANGE_NUM_MIN = 1; // 区间最小值
export const RANGE_NUM_MAX = 999; // 区间最大值
export const COLUMNS_ADD_RANGE_OWNED = 8; // 添加区间按钮所占列数
export const SERVICEINFO_HOSPITALID_MSID = 'SERVICEINFO_HOSPITALID_MSID'; // {医院标识,服务标识集合,医疗服务编码,医疗服务名称}
/* 列合并属性 */
export enum SPAN_MERGES_PROPERTY {
  /* 计费对象 */
  MsChargeObjectTypeDesc = 'msChargeObjectTypeDesc',
  /* 区间下限 */
  RangeLowerNum = 'rangeLowerNum',
  /* 区间上限 */
  RangeUpperNum = 'rangeUpperNum',
}
