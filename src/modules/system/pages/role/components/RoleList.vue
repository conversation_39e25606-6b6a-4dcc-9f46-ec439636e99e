<script setup lang="tsx">
  import { computed, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import {
    querySystemRoleByExample,
    updateSystemRoleById,
  } from '../../../api/role';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { debounce } from '@sun-toolkit/shared';
  import AddRole from './AddRole.vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { useTranslation } from 'i18next-vue';
  import { Title, ProTable, HospitalSelect } from 'sun-biz';
  const { t } = useTranslation();

  function getColumns() {
    return [
      {
        label: t('global:sequenceNumber'),
        prop: 'codeSystemNo',
        render: (row: object, index: number) => <>{index + 1}</>,
      },
      {
        label: t('global:name'),
        prop: 'roleName',
      },
      {
        label: t('global:enabledFlag'),
        prop: 'action',
        render: (row: Role.SystemRoleInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              activeText={t('global:enabled')}
              inactiveText={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'action',
        render: (row: Role.SystemRoleInfo) => {
          return (
            <el-button
              link
              type="primary"
              onClick={() => {
                props.setSelectRoleRow({
                  ...row,
                  edit: true,
                });
              }}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ];
  }

  type Props = {
    setSelectRoleRow: (info: Role.SystemRoleInfo, clear?: boolean) => void;
    selectRoleRow: Role.SystemRoleInfo | null;
  };

  const props = defineProps<Props>();
  const hospitalId = ref<string>('');
  const tableData = ref<Role.SystemRoleInfo[]>([]);
  const loading = ref<boolean>(false);
  const columns = computed(() => getColumns());
  function handleEnableSwitch(row: Role.SystemRoleInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.roleName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await updateSystemRoleById({
            ...row,
            enabledFlag:
              row.enabledFlag === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
          });
          if (result?.success) {
            currentChange({
              ...row,
              enabledFlag:
                row.enabledFlag === ENABLED_FLAG.YES
                  ? ENABLED_FLAG.NO
                  : ENABLED_FLAG.YES,
            });
            resolve();
            handleEnter();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  function currentChange(row: object) {
    props.setSelectRoleRow(row as Role.SystemRoleInfo);
  }

  const tableRef = ref();
  const keyWord = ref<string>('');

  async function fetchData() {
    loading.value = true;
    let [, result] = await querySystemRoleByExample({
      keyWord: keyWord.value,
      hospitalId: hospitalId.value,
    });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
      if ((result.data || [])?.length) {
        if (props.selectRoleRow?.roleId) {
          let found = result.data.find(
            (item) => item.roleId === props.selectRoleRow?.roleId,
          );
          if (found)
            tableRef.value.setCurrentRow({ ...(found || {}), edit: false });
        } else {
          tableRef.value.setCurrentRow({ ...result.data[0], edit: false });
        }
      } else {
        props.setSelectRoleRow(
          {
            edit: false,
          } as Role.SystemRoleInfo,
          true,
        );
      }
    }
  }

  function handleEnter() {
    fetchData();
  }

  function changeHospital(value: string) {
    hospitalId.value = value;
    fetchData();
  }

  let inputChange = debounce(handleEnter, 500);

  defineExpose({
    fetchData,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('role.list', '角色列表')" class="mb-5">
      <span>
        <HospitalSelect
          v-model="hospitalId"
          @change="changeHospital"
          :clearable="false"
          class="mr-5 w-52"
        />
        <el-input
          v-model="keyWord"
          @input="inputChange"
          @keydown.enter="handleEnter"
          class="mr-5 w-48"
          :placeholder="$t('global:placeholder.keyword')"
          :suffix-icon="Search"
        />
        <AddRole
          :hospital-id="hospitalId"
          @success="
            () => {
              handleEnter();
            }
          "
        />
      </span>
    </Title>
    <pro-table
      :loading="loading"
      row-key="roleId"
      row-class-name="cursor-pointer"
      :highlight-current-row="true"
      @current-change="currentChange"
      ref="tableRef"
      :data="tableData"
      :columns="columns"
    />
  </div>
</template>
