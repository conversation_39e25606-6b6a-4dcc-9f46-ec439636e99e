<script setup lang="tsx">
  import { ref, computed, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, Title } from 'sun-biz';
  import { updateSystemRoleById } from '../../../api/role';
  import { ENABLED_FLAG } from '@/utils/constant';

  const { t } = useTranslation();

  function getDefaultValue(value: string = '', edit: boolean = false) {
    return edit !== true ? value || '--' : value;
  }

  function getBaseInfoData(initData: Partial<Role.SystemRoleInfo>) {
    return [
      {
        name: 'roleId',
        label: t('RoleInfo.roleId.label', '角色标识'),
        defaultValue: initData?.roleId,
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        label: t('global:name'),
        name: 'roleName',
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.roleName, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('AddStructureGroup.menuName.message', '请输入名称'),
        rules: [
          {
            required: false,
            message: t('AddStructureGroup.menuName.message', '请输入名称'),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('AddRole.role2ndName', '角色辅助名称'),
        name: 'role2ndName',
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.role2ndName, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('AddRole.role2ndName.placeholder', '请输入角色辅助名称'),
      },
      {
        label: t('AddRole.roleExtName', '角色扩展名称'),
        name: 'roleExtName',
        supportCopyAndTips: true,
        defaultValue: getDefaultValue(initData?.roleExtName, initData?.edit),
        component: initData?.edit ? 'input' : 'text',
        placeholder: t('AddRole.roleExtName.placeholder', '请输入角色扩展名称'),
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          disabled: true,
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ];
  }
  type Props = {
    selectRoleRow: Role.SystemRoleInfo | null;
    setSelectRoleRow: (inof: Role.SystemRoleInfo) => void;
    refreshRoleList: () => void;
  };

  const props = withDefaults(defineProps<Props>(), {});
  const formModel = ref();
  const show = ref(false);

  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();

  function handleClick() {
    show.value = !show.value;
    if (show.value === false) {
      cancelClick();
    }
  }

  /**
   * 保存
   */

  function saveClick() {
    // edit.value = false;
    formRef?.value?.ref.validate(async (valid) => {
      let model = formRef?.value?.model;
      if (valid) {
        let [, result] = await updateSystemRoleById({
          ...model,
          enabledFlag: props.selectRoleRow?.enabledFlag,
        } as unknown as Role.ReqUpdateSystemRoleById);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:save.success'),
          });
          props.refreshRoleList();
        }
      }
    });
  }

  /**
   * 点击编辑的时候默认打开下拉框
   */
  watch(
    () => props.selectRoleRow?.edit,
    () => {
      if (props.selectRoleRow?.edit) {
        show.value = true;
      }
    },
  );

  watch(
    () => props.selectRoleRow,
    () => {
      formModel.value = props.selectRoleRow;
    },
    {
      immediate: true,
      deep: true,
    },
  );

  /**
   * 取消
   */
  function cancelClick() {
    props.setSelectRoleRow({ edit: false } as Role.SystemRoleInfo);
  }

  const baseInfoDescData = computed(() => {
    return getBaseInfoData(props.selectRoleRow || {});
  });
</script>
<template>
  <div style="flex-shrink: 0">
    <div :class="['mb-4', 'cursor-pointer', 'pt-2']" @click="handleClick">
      <Title :title="$t('role.info', '权限信息')">
        <span>
          <el-icon v-if="show"><CaretBottom /></el-icon>
          <el-icon v-else><CaretLeft /></el-icon>
        </span>
      </Title>
    </div>
    <el-row
      :gutter="20"
      :class="[
        show ? 'mb-2 h-36' : 'h-0',
        'w-full overflow-hidden pl-7 pr-7 transition-all duration-300 ease-in',
      ]"
    >
      <ProForm
        ref="formRef"
        style="width: 100%"
        :column="2"
        v-model="formModel"
        :with-colon="true"
        :data="baseInfoDescData"
      >
        <span class="mb-5 flex justify-end" v-if="show">
          <el-button v-if="selectRoleRow?.edit" @click="cancelClick">{{
            $t('global:cancel', '取消')
          }}</el-button>
          <el-button
            v-if="selectRoleRow?.edit"
            type="primary"
            @click="saveClick"
          >
            {{ $t('global:save', '保存') }}
          </el-button>
        </span></ProForm
      >
    </el-row>
    <hr class="mb-4" />
  </div>
</template>
