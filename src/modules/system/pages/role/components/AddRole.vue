<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { addSystemRole } from '../../../api/role';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { ProForm, ProDialog } from 'sun-biz';
  const { t } = useTranslation();

  type Props = {
    hospitalId?: string;
  };

  const props = withDefaults(defineProps<Props>(), {
    hospitalId: '',
  });
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();

  const emits = defineEmits<{
    success: [];
  }>();
  function getBaseInfoData(props: { hospitalId: string }) {
    return [
      {
        name: 'roleName',
        label: t('AddRole.roleName', '角色名称'),
        defaultValue: '',
        component: 'input',
        placeholder: t('AddRole.roleName.placeholder', '请输入角色名称'),
        rules: [
          {
            required: true,
            message: t('AddRole.roleName.placeholder', '请输入角色名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'role2ndName',
        label: t('AddRole.role2ndName', '角色辅助名称'),
        defaultValue: '',
        component: 'input',
        placeholder: t('AddRole.role2ndName.placeholder', '请输入角色辅助名称'),
        rules: [
          {
            required: false,
            message: t('AddRole.role2ndName.placeholder', '请输入角色辅助名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'roleExtName',
        label: t('AddRole.roleExtName', '角色扩展名称'),
        defaultValue: '',
        component: 'input',
        placeholder: t('AddRole.roleExtName.placeholder', '请输入角色扩展名称'),
        rules: [
          {
            required: false,
            message: t('AddRole.roleExtName.placeholder', '请输入角色扩展名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'hospitalId',
        label: t('AddRole.hospitalId', '所属医院'),
        defaultValue: props.hospitalId,
        component: 'hospitalSelect',
        placeholder: t('AddRole.hospitalId.placeholder', '请输入所属医院'),
        rules: [
          {
            required: true,
            message: t('AddRole.hospitalId.placeholder', '请输入所属医院标识'),
            trigger: 'change',
          },
        ],
        extraProps: {
          hospitalId: props.hospitalId,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t('AddRole.enabledFlag.placeholder', '请输入启用状态'),
        label: t('AddRole.enabledFlag', '启用状态'),
        defaultValue: 1,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
    ];
  }

  let baseInfoDescData = computed(() => {
    return getBaseInfoData(props);
  });

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = await addSystemRole({
            ...(model as unknown as Role.ReqSystemRole),
          });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t('global:create.success'),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :title="$t('add.role', '新增角色')"
    :button-text="$t('global:add')"
    :width="800"
    destroy-on-close
    :include-button="true"
    @success="emits('success')"
    ref="copyRef"
  >
    <ProForm ref="formRef" :column="1" :data="baseInfoDescData"
  /></ProDialog>
</template>
