<script setup lang="ts" name="PageElement">
  import { ref, computed, watch } from 'vue';
  import { FLAG } from '@/utils/constant';
  import { MenuObj } from './SystemMenu.vue';

  const {
    selectMenu,
    selectInfo = [],
    isDisabled,
  } = defineProps<{
    selectMenu?: Menu.SystemInfo & MenuObj;
    selectInfo: (Menu.SystemInfo & MenuObj)[];
    isDisabled: boolean;
  }>();

  const emits = defineEmits(['selectChange']);

  /** 选中的元素集合 */
  const elementsInfo = ref<(Menu.SystemInfo & MenuObj)[]>([]);
  /** 是否禁用 */
  const disabled = computed(() => isDisabled);

  /** 过滤出 defaultAllowUseFlag 不为 1 的元素 */
  const defaultAllowUseData = computed(() => {
    if (!selectMenu?.children) return [];
    return selectMenu.children.filter(
      (element) =>
        (element as unknown as { defaultAllowUseFlag: FLAG })
          .defaultAllowUseFlag !== FLAG.YES,
    ) as (Menu.SystemInfo & MenuObj)[];
  });

  /** 全选判断 */
  const isAllSelect = computed(() => {
    if (!defaultAllowUseData.value || defaultAllowUseData.value.length === 0)
      return false;
    return defaultAllowUseData.value.every((element) =>
      elementsInfo.value.includes(element),
    );
  });

  /** 更新 elementsInfo */
  const updateElementsInfo = async () => {
    elementsInfo.value = defaultAllowUseData.value.filter((element) =>
      selectInfo.some((info) => info.identification === element.identification),
    );
  };

  // 判断 item 是否在 selectInfo 中
  const isItemInSelectInfo = (item: Menu.SystemInfo & MenuObj) => {
    return selectInfo?.some(
      (info) => info.identification === item.identification,
    );
  };

  /** 全选/取消全选 */
  const changeAllSelect = (isSelected: boolean) => {
    elementsInfo.value = isSelected ? defaultAllowUseData.value : [];
    emits('selectChange', elementsInfo.value ?? []);
  };

  watch(
    () => selectInfo,
    async () => {
      await updateElementsInfo();
    },
  );

  watch(
    () => selectMenu,
    async () => {
      await updateElementsInfo();
    },
    {
      immediate: true,
    },
  );

  defineExpose({
    update: updateElementsInfo,
  });
</script>
<template>
  <div>
    <div class="flex items-center gap-3">
      {{ selectMenu?.label ?? '--'
      }}{{ selectMenu?.label ? $t('page.element', '的页面元素') : '' }}
      <el-checkbox
        label="全选"
        @change="changeAllSelect"
        v-model="isAllSelect"
        :checked="isAllSelect"
        :value="isAllSelect"
        :disabled="defaultAllowUseData.length === 0 || disabled"
      />
    </div>

    <div>
      <el-checkbox-group
        v-model="elementsInfo"
        :disabled="disabled"
        @change="
          () => {
            emits('selectChange', elementsInfo ?? []);
          }
        "
      >
        <el-checkbox
          :label="item.label"
          :value="item"
          v-for="item in defaultAllowUseData"
          :key="item?.identification"
          :checked="isItemInSelectInfo(item)"
        />
      </el-checkbox-group>
    </div>
  </div>
</template>
