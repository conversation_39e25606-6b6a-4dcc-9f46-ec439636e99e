<script setup lang="ts" name="orgManage">
  import {
    ref,
    onBeforeMount,
    nextTick,
    watchEffect,
    computed,
    watch,
  } from 'vue';
  import { MENU_AND_GROUP_FLAG, LIMIT_OBJECT_TYPE_CODE } from '../constant';
  import { queryMenuStructListByExample } from '@/modules/system/api/menu';
  import { ENABLED_FLAG, FLAG } from '@/utils/constant';
  import { debounce } from '@sun-toolkit/shared';
  import { saveSystemRolePermission } from '../../../api/role';
  import { ElMessage, ElTree } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { type AnyObject } from 'sun-biz';
  import PageElement from './PageElement.vue';

  const CHECKED = 'Checked';
  const HALFCHECKED = 'halfChecked';
  const NOCHECKED = 'noChecked';
  export type MenuObj = {
    identification: string;
    parentMenuId?: string;
    children: MenuObj[];
  };
  const { t } = useTranslation();

  type Props = {
    selectRoleRow: Role.SystemRoleInfo | null;
    refreshRoleList: () => void;
    changeModifiedStatus: (value: boolean) => void;
  };
  const props = defineProps<Props>();
  const refreshIndex = ref<number>(0);
  let state = ref<Menu.MixSystemMenuElement>({});
  const loading = ref(false);
  const buttonLoading = ref(false);
  const treeRef = ref();
  const pageElementRef = ref();
  const scrollbarRef = ref();
  let systemList = ref<(Menu.SystemInfo & MenuObj)[]>([]);
  const filterText = ref(''); // 绑定输入框
  onBeforeMount(() => {
    fetchMenuStructListByExample();
  });

  /** 点击的菜单 */
  const selectMenu = ref<(Menu.SystemInfo & MenuObj) | undefined>();
  /** 树形结构已选中的节点集合 */
  const checkedNodes = ref<Menu.MenuInfo[]>([]);
  /** 树形结构半选中的节点集合 */
  const halfCheckedNodes = ref<Menu.MenuInfo[]>([]);

  /** 当前选中的菜单元素的集合 */
  const selectInfo = ref<(Menu.SystemInfo & MenuObj)[]>([]);
  /** 切换角色的时候初始化的勾选的项 */
  const initialCheckedKeysData = ref<
    {
      type?: string;
      limitObjectId: string;
      limitObjectTypeCode: string;
    }[]
  >([]);

  /** 去除菜单元素之后的树结构 */
  const treeData = computed(() => filterElements(systemList.value));

  /** 判断子组件是否禁用页面元素 */
  const disabledPageElement = computed(() => {
    const checkInfo = [
      ...(checkedNodes.value ?? []),
      ...(halfCheckedNodes.value ?? []),
    ];
    const flag = checkInfo.find(
      (item) => item.identification === selectMenu.value?.identification,
    );
    if (flag) return false;
    return true;
  });

  /** 选中菜单下边是否有元素 */
  const isHasElement = computed(() => {
    const selectMenuChildren = selectMenu.value?.children.filter(
      (child) =>
        (child as unknown as { defaultAllowUseFlag: FLAG })
          .defaultAllowUseFlag !== FLAG.YES,
    );
    if (selectMenuChildren && selectMenuChildren.length > 0) return true;
    return false;
  });

  /** 去除树为元素的项 */
  function filterElements(
    data: (Menu.SystemInfo & MenuObj)[],
  ): (Menu.SystemInfo & MenuObj)[] {
    return data
      .filter(
        (item: Menu.SystemInfo & MenuObj) =>
          (
            item as unknown as {
              menuFlag: MENU_AND_GROUP_FLAG;
            }
          ).menuFlag !== MENU_AND_GROUP_FLAG.ELEMENT,
      ) // 过滤掉 ELEMENT 节点
      .map((item: Menu.SystemInfo & MenuObj) => ({
        ...item,
        children: item.children
          ? filterElements(item.children as (Menu.SystemInfo & MenuObj)[])
          : [], // 递归处理子节点
      }));
  }

  /**
   * 设置选中
   * @param data 返回的选中的节点数组
   */
  const setCheckMode = async (data: Menu.MenuInfo[]) => {
    if (!selectMenu.value) return;

    /** 过滤掉 defaultAllowUseFlag不为1的菜单元素集合 */
    const selectMenuChildren = selectMenu.value?.children.filter(
      (child) =>
        (child as unknown as { defaultAllowUseFlag: FLAG })
          .defaultAllowUseFlag !== FLAG.YES,
    );

    /** 当节点下边的菜单元素为空数组时 */
    if (data.length === 0 && selectMenuChildren.length === 0) return;
    props.changeModifiedStatus(true);
    /** 获取所传data的identification 集合 */
    const dataIds = data.map((item) => item.identification);

    /** 过滤出selectMenu.children中有、但data中没有的项 */
    const missingItems = selectMenuChildren.filter(
      (child) => !dataIds.includes(child.identification),
    );

    /** 从 selectInfo 中移除 missingItems 中的元素 */
    selectInfo.value = selectInfo.value.filter(
      (item) =>
        !missingItems.some(
          (missingItem) => missingItem.identification === item.identification,
        ),
    );

    /** 如果 selectInfo 中不存在 data 中的元素，则将其添加到 selectInfo 中 */
    data.forEach((item) => {
      if (
        !selectInfo.value.some(
          (info) => info.identification === item.identification,
        )
      ) {
        selectInfo.value.push(item as unknown as Menu.SystemInfo & MenuObj);
      }
    });

    // /** 判断missingItems的每一项是否都和selectMenuChildren相同 */
    // const areArraysEqual = () => {
    //   if (missingItems.length !== selectMenuChildren.length) return false;

    //   return missingItems.every(
    //     (item, index) =>
    //       item.identification === selectMenuChildren[index].identification,
    //   );
    // };
    // if (missingItems && missingItems.length === 0) {
    //   /** 设置全选 */
    //   // treeRef.value?.setChecked(selectMenu.value?.identification, true, false);
    //   selectInfo.value = [
    //     ...selectInfo.value,
    //     ...(data as unknown as (Menu.SystemInfo & MenuObj)[]),
    //   ];
    // } else if (missingItems && areArraysEqual()) {
    //   /** 设置全不选 */
    //   /** 从 selectInfo.value 中移除 selectMenuChildren 中的项 */
    //   // treeRef.value?.setChecked(selectMenu.value?.identification, false, false);
    //   selectInfo.value = selectInfo.value.filter(
    //     (item) =>
    //       !selectMenuChildren.some(
    //         (child) => child.identification === item.identification,
    //       ),
    //   );
    // } else {
    //   /** 设置半选 */
    //   // treeRef.value?.setChecked(selectMenu.value?.identification, true, false);

    //   // setHalfSelected(selectMenu.value?.identification);

    //   /** 从selectInfo中移除没有选中的 */
    //   selectInfo.value = selectInfo.value.filter(
    //     (item) =>
    //       !missingItems.some(
    //         (child) => child.identification === item.identification,
    //       ),
    //   );
    //   selectInfo.value = [
    //     ...selectInfo.value,
    //     ...(data as unknown as (Menu.SystemInfo & MenuObj)[]),
    //   ];
    // }
  };

  /**
   * 递归查找匹配的节点
   * @param identification 要查找的节点标识
   * @param data 当前遍历的节点数组
   * @returns 匹配到的节点，未找到则返回 undefined
   */
  function findNodeByIdentification(
    identification: string,
    data: (Menu.SystemInfo & MenuObj)[],
  ): (Menu.SystemInfo & MenuObj) | undefined {
    for (const item of data) {
      if (item.identification === identification) {
        return item; // 找到匹配的节点
      }
      if (item.children && item.children.length > 0) {
        const found = findNodeByIdentification(
          identification,
          item.children as (Menu.SystemInfo & MenuObj)[],
        ); // 递归查找子节点
        if (found) {
          return found; // 如果子节点中找到匹配的节点，返回结果
        }
      }
    }
    return undefined; // 未找到匹配的节点
  }

  /** 切换角色的时候滚动回顶部 */
  const scrollTreeTop = async () => {
    loading.value = true;
    const timer = setTimeout(async () => {
      await scrollbarRef.value?.setScrollTop(0);
      loading.value = false;
      clearTimeout(timer);
    }, 500);
  };

  /** 递归遍历所传节点，获取所有菜单元素集合 */
  // const collectElements = (node: Menu.SystemInfo & MenuObj & Menu.MenuInfo) => {
  //   let elements: Menu.MenuInfo[] = [];

  //   // 如果当前节点是 ELEMENT，添加到集合中
  //   if (
  //     (node.menuFlag as MENU_AND_GROUP_FLAG) === MENU_AND_GROUP_FLAG.ELEMENT
  //   ) {
  //     elements.push(node);
  //   }

  //   // 递归遍历子节点
  //   if (node.children && node.children.length > 0) {
  //     node.children.forEach((child) => {
  //       elements = elements.concat(
  //         collectElements(child as Menu.SystemInfo & MenuObj & Menu.MenuInfo),
  //       );
  //     });
  //   }

  //   return elements;
  // };

  /** 监听选中、半选的节点数据 */
  watch(
    () => [
      treeRef.value?.getCheckedNodes(),
      treeRef.value?.getHalfCheckedNodes(),
    ],
    ([checkedNode, halfCheckedNode]) => {
      checkedNodes.value = checkedNode;
      halfCheckedNodes.value = halfCheckedNode;
    },
    { deep: true, immediate: true },
  );

  watch(
    () => initialCheckedKeysData.value,
    () => {
      selectInfo.value = [];

      /**  从initialCheckedKeys中过滤出是菜单元素的 */
      const filteredElements = initialCheckedKeysData.value?.filter(
        (item) => item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.ELEMENT,
      );

      (filteredElements ?? []).forEach((item) => {
        const checkItem = findNodeByIdentification(
          item.limitObjectId,
          systemList.value,
        );

        if (checkItem) {
          selectInfo.value.push(checkItem);
        }
      });

      // /** 给selectMenu赋初值 */
      // const menuNode = initialCheckedKeysData.value.find(
      //   (node) => node.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.MENU,
      // );

      // const checkNode = findNodeByIdentification(
      //   menuNode?.limitObjectId as string,
      //   systemList.value,
      // );

      // if (checkNode) {
      //   selectMenu.value = checkNode;
      // }
    },
  );

  /** 查询菜单结构 处理树数据 */
  async function fetchMenuStructListByExample() {
    loading.value = true;
    let [, result] = await queryMenuStructListByExample({
      enabledFlag: ENABLED_FLAG.YES,
      accessFlag: FLAG.YES,
    });
    loading.value = false;
    if (result?.success) {
      systemList.value = result.data.map((item) => ({
        ...item,
        label: item.sysName,
        identification: item.sysId,
        limitObjectTypeCode: LIMIT_OBJECT_TYPE_CODE.SYSTEM,
        children: (item.sysXMenuList || []).map((cur) => ({
          ...cur,
          label: cur.menuName,
          parentSysId: item.sysId,
          sysName: item.sysName,
          sysSpellNo: item.spellNo,
          sysWbNo: item.wbNo,
          identification: cur.sysXMenuId,
          limitObjectTypeCode: LIMIT_OBJECT_TYPE_CODE.MENU,
          disabled:
            !cur.subSysXMenuList.length &&
            cur.menuFlag === MENU_AND_GROUP_FLAG.GROUP,
          children: (cur.pageElementList || []).length
            ? (cur.pageElementList || []).map((element) => ({
                ...element,
                label: element.pageElementName,
                menuFlag: MENU_AND_GROUP_FLAG.ELEMENT,
                sysSpellNo: item.spellNo,
                sysWbNo: item.wbNo,
                menuSpellNo: cur.spellNo,
                menuSWbNo: cur.wbNo,
                sysName: item.sysName,
                menuName: cur.menuName,
                parentMenuId: cur.sysXMenuId,
                limitObjectTypeCode: LIMIT_OBJECT_TYPE_CODE.ELEMENT,
                identification: `${cur.sysXMenuId}_${element.pageElementId}`,
                children: [],
              }))
            : (cur.subSysXMenuList || []).map((obj) => ({
                ...obj,
                label: obj.menuName,
                parentSysId: item.sysId,
                groupName: cur.menuName,
                sysName: item.sysName,
                sysSpellNo: item.spellNo,
                sysWbNo: item.wbNo,
                groupSpellNo: cur.spellNo,
                groupWbNo: cur.wbNo,
                parentSysXMenuId: cur.sysXMenuId,
                identification: obj.sysXMenuId,
                limitObjectTypeCode: LIMIT_OBJECT_TYPE_CODE.MENU,
                children: (obj.pageElementList || []).map((element) => ({
                  ...element,
                  label: element.pageElementName,
                  menuFlag: MENU_AND_GROUP_FLAG.ELEMENT,
                  parentMenuId: obj.sysXMenuId,
                  sysSpellNo: item.spellNo,
                  sysWbNo: item.wbNo,
                  groupSpellNo: cur.spellNo,
                  groupWbNo: cur.wbNo,
                  menuSpellNo: obj.spellNo,
                  menuSWbNo: obj.wbNo,
                  groupName: cur.menuName,
                  sysName: item.sysName,
                  menuName: obj.menuName,
                  identification: `${obj.sysXMenuId}_${element.pageElementId}`,
                  limitObjectTypeCode: LIMIT_OBJECT_TYPE_CODE.ELEMENT,
                  children: [],
                })),
              })),
        })),
      }));
    }
  }

  // 根据用户输入过滤树节点
  const filterTree = () => {
    treeRef?.value.filter(filterText.value);
  };

  function isIncludes(curString: string = '', value: string = '') {
    return curString.toLocaleUpperCase().includes(value.toLocaleUpperCase());
  }

  // 定义节点的过滤规则
  const filterNode = (value: string, data: Menu.MixSystemMenuElement) => {
    if (!value) return true; // 没有输入时，显示所有节点

    return (
      (data?.label || '').includes(value) ||
      (data?.groupName || '').includes(value) ||
      (data?.sysName || '').includes(value) ||
      (data?.menuName || '').includes(value) ||
      isIncludes(data?.spellNo, value) ||
      isIncludes(data?.wbNo, value) ||
      isIncludes(data?.sysSpellNo, value) ||
      isIncludes(data?.sysWbNo, value) ||
      isIncludes(data?.groupSpellNo, value) ||
      isIncludes(data?.groupWbNo, value) ||
      isIncludes(data?.menuSpellNo, value) ||
      isIncludes(data?.menuWbNo, value)
    ); // 过滤条件：节点标签包含输入值
  };

  let inputChange = debounce(filterTree, 700);

  async function saveClick() {
    let checkedNodes = treeRef.value!.getCheckedNodes();
    const halfCheckedNodes = treeRef.value!.getHalfCheckedNodes();
    buttonLoading.value = true;
    if (!props.selectRoleRow) return;
    let [, result] = await saveSystemRolePermission({
      roleId: props.selectRoleRow.roleId,
      rolePermissionList: [
        ...checkedNodes,
        ...halfCheckedNodes,
        ...selectInfo.value,
      ]
        .filter((item) => {
          if (
            item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.MENU &&
            item.subSysXMenuList.length
          ) {
            return false;
          }
          return true;
        })
        .map(
          (item: {
            enabledFlag: number | null;
            identification: string;
            limitObjectTypeCode: string;
          }) => ({
            limitObjectTypeCode: item.limitObjectTypeCode,
            limitObjectId:
              item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.ELEMENT
                ? item.identification.split('_')[1]
                : item.identification,
          }),
        ),
    });
    buttonLoading.value = false;
    if (result?.success) {
      props.changeModifiedStatus(false);
      ElMessage({
        type: 'success',
        message: t('global:save.success'),
      });
      props.refreshRoleList();
      // await fetchMenuStructListByExample();
    }
  }

  function disabledButton() {
    if (!treeRef.value) {
      return true;
    }
    return !treeRef.value!.getCheckedKeys(false).length;
  }

  /**
   * 找出所有的父级menuId
   */
  function filterAllParentMenuId(limitObjectId = '', data: MenuObj[] = []) {
    let result: string[] = [];

    function recursion(data: MenuObj[] = []) {
      data.forEach((item) => {
        if (item.identification === `${item.parentMenuId}_${limitObjectId}`) {
          let find = (props.selectRoleRow?.rolePermissionList || []).find(
            (cur) => cur.limitObjectId === item.parentMenuId,
          );
          if (find) {
            result.push(`${item.parentMenuId}_${limitObjectId}`);
          }
        }
        if (item.children) {
          recursion(item.children);
        }
      });
    }
    recursion(data);
    return result;
  }

  /**
   * 判断是菜单否有选中的元素
   */
  function getSelectStatus(limitObjectId: string, data: MenuObj[] = []) {
    let elements: MenuObj[] = []; //全部可以选择的元素

    function findElement(data: MenuObj[] = []) {
      data.forEach((item) => {
        if (item.identification === limitObjectId) {
          return (elements = item.children);
        }
        if (item.children) {
          findElement(item.children);
        }
      });
    }
    findElement(data);
    let selected = elements.filter((item) => {
      return props.selectRoleRow?.rolePermissionList.some(
        (cur) => cur.limitObjectId === item.identification,
      );
    });

    //elements 没数据  设置全选
    //elements 有长度 selected没长度 设置半选
    //elements有长度 selected有数据  过滤掉 会在元素选中自动选中菜单
    if (!elements.length) {
      //有选中的元素
      return CHECKED;
    } else if (elements.length && !selected.length) {
      return HALFCHECKED;
    } else {
      return NOCHECKED;
    }
  }

  watchEffect(() => {
    const initialCheckedKeys: {
      type?: string;
      limitObjectId: string;
      limitObjectTypeCode: string;
    }[] = [];
    (props.selectRoleRow?.rolePermissionList || []).map((item) => {
      if (item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.MENU) {
        initialCheckedKeys.push({
          ...item,
          type: getSelectStatus(item.limitObjectId, systemList.value),
        });
      }
      if (item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.ELEMENT) {
        filterAllParentMenuId(item.limitObjectId, systemList.value).forEach(
          (cur) => {
            initialCheckedKeys.push({
              ...item,
              limitObjectId: cur,
            });
          },
        );
      }
    });

    /** 切换角色的时候初始化选中的页面元素和所选菜单 */
    initialCheckedKeysData.value = initialCheckedKeys;
    selectMenu.value = undefined;
    scrollTreeTop();

    nextTick(() => {
      treeRef.value.setCheckedKeys([]);
      initialCheckedKeys.forEach((item) => {
        if (item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.ELEMENT) {
          treeRef.value?.setChecked(item.limitObjectId, true, false);
        }
        if (item.limitObjectTypeCode === LIMIT_OBJECT_TYPE_CODE.MENU) {
          // if (item?.type === HALFCHECKED) {
          //   treeRef.value?.setChecked(item.limitObjectId, true, false);
          //   setHalfSelected(item.limitObjectId);
          // }
          if (item?.type === CHECKED || item?.type === HALFCHECKED) {
            treeRef.value?.setChecked(item.limitObjectId, true, false);
          }
        }
      });
    });
  });

  // 设置节点为半选状态
  function setHalfSelected(id: string) {
    const tree = treeRef.value;
    if (tree) {
      const store = tree.store;

      // 手动设置指定节点为半选状态
      const node = store.nodesMap[id]; // 例如设置 ID 为 2 的节点为半选
      if (node) {
        node.indeterminate = true; // 半选状态
        node.checked = false; // 确保未全选
      }
    }
  }

  // 处理选中状态变更
  async function handleCheckChange(
    node: AnyObject,
    { checkedKeys }: { checkedKeys: string[] },
  ) {
    props.changeModifiedStatus(true);
    const tree = treeRef.value;
    if (
      tree &&
      node.parentMenuId &&
      !checkedKeys.includes(node.identification)
    ) {
      tree?.setChecked(node.parentMenuId, true, false);
      setHalfSelected(node.parentMenuId);
    }

    await updatePageElement(node);
    /** 判断 node 是否是选中状态 */
    // const isNodeInCheckedNodes = [
    //   ...halfCheckedNodes.value,
    //   ...checkedNodes.value,
    // ].some((checkedNode) => checkedNode.identification === node.identification);

    // if (isNodeInCheckedNodes) {
    // } else {
    //   selectMenu.value = undefined;
    // }

    // /** 从菜单元素集合中过滤掉defaultAllowUseFlag为1的 */
    // const elementInfo = collectElements(
    //   checkNode as Menu.SystemInfo & MenuObj & Menu.MenuInfo,
    // ).filter(
    //   (item) =>
    //     (item as unknown as { defaultAllowUseFlag: FLAG })
    //       .defaultAllowUseFlag !== FLAG.YES,
    // );

    // /** 在选中的节点集合中 */
    // if (isNodeInCheckedNodes) {
    //   // 过滤出 elementInfo 中不存在于 selectInfo 的项
    //   const newElements = elementInfo.filter(
    //     (element) =>
    //       !selectInfo.value.some(
    //         (info) => info.identification === element.identification,
    //       ),
    //   );

    //   // 将新项添加到 selectInfo 中
    //   if (newElements.length > 0) {
    //     selectInfo.value.push(
    //       ...(newElements as unknown as (Menu.SystemInfo & MenuObj)[]),
    //     );
    //   }
    // } else {
    //   selectInfo.value = selectInfo.value.filter(
    //     (info) =>
    //       !elementInfo.some(
    //         (element) => element.identification === info.identification,
    //       ),
    //   );
    // }
  }

  const updatePageElement = async (node: AnyObject) => {
    if (node.menuFlag !== MENU_AND_GROUP_FLAG.MENU) {
      selectMenu.value = undefined;
      return;
    }
    /** 从systemList中获取当前的节点信息 */
    const checkNode = findNodeByIdentification(
      node.identification,
      systemList.value,
    );

    selectMenu.value = checkNode;

    pageElementRef.value.update();
  };

  defineExpose({
    fetchMenuStructListByExample,
    saveClick,
  });
</script>

<template>
  <div class="flex flex-1 flex-col">
    <div class="mb-4">
      <el-input
        v-model="filterText"
        class="mr-5 w-60"
        :placeholder="$t('structure.searchInput.placeholder', '输入关键字查询')"
        @input="inputChange"
        clearable
      />
      <el-button
        type="primary"
        @click="saveClick"
        :key="String(disabledButton())"
        :loading="buttonLoading"
        :disabled="buttonLoading"
      >
        {{ $t('global:save') }}
      </el-button>
    </div>

    <el-scrollbar class="flex-1" ref="scrollbarRef" v-loading="loading">
      <el-tree
        ref="treeRef"
        node-key="identification"
        :key="refreshIndex"
        :highlight-current="true"
        :expand-on-click-node="false"
        :check-on-click-node="false"
        :current-node-key="state?.identification"
        :data="treeData"
        :filter-node-method="filterNode"
        default-expand-all
        show-checkbox
        @check="handleCheckChange"
        @node-click="(node: AnyObject) => updatePageElement(node)"
        :props="{
          label: 'label',
          children: 'children',
          disabled: 'disabled',
        }"
      >
        <template #default="{ node, data }">
          <span class="flex w-full justify-between">
            <span
              :title="`${data?.label} ${data?.identification}`"
              @click="
                (e: Event) => {
                  updatePageElement(data);
                  e.stopPropagation();
                }
              "
              >{{ node.label }}

              <el-tag
                class="ml-1"
                v-if="data.menuFlag === MENU_AND_GROUP_FLAG.GROUP"
                type="warning"
                >{{ $t('structure.grouping.tag', '分组') }}</el-tag
              >
              <el-tag
                class="ml-1"
                v-else-if="data.menuFlag === MENU_AND_GROUP_FLAG.MENU"
                type="success"
              >
                {{ $t('structure.menu.tag', '菜单') }}
              </el-tag>
              <el-tag
                class="ml-1"
                v-else-if="data.menuFlag === MENU_AND_GROUP_FLAG.ELEMENT"
                type="info"
              >
                {{ $t('structure.element.tag', '元素') }}
              </el-tag>
              <el-tag class="ml-1" v-else>{{
                $t('structure.system.tag', '系统')
              }}</el-tag>
            </span>
          </span>
        </template>
        <template #empty>
          <el-empty :image-size="100" />
        </template>
      </el-tree>
    </el-scrollbar>

    <div
      :class="[
        isHasElement ? 'h-40' : 'h-0',
        'overflow-hidden transition-all duration-300 ease-in-out',
      ]"
    >
      <PageElement
        class="mt-5 border-t border-solid border-[#e5e7eb] px-2 py-3"
        ref="pageElementRef"
        :select-menu="selectMenu"
        :select-info="selectInfo"
        :is-disabled="disabledPageElement"
        @select-change="(data) => setCheckMode(data)"
      />
    </div>
  </div>
</template>
