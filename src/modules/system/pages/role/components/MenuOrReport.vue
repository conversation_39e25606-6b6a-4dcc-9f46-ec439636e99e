<script setup lang="tsx">
  import { ref } from 'vue';
  import SystemMenu from './SystemMenu.vue';
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();
  const MENU = 'MENU';
  const REPORT = 'REPORT';
  const activeName = ref('MENU');
  const systemMenuRef = ref();
  type Props = {
    selectRoleRow: Role.SystemRoleInfo | null;
    refreshRoleList: () => void;
    changeModifiedStatus: (value: boolean) => void;
  };
  const props = defineProps<Props>();
  defineExpose({
    saveClick: () => {
      systemMenuRef.value?.saveClick();
    },
  });
</script>
<template>
  <!-- 你的模板内容 -->

  <el-tabs
    v-model="activeName"
    type="card"
    class="flex h-full flex-1 overflow-hidden p-[1px]"
  >
    <el-tab-pane
      :label="t('menuOrReport.tabs.menu', '系统菜单')"
      :name="MENU"
      class="flex h-full flex-1"
    >
      <SystemMenu
        ref="systemMenuRef"
        :change-modified-status="props.changeModifiedStatus"
        :select-role-row="props.selectRoleRow"
        :refresh-role-list="props.refreshRoleList"
      />
    </el-tab-pane>
    <el-tab-pane
      :label="t('menuOrReport.tabs.report', '系统报表')"
      disabled
      :name="REPORT"
      >Config</el-tab-pane
    >
  </el-tabs>
</template>
<style scoped>
  /* 你的样式代码 */
</style>
