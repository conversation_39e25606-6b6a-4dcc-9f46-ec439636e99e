<script setup lang="ts" name="codeManage">
  import { reactive, ref } from 'vue';
  import RoleList from './components/RoleList.vue';
  import RoleInfo from './components/RoleInfo.vue';
  import MenuOrReport from './components/MenuOrReport.vue';
  import { ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();
  export type AppState = {
    showRoleInfo: boolean;
    selectRoleRow: Role.SystemRoleInfo | null;
  };

  const appState = reactive<AppState>({
    showRoleInfo: false,
    selectRoleRow: null,
  });
  const isModified = { value: false }; //判断是否有修改未保存的数据
  const menuOrReportRef = ref();
  const roleRef = ref();
  async function setSelectRoleRow(
    row: Role.SystemRoleInfo,
    clear: boolean = false,
  ) {
    if (clear) {
      appState.selectRoleRow = { ...row };
      return;
    }

    const currentRole = appState.selectRoleRow;
    if (row.roleId === currentRole?.roleId || !isModified.value) {
      appState.selectRoleRow = { ...currentRole, ...row };
      return;
    }

    try {
      const action = await ElMessageBox.confirm(
        t(
          'role.change.switch.ask.title',
          '“{{name}}” 可能存在未存盘的改动，是否继续切换？',
          {
            name: currentRole?.roleName,
          },
        ),
        t('global:tip'),
        {
          confirmButtonText: t('message.switch.and.saving', '保存并切换'),
          cancelButtonText: t('message.switch.without.saving', '直接切换'),
          type: 'warning',
        },
      ).catch(() => 'close'); // 捕获取消操作

      if (action === 'confirm') {
        try {
          await menuOrReportRef.value?.saveClick();
        } catch (err) {
          console.error(err);
          return;
        }
      }
      changeModifiedStatus(false);
      appState.selectRoleRow = { ...currentRole, ...row };
    } catch (err) {
      console.error('MessageBox error:', err);
    }
  }

  function refreshRoleList() {
    roleRef?.value?.fetchData();
  }

  function changeModifiedStatus(flag: boolean) {
    isModified.value = flag;
  }
</script>

<template>
  <el-row class="p-box h-full">
    <el-col :span="12" class="h-full border-r pr-5">
      <RoleList
        :set-select-role-row="setSelectRoleRow"
        ref="roleRef"
        :is-modified="isModified"
        :change-modified-status="changeModifiedStatus"
        :select-role-row="appState.selectRoleRow"
      />
    </el-col>
    <el-col class="flex h-full flex-col pl-5" :span="12">
      <RoleInfo
        :select-role-row="appState.selectRoleRow"
        :set-select-role-row="setSelectRoleRow"
        :refresh-role-list="refreshRoleList"
      />
      <MenuOrReport
        ref="menuOrReportRef"
        :change-modified-status="changeModifiedStatus"
        :select-role-row="appState.selectRoleRow"
        :refresh-role-list="refreshRoleList"
      />
    </el-col>
  </el-row>
</template>

<style>
  micro-app-body {
    height: 100%;
  }
</style>
