import { FLAG } from '@sun-toolkit/enums';
import { useFormConfig, FormDescItem } from 'sun-biz';

export function useCliPermissionForm(options: Employee.CliPermission[]) {
  return useFormConfig({
    getData: () => genOptions(options) as FormDescItem[],
  });

  /**
   *
   * @param { Employee.CliPermission[] } options
   * @returns
   */
  function genOptions(options: Employee.CliPermission[]) {
    const res: FormDescItem[] = [];
    options.forEach((op) => {
      const {
        cliPermissionName,
        codeSystemId, // [权限唯一， 不同类型的权限不同]
        cliPermissionValueList = [],
        multiplyCheckFlag,
      } = op;
      const formItemConfig: FormDescItem = {
        label: cliPermissionName,
        name: codeSystemId,
        component: componentName(multiplyCheckFlag),
        triggerModelChange: true,
        extraProps: {
          options: genValueRangeOptions(cliPermissionValueList, codeSystemId),
        },
      };
      res.push(formItemConfig);
    });

    function componentName(type: FLAG.NO | FLAG.YES) {
      return type === FLAG.YES ? 'checkbox-group' : 'radio-group';
    }

    function genValueRangeOptions(
      options: Employee.ValuesOfPermissionRange[] = [],
      codeSystemId: string,
    ): Employee.ValueRangeOption[] {
      return options.map(
        ({ cliPermissionValueName, cliPermissionValueId, dataValueId }) => {
          return {
            label: cliPermissionValueName,
            value: JSON.stringify({
              codeSystemId,
              dataValueId,
              cliPermissionValueId,
            }),
          };
        },
      );
    }

    return res;
  }
}
