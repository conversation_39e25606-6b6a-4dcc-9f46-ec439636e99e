import { useRouter } from 'vue-router';
import { useColumnConfig } from 'sun-biz';

export function useEmployeeColumnConfig(
  // initPassword: (data: string[]) => void,
  queryCliPermissionList: (user: Employee.Item) => void,
) {
  const router = useRouter();
  const toggleEdit = (row: { empNo: string }) => {
    router.push(`/detail/${row.empNo}`);
  };
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:index.number', '序号'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('global:code', '编码'),
        prop: 'empNo',
        minWidth: 100,
      },
      {
        label: t('global:personName', '姓名'),
        minWidth: 100,
        prop: 'nameDisplay',
      },
      {
        label: t('global:secondName', '辅助名称'),
        prop: 'person2ndName',
        minWidth: 100,
      },
      {
        label: t('employee.hospitalName', '所属医院'),
        prop: 'hospitalName',
        minWidth: 140,
      },
      {
        label: t('employee.deptName', '所属科室'),
        prop: 'deptName',
        minWidth: 120,
      },
      {
        label: t('employee.titleDesc', '职称'),
        prop: 'titleDesc',
        minWidth: 120,
      },
      {
        label: t('person.intro', '职工简介'),
        prop: 'personSimpleDesc',
        minWidth: 160,
      },
      {
        label: t('employee.userStatusDesc', '职工状态'),
        prop: 'userStatusDesc',
        minWidth: 160,
      },
      {
        label: t('employee.userJob', '用户岗位'),
        prop: 'userJobDesc',
        minWidth: 100,
      },
      {
        label: t('person.status', '用户状态'),
        prop: 'enabledFlag',
        minWidth: 120,
        render: (row: { enabledFlag: number }) => {
          return row.enabledFlag ? t('global:enabled') : t('global:disabled');
        },
      },
      {
        label: t('global:creator'),
        prop: 'createdUserName',
        minWidth: 120,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        fixed: 'right',
        render: (row: { empNo: string; userId: string }) => {
          return (
            <>
              {/* <el-button
                type="danger"
                v-permission={'ZGGL-CSHMM'}
                onClick={() => initPassword([row.userId])}
                link
              >
                {t('initpassword', '初始化密码')}
              </el-button> */}
              {/* 临床权限 */}
              <el-button
                link
                type="danger"
                v-permission={'ZGGL-CSHMM'}
                onClick={() => queryCliPermissionList(row)}
              >
                {t('system.employee.cliPermission', '临床权限')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
            </>
          );
        },
      },
    ],
  });
}
