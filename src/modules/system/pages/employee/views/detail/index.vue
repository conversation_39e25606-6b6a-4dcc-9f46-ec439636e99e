<script setup lang="ts" name="employeeDetail">
  import { ref, computed, watchEffect } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { queryEmployeeListByExample } from '@modules/system/api/employee';

  import EmployeeDetail from '@/modules/system/components/employeeDetail/index.vue';

  const route = useRoute();
  const router = useRouter();

  const empNo = computed(() => route?.params?.id);
  // 是否为新增状态
  const isAdd = computed(() => empNo.value === 'add');

  type EmployeeInfo = Omit<
    Employee.Item,
    | 'userRoleList'
    | 'loginOrgLocationList'
    | 'perCertificateList'
    | 'bizUnitIds'
  >;
  const loading = ref(false);
  const isExistEmployee = ref(false);

  const detailData = ref<{
    employeeData: Employee.Item;
    userInfo: Employee.UserInfo;
    employeeInfo: EmployeeInfo;
  }>({
    employeeData: {} as Employee.Item,
    userInfo: {} as Employee.UserInfo,
    employeeInfo: {} as EmployeeInfo,
  });

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  /**
   * 查询职工列表
   * @param params 查询参数
   */
  const queryEmployeeData = async () => {
    loading.value = true;
    const [, res] = await queryEmployeeListByExample({
      pageNumber: 0,
      empNo: empNo.value,
    });
    loading.value = false;
    if (res?.data) {
      if (res.data?.length > 0) {
        isExistEmployee.value = true;
      } else {
        isExistEmployee.value = false;
      }
      const infoData = res.data[0];
      detailData.value.employeeData = infoData ?? {};
      detailData.value.userInfo = {
        userJobCode: infoData.userJobCode,
        userEnabledFlag: infoData.enabledFlag,
        invoiceAgentUserId: infoData.invoiceAgentUserId,
        paySumTypeCode: infoData.paySumTypeCode,
        paySumBelongUserId: infoData.paySumBelongUserId,
      };
      detailData.value.employeeInfo = { ...infoData };
    }
  };

  watchEffect(() => {
    if (!isAdd.value && empNo?.value) {
      queryEmployeeData();
    }
  });
</script>

<template>
  <div class="p-box size-full pr-0">
    <el-page-header @back="goBack" class="mb-2">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('employee.add', '新增职工')
              : `${$t('global:edit', '编辑')}-${detailData.employeeData?.nameDisplay}`
          }}
        </span>
      </template>
    </el-page-header>
    <EmployeeDetail
      v-if="isExistEmployee || isAdd"
      @back="goBack"
      :is-add="isAdd ?? false"
      :detail-data="detailData"
    />
  </div>
</template>
