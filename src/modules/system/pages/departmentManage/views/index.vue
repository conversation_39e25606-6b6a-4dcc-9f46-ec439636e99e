<script setup lang="ts">
  import {
    ProForm,
    Title,
    useAppConfigData,
    MAIN_APP_CONFIG,
    ProTable,
  } from 'sun-biz';
  import { ref } from 'vue';
  import { useCommodityCategorySearchFormConfig } from '../config/useFormConfig.tsx';
  import { useCommodityCategoryTableConfig } from '@/modules/system/pages/departmentManage/config/useTableConfig.tsx';
  import {
    queryDepartmentListByExampleTree,
    updateOrgEnabledFlagById,
  } from '@/modules/system/api/departmentManage.ts';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useRouter } from 'vue-router';
  import { useOrgStore } from '@modules/system/store/org.ts';

  const { t } = useTranslation();
  const router = useRouter();
  const orgStore = useOrgStore();
  const loading = ref<boolean>(false); //加载中
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const searchParams = ref<DepartmentManage.RequestParameters>({
    hospitalId: currentOrg?.orgId || '',
    enabledFlag: -1,
    keyWord: '',
  }); //from获取的参数

  const searchConfig = useCommodityCategorySearchFormConfig();
  //启用禁用科室
  const handleEnableSwitch = (value: DepartmentManage.getExampleTree) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          value.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: value.orgName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, res] = await updateOrgEnabledFlagById({
          orgId: value.orgId,
          enabledFlag:
            value.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        });
        if (res?.success) {
          ElMessage({
            type: 'success',
            message: t(
              'departmentManage.theOperationWasSuccessful',
              '操作成功',
            ),
          });
          initFun();
        }
      })
      .catch(() => {});
    return;
  };
  //编辑科室
  const editorialSection = async (value: DepartmentManage.getExampleTree) => {
    await orgStore.getOrgList({ orgId: value.orgId });
    router.push(`/detail/${value.orgId}`);
  };
  /**
   * 新增组织
   */
  const handleAddOrgItem = (query = {}) => {
    router.push({
      path: '/detail/add',
      query,
    });
  };
  const tableColumnsConfig = useCommodityCategoryTableConfig(
    handleEnableSwitch,
    editorialSection,
    handleAddOrgItem,
    currentOrg?.orgId || '',
  );
  const listOfDepartments = ref<DepartmentManage.getExampleTree[]>(); //科室列表
  //初始化数据
  const initFun = async () => {
    loading.value = true;
    const params: DepartmentManage.RequestParameters = {
      hospitalId: searchParams.value.hospitalId,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
      keyWord: searchParams.value.keyWord,
    };
    const [, res] = await queryDepartmentListByExampleTree(params); //获取查询列表
    listOfDepartments.value = (res?.data || []).map(
      (item: DepartmentManage.getExampleTree) => {
        return {
          ...item,
          addSubordinates: true,
        };
      },
    );
    loading.value = false;
  };
  initFun();
  //新增科室
  const newlyAdded = () => {
    router.push({
      path: '/detail/add',
    });
  };
  //表单事假触发
  const modelChange = (data: DepartmentManage.RequestParameters) => {
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    initFun();
  };
</script>

<template>
  <div>
    <div class="p-box flex h-full flex-col">
      <Title :title="$t('departmentManage.list.title', '科室管理')" />
      <div class="mt-3 flex justify-between">
        <div class="el-form-item">
          <ProForm
            :show-search-button="true"
            v-model="searchParams"
            layout-mode="inline"
            :data="searchConfig"
            @model-change="modelChange"
          />
        </div>
        <div>
          <el-button class="mr-2" type="primary" @click="newlyAdded">
            {{ $t('global:add') }}
          </el-button>
        </div>
      </div>
      <ProTable
        :loading="loading"
        row-key="orgId"
        :data="listOfDepartments"
        :columns="tableColumnsConfig"
        :tree-props="{ children: 'subOrgList' }"
      />
    </div>
  </div>
</template>
