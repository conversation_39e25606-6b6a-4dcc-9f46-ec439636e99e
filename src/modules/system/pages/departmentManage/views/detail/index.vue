<script setup lang="ts">
  import OrganizationalManagement from '@/modules/system/components/OrganizationalManagement/index.vue';
  import { ORG_TYPE_CODE } from '@/utils/constant.ts';
  import { MAIN_APP_CONFIG, useAppConfigData } from 'sun-biz';

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  //获取当前账户组织信息
  const orgTypeCode = ORG_TYPE_CODE.DEPARTMENT;
  const parentOrgId = currentOrg?.orgId;
  const parentOrgName = currentOrg?.orgName;
  const tenantId = currentOrg?.tenantId || '';
  const disabledOrgType = true;
</script>

<template>
  <div class="p-4">
    <OrganizationalManagement
      :org-type-code="orgTypeCode"
      :parent-org-id="parentOrgId"
      :parent-org-name="parentOrgName"
      :tenant-id="tenantId"
      :disabled-org-type="disabledOrgType"
    ></OrganizationalManagement>
  </div>
</template>
