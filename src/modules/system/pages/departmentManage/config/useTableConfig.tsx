import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';
import { ORG_TYPE_CODE, ORG_TYPE_LOW_LEVEL_MAP } from '@/utils/constant';
export function useCommodityCategoryTableConfig(
  handleEnableSwitch: (row: DepartmentManage.getExampleTree) => void,
  editorialSection: (row: DepartmentManage.getExampleTree) => void,
  handleAddOrgItem: (row: DepartmentManage.HandleAddOrgItem) => void,
  hospitalId: string,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 100,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.deptTypeDesc',
            '科室类型',
          ),
          prop: 'deptTypeDesc',
          minWidth: 90,
        },
        {
          label: t('departmentManage.departmentManageTable.orgNo', '科室编码'),
          prop: 'orgNo',
          minWidth: 90,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.orgName',
            '科室名称',
          ),
          prop: 'orgName',
          minWidth: 120,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.org2ndName',
            '科室辅助名称',
          ),
          prop: 'org2ndName',
          minWidth: 120,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.orgExtName',
            '科室扩展名称',
          ),
          prop: 'orgExtName',
          minWidth: 120,
        },
        {
          label: t('departmentManage.departmentManageTable.orgDesc', '简介'),
          prop: 'orgDesc',
          minWidth: 150,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.createdUserName',
            '创建人',
          ),
          prop: 'createdUserName',
          minWidth: 80,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.createdAt',
            '创建时间',
          ),
          prop: 'createdAt',
          minWidth: 120,
        },
        {
          label: t(
            'departmentManage.departmentManageTable.enabledFlag',
            '启用标志',
          ),
          prop: 'enabledFlag',
          minWidth: 80,
          render: (row: DepartmentManage.getExampleTree) => {
            return (
              <>
                <el-switch
                  modelValue={row.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  before-change={() => handleEnableSwitch(row)}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                ></el-switch>
              </>
            );
          },
        },
        {
          label: t(
            'departmentManage.departmentManageTable.contactNo',
            '联系方式',
          ),
          prop: 'contactNo',
          minWidth: 160,
          render: (row: DepartmentManage.getExampleTree) => {
            return (
              <>
                {row.orgXContactList?.length
                  ? row.orgXContactList[0]?.contactNo
                    ? row.orgXContactList[0]?.contactTypeCodeDesc +
                      ':' +
                      row.orgXContactList[0]?.contactNo
                    : '--'
                  : '--'}
              </>
            );
          },
        },
        {
          label: t('departmentManage.departmentManageTable.orgDesc', '操作'),
          prop: 'orgDesc',
          minWidth: 140,
          fixed: 'right',
          render: (row: DepartmentManage.getExampleTree) => {
            return (
              <>
                <el-button onClick={() => editorialSection(row)} type="text">
                  {t('global:edit')}
                </el-button>
                {row?.addSubordinates ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      const orgTypeCode =
                        ORG_TYPE_LOW_LEVEL_MAP[
                          ORG_TYPE_CODE.DEPARTMENT as keyof typeof ORG_TYPE_LOW_LEVEL_MAP
                        ];
                      handleAddOrgItem({
                        parentOrgId: row.orgId,
                        parentOrgName: row.orgNameDisplay || '--',
                        tenantId: hospitalId,
                        orgTypeCode,
                        disabledOrgType: true,
                        parentOrgTypeCode: ORG_TYPE_CODE.DEPARTMENT,
                      });
                    }}
                  >
                    {t('departmentManage.addANewSubordinate', '新增下级')}
                  </el-button>
                ) : (
                  <></>
                )}
              </>
            );
          },
        },
      ];

      return data;
    },
  });
}
