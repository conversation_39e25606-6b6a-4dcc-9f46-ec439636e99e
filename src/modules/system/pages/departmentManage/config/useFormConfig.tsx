import { useFormConfig } from 'sun-biz';

export function useCommodityCategorySearchFormConfig() {
  // onQuery: (type: DepartmentManage.RequestParameters['enabledFlag']) => void,
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
        },
      },
    ],
  });
  return data;
}
