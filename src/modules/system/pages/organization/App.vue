<script setup lang="ts" name="orgManage">
  import { ref, computed, watch, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { DmlButton } from 'sun-biz';
  import { useOrgStore } from '@modules/system/store/org.ts';
  import {
    ORG_TYPE_LOW_LEVEL_MAP,
    ORG_TYPE_CODE,
    BIZ_ID_TYPE_CODE,
  } from '@/utils/constant';
  import HospitalSystemSettingsDialog from './components/HospitalSystemSettingsDialog.vue';
  import { useResizable } from '@/hooks/useResizable';
  const { width, onMouseDown } = useResizable(280, 270, 420);
  type CheckProps = {
    checkedKeys: string[];
    halfCheckedKeys: string[];
  };
  const router = useRouter();
  const route = useRoute();
  const keyWord = ref('');
  const loading = ref(false);
  const checkedKeysData = ref<{
    checked: string[];
    halfChecked: string[];
  }>({
    checked: [],
    halfChecked: [],
  });
  const treeRef = ref();
  const orgId = computed(() => (route.params.orgId ? route.params.orgId : '0'));
  const defaultExpandKeys = computed(() =>
    orgStore?.orgList?.map((item: Org.Item) => item.orgId),
  );
  const expandKeys = ref<string[]>([]);
  const hospitalSystemSettingsDialogRef = ref();

  const orgStore = useOrgStore();
  // 获取组织列表
  onMounted(async () => {
    await queryOrgListData();
    if (
      orgStore.orgList &&
      orgStore.orgList.length &&
      orgStore.orgList[0]?.orgId
    ) {
      if (route.name === 'detail&add') return;
      gotoInfoPage(orgStore.orgList[0].orgId);
    }
  });

  const queryOrgListData = async (keyWord = '') => {
    loading.value = true;
    await orgStore.getOrgList({ keyWord });
    loading.value = false;
  };
  /**
   * 新增组织
   */
  const handleAddOrgItem = (query = {}) => {
    router.push({
      path: '/detail/add',
      query,
    });
  };
  /**
   * 组织详情页面
   */
  const gotoInfoPage = (no: string | number) => {
    router.push(`/list/${no}`);
  };

  const handleNodeClick = (node: Org.Item) => {
    if (node.orgId) {
      gotoInfoPage(node.orgId);
    }
  };

  const handleOpenHospitalSystemSettingsDialog = (data: Org.Item) => {
    hospitalSystemSettingsDialogRef.value.open(data.orgNameDisplay);
  };

  watch(
    () => orgId.value,
    (val) => {
      if (!expandKeys.value.find((key: string) => key === val)) {
        expandKeys.value.push(val as string);
        treeRef.value?.setExpandedKeys([
          ...defaultExpandKeys.value,
          ...expandKeys.value,
        ]);
      }
    },
  );
</script>

<template>
  <ul class="flex size-full">
    <li
      class="pt-box pl-box h-full w-[278px] flex-shrink-0 flex-grow-0"
      :style="{ width: width + 'px' }"
    >
      <div class="mb-4 mr-2.5 flex">
        <el-input
          class="mr-2 min-w-36"
          :placeholder="$t('global:placeholder.keyword', '请输入关键字查询')"
          v-model="keyWord"
          prefix-icon="Search"
          @keyup.enter="() => queryOrgListData(keyWord)"
        ></el-input>
        <el-button
          v-if="!checkedKeysData.checked.length"
          type="primary"
          plain
          :disabled="route.path.includes('detail/add')"
          @click="
            () => {
              handleAddOrgItem();
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          v-else
          :code="BIZ_ID_TYPE_CODE.DICT_ORGANIZATION"
          :biz-data="[
            ...checkedKeysData.halfChecked,
            ...checkedKeysData.checked,
          ]"
          @success="
            () => {
              treeRef.setCheckedKeys([]);
              checkedKeysData = {
                checked: [],
                halfChecked: [],
              };
            }
          "
        />
      </div>
      <el-auto-resizer v-loading="loading" style="height: calc(100% - 76px)">
        <template #default="{ height }">
          <el-tree-v2
            v-if="defaultExpandKeys?.length"
            class="mr-2.5 size-full"
            ref="treeRef"
            node-key="orgId"
            :highlight-current="true"
            :current-node-key="orgId"
            :data="orgStore.orgList"
            :props="{
              value: 'orgId',
              label: 'orgNameDisplay',
              children: 'subOrgList',
            }"
            :height="height"
            @node-click="handleNodeClick"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandKeys"
            show-checkbox
            @check="
              (data: unknown, info: CheckProps) => {
                checkedKeysData = {
                  checked: info.checkedKeys,
                  halfChecked: info.halfCheckedKeys,
                };
              }
            "
          >
            <template #default="{ node, data }">
              <span
                class="flex size-full justify-between"
                @click.stop.prevent="
                  (e) => {
                    handleNodeClick(node.data);
                  }
                "
              >
                <span
                  class="truncate"
                  :style="{ width: `calc(${width - node.level * 16 - 60}px)` }"
                  >{{ node.label }}
                  <el-tag class="ml-1" size="small">{{
                    data.orgTypeDesc
                  }}</el-tag></span
                >
                <span v-if="data.orgId === orgId">
                  <el-dropdown placement="bottom-end">
                    <el-icon style="margin-top: 6px; margin-right: 5px"
                      ><MoreFilled size="16" class="origin-center rotate-90"
                    /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          @click="
                            () => {
                              handleAddOrgItem({
                                orgTypeCode: data.orgTypeCode,
                                parentOrgId: data.parentOrgId,
                                parentOrgName: data.parentOrgName,
                                tenantId: data.tenantId || '',
                                disabledOrgType: true,
                              });
                            }
                          "
                          >新增同级</el-dropdown-item
                        >
                        <el-dropdown-item
                          :disabled="data.orgTypeCode === ORG_TYPE_CODE.AREA"
                          @click="
                            () => {
                              const orgTypeCode =
                                ORG_TYPE_LOW_LEVEL_MAP[
                                  data.orgTypeCode as keyof typeof ORG_TYPE_LOW_LEVEL_MAP
                                ];
                              handleAddOrgItem({
                                parentOrgId: data.orgId,
                                parentOrgName: data.orgNameDisplay || '--',
                                tenantId: data.tenantId || '',
                                orgTypeCode,
                                disabledOrgType:
                                  data.orgTypeCode === ORG_TYPE_CODE.GROUP ||
                                  data.orgTypeCode === ORG_TYPE_CODE.DEPARTMENT
                                    ? true
                                    : '',
                                parentOrgTypeCode: data.orgTypeCode,
                              });
                            }
                          "
                          >新增下级</el-dropdown-item
                        >
                        <el-dropdown-item
                          v-if="data.orgTypeCode === ORG_TYPE_CODE.HOSPITAL"
                          @click="handleOpenHospitalSystemSettingsDialog(data)"
                        >
                          {{
                            $t(
                              'organization.tree.hospitalSystemSetting',
                              '系统设置',
                            )
                          }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </span>
              </span>
            </template>
            <template #empty>
              <el-empty :image-size="100" />
            </template>
          </el-tree-v2>
        </template>
      </el-auto-resizer>
    </li>
    <li
      @mousedown="onMouseDown"
      style="width: 2px"
      class="box-border cursor-ew-resize border-r border-solid border-slate-300 hover:border-r-2"
    ></li>
    <li class="pt-box full pb-box ml-4 w-full flex-auto overflow-auto">
      <router-view v-slot="{ Component, route: dRoute }">
        <component
          :is="Component"
          :key="orgId || dRoute.name"
          :update-fn="
            () => {
              queryOrgListData();
            }
          "
        />
      </router-view>
    </li>
  </ul>
  <HospitalSystemSettingsDialog
    ref="hospitalSystemSettingsDialogRef"
    :org-id="orgId as string"
  />
</template>
