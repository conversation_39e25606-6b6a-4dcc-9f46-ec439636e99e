<script setup lang="ts" name="orgManageList">
  import { ref, computed } from 'vue';
  import { useOrgStore } from '@modules/system/store/org.ts';
  import { updateOrgSort } from '@modules/system/api/org';
  import { useRoute } from 'vue-router';
  import { useOrgColumnConfig } from '@modules/system/components/OrganizationalManagement/config/useOrgColumnConfig';
  import { ProTable, Title } from 'sun-biz';

  const route = useRoute();
  const { updateFn } = defineProps<{
    updateFn: () => void;
  }>();
  const orgStore = useOrgStore();
  const keyWord = ref('');

  const flatOrgList = computed(() => orgStore.flatOrgList);
  const orgItem = computed(() =>
    flatOrgList.value.find(
      (item: Org.Item) => item.orgId === route.params.orgId,
    ),
  );
  const subOrgList = computed(() => {
    const list =
      orgItem.value && orgItem.value.subOrgList ? orgItem.value.subOrgList : [];

    return list.filter(
      ({ orgName, orgNameDisPlay }: Org.Item) =>
        (orgNameDisPlay || orgName)?.indexOf(keyWord.value) !== -1,
    );
  });

  const columnData = useOrgColumnConfig(updateFn);
  const handleSortEnd = async (data: Org.Item[]) => {
    await updateOrgSort({
      orgSortList: data.map((item, index) => ({
        orgId: item.orgId,
        sort: index,
      })),
    });
    await orgStore.getOrgList({});
  };
</script>

<template>
  <div class="flex size-full flex-col pr-2">
    <div>
      <Title title="基本信息" class="mb-4" />
      <pro-table
        :data="orgItem ? [orgItem] : []"
        :columns="columnData"
        row-key="orgId"
      />
      <Title class="mt-8" title="下级组织">
        <el-input
          class="w-48"
          v-model="keyWord"
          placeholder="请输入关键字查询"
          prefix-icon="Search"
          @keyup.enter="
            (e: Event) => {
              console.log((e.target as HTMLInputElement).value);
            }
          "
        ></el-input>
      </Title>
    </div>
    <pro-table
      :data="subOrgList"
      :columns="columnData"
      :draggable="true"
      row-key="orgId"
      @drag-end="handleSortEnd"
    />
  </div>
</template>
