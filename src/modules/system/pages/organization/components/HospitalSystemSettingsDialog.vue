<script setup lang="ts" name="HospitalSystemSettingsDialog">
  import { ref, onMounted } from 'vue';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProTable, ProDialog } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { ENABLED_FLAG } from '@/utils/constant';
  import {
    saveHospitalXSyetem,
    querySystemListByExample,
    queryHospitalXSyetemByExample,
  } from '@modules/system/api/menu';
  import { useHospitalXSysTableConfig } from '@modules/system/components/OrganizationalManagement/config/useOrgColumnConfig';

  type HospitalSystemRowData = Menu.HospitalXSysInfo & { editable: boolean };
  type SysSelections = SelectOptions & {
    spellNo: string;
    wbNo: string;
  };
  const props = defineProps<{
    orgId: string;
  }>();
  const dialogRef = ref();
  const hospitalXSysTableRef = ref();
  const { t } = useTranslation();
  const emits = defineEmits<{ success: [] }>();
  const modalTitle = ref('');
  const loading = ref(false);
  const hospitalXSysList = ref<HospitalSystemRowData[]>([]);
  const sysSelections = ref<SysSelections[]>([]);

  const queryHospitalXSystemList = async () => {
    loading.value = true;
    const [, res] = await queryHospitalXSyetemByExample({
      hospitalId: props.orgId,
    });
    loading.value = false;
    if (res?.success) {
      hospitalXSysList.value = res.data || [];
    }
  };

  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      sysSelections.value = res.data.map((item: Menu.SystemInfo) => {
        return {
          label: item.sysName,
          value: item.sysId,
          spellNo: item.spellNo,
          wbNo: item.wbNo,
        };
      });
    }
  };

  const handleOpen = async (orgName: string) => {
    modalTitle.value = orgName;
    queryHospitalXSystemList();
    dialogRef.value.open();
  };

  const handleAdd = () => {
    addItem({ editable: true } as HospitalSystemRowData);
  };

  const handleSave = async () => {
    if (hospitalXSysList.value.some((item) => !!item.editable)) {
      ElMessage({
        type: 'warning',
        message: t(
          'organization.errorTip.isEditing',
          '请先确定正在编辑的医院系统信息！',
        ),
      });
      return;
    }
    const sysIdArr = hospitalXSysList.value.map((item) => item.sysId);
    const newSysIdArr = Array.from(new Set(sysIdArr));
    if (newSysIdArr.length < sysIdArr.length) {
      ElMessage({
        type: 'warning',
        message: t('organization.errorTip.repeatSystem', '系统信息重复！'),
      });
      return;
    }
    const params = {
      hospitalId: props.orgId,
      hospitalXSysList: hospitalXSysList.value.map((item, index) => ({
        sysId: item.sysId,
        sort: index + 1,
      })),
    };
    const [, res] = await saveHospitalXSyetem(params);
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:save.success'),
      });
      queryHospitalXSystemList();
    }
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const { hospitalXSysTableConfig, addItem } = useHospitalXSysTableConfig(
    hospitalXSysTableRef,
    hospitalXSysList,
    sysSelections,
  );

  onMounted(() => {
    querySystemList();
  });

  defineExpose({ dialogRef, open: handleOpen });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="modalTitle"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="() => {}"
    :include-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <div class="mb-2 text-right">
      <el-button type="primary" @click="handleAdd">
        {{ $t('global:add') }}
      </el-button>
      <el-button type="primary" @click="handleSave">
        {{ $t('global:save') }}
      </el-button>
      <el-button @click="handleClose">{{ $t('global:cancel') }}</el-button>
    </div>
    <ProTable
      ref="hospitalXSysTableRef"
      row-key="hospitalXSysId"
      :loading="loading"
      :data="hospitalXSysList"
      :columns="hospitalXSysTableConfig"
      :max-height="420"
      editable
    />
  </ProDialog>
</template>
