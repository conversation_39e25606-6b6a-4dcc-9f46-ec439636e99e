<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProDialog } from 'sun-biz';
  import { useTenantDialogFormConfig } from '../config/useFormConfig.ts';
  import { updateTenantById, addTenant } from '@/modules/system/api/tenant';
  const { t } = useTranslation();
  const dialogRef = ref();
  const emits = defineEmits(['success']);
  const props = defineProps<{
    mode: string;
    data: Tenant.UpsertEventParams;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: Tenant.UpsertEventParams;
  }>();
  const tenantForm = ref<Tenant.UpsertParams>({});
  const formConfig = useTenantDialogFormConfig();
  watch(
    () => props,
    () => {
      tenantForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );
  const handleConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          let [, result] = props.data.tenantId
            ? await updateTenantById({
                ...tenantForm.value,
                tenantId: props.data.tenantId,
              })
            : await addTenant(tenantForm.value);
          if (result?.success) {
            ElMessage.success({
              message: t(`global:${props.mode}.success`),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            ElMessage.error({
              message: result?.message || t(`global:${props.mode}.error`),
            });
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('表单校验未通过')]);
        }
      });
    });
  };
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${$t(`global:${props.mode}`)}${$t('tenant.name', '租户')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="handleConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="tenantForm"
      :column="3"
      :data="formConfig"
    />
  </ProDialog>
</template>
