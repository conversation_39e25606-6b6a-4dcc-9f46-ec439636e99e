import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

export function useTenantTableConfig(
  handleEnableSwitch: (data: Tenant.UpsertTenantParams) => void,
  handleOpenTenantDialog: (
    mode: string,
    data: Tenant.UpsertTenantParams,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('tenant.tenantTable.tenantName', '租户名称'),
        prop: 'tenantName',
        minWidth: 200,
      },
      {
        label: t('tenant.tenantTable.contactPersonName', '联系人'),
        prop: 'contactPersonName',
        minWidth: 200,
      },
      {
        label: t('tenant.tenantTable.contactPersonPhone', '联系电话'),
        prop: 'contactPersonPhone',
        minWidth: 200,
      },
      {
        label: t('tenant.tenantTable.startAt', '开始时间'),
        prop: 'startAt',
        minWidth: 200,
      },
      {
        label: t('tenant.tenantTable.endAt', '结束时间'),
        prop: 'endAt',
        minWidth: 200,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: Tenant.UpsertTenantParams) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        render: (row: Tenant.UpsertTenantParams) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleOpenTenantDialog('edit', row)}
              >
                {t('global:edit')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
