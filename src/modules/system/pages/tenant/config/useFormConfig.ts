import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';

export function useTenantSearchFormConfig(
  queryTenantList: (data?: Tenant.QueryParams) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryTenantList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
}

export function useTenantDialogFormConfig() {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('tenant.form.tenantName', '租户名称'),
        name: 'tenantName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('tenant.form.tenantName', '租户名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('tenant.form.tenantName', '租户名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('tenant.form.contactPersonName', '联系人'),
        name: 'contactPersonName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('tenant.form.contactPersonName', '联系人'),
        }),
      },
      {
        label: t('tenant.form.contactPersonPhone', '联系电话'),
        name: 'contactPersonPhone',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('tenant.form.contactPersonPhone', '联系电话'),
        }),
      },
      {
        label: t('tenant.form.startAt', '开始时间'),
        name: 'startAt',
        component: 'datePicker',
        placeholder: t('global:placeholder.input.template', {
          content: t('tenant.form.startAt', '开始时间'),
        }),
        extraProps: {
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: false,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('tenant.form.startAt', '开始时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('tenant.form.endAt', '结束时间'),
        name: 'endAt',
        component: 'datePicker',
        placeholder: t('global:placeholder.input.template', {
          content: t('tenant.form.endAt', '结束时间'),
        }),
        extraProps: {
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: false,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('tenant.form.endAt', '结束时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}
