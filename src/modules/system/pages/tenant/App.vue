<script setup lang="ts" name="tenant">
  import { ref } from 'vue';
  import { dayjs } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { useTenantTableConfig } from './config/useTableConfig.tsx';
  import { useTenantSearchFormConfig } from './config/useFormConfig.ts';
  import {
    queryTenantListByExample,
    updateTenantById,
  } from '@/modules/system/api/tenant';
  import TenantUpsertDialog from '@/modules/system/pages/tenant/components/TenantUpsertDialog.vue';
  const { t } = useTranslation();
  const searchParams = ref<Tenant.QueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const loading = ref(false);
  const tableData = ref([]);
  const tenantUpsertDialogMode = ref<string>('');
  const tenantUpsertDialogRef = ref();
  const tenantUpsertParams = ref<Tenant.UpsertParams>({});
  const queryTenantList = async (data?: Tenant.QueryParams) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryTenantListByExample(params);
    loading.value = false;
    if (res?.success) {
      tableData.value = res.data;
    }
  };
  const handleEnableSwitch = async (row: Tenant.UpsertParams) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.tenantName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateTenantById(params);
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:success'),
        });
        queryTenantList();
      }
    });
  };
  const handleOpenTenantDialog = (mode: string, data?: Tenant.UpsertParams) => {
    tenantUpsertDialogMode.value = mode;
    tenantUpsertParams.value =
      mode === 'add'
        ? {
            enabledFlag: ENABLED_FLAG.YES,
            startAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            endAt: dayjs('2099-12-31 23:59:59').format('YYYY-MM-DD HH:mm:ss'),
          }
        : { ...data };
    tenantUpsertDialogRef.value.dialogRef.open();
  };
  const tableColumnsConfig = useTenantTableConfig(
    handleEnableSwitch,
    handleOpenTenantDialog,
  );
  const searchConfig = useTenantSearchFormConfig(queryTenantList);
  queryTenantList();
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('tenant.list.title', '租户列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryTenantList"
        />
      </div>
      <div>
        <el-button
          class="mr-2"
          type="primary"
          @click="
            () => {
              handleOpenTenantDialog('add');
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="tableRef"
      :data="tableData"
      :loading="loading"
      :columns="tableColumnsConfig"
    />
  </div>
  <TenantUpsertDialog
    ref="tenantUpsertDialogRef"
    :mode="tenantUpsertDialogMode"
    :data="tenantUpsertParams"
    @success="queryTenantList"
  />
</template>
