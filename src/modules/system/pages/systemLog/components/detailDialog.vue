<script setup lang="ts" name="logDetailDialog">
  import { ProForm, ProDialog } from 'sun-biz';
  import { ref, nextTick } from 'vue';
  import { useLogDetail } from '../config/useDetailFormConfig';
  import { queryLogDetailById } from '@/modules/system/api/systemLog';

  const { appLogId } = defineProps<{
    appLogId: string | undefined;
  }>();

  const dialogRef = ref();
  const loading = ref(false);
  const formModel = ref<SystemLog.LogDetailReqItem>();

  /** 打开弹窗 */
  const openDialog = async () => {
    dialogRef.value.open();
    nextTick(async () => {
      await getDetail();
    });
  };

  /** 获取日志详情 */
  const getDetail = async () => {
    loading.value = true;
    const [, res] = await queryLogDetailById({
      appLogId: appLogId as string,
    });
    loading.value = false;
    if (res?.success) {
      formModel.value = res?.data;
    }
  };

  const logDetailConfig = useLogDetail();

  defineExpose({ dialogRef, open: openDialog });
</script>
<template>
  <ProDialog
    class="w-1/2"
    ref="dialogRef"
    :title="$t('log.detail', '日志详情')"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :align-center="true"
    :before-close="
      (done: () => void) => {
        done && done();
      }
    "
  >
    <div v-loading="loading" class="h-[600px] overflow-auto">
      <el-scrollbar>
        <!-- 详情 -->
        <ProForm
          :column="4"
          :data="logDetailConfig"
          v-model="formModel"
          label-suffix=":"
        />
      </el-scrollbar>
    </div>

    <!-- 关闭按钮 -->
    <template #footer>
      <el-button
        type="primary"
        @click="
          () => {
            dialogRef.close();
          }
        "
        >{{ $t('global:close') }}
      </el-button>
    </template>
  </ProDialog>
</template>
