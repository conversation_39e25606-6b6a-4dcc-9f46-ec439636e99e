<script setup lang="ts" name="logWriterSelect">
  import { FormModelType } from '../App.vue';
  import { useFetchDataset } from 'sun-biz';
  import { LOG_WRITER_TYPE_CODE_NAME } from '@/utils/constant';

  const { data, updateCode } = defineProps<{
    data: FormModelType;
    updateCode: (code: string) => void;
    updateName: (name: string) => void;
  }>();

  const dataSetList = useFetchDataset([LOG_WRITER_TYPE_CODE_NAME]);
</script>
<template>
  <div class="log-writer flex">
    <div class="w-32">
      <el-select
        :clearable="true"
        :filterable="true"
        :model-value="data.logWriterTypeCode"
        :placeholder="
          $t('global:placeholder.select.template', {
            name: $t('log.logWriterTypeCode', '写入方类型'),
          })
        "
        @change="
          (code: string) => {
            updateCode(code);
          }
        "
      >
        <el-option
          v-for="item in dataSetList?.[LOG_WRITER_TYPE_CODE_NAME] || []"
          :key="item.dataValueNo"
          :label="item.dataValueNameDisplay"
          :value="item.dataValueNo"
        ></el-option>
      </el-select>
    </div>
    <el-input
      class="w-full"
      :clearable="true"
      :model-value="data.logWriterName"
      @input="
        (name: string) => {
          updateName(name);
        }
      "
      :placeholder="
        $t('global:placeholder.input.template', {
          content: $t('log.logWriter', '写入方'),
        })
      "
    ></el-input>
  </div>
</template>

<style scoped>
  .log-writer :deep(.el-select__wrapper) {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .log-writer :deep(.el-select__wrapper.is-focused) {
    z-index: 10 !important;
  }

  .log-writer :deep(.el-input__wrapper) {
    height: 32px;
    margin: 1px 0 0 -1px;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
</style>
