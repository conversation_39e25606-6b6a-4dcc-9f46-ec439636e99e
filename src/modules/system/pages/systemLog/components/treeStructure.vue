<script setup lang="ts" name="treeStructure">
  import { Graph, type Node, type Edge } from '@antv/x6';
  import { ProForm } from 'sun-biz';
  import { LOG_TYPE_CODE } from '@/utils/constant';
  import { Check, Close } from '@element-sun/icons-vue';
  import { useLogDetail } from '../config/useDetailFormConfig';
  import { queryLogDetailById } from '@/modules/system/api/systemLog';
  import { h, ref, render, onMounted, onUnmounted } from 'vue';

  const props = defineProps<{
    data: SystemLog.LogReqItem;
  }>();

  /** 容器 */
  const container = ref();
  /** 控制弹出框popover显示状态 */
  const popoverVisible = ref(false);
  /** 虚拟引用，用于弹出框定位 */
  const virtualRef = ref();
  /** 当前选中的节点数据 */
  const currentNode = ref<Node>();
  /** 最后点击的节点ID */
  const lastClickedNodeId = ref();
  /** formModel */
  const formModel = ref();
  /** 图表实例 */
  let graph: Graph | null = null;

  /** 获取日志详情 */
  const getDetail = async (appLogId: string) => {
    const [, res] = await queryLogDetailById({
      appLogId: appLogId,
    });
    if (res?.success) {
      formModel.value = res?.data;
    }
  };

  // 注册自定义节点
  const registerCustomNode = () => {
    /** 先检查并注销已存在的节点 */
    if (Graph.unregisterNode) {
      Graph.unregisterNode('custom-node');
    }

    /** 注册节点 */
    Graph.registerNode('custom-node', {
      inherit: 'rect',
      width: 350,
      height: 70,
      /** dom结构 */
      markup: [
        {
          tagName: 'rect', // 节点主体背景
          selector: 'body',
        },
        {
          tagName: 'rect', // 图标背景
          selector: 'iconBg',
        },
        {
          tagName: 'foreignObject',
          selector: 'icon-container',
          attrs: {
            width: 70,
            height: 70,
          },
        },
        {
          tagName: 'foreignObject', // 使用 foreignObject 来包含 HTML 元素
          selector: 'text-container',
          attrs: {
            width: 260, // 350 - 80(图标宽度)
            height: 70,
            x: 80,
            y: 0,
          },
        },
        {
          tagName: 'g', // 添加一个组来包含徽标和文字
          selector: 'badge-group',
          attrs: {
            class: 'badge-group', // 添加类名
          },
          children: [
            {
              tagName: 'circle',
              selector: 'badge',
              attrs: {
                class: 'badge', // 添加类名
                cursor: 'pointer', // 添加鼠标样式
              },
            },
            {
              tagName: 'text',
              selector: 'badgeLabel',
              attrs: {
                class: 'badge-label', // 添加类名
                cursor: 'pointer', // 添加鼠标样式
              },
            },
          ],
        },
      ],
      attrs: {
        body: {
          fill: '#fff',
          stroke: '#d9d9d9',
          strokeWidth: 1,
          rx: 4,
          ry: 4,
        },
        iconBg: {
          width: 70, // 从 40 改为 70
          height: 70,
          fill: '#fff',
          stroke: 'none',
          refX: 0,
          refY: 0,
          rx: 4,
          ry: 4,
        },
        'text-container': {
          // 添加 text-container 的基础样式
          refX: 0,
          refY: 0,
        },
        'icon-container': {
          // 添加 icon-container 的基础样式
          refX: 0,
          refY: 0,
        },
        'badge-group': {
          refX: '100%',
          refY: '50%',
          zIndex: 3,
        },
        badge: {
          r: 12,
          fill: '#d9d9d9',
          stroke: 'none',
          refX: 0,
          refY: 0,
          visibility: 'hidden',
        },
        badgeLabel: {
          fill: '#fff',
          fontSize: 12,
          textAnchor: 'middle',
          textVerticalAnchor: 'middle',
          refX: 0,
          refY: 0,
          text: '',
          visibility: 'hidden',
        },
      },
    });
  };

  // 修改节点间距
  const createFlow = (
    data: SystemLog.LogReqItem,
    x = 50,
    y = 50,
    parent: Node | null = null,
  ) => {
    const node = createNode(
      {
        name: data.logTypeDesc,
        isSubLog: data.isSubLog as boolean,
        data: data,
      },
      x,
      y,
    );

    if (parent) {
      graph?.addEdge({
        source: parent,
        target: node,
        zIndex: 1, // 设置边的层级
        attrs: {
          line: {
            stroke: '#ccc',
            strokeWidth: 1,
            targetMarker: null,
          },
        },
        router: {
          name: 'manhattan',
          args: {
            direction: 'H',
            padding: 20,
          },
        },
        connector: {
          name: 'normal',
        },
      });
    }

    /** 计算子节点的总高度需求 */
    const calculateTotalHeight = (subLogs: SystemLog.LogReqItem[]) => {
      if (!subLogs || subLogs.length === 0) return 0;
      return (
        subLogs.length * 120 + // 每个子节点的基础间距
        subLogs.reduce((acc: number, curr: SystemLog.LogReqItem): number => {
          // 递归计算每个子节点的子节点所需高度
          return acc + calculateTotalHeight(curr.subLogList || []);
        }, 0)
      );
    };

    if (data.subLogList) {
      let currentY = y;

      // 计算所有子节点的总高度
      const totalHeight = calculateTotalHeight(data.subLogList);

      // 根据子节点数量动态调整间距
      const nodeSpacing = Math.min(
        120,
        Math.max(80, totalHeight / data.subLogList.length),
      );

      data.subLogList.forEach((child) => {
        const childHeight = calculateTotalHeight(child.subLogList || []);
        createFlow(child, x + 450, currentY, node);
        // 使用较小的基础间距，但确保有最小间距
        currentY += Math.max(nodeSpacing, childHeight + 60);
      });
    }
  };

  const getIconByName = (name: string, data: SystemLog.LogReqItem) => {
    const iconProps = {
      style: {
        width: '30x',
        height: '30px',
        color: '#fff',
      },
    };

    if (data.logTypeCode === LOG_TYPE_CODE.REQUEST_RESPONSE_LOG) {
      if (data.exceptionFlag === 0) {
        const vnode = h(Check, iconProps);
        const container = document.createElement('div');
        render(vnode, container);
        return container.innerHTML;
      } else {
        const vnode = h(Close, iconProps);
        const container = document.createElement('div');
        render(vnode, container);
        return container.innerHTML;
      }
    } else if (data.logTypeCode === LOG_TYPE_CODE.ERROR_LOG) {
      return '<div style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; color: #333; font-size: 14px;">ERR</div>';
    } else if (data.logTypeCode === LOG_TYPE_CODE.NORMAL_LOG) {
      return '<div style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; color: #333; font-size: 14px;">LOG</div>';
    } else if (data.logTypeCode === LOG_TYPE_CODE.SQL_LOG) {
      return '<div style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; color: #333; font-size: 14px;">SQL</div>';
    }
    return '';
  };

  const createNode = (
    data: { name: string; isSubLog: boolean; data: SystemLog.LogReqItem },
    x: number,
    y: number,
  ) => {
    const childrenCount = data.data.subLogList?.length || 0;
    const iconHtml = getIconByName(data.name, data.data);

    // 根据 logTypeCode 构建不同的文本内容
    let firstLine = '';
    let secondLine = '';
    let thirdLine = '';

    if (data.data.logTypeCode === LOG_TYPE_CODE.REQUEST_RESPONSE_LOG) {
      // REQUEST_RESPONSE_LOG 类型显示 methodName 和 className#method
      firstLine = data.data.methodName || '--';
      secondLine = data.data.className
        ? `${data.data.className}#${data.data.method || '--'}`
        : data.data.method || '--';
      thirdLine = `${data.data.logGenerateAt || '--'} ${data.data.invokeUseTime ? `${data.data.invokeUseTime}ms` : '--'}`;
    } else {
      // 其他类型显示 logGenerateAt 和 logAbstract
      firstLine = data.data.logGenerateAt || '--';
      secondLine = data.data.logAbstract || '--';
      thirdLine = ''; // 第三行为空
    }

    const textContent = `
      <div class="flex flex-col justify-between h-full py-2">
        <div class="group relative">
          <div class="text-[12px] text-[#333] truncate">${firstLine}</div>
          <div class="invisible group-hover:visible absolute z-50 bg-black text-white text-base rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
            ${firstLine}
          </div>
        </div>
        <div class="group relative">
          <div class="text-[12px] text-[#666] truncate">${secondLine}</div>
          <div class="invisible group-hover:visible absolute z-50 bg-black text-white text-base rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
            ${secondLine}
          </div>
        </div>
        ${
          thirdLine
            ? `
        <div class="group relative">
          <div class="text-[12px] text-[#999] truncate">${thirdLine}</div>
          <div class="invisible group-hover:visible absolute z-50 bg-black text-white text-base rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
            ${thirdLine}
          </div>
        </div>
        `
            : ''
        }
      </div>
    `;

    const node = graph?.addNode({
      shape: 'custom-node',
      x,
      y,
      zIndex: 2,
      attrs: {
        'text-container': {
          html: textContent,
        },
        'icon-container': {
          html: `<div style="width: 70px; height:70px; display: flex; align-items: center; justify-content: center;">${iconHtml}</div>`,
        },
        // ... 其他属性保持不变
      },
      data: data,
    });

    // 根据状态设置节点样式
    if (data.data.logTypeCode === LOG_TYPE_CODE.REQUEST_RESPONSE_LOG) {
      if (data.data.exceptionFlag === 0) {
        node?.setAttrs({
          body: {
            stroke: '#67C23A',
            fill: '#f0f9eb',
          },
          iconBg: {
            fill: '#67C23A',
          },
          icon: {
            fill: '#fff',
          },
          badge: {
            fill: '#67C23A',
            visibility: childrenCount > 0 ? 'visible' : 'hidden',
          },
          badgeLabel: {
            text: childrenCount.toString(),
            visibility: childrenCount > 0 ? 'visible' : 'hidden',
          },
        });
      } else {
        node?.setAttrs({
          body: {
            stroke: '#F56C6C',
            fill: '#fef0f0',
          },
          iconBg: {
            fill: '#F56C6C',
          },
          icon: {
            fill: '#fff',
          },
          badge: {
            fill: '#F56C6C',
            visibility: childrenCount > 0 ? 'visible' : 'hidden',
          },
          badgeLabel: {
            text: childrenCount.toString(),
            visibility: childrenCount > 0 ? 'visible' : 'hidden',
          },
        });
      }
    } else if (data.data.logTypeCode === LOG_TYPE_CODE.ERROR_LOG) {
      node?.setAttrs({
        body: {
          stroke: '#F56C6C',
          fill: '#fef0f0',
        },
        iconBg: {
          fill: '#F56C6C',
        },
        icon: {
          fill: '#fff',
        },
        badge: {
          fill: '#F56C6C',
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
        badgeLabel: {
          text: childrenCount.toString(),
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
      });
    } else if (data.data.logTypeCode === LOG_TYPE_CODE.SQL_LOG) {
      node?.setAttrs({
        body: {
          stroke: '#409EFF',
          fill: '#ecf5ff',
        },
        iconBg: {
          fill: '#409EFF',
        },
        icon: {
          fill: '#fff',
        },
        badge: {
          fill: '#409EFF',
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
        badgeLabel: {
          text: childrenCount.toString(),
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
      });
    } else {
      // NORMAL_LOG
      node?.setAttrs({
        body: {
          stroke: '#E6A23C',
          fill: '#fdf6ec',
        },
        iconBg: {
          fill: '#E6A23C',
        },
        icon: {
          fill: '#fff',
        },
        badge: {
          fill: '#E6A23C',
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
        badgeLabel: {
          text: childrenCount.toString(),
          visibility: childrenCount > 0 ? 'visible' : 'hidden',
        },
      });
    }

    return node;
  };

  // 修改显示节点及其子节点的函数
  const showNodeAndChildren = (node: Node) => {
    if (!node) return;
    node.setVisible(true);
    const edges = graph?.getOutgoingEdges(node);
    if (edges) {
      edges.forEach((edge: Edge) => {
        const targetNode = edge.getTargetNode();
        if (targetNode) {
          // 显示边和直接子节点
          edge.setVisible(true);
          targetNode.setVisible(true);
          // 设置子节点为折叠状态
          targetNode.setData({
            ...targetNode.getData(),
            collapsed: true,
          });
        }
      });
    }
    // 设置当前节点为展开状态
    node.setData({
      ...node.getData(),
      collapsed: false,
    });
  };

  // 添加隐藏节点及其子节点的函数
  const hideNodeAndChildren = (node: Node) => {
    if (!node) return;
    node.setVisible(false);
    const edges = graph?.getOutgoingEdges(node);
    if (edges) {
      edges.forEach((edge: Edge) => {
        edge.setVisible(false);
        const targetNode = edge.getTargetNode();
        if (targetNode) {
          hideNodeAndChildren(targetNode);
        }
      });
    }
  };

  // 修改初始化图表中的双击事件
  const initGraph = () => {
    if (!container.value) return;

    graph = new Graph({
      container: container.value,
      grid: false,
      // 添加自动调整画布大小的配置
      autoResize: true,
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: ['ctrl', 'meta'],
      },
      // 添加平移配置
      panning: {
        enabled: true,
        // modifiers: 'shift', // 按住 shift 键可以平移
      },
      // 添加缩放限制
      scaling: {
        min: 0.2, // 最小缩放比例
        max: 2, // 最大缩放比例
      },
      connecting: {
        // 禁用连接功能
        allowMulti: false,
        allowLoop: false,
        allowBlank: false,
        allowNode: false,
        allowEdge: false,
        allowPort: false,
      },
      interacting: {
        nodeMovable: false,
        edgeMovable: false,
        edgeLabelMovable: false,
        vertexMovable: false,
        vertexAddable: false,
        vertexDeletable: false,
      },
      // 移除无效的layer配置
      preventDefaultContextMenu: false, // 添加此配置，允许默认右键菜单
    });

    // 修改双击事件处理
    graph.on('node:dblclick', async ({ e, node }) => {
      e.stopPropagation();

      // 检查点击是否在徽标区域
      const elem = e.target;
      const badgeGroup = elem.closest('g');
      const isBadgeClick =
        elem.tagName === 'circle' ||
        (elem.tagName === 'tspan' &&
          badgeGroup.tagName === 'g' &&
          badgeGroup?.getAttribute('class') === 'badge-group');

      // 如果点击的是徽标区域，不触发 popover
      if (isBadgeClick) {
        return;
      }

      // 如果 isSubLog 为 false，不触发详情查询
      if (node.data.isSubLog) {
        return;
      }

      // 如果点击的是同一个节点
      if (lastClickedNodeId.value === node.id) {
        // 如果 popover 当前是显示的，就隐藏它
        if (popoverVisible.value) {
          popoverVisible.value = false;
          lastClickedNodeId.value = null;
        } else {
          // 如果 popover 当前是隐藏的，就显示它
          await getDetail(node.data.data?.appLogId);
          currentNode.value = node;

          const nodeBBox = node.getBBox();
          const containerRect = container.value?.getBoundingClientRect();

          /** 计算popover出来的位置,现在是让其出现在点击节点的下边 */
          virtualRef.value = {
            getBoundingClientRect() {
              return {
                width: nodeBBox.width,
                height: 0,
                top: containerRect.top + nodeBBox.y + nodeBBox.height,
                right: containerRect.left + nodeBBox.x + nodeBBox.width,
                bottom: containerRect.top + nodeBBox.y + nodeBBox.height,
                left: containerRect.left + nodeBBox.x, // 移除手动计算的偏移
              };
            },
          };

          popoverVisible.value = true;
        }
      } else {
        // 如果点击的是不同节点
        if (popoverVisible.value) {
          popoverVisible.value = false;
        }

        // 延迟显示新节点的 popover
        setTimeout(async () => {
          lastClickedNodeId.value = node.id;
          // 如果 popover 当前是隐藏的，就显示它
          await getDetail(node.data.data.appLogId);
          currentNode.value = node;

          const nodeBBox = node.getBBox();
          const containerRect = container.value.getBoundingClientRect();

          virtualRef.value = {
            getBoundingClientRect() {
              return {
                width: nodeBBox.width,
                height: 0,
                top: containerRect.top + nodeBBox.y + nodeBBox.height,
                right: containerRect.left + nodeBBox.x + nodeBBox.width,
                bottom: containerRect.top + nodeBBox.y + nodeBBox.height,
                left: containerRect.left + nodeBBox.x, // 移除手动计算的偏移
              };
            },
          };

          popoverVisible.value = true;
        }, 200);
      }
    });

    // 添加徽标点击事件
    graph.on('cell:click', ({ e, cell }) => {
      // 检查点击是否在徽标区域
      const elem = e.target;
      const badgeGroup = elem.closest('g');
      const isBadgeClick =
        elem.tagName === 'circle' ||
        (elem.tagName === 'tspan' &&
          badgeGroup.tagName === 'g' &&
          badgeGroup?.getAttribute('class') === 'badge-group');

      if (isBadgeClick) {
        const node = cell;
        const edges = graph?.getOutgoingEdges(node);
        const isCollapsed = node.getData().collapsed || false;

        if (edges) {
          if (isCollapsed) {
            // 展开：只显示直接子节点
            showNodeAndChildren(node as Node);
          } else {
            // 收起：隐藏所有子节点
            edges.forEach((edge) => {
              edge.setVisible(false);
              const targetNode = edge.getTargetNode();
              if (targetNode) {
                hideNodeAndChildren(targetNode);
              }
            });
            // 设置当前节点为折叠状态
            node.setData({
              ...node.getData(),
              collapsed: true,
            });
          }
        }

        // 更新节点的折叠状态
        node.setData({ ...node.getData(), collapsed: !isCollapsed });
      }
    });

    // 修改画布点击事件
    graph.on('blank:click', ({ e }) => {
      // 确保点击的是画布空白处
      if (e.target === container.value || e.target.tagName === 'svg') {
        popoverVisible.value = false;
        lastClickedNodeId.value = null;
      }
    });

    registerCustomNode();
    createFlow(props.data);

    // 修改初始化图表中的自适应内容部分
    setTimeout(() => {
      const contentArea = graph?.getContentArea();
      const padding = 50; // 增加padding值
      const minHeight = 300;
      if (contentArea && graph && container.value) {
        const calculatedHeight = Math.max(
          minHeight,
          contentArea.height + padding * 2,
        );

        graph.resize(contentArea.width + padding * 2, calculatedHeight);
        container.value.style.height = `${calculatedHeight}px`;
      }

      // 调整视口以确保所有内容可见
      graph?.centerContent();
    }, 100);
  };

  /** form配置 */
  const logDetailConfig = useLogDetail();

  onMounted(() => {
    initGraph();
  });

  onUnmounted(() => {
    graph?.dispose();
  });
</script>

<template>
  <div class="size-full">
    <el-scrollbar>
      <div ref="container" class="min-h-[300px] w-full bg-white pb-20"></div>
    </el-scrollbar>
    <el-popover
      v-model:visible="popoverVisible"
      :virtual-ref="virtualRef"
      virtual-triggering
      trigger="manual"
      placement="bottom"
      :width="400"
      popper-class="p-0"
      :teleported="false"
    >
      <template #default>
        <div
          class="msx-h-[300px] overflow-y-auto rounded bg-[#f5f7fa] p-3 text-lg"
        >
          <el-scrollbar height="300px">
            <ProForm
              :column="4"
              :data="logDetailConfig"
              v-model="formModel"
              label-suffix=":"
            />
          </el-scrollbar>
        </div>
      </template>
    </el-popover>
  </div>
</template>
