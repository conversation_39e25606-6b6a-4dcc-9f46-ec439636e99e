import { useFormConfig } from 'sun-biz';

/** 日志详情 */
export function useLogDetail() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('log.logTypeDesc', '日志类型'),
        name: 'logTypeDesc',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.methodName', '方法名称'),
        name: 'methodName',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.className', '对应类名'),
        name: 'className',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.method', '对应方法'),
        name: 'method',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.userName', '操作员'),
        name: 'userName',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.traceId', '跟踪ID'),
        name: 'traceId',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.logWriterName', '写入方名称'),
        name: 'logWriterName',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.clientIpAddress', '客户端IP'),
        name: 'clientIpAddress',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.resParam', '入参'),
        name: 'resParam',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.reqParam', '出参'),
        name: 'reqParam',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.logGenerateAt', '日志产生时间'),
        name: 'logGenerateAt',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.invokeUseTime', '调用耗时(ms)'),
        name: 'invokeUseTime',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
      {
        label: t('log.logDetail', '日志详情'),
        name: 'logDetail',
        component: 'text',
        isFullWidth: true,
        className: 'mb-2',
      },
    ],
  });
  return data;
}
