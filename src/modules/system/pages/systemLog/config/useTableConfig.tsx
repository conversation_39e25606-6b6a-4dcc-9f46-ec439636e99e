import { useColumnConfig } from 'sun-biz';
import { LOG_TYPE_CODE } from '@/utils/constant';
import { Close, Select } from '@element-sun/icons-vue';

/** 日志列表table */
export function useLogTableConfig(options: {
  handleDetail: (row: SystemLog.LogReqItem) => Promise<void>;
}) {
  const { handleDetail } = options;
  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('log.logTypeDesc', '日志类型'),
        prop: 'logTypeDesc',
        minWidth: 150,
        headerAlign: 'center',
        align: 'left',
      },
      {
        label: t('log.logGenerateAt', '产生时间'),
        prop: 'logGenerateAt',
        minWidth: 150,
      },
      {
        label: t('log.logAbstract', '日志摘要'),
        prop: 'logAbstract',
        minWidth: 150,
      },
      {
        label: t('log.userName', '操作员'),
        prop: 'userName',
        minWidth: 150,
      },
      {
        label: t('log.exceptionFlag', '异常标志'),
        prop: 'exceptionFlag',
        minWidth: 150,
        render: (row: SystemLog.LogReqItem) => {
          return row.logTypeCode === LOG_TYPE_CODE.ERROR_LOG ||
            row.logTypeCode === LOG_TYPE_CODE.REQUEST_RESPONSE_LOG ? (
            <el-icon>
              {row.exceptionFlag ? (
                <Close style={{ color: 'var(--el-color-danger)' }} />
              ) : (
                <Select style={{ color: 'var(--el-color-success)' }} />
              )}
            </el-icon>
          ) : (
            <div>-</div>
          );
        },
      },
      {
        label: t('log.logWriterTypeDesc', '写入方类型'),
        prop: 'logWriterTypeDesc',
        minWidth: 150,
      },
      {
        label: t('log.logWriterName', '写入方名称'),
        prop: 'logWriterName',
        minWidth: 150,
      },
      {
        label: t('log.clientIpAddress', '客户端IP'),
        prop: 'clientIpAddress',
        minWidth: 150,
      },
      {
        label: t('log.methodName', '方法名称'),
        prop: 'methodName',
        minWidth: 150,
      },
      {
        label: t('log.methodAddress', '方法地址(类名、方法名)'),
        prop: 'className',
        minWidth: 230,
        render: (row: SystemLog.LogReqItem) => {
          return (
            <div class={'overflow-hidden text-ellipsis whitespace-nowrap'}>
              {row.className && row.method
                ? `${row.className}.${row.method}`
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('log.invokeUseTime', 'API耗时'),
        prop: 'invokeUseTime',
        minWidth: 150,
        render: (row: SystemLog.LogReqItem) => {
          return <div>{row.invokeUseTime}ms</div>;
        },
      },
      {
        label: t('global:operation'),
        prop: 'action',
        fixed: 'right',
        minWidth: 200,
        render: (row: SystemLog.LogReqItem) => {
          return (
            <el-button
              link
              type={row.isSubLog ? 'info' : 'primary'}
              disabled={row.isSubLog}
              onClick={() => handleDetail(row)}
            >
              {t('log.detail', '详情')}
            </el-button>
          );
        },
      },
    ],
  });
  return data;
}
