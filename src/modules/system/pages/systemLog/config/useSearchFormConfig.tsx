import { Ref, ComputedRef } from 'vue';
import { useFormConfig } from 'sun-biz';
import { FormModelType } from '../App.vue';
import { useTranslation } from 'i18next-vue';
import { LOG_TYPE_CODE_NAME } from '@/utils/constant';
import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { UserReqItem, UserReqParams } from '@/api/types';
import LogWriterSelect from '../components/logWriterSelect.vue';

/** 日志查询form */
export function useSearchFormConfig(options: {
  orgId: ComputedRef<string | undefined>;
  userList: Ref<UserReqItem[]>;
  searchModel: Ref<FormModelType>;
  getUserList: (params: UserReqParams) => Promise<void>;
}) {
  const { orgId, userList, searchModel, getUserList } = options;
  const { t } = useTranslation();
  /** 耗时下限规则校验 */
  const invokeUseTimeLowerRule = (
    rules: unknown,
    value: string,
    callback: (error?: Error | undefined) => void,
  ) => {
    if (!value) {
      callback();
    }
    if (!/^\d+(\.\d+)?$/.test(value)) {
      callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('number', '数字'),
          }),
        ),
      );
    } else {
      callback();
    }
  };

  const data = useFormConfig({
    dataSetCodes: [LOG_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('log.userId', '操作员'),
        name: 'userId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('log.userId', '操作员'),
        }),
        className: 'w-80',
        extraProps: {
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: userList.value,
          remoteMethod: (keyWord: string) => {
            getUserList({
              pageNumber: 1,
              pageSize: ONE_PAGE_SIZE,
              keyWord: keyWord,
              hospitalId: orgId.value as string,
            });
          },
          props: {
            label: 'userName',
            value: 'userId',
          },
        },
      },
      {
        label: t('log.logGenerateDate', '请求时间'),
        name: 'logGenerateDate',
        type: 'datetimerange',
        component: 'date-picker',
        placeholder: t('global:placeholder.select.template', {
          name: t('log.logGenerateDate', '请求时间'),
        }),
        extraProps: {
          clearable: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          startPlaceholder: t('global:startTime'),
          endPlaceholder: t('global:endTime'),
        },
      },
      {
        label: t('log.traceId', '跟踪ID'),
        name: 'traceId',
        className: 'w-80',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('log.traceId', '跟踪ID'),
        }),
      },
      {
        label: t('log.clientTraceId', '客户端跟踪ID'),
        name: 'clientTraceId',
        component: 'input',
        className: 'w-80',
        placeholder: t('global:placeholder.input.template', {
          content: t('log.clientTraceId', '客户端跟踪ID'),
        }),
      },
      {
        label: t('log.logWriter', '写入方'),
        name: 'logWriter',
        className: 'w-80',
        render: () => {
          return (
            <div class="w-full">
              <LogWriterSelect
                data={searchModel.value}
                updateCode={(code: string) => {
                  searchModel.value.logWriterTypeCode = code;
                }}
                updateName={(name: string) => {
                  searchModel.value.logWriterName = name;
                }}
              />
            </div>
          );
        },
      },
      {
        label: t('log.clientIpAddress', '客户端IP'),
        name: 'clientIpAddress',
        component: 'input',
        className: 'w-80',
        placeholder: t('global:placeholder.input.template', {
          content: t('log.clientIpAddress', '客户端IP'),
        }),
      },
      {
        label: t('log.serverIpAddress', '服务端IP'),
        name: 'serverIpAddress',
        component: 'input',
        className: 'w-80',
        placeholder: t('global:placeholder.input.template', {
          content: t('log.serverIpAddress', '服务端IP'),
        }),
      },
      {
        label: t('log.logTypeCode', '日志类型'),
        name: 'logTypeCode',
        component: 'select',
        className: 'w-80',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('log.logTypeCode', '日志类型'),
        }),
        extraProps: {
          clearable: true,
          filterable: true,
          options: dataSet?.value ? dataSet?.value[LOG_TYPE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('log.logKeyword', '关键字'),
        name: 'logKeyword',
        component: 'input',
        className: 'w-80',
        placeholder: t('global:placeholder.input.template', {
          content: t('log.logKeyword', '关键字'),
        }),
      },
      {
        label: t('log.invokeUseTimeLower', '耗时下限'),
        name: 'invokeUseTimeLower',
        rules: [
          {
            validator: invokeUseTimeLowerRule,
            trigger: ['blur', 'change'],
          },
        ],
        render: () => {
          return (
            <div class={'w-max-40 flex'}>
              <el-input
                v-model={searchModel.value.invokeUseTimeLower}
                placeholder={t('global:placeholder.input.template', {
                  content: t('log.invokeUseTimeLower', '耗时下限'),
                })}
              />
              <span class="ml-2">ms</span>
            </div>
          );
        },
      },
    ],
  });
  return data;
}
