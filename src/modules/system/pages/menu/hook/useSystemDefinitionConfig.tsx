import { useColumnConfig } from 'sun-biz';

export function useDefinitionColumnConfig(
  deleteClick: (codeRepositoryId: string) => void,
  isCloudEnv: boolean | undefined,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
          selectable: (row: { systemDefId: string }) =>
            !!row.systemDefId && isCloudEnv,
        },
        {
          label: t('definition.table.codeRepositoryName.title', '代码仓库'),
          prop: 'codeRepositoryName',
          supportCopyAndTips: true,
          minWidth: 200,
        },
        {
          label: t('definition.table.codeRepositoryDesc.title', '代码仓库描述'),
          prop: 'codeRepositoryDesc',
          supportCopyAndTips: true,
          minWidth: 200,
        },
        {
          label: t(
            'definition.table.codeRepositoryTypeCodeDesc.title',
            '代码仓库类型',
          ),
          prop: 'codeRepositoryTypeCodeDesc',
          supportCopyAndTips: true,
          supportTextCopy: false,
          minWidth: 100,
        },
        {
          label: t('global:operation'),
          prop: 'tagExtName',
          minWidth: 100,
          render: (row: { codeRepositoryId: string }) => {
            return (
              <el-button
                type="primary"
                link={true}
                onClick={() => deleteClick(row.codeRepositoryId)}
              >
                {t('global:delete')}
              </el-button>
            );
          },
        },
      ];
    },
  });
}

export function useRepositoryColumnConfig() {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
        },
        {
          label: t(
            'definition.table.codeRepositoryTypeCodeDesc.title',
            '代码仓库类型',
          ),
          prop: 'codeRepositoryTypeCodeDesc',
          supportCopyAndTips: true,
          minWidth: 200,
        },
        {
          label: t('definition.table.codeRepositoryName.title', '代码仓库'),
          prop: 'codeRepositoryName',
          supportCopyAndTips: true,
          minWidth: 200,
        },
        {
          label: t('definition.table.codeRepositoryDesc.title', '代码仓库描述'),
          prop: 'codeRepositoryDesc',
          supportCopyAndTips: true,
          minWidth: 200,
        },
      ];
    },
  });
}
