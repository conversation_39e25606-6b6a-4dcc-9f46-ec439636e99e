import { useColumnConfig, type AnyObject } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
export function getColumns(
  emit: {
    (event: 'changeMenuId', menuId: string, menuName: string): void;
  },
  handleEnableSwitch: (row: Menu.SystemInfo | Menu.MenuInfo) => Promise<void>,
  openMenuDialog: (data: AnyObject) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
          minWidth: 50,
        },
        {
          label: t('global:sequenceNumber', '序号'),
          prop: 'sequence',
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('manage.menuTable.menuName', '菜单名称'),
          prop: 'menuName',
          minWidth: 180,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.menuTable.menu2ndName', '菜单辅助名称'),
          prop: 'menu2ndName',
          minWidth: 140,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.menuTable.menuExtName', '菜单扩展名称'),
          prop: 'menuExtName',
          minWidth: 140,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.menuTable.sysName', '所属系统'),
          prop: 'sysName',
          editable: false,
          minWidth: 140,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.menuTable.url', '地址'),
          prop: 'url',
          editable: false,
          minWidth: 280,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.menuTable.menuSourceDesc', '菜单来源'),
          prop: 'menuSourceDesc',
          editable: false,
          minWidth: 140,
        },
        {
          label: t('manage.menuTable.assignToName', '菜单运维'),
          prop: 'assignToName',
          editable: false,
          minWidth: 140,
        },
        {
          label: t('manage.menuTable.createdUserName', '创建人'),
          prop: 'createdUserName',
          editable: false,
          minWidth: 140,
        },
        {
          label: t('manage.menuTable.createdAt', '创建时间'),
          prop: 'createdAt',
          editable: false,
          minWidth: 200,
        },
        {
          label: t('manage.menuTable.enabledFlag', '启用标志'),
          prop: 'enabledFlag',
          fixed: 'right',
          render: (row: Menu.MenuInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'action',
          fixed: 'right',
          minWidth: 200,
          render: (row: Menu.MenuInfo) => {
            return (
              <>
                <el-button
                  type={'primary'}
                  link
                  disabled={row.enabledFlag === ENABLED_FLAG.NO}
                  onClick={() => {
                    emit('changeMenuId', row.menuId, row.menuName);
                  }}
                >
                  {t('tabs.element.title', '页面元素')}
                </el-button>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    openMenuDialog({
                      row: { ...row },
                      title: t(
                        'global:edit.name.template',
                        '编辑 “{{name}}” ',
                        {
                          name: row.menuName,
                        },
                      ),
                    });
                  }}
                >
                  {t('global:edit')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
