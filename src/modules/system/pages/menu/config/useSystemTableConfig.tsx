import { useColumnConfig, type AnyObject } from 'sun-biz';
import { ENABLED_FLAG, SYSTEM_ID } from '@/utils/constant';

export function getColumns(
  emit: {
    (event: 'changeSysId', sysId: string, sysName: string): void;
  },
  handleEnableSwitch: (row: Menu.SystemInfo | Menu.MenuInfo) => Promise<void>,
  handleAccessSwitch: (row: Menu.SystemInfo & Menu.MenuInfo) => Promise<void>,
  openSystemDialog: (data: AnyObject) => void,
  openDefinitionDialog: (data: AnyObject) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
          minWidth: 50,
        },
        {
          label: t('global:sequenceNumber', '序号'),
          prop: 'sequence',
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('manage.table.sysName', '系统名称'),
          prop: 'sysName',
          minWidth: 150,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.table.sys2ndName', '系统辅助名称'),
          prop: 'sys2ndName',
          minWidth: 150,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.table.sysExtName', '系统扩展名称'),
          prop: 'sysExtName',
          minWidth: 170,
          supportCopyAndTips: true,
        },
        {
          label: t('manage.table.url', '地址'),
          prop: 'url',
          supportCopyAndTips: true,
        },
        {
          label: t('manage.table.devGroupCodeDesc', '开发组别'),
          prop: 'devGroupCodeDesc',
          supportCopyAndTips: true,
        },
        {
          label: t('manage.table.createdUserName', '创建人'),
          prop: 'createdUserName',
          minWidth: 130,
        },
        {
          label: t('manage.table.createdAt', '创建时间'),
          prop: 'createdAt',
          editable: false,
          minWidth: 180,
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 130,
          fixed: 'right',
          render: (row: Menu.SystemInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                before-change={() => handleEnableSwitch(row)}
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('manage.table.accessFlag', '访问标志'),
          prop: 'accessFlag',
          minWidth: 130,
          fixed: 'right',
          render: (row: Menu.SystemInfo & Menu.MenuInfo) => {
            return (
              <el-switch
                modelValue={row.accessFlag}
                inline-prompt
                disabled={row.sysId === SYSTEM_ID}
                onChange={() => handleAccessSwitch(row)}
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                active-text={t('access', '允许访问')}
                inactive-text={t('not.access', '停止访问')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'action',
          fixed: 'right',
          minWidth: 170,
          render: (row: Menu.SystemInfo) => {
            return (
              <>
                <el-button
                  type={'primary'}
                  link
                  disabled={row.enabledFlag === ENABLED_FLAG.NO}
                  onClick={() => {
                    emit('changeSysId', row.sysId, row.sysName);
                  }}
                >
                  {t('manage.table.menuLink', '菜单')}
                </el-button>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    openSystemDialog({
                      row: { ...row },
                      title: t(
                        'global:edit.name.template',
                        '编辑 “{{name}}” ',
                        {
                          name: row.sysName,
                        },
                      ),
                    });
                  }}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    openDefinitionDialog({
                      ...row,
                      title: t(
                        'system.table.definition.dialog.title',
                        '编辑 “{{name}}” 的系统定义',
                        {
                          name: row.sysName,
                        },
                      ),
                    });
                  }}
                >
                  {t('system.table.definition.title', '定义')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
