import { useColumnConfig, type AnyObject } from 'sun-biz';
import { ENABLED_FLAG, FLAG } from '@/utils/constant';
export function getColumns(
  isCloudEnv: boolean | undefined,
  handleEnableSwitch: (row: Menu.SystemInfo | Menu.MenuInfo) => Promise<void>,
  openElementDialog: (data: AnyObject) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
          minWidth: 50,
        },
        {
          label: t('global:sequenceNumber', '序号'),
          prop: 'sequence',
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('element.table.pageElementNo', '页面元素编码'),
          prop: 'pageElementNo',
          minWidth: 180,
          supportCopyAndTips: true,
        },
        {
          label: t('element.table.pageElementName', '页面元素名称'),
          prop: 'pageElementName',
          minWidth: 140,
          supportCopyAndTips: true,
        },
        {
          label: t('element.table.menuName', '所属菜单'),
          prop: 'menuName',
          editable: false,
          minWidth: 140,
          supportCopyAndTips: true,
        },
        {
          label: t('element.table.defaultAllowUseFlag', '默认允许使用标志'),
          prop: 'defaultAllowUseFlag',
          editable: false,
          minWidth: 140,
          render: (row: { defaultAllowUseFlag: FLAG }) => {
            return row.defaultAllowUseFlag === FLAG.YES
              ? t('global:yes')
              : t('global:no');
          },
        },
        {
          label: t('global:spellNo'),
          prop: 'spellNo',
          editable: false,
          minWidth: 140,
        },
        {
          label: t('global:wbNo'),
          prop: 'wbNo',
          editable: false,
          minWidth: 140,
        },
        {
          label: t('manage.menuTable.enabledFlag', '启用标志'),
          prop: 'enabledFlag',
          fixed: 'right',
          render: (row: Menu.MenuInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'action',
          fixed: 'right',
          minWidth: 200,
          render: (row: Menu.ElementInfo) => {
            return (
              <>
                <el-button
                  type="primary"
                  link
                  disabled={!isCloudEnv}
                  onClick={() => {
                    openElementDialog({
                      row: { ...row },
                      title: t(
                        'global:edit.name.template',
                        '编辑 “{{name}}” ',
                        {
                          name: row.pageElementName,
                        },
                      ),
                    });
                  }}
                >
                  {t('global:edit')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
