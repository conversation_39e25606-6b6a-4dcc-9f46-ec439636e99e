<script setup lang="tsx">
  import SystemTable from './components/SystemTable.vue';
  import PageELementTable from './components/PageElementTable.vue';
  import MenuTable from './components/MenuTable.vue';
  import { ACTIVE_NAME } from './config/constant.tsx';
  import { ref } from 'vue';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  import { Title } from 'sun-biz';

  let activeName = ref(ACTIVE_NAME.SYSTEM);
  let sysId = ref<string>();
  let sysName = ref<string>();
  let menuId = ref<string>();
  let menuName = ref<string>();
  const dmlList = useGetDMLList();
  function tabChange(value: ACTIVE_NAME) {
    activeName.value = value;
    sysId.value = undefined;
    menuId.value = undefined;
  }

  function changeSysId(value: string, name: string) {
    activeName.value = ACTIVE_NAME.MENU;
    sysId.value = value;
    sysName.value = name;
  }

  function changeMenuId(value: string, name: string) {
    activeName.value = ACTIVE_NAME.ElEMENT;
    menuId.value = value;
    menuName.value = name;
  }
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('menuManage.title', '菜单管理')" class="mb-2"> </Title>
    <el-tabs
      class="flex h-full flex-1 overflow-hidden"
      v-model="activeName"
      @tab-change="tabChange"
    >
      <el-tab-pane
        :label="$t('tabs.system.title', '应用系统')"
        :name="ACTIVE_NAME.SYSTEM"
        class="flex h-full flex-1"
      >
        <SystemTable :menu-list="dmlList" @change-sys-id="changeSysId" />
      </el-tab-pane>
      <el-tab-pane
        :label="$t('tabs.menu.title', '应用菜单')"
        :name="ACTIVE_NAME.MENU"
        class="flex h-full flex-1"
      >
        <MenuTable
          v-if="activeName === ACTIVE_NAME.MENU"
          :menu-list="dmlList"
          @change-menu-id="changeMenuId"
          :prop-sys-id="sysId"
          :prop-sys-name="sysName"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="$t('tabs.element.title', '页面元素')"
        :name="ACTIVE_NAME.ElEMENT"
        class="flex h-full flex-1"
      >
        <PageELementTable
          v-if="activeName === ACTIVE_NAME.ElEMENT"
          :menu-list="dmlList"
          :prop-menu-id="menuId"
          :prop-menu-name="menuName"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style scoped>
  /* 你的样式代码 */
</style>
