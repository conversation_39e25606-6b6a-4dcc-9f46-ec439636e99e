<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { saveSystemDefinition } from '../../../api/menu';
  import { ElMessage } from 'element-sun';
  import { ProDialog, ProTable, DmlButton } from 'sun-biz';
  import { useDefinitionColumnConfig } from '../hook/useSystemDefinitionConfig.tsx';
  import RepositoryTable from './RepositoryTable.vue';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useTranslation } from 'i18next-vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  type Props = {
    codeRepositoryList?: Menu.CodeRepositoryInfo[];
    sysId?: string;
    title?: string;
  };
  const emits = defineEmits<{
    success: [];
  }>();
  const props = defineProps<Props>();
  const dialogRef = ref();
  const tableData = ref<Menu.CodeRepositoryInfo[]>([]);
  const checkedKeys = ref<Menu.CodeRepositoryInfo[]>([]);
  const tableRef = ref();
  watch(
    () => props.codeRepositoryList,
    () => {
      tableData.value = props.codeRepositoryList || [];
    },
    {
      immediate: true,
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      saveSystemDefinition({
        sysId: props.sysId || '',
        codeRepositoryList: tableData.value.map((item) => ({
          systemDefId: item.systemDefId,
          codeRepositoryId: item.codeRepositoryId,
        })),
      }).then(([, result]) => {
        if (result?.success) {
          ElMessage.success(t('global:save.success'));
          resolve([] as unknown as [never, unknown]);
        } else {
          reject(['', new Error('接口错误')]);
        }
      });
    });
  }

  function deleteClick(codeRepositoryId: string) {
    tableData.value = tableData.value.filter((item) => {
      return item.codeRepositoryId !== codeRepositoryId;
    });
  }

  const tableColumn = useDefinitionColumnConfig(deleteClick, isCloudEnv);
  const bizData = computed(() => {
    return checkedKeys.value.map((item) => {
      return item.systemDefId || '';
    });
  });

  const selectionChange = (value: Menu.CodeRepositoryInfo[]) => {
    checkedKeys.value = value;
  };

  function changeSelectedList(list: Menu.CodeRepositoryInfo[]) {
    tableData.value = list.map((item) => {
      let findObj = (props.codeRepositoryList || []).find(
        (cur) => cur.codeRepositoryId === item.codeRepositoryId,
      );
      if (findObj) return findObj;
      return item;
    });
  }

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="1100"
    ref="dialogRef"
    :title="props.title"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <div class="mb-4 flex justify-between">
      <RepositoryTable
        :selected-list="tableData"
        :change-selected-list="changeSelectedList"
      />
      <DmlButton
        v-if="isCloudEnv"
        :code="BIZ_ID_TYPE_CODE.DICT_SYSTEM_DEF"
        :biz-data="bizData"
        @success="
          () => {
            tableRef?.proTableRef.clearSelection();
            checkedKeys = [];
          }
        "
      />
    </div>
    <ProTable
      ref="tableRef"
      :columns="tableColumn"
      row-key="systemDefId"
      :max-height="300"
      :data="tableData"
      @selection-change="selectionChange"
    />
  </ProDialog>
</template>
