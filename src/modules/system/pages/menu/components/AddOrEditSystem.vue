<script setup lang="ts">
  import { ref, useAttrs, computed, watch } from 'vue';
  import { addSystem, updateSystemById } from '../../../api/menu';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    ENABLED_FLAG,
    SYSTEM_ID,
    DEV_GROUP_CODE_NAME,
  } from '@/utils/constant';
  import { ProForm, ProDialog, useFetchDataset } from 'sun-biz';

  const dataSetList = useFetchDataset([DEV_GROUP_CODE_NAME]);

  const devGroupCodeData = computed(() =>
    (dataSetList?.value?.[DEV_GROUP_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );

  function getBaseInfoData() {
    return [
      {
        name: 'sysName',
        label: t('addOrEditSystem.sysName', '系统名称'),
        autoConvertSpellNoAndWbNo: true,
        component: 'input',
        placeholder: t('addOrEditSystem.sysName.placeholder', '请输入系统名称'),
        rules: [
          {
            required: true,
            message: t('addOrEditSystem.sysName.placeholder', '请输入系统名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'sys2ndName',
        label: t('addOrEditSystem.sys2ndName', '系统辅助名称'),

        component: 'input',
        placeholder: t(
          'addOrEditSystem.sys2ndName.placeholder',
          '请输入系统辅助名称',
        ),
      },
      {
        name: 'sysExtName',
        label: t('addOrEditSystem.sysExtName', '系统扩展名称'),
        component: 'input',
        placeholder: t(
          'addOrEditSystem.sysExtName.placeholder',
          '请输入系统扩展名称',
        ),
      },
      {
        name: 'spellNo',
        component: 'input',
        placeholder: t('addOrEditSystem.spellNo.placeholder', '请输入拼音码'),
        label: t('global:spellNo'),
      },
      {
        name: 'wbNo',
        component: 'input',
        placeholder: t('addOrEditSystem.wbNo.placeholder', '请输入五笔码'),

        label: t('global:wbNo'),
      },
      {
        name: 'url',
        component: 'input',
        placeholder: t('addOrEditSystem.url.placeholder', '请输入系统地址'),
        label: t('addOrEditSystem.url', '系统地址'),
        rules: [
          {
            required: true,
            message: t('addOrEditSystem.url.placeholder', '请输入系统地址'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'devGroupCode',
        component: 'select',
        placeholder: t(
          'addOrEditSystem.devGroupCode.placeholder',
          '请输入开发组别',
        ),
        label: t('addOrEditSystem.devGroupCode', '开发组别'),
        extraProps: {
          options: devGroupCodeData.value,
        },
      },
      // {
      //   type: 'row',
      //   name: 'layout',
      //   children: [
      //     {
      //       name: 'iconUri',
      //       component: 'upload',
      //       placeholder: '请选择图标',
      //       defaultValue: '',
      //
      //       label: '图标',
      //     },
      //   ],
      // },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addOrEditSystem.enabledFlag.placeholder',
          '请输入启用状态',
        ),
        label: t('addOrEditSystem.enabledFlag', '启用状态'),
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
      {
        name: 'accessFlag',
        component: 'switch',
        placeholder: t(
          'addOrEditSystem.accessFlag.placeholder',
          '请输入访问标志',
        ),
        label: t('addOrEditSystem.accessFlag', '访问标志'),
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          disabled: props.row?.sysId === SYSTEM_ID,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
      {
        name: 'mdmApiUrl',
        label: t('addOrEditSystem.MDMInterfaceAddress', 'MDM接口地址'),
        component: 'input',
        placeholder: t(
          'addOrEditSystem.sysExtName.MDMInterfaceAddress',
          '请输入MDM接口地址',
        ),
      },
    ];
  }
  const { t } = useTranslation();
  export type Props = {
    row?: {
      sysId: string;
      sysName: string;
      sys2ndName: string;
      sysExtName: string;
      url: string;
      enabledFlag: 0 | 1;
      wbNo: string;
      spellNo: string;
      sort: number;
      devGroupCode: string;
    };
    initSort: number;
  };

  const props = defineProps<Props>();
  const attrs = useAttrs();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const formModel = ref({});
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const baseInfoDescData = computed(() => getBaseInfoData());

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = props.row?.sysId
            ? await updateSystemById({
                ...(model as unknown as Menu.ResUpdateSystemParams),
                sysId: props.row?.sysId,
                sort: props.row?.sort || 99,
              })
            : await addSystem({
                ...(model as unknown as Menu.ResAddSystemParams),
                sort: props.initSort,
              });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.sysId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="formModel"
      :column="1"
      :data="baseInfoDescData"
  /></ProDialog>
</template>
