<script setup lang="tsx">
  import AddOrEditSystem from './AddOrEditSystem.vue';
  import { Search } from '@element-sun/icons-vue';
  import { ProTable, type AnyObject } from 'sun-biz';
  import { computed, onMounted } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { getColumns } from '../config/useSystemTableConfig.tsx';
  import {
    querySystemListByExample,
    updateSystemEnabledFlagById,
    updateSystemSortByIds,
  } from '../../../api/menu';
  import { reactive, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { updateSystemById } from '../../../api/menu';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { downloadFile, debounce, cloneDeep } from '@sun-toolkit/shared';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { SelectOptions } from '@/typings/common.ts';
  import SystemDefinition from './SystemDefinition.vue';

  type DialogData = {
    [key: string]: unknown;
  };
  const { t } = useTranslation();
  type Props = {
    menuList: SelectOptions[];
  };
  const emit = defineEmits(['changeSysId', 'changeMenuId']);
  const props = withDefaults(defineProps<Props>(), {});
  const enabledFlag = ref<0 | 1 | -1>(ENABLED_FLAG.ALL);
  const loading = ref<boolean>(false);
  const keyWord = ref<string>('');
  const tableRef = ref();
  const addOrEditSystemRef = ref();
  const systemDefinitionRef = ref();
  const systemData = reactive<Menu.MixSystemMenuElement & { title?: string }>(
    {},
  );
  const systems = ref<Menu.SystemInfo[]>([]);
  const selections = ref<Menu.MixSystemMenuElement[]>([]);
  const options = computed(() => [
    {
      label: t('global:all'),
      value: ENABLED_FLAG.ALL,
    },
    {
      label: t('global:yes'),
      value: ENABLED_FLAG.YES,
    },
    {
      label: t('global:no'),
      value: ENABLED_FLAG.NO,
    },
  ]);

  let inputChange = debounce(fetchData, 500);
  const columns = getColumns(
    emit,
    handleEnableSwitch,
    handleAccessSwitch,
    openSystemDialog,
    openDefinitionDialog,
  );
  function onClose() {
    fetchData();
  }

  /**
   * 禁用系统或者菜单
   */
  async function getEnableFunction(
    row: Menu.SystemInfo | Menu.MenuInfo | Menu.ElementInfo,
  ) {
    return await updateSystemEnabledFlagById({
      enabledFlag:
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES,
      sysId: (row as Menu.SystemInfo).sysId,
    });
  }

  function handleEnableSwitch(row: Menu.SystemInfo | Menu.MenuInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name:
            (row as Menu.MenuInfo)?.menuName ||
            (row as Menu.SystemInfo)?.sysName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await getEnableFunction(row);
          if (result?.success) {
            resolve();
            fetchData();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  async function handleAccessSwitch(row: Menu.SystemInfo & Menu.MenuInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.accessFlag === ENABLED_FLAG.YES
            ? t('not.access', '停止访问')
            : t('access', '允许访问'),
        name:
          (row as Menu.MenuInfo)?.menuName || (row as Menu.SystemInfo)?.sysName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await updateSystemById({
        sysId: row?.sysId ?? undefined,
        enabledFlag: (row?.enabledFlag ?? ENABLED_FLAG.YES) as 0 | 1,
        sysName: row?.sysName ?? undefined,
        sys2ndName: row?.sys2ndName ?? undefined,
        sysExtName: row?.sysExtName ?? undefined,
        url: row?.url ?? undefined,
        iconUri: row?.iconUri ?? undefined,
        spellNo: row?.spellNo ?? undefined,
        wbNo: row?.wbNo ?? undefined,
        devGroupCode: row?.devGroupCode ?? undefined,
        accessFlag:
          row?.accessFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
        sort: row?.sort ?? undefined,
      });
      if (res?.success) {
        await fetchData();
        ElMessage({
          type: 'success',
          message: t('global:modify.success'),
        });
      }
    });
  }

  /**
   * 查询表格数据
   */
  async function fetchData() {
    loading.value = true;
    let [, result] = await querySystemListByExample({
      enabledFlag:
        enabledFlag.value === ENABLED_FLAG.ALL ? undefined : enabledFlag.value,
      keyWord: keyWord.value,
    });
    loading.value = false;
    if (result?.success) {
      systems.value = result.data;
    }
  }

  onMounted(() => {
    fetchData(); //查询表格数据
  });

  const handleSortEnd = async (data: AnyObject[]) => {
    let [, result] = await updateSystemSortByIds({
      sysSortList: data.map((item, index) => ({
        sysId: item.sysId,
        sort: index + 1,
      })),
    });

    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      fetchData();
    }
  };

  async function clickDropdown(item: SelectOptions) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_SYSTEM,
      bisIds: selections.value.map(
        (item) => item.pageElementId || item.menuId || item.sysId,
      ) as string[],
      dataBaseTypeCode: item.value as string,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  function selectChange(value: AnyObject[]) {
    selections.value = value as Menu.MixSystemMenuElement[];
  }

  function openSystemDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (systemData as DialogData)[key] = data[key];
    });
    addOrEditSystemRef.value.dialogRef.open();
  }

  function openDefinitionDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (systemData as DialogData)[key] = cloneDeep(data[key]);
    });
    systemDefinitionRef.value.dialogRef.open();
  }

  function addClick() {
    openSystemDialog({
      row: {},
      title: t('manageTable.dialog.addSystem', '新增系统'),
    });
  }
</script>
<template>
  <!-- 你的模板内容 -->
  <div class="flex h-full w-full flex-1 flex-col overflow-hidden">
    <div class="mb-2 flex justify-between">
      <span>
        <span class="mr-5">{{ $t('global:enabledFlag') }}</span>
        <el-select
          v-model="enabledFlag"
          @change="
            () => {
              fetchData();
            }
          "
          class="mr-5 w-72"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="keyWord"
          @input="inputChange"
          clearable
          @keydown.enter="fetchData"
          class="mr-5 w-72"
          :placeholder="$t('manageTable.input.placeholder', '请输入关键字查询')"
          :suffix-icon="Search"
        />
      </span>
      <span>
        <el-dropdown>
          <el-button :disabled="!selections.length" class="mr-5">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown v-if="selections.length">
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in props.menuList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="addClick">{{
          $t('global:add')
        }}</el-button>
      </span>
    </div>
    <pro-table
      ref="tableRef"
      row-key="sysId"
      :draggable="true"
      @drag-end="handleSortEnd"
      @selection-change="selectChange"
      :loading="loading"
      :columns="columns"
      :data="systems"
    />
    <AddOrEditSystem
      ref="addOrEditSystemRef"
      v-bind="systemData"
      :init-sort="systems.length + 1"
      @success="onClose"
    />
    <SystemDefinition
      ref="systemDefinitionRef"
      v-bind="systemData"
      :init-sort="systems.length + 1"
      @success="onClose"
    />
  </div>
</template>
