<script setup lang="ts">
  import { ref, useAttrs, watch, computed } from 'vue';
  import { CodeSystemType } from '@/typings/codeManage';
  import { ProForm, ProDialog, useFetchDataset } from 'sun-biz';
  import {
    ENABLED_FLAG,
    FLAG,
    INTERNAL_HOSPITAL_ID,
    MENU_SOURCE_CODE,
  } from '@/utils/constant';
  import {
    addMenu,
    updateMenuById,
    querySystemListByExample,
  } from '../../../api/menu';
  import { debounce } from '@sun-toolkit/shared';
  import { useGetUserInfo } from '@/hooks/useGetUserList';
  import { UserReqItem } from '@/api/types';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  const { t } = useTranslation();

  type Props = {
    row?: {
      menuId?: string;
      enabledFlag?: 0 | 1;
      sysId?: string;
      sysName?: string;
      menuName?: string;
      menu2ndName?: string;
      menuExtName?: string;
      url?: string;
      iconUri?: string;
      menuSourceCode?: string;
      spellNo?: string;
      wbNo?: string;
      assignTo?: string;
      assignToName?: string;
    };
  };

  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [];
  }>();
  const systems = ref<SelectOptions[]>();
  const dialogRef = ref();
  const attrs = useAttrs();
  const formModel = ref();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const { userList, getUserList } = useGetUserInfo();
  let sysIdRemoteMethod = debounce(fetchSystemListByExample, 700); //动态查询系统
  function getBaseInfoData(
    systems: SelectOptions[] = [],
    menuSources: SelectOptions[] = [],
  ) {
    return [
      {
        name: 'sysId',
        label: t('addOrEditMenu.sysName', '所属系统'),
        component: 'select',
        placeholder: t('addOrEditMenu.sysName.placeholder', '请输入系统名称'),
        rules: [
          {
            required: true,
            message: t('addOrEditMenu.sysName.placeholder', '请输入系统名称'),
            trigger: 'change',
          },
        ],
        extraProps: {
          options: systems,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            sysIdRemoteMethod(keyWord);
          },
        },
      },
      {
        name: 'menuName',
        label: t('addOrEditMenu.menuName', '菜单名称'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('addOrEditMenu.menuName.placeholder', '请输入菜单名称'),
        rules: [
          {
            required: true,
            message: t('addOrEditMenu.menuName.placeholder', '请输入菜单名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menu2ndName',
        label: t('addOrEditMenu.menu2ndName', '菜单辅助名称'),
        component: 'input',
        placeholder: t(
          'addOrEditMenu.menu2ndName.placeholder',
          '请输入菜单辅助名称',
        ),
      },
      {
        name: 'menuExtName',
        label: t('addOrEditMenu.menuExtName', '菜单扩展名称'),
        component: 'input',
        placeholder: t(
          'addOrEditMenu.menuExtName.placeholder',
          '请输入菜单扩展名称',
        ),
      },
      {
        name: 'spellNo',
        component: 'input',
        placeholder: t('addOrEditMenu.spellNo.placeholder', '请输入拼音码'),
        label: t('global:spellNo'),
      },
      {
        name: 'wbNo',
        component: 'input',
        placeholder: t('addOrEditMenu.wbNo.placeholder', '请输入五笔码'),
        label: t('global:wbNo'),
      },
      {
        name: 'url',
        component: 'input',
        placeholder: t('addOrEditMenu.url.placeholder', '请输入菜单地址'),
        label: t('addOrEditMenu.url', '菜单地址'),
        rules: [
          {
            required: true,
            message: t('addOrEditMenu.url.placeholder', '请输入菜单地址'),
            trigger: 'change',
          },
        ],
      },
      // {
      //   type: 'row',
      //   name: 'layout',
      //   children: [
      //     {
      //       name: 'iconUri',
      //       component: 'upload',
      //       placeholder: '请选择图标',
      //       defaultValue: '',
      //
      //       label: '图标',
      //     },
      //   ],
      // },
      {
        name: 'assignTo',
        component: 'select',
        placeholder: t('addOrEditMenu.assignTo.placeholder', '请选择菜单运维'),
        label: t('addOrEditMenu.assignTo', '菜单运维'),
        extraProps: {
          clearable: true,
          remote: true,
          filterable: true,
          options: userList.value,
          remoteMethod: (keyWord: string) => {
            getUserList({
              keyWord: keyWord,
              pageSize: 300,
              hospitalId: INTERNAL_HOSPITAL_ID,
            });
          },
          props: {
            label: 'userName',
            value: 'userId',
          },
        },
      },
      {
        name: 'menuSourceCode',
        component: 'select',
        placeholder: t(
          'addOrEditMenu.menuSourceCode.placeholder',
          '请选择菜单来源',
        ),
        label: t('addOrEditMenu.menuSourceCode', '菜单来源'),
        rules: [
          {
            required: true,
            message: t(
              'addOrEditMenu.menuSourceCode.placeholder',
              '请选择菜单来源',
            ),
            trigger: 'change',
          },
        ],
        defaultValue: MENU_SOURCE_CODE.SELF,
        extraProps: {
          filterable: true,
          options: menuSources,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addOrEditMenu.enabledFlag.placeholder',
          '请输入启用状态',
        ),
        label: t('addOrEditMenu.enabledFlag', '启用状态'),
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
    ];
  }

  let baseInfoDescData = computed(() => {
    return getBaseInfoData(systems.value, menuSources.value);
  });

  /**
   * 查询系统列表
   */
  async function fetchSystemListByExample(keyWord: string) {
    let [, result] = await querySystemListByExample({
      keyWord,
      enabledFlag: FLAG.YES,
    });
    if (result?.success) {
      systems.value = result.data.map((item) => {
        return {
          value: item.sysId,
          label: item.sysName,
        };
      });
    }
  }

  const dataSetList = useFetchDataset([CodeSystemType.MENU_SOURCE_CODE]);

  const menuSources = computed(() =>
    (dataSetList.value?.[CodeSystemType.MENU_SOURCE_CODE] || []).map((item) => {
      return {
        value: item.dataValueNo,
        label: item.dataValueCnName,
      };
    }),
  );

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
      if (props.row?.sysId && props.row?.sysName) {
        systems.value = [
          {
            value: props.row?.sysId,
            label: props.row?.sysName,
          },
        ];
      }
      if (
        props.row?.assignTo &&
        !userList.value.some((item) => item.userId === props.row?.assignTo)
      ) {
        userList.value.push({
          userId: props.row?.assignTo,
          userName: props.row?.assignToName || '',
        } as unknown as UserReqItem);
      }
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = props.row?.menuId
            ? await updateMenuById({
                ...(model as unknown as Menu.ResUpdateMenuParams),
                menuId: props.row?.menuId,
              })
            : await addMenu({
                ...(model as unknown as Menu.ResAddMenuParams),
              });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.menuId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      :column="1"
      v-model="formModel"
      ref="formRef"
      :data="baseInfoDescData"
    />
  </ProDialog>
</template>
