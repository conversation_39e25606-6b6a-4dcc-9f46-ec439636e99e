<script setup lang="ts">
  import { ref, nextTick, watch, computed } from 'vue';
  import { ProTable, useFetchDataset } from 'sun-biz';
  import { debounce } from '@sun-toolkit/shared';
  import { useRepositoryColumnConfig } from '../hook/useSystemDefinitionConfig.tsx';
  import { queryCodeRepositoryByExample } from '../../../api/menu';
  import { CODE_REPOSITORY_TYPE_CODE_NAME, FLAG } from '@/utils/constant';
  type Props = {
    selectedList: Menu.CodeRepositoryInfo[];
    changeSelectedList: (data: Menu.CodeRepositoryInfo[]) => void;
  };
  const ALL = 'ALL';
  const props = defineProps<Props>();
  const keyWord = ref<string | undefined>('');
  const loading = ref(true);
  const selectRef = ref();
  const tableData = ref<Menu.CodeRepositoryInfo[]>([]);
  const codeRepositoryTypeCode = ref(ALL);
  const checkedKeys = ref<Menu.CodeRepositoryInfo[]>([]);
  const dataSetList = useFetchDataset([CODE_REPOSITORY_TYPE_CODE_NAME]);
  const tableRef = ref();
  let inputChange = debounce(fetchData, 800);
  function editSelection(selectedList: Menu.CodeRepositoryInfo[]) {
    tableRef.value!.proTableRef.clearSelection();
    selectedList.forEach((info) => {
      tableRef.value!.proTableRef.toggleRowSelection(info, true);
    });
  }

  watch(
    () => props.selectedList,
    () => {
      nextTick(() => {
        editSelection(props.selectedList);
      });
    },
    {
      immediate: true,
      deep: true,
    },
  );

  async function fetchData() {
    loading.value = true;
    let [, result] = await queryCodeRepositoryByExample({
      keyWord: keyWord.value,
      enabledFlag: FLAG.YES,
      codeRepositoryTypeCode:
        codeRepositoryTypeCode.value === ALL
          ? undefined
          : codeRepositoryTypeCode.value,
    });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  }

  function saveClick() {
    props.changeSelectedList(checkedKeys.value);
    selectRef.value.blur();
  }

  function cancelClick() {
    selectRef.value.blur();
    editSelection(props.selectedList);
  }

  // 选中行设置
  function selectionChange(value: Menu.CodeRepositoryInfo[]) {
    checkedKeys.value = value;
  }

  const tableColumn = useRepositoryColumnConfig();
  const radios = computed(() => {
    return dataSetList.value?.[CODE_REPOSITORY_TYPE_CODE_NAME] || [];
  });
</script>
<template>
  <el-select
    class="repository-table-box w-80"
    ref="selectRef"
    :placeholder="
      $t('repository.table.input.placeholder', '请点击这里选择新的代码仓库')
    "
    :loading="loading && !tableData.length"
    :remote="true"
    filterable
    :remote-method="
      (value: string) => {
        keyWord = value;
        inputChange();
      }
    "
  >
    <el-option
      :disabled="true"
      value="init"
      style="height: initial; padding: 0"
    >
      <div style="padding: 0.5rem 0.75rem">
        <el-radio-group
          @change="fetchData"
          v-model="codeRepositoryTypeCode"
          class="mb-0.5"
        >
          <el-radio :value="ALL">{{ $t('global:all') }}</el-radio>
          <el-radio
            v-for="item in radios"
            :key="item.dataValueId"
            :value="item.dataValueNo"
            >{{ item.dataValueNameDisplay }}</el-radio
          >
        </el-radio-group>
        <ProTable
          ref="tableRef"
          :columns="tableColumn"
          :data="tableData"
          row-key="codeRepositoryId"
          :loading="loading"
          highlight-current-row
          @selection-change="selectionChange"
          row-class-name="cursor-pointer"
          max-height="255px"
          class="mb-1"
        />
        <el-button type="primary" @click="saveClick">{{
          $t('global:save')
        }}</el-button>
        <el-button @click="cancelClick">{{ $t('global:cancel') }}</el-button>
      </div>
    </el-option>
  </el-select>
</template>

<style>
  /* stylelint-disable selector-class-pattern */
  .el-select-dropdown__wrap {
    max-height: 370px !important;
  }
</style>
