<script setup lang="tsx">
  import AddOrEditElement from './AddOrEditElement.vue';
  import { Search } from '@element-sun/icons-vue';
  import { ProTable, type AnyObject } from 'sun-biz';
  import { getColumns } from '../config/usePageElementConfig.tsx';
  import { computed } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    queryMenuListByExample,
    queryPageElementByExample,
    updatePageElementEnabledFlagById,
  } from '../../../api/menu.ts';
  import { reactive, ref, watch } from 'vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  import { BIZ_ID_TYPE_CODE, FLAG, ENABLED_FLAG } from '@/utils/constant.ts';
  import { SelectOptions } from '@/typings/common.ts';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  type DialogData = {
    [key: string]: unknown;
  };
  const { t } = useTranslation();
  type Props = {
    propMenuId?: string | undefined;
    propMenuName?: string | undefined;
    menuList: SelectOptions[];
  };
  const props = withDefaults(defineProps<Props>(), {
    propSysId: undefined,
    propMenuId: undefined,
    propMenuName: undefined,
  });
  const menus = ref<SelectOptions[]>([]);
  const elements = ref<Menu.ElementInfo[]>([]);
  const enabledFlag = ref<0 | 1 | -1>(ENABLED_FLAG.ALL);
  const menuId = ref<string>('');
  const menuName = ref<string>('');
  const loading = ref<boolean>(false);
  const keyWord = ref<string>('');
  const tableRef = ref();
  const addOrEditElementRef = ref();
  const elementData = reactive<Menu.MixSystemMenuElement>({});
  const selections = ref<Menu.MixSystemMenuElement[]>([]);
  const options = computed(() => [
    {
      label: t('global:all'),
      value: ENABLED_FLAG.ALL,
    },
    {
      label: t('global:yes'),
      value: ENABLED_FLAG.YES,
    },
    {
      label: t('global:no'),
      value: ENABLED_FLAG.NO,
    },
  ]);

  /**
   * 应用菜单点击元素进入
   */
  watch(
    () => props.propMenuId,
    () => {
      if (props.propMenuId && props.propMenuName) {
        menus.value = [
          {
            value: props.propMenuId as string,
            label: props.propMenuName as string,
          },
        ];
      }
      changeMenuId(props.propMenuId as string);
    },
    {
      immediate: true,
    },
  );

  let inputChange = debounce(refreshMenuTable.bind(null, true), 500);
  const columns = getColumns(isCloudEnv, handleEnableSwitch, openElementDialog);
  function onClose() {
    refreshMenuTable();
  }

  /**
   * 禁用系统或者菜单
   */
  async function getEnableFunction(
    row: Menu.SystemInfo | Menu.MenuInfo | Menu.ElementInfo,
  ) {
    return updatePageElementEnabledFlagById({
      enabledFlag:
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES,
      pageElementId: (row as Menu.ElementInfo).pageElementId,
    });
  }

  function handleEnableSwitch(row: Menu.SystemInfo | Menu.MenuInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: (row as Menu.MenuInfo)?.pageElementName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await getEnableFunction(row);
          if (result?.success) {
            resolve();
            refreshMenuTable();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  /**
   * 查询表格数据
   */
  async function fetchData(params: Code.PageInfo) {
    loading.value = true;
    let [, result] = await queryPageElementByExample({
      enabledFlag:
        enabledFlag.value === ENABLED_FLAG.ALL ? undefined : enabledFlag.value,
      keyWord: keyWord.value,
      menuIds: menuId.value ? [menuId.value] : undefined,
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
    });

    loading.value = false;
    if (result?.success) {
      return {
        data: result?.data,
        total: result?.total,
      };
    }
  }
  function refreshMenuTable(init = false) {
    tableRef?.value?.fetchList(init);
  }

  /**
   * 选择所属菜单
   * @param value
   */
  function changeMenuId(value: string) {
    menuId.value = value;
    let findObj = menus.value.find((cur) => cur.value === value);
    menuName.value = findObj?.label as string;
    refreshMenuTable(true);
  }

  async function fetchMenuList(keyWord: string) {
    let [, result] = await queryMenuListByExample({
      keyWord,
      enabledFlag: FLAG.YES,
      pageNumber: 1,
      pageSize: 50,
    });
    if (result?.success) {
      menus.value = result.data.map((item) => ({
        value: item.menuId,
        label: item.menuName,
      }));
    }
  }

  function getBizIdTypeCode() {
    return BIZ_ID_TYPE_CODE.DICT_PAGE_ELEMENT;
  }

  async function clickDropdown(item: SelectOptions) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: getBizIdTypeCode(),
      bisIds: selections.value.map(
        (item) => item.pageElementId || item.menuId || item.sysId,
      ) as string[],
      dataBaseTypeCode: item.value as string,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  function selectChange(value: AnyObject[]) {
    selections.value = value as Menu.MixSystemMenuElement[];
  }

  function openElementDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (elementData as DialogData)[key] = data[key];
    });
    addOrEditElementRef.value.dialogRef.open();
  }

  function addClick() {
    openElementDialog({
      row: {
        menuId: menuId.value,
        menuName: menuName.value,
      },
      title: t('manageTable.dialog.addMenu', '新增页面元素'),
    });
  }
  let menuIdRemoteMethod = debounce(fetchMenuList, 700); //动态查询系统
</script>
<template>
  <!-- 你的模板内容 -->
  <div class="flex h-full w-full flex-1 flex-col overflow-hidden">
    <div class="mb-2 flex justify-between">
      <span>
        <span class="mr-5">{{
          $t('manageTable.belonging.menuName', '所属菜单')
        }}</span>
        <el-select
          v-model="menuId"
          @change="changeMenuId"
          clearable
          :remote="true"
          :remote-method="menuIdRemoteMethod"
          :filterable="true"
          class="mr-5 w-72"
          :placeholder="
            t('global:placeholder.select.template', {
              name: t('manageTable.belonging.menuName', '所属菜单'),
            })
          "
        >
          <el-option
            v-for="item in menus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span class="mr-5">{{ $t('global:enabledFlag') }}</span>
        <el-select
          v-model="enabledFlag"
          @change="
            () => {
              refreshMenuTable(true);
            }
          "
          class="mr-5 w-72"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="keyWord"
          @input="inputChange"
          clearable
          @keydown.enter="refreshMenuTable(true)"
          class="mr-5 w-72"
          :placeholder="$t('manageTable.input.placeholder', '请输入关键字查询')"
          :suffix-icon="Search"
        />
      </span>
      <span>
        <el-dropdown>
          <el-button :disabled="!selections.length" class="mr-5">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown v-if="selections.length">
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in props.menuList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button :disabled="!isCloudEnv" type="primary" @click="addClick">{{
          $t('global:add')
        }}</el-button>
      </span>
    </div>
    <pro-table
      ref="tableRef"
      class="flex h-full w-full flex-1 flex-col"
      row-key="pageElementId"
      :pagination="true"
      :fetch-data="fetchData"
      @selection-change="selectChange"
      :loading="loading"
      :columns="columns"
    />

    <AddOrEditElement
      ref="addOrEditElementRef"
      v-bind="elementData"
      :init-sort="elements.length + 1"
      @success="onClose"
    />
  </div>
</template>
