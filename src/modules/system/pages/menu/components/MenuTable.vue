<script setup lang="tsx">
  import AddOrEditMenu from './AddOrEditMenu.vue';
  import { Search } from '@element-sun/icons-vue';
  import { ProTable, type AnyObject } from 'sun-biz';
  import { getColumns } from '../config/useMenuTableConfig.tsx';
  import { computed } from 'vue';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    querySystemListByExample,
    queryMenuListByExample,
    updateMenuEnabledFlagById,
  } from '../../../api/menu';
  import { reactive, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { BIZ_ID_TYPE_CODE, FLAG, ENABLED_FLAG } from '@/utils/constant.ts';
  import { SelectOptions } from '@/typings/common.ts';
  type DialogData = {
    [key: string]: unknown;
  };
  const { t } = useTranslation();
  type Props = {
    propSysId?: string | undefined;
    propSysName?: string | undefined;
    menuList: SelectOptions[];
  };
  const emit = defineEmits(['changeSysId', 'changeMenuId']);
  const props = withDefaults(defineProps<Props>(), {
    propSysId: undefined,
    propSysName: undefined,
  });
  const enabledFlag = ref<0 | 1 | -1>(ENABLED_FLAG.ALL);
  const sysId = ref<string>();
  const sysName = ref<string>();
  const systems = ref<SelectOptions[]>([]);
  const loading = ref<boolean>(false);
  const keyWord = ref<string>('');
  const tableRef = ref();
  const addOrEditMenuRef = ref();
  const menuData = reactive<Menu.MixSystemMenuElement>({});
  const selections = ref<Menu.MixSystemMenuElement[]>([]);
  const options = computed(() => [
    {
      label: t('global:all'),
      value: ENABLED_FLAG.ALL,
    },
    {
      label: t('global:yes'),
      value: ENABLED_FLAG.YES,
    },
    {
      label: t('global:no'),
      value: ENABLED_FLAG.NO,
    },
  ]);

  /**
   * 应用系统点击菜单进入
   */
  watch(
    () => props.propSysId,
    () => {
      if (props.propSysId && props.propSysName) {
        systems.value = [
          {
            label: props.propSysName as string,
            value: props.propSysId as string,
          },
        ];
      }

      changeSysId(props.propSysId as string);
    },
    {
      immediate: true,
    },
  );

  let searchChange = debounce(refreshMenuTable.bind(null, true), 700); //关键字查询
  const columns = getColumns(emit, handleEnableSwitch, openMenuDialog);
  function onClose() {
    refreshMenuTable();
  }

  /**
   * 禁用系统或者菜单
   */
  async function getEnableFunction(
    row: Menu.SystemInfo | Menu.MenuInfo | Menu.ElementInfo,
  ) {
    return await updateMenuEnabledFlagById({
      enabledFlag:
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES,
      menuId: (row as Menu.MenuInfo).menuId,
    });
  }

  function handleEnableSwitch(row: Menu.SystemInfo | Menu.MenuInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name:
            (row as Menu.MenuInfo)?.menuName ||
            (row as Menu.SystemInfo)?.sysName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await getEnableFunction(row);
          if (result?.success) {
            resolve();
            refreshMenuTable();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  /**
   * 查询表格数据
   */
  async function fetchData(params: Code.PageInfo) {
    loading.value = true;
    let [, result] = await queryMenuListByExample({
      enabledFlag:
        enabledFlag.value === ENABLED_FLAG.ALL ? undefined : enabledFlag.value,
      keyWord: keyWord.value,
      sysIds: sysId.value ? [sysId.value] : undefined,
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
    });
    loading.value = false;
    return {
      data: result?.data,
      total: result?.total,
    };
  }

  /**
   * 选择所属系统
   * @param value
   */
  function changeSysId(value: string) {
    let findObj = systems.value.find((cur) => cur.value === value);
    sysId.value = value;
    sysName.value = findObj?.label;
    refreshMenuTable(true);
  }

  async function fetchSystemList(keyWord: string) {
    let [, result] = await querySystemListByExample({
      enabledFlag: FLAG.YES,
      keyWord,
    });
    if (result?.success) {
      systems.value = result.data.map((item) => ({
        value: item.sysId,
        label: item.sysName,
      }));
    }
  }

  async function clickDropdown(item: SelectOptions) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_MENU,
      bisIds: selections.value.map(
        (item) => item.pageElementId || item.menuId || item.sysId,
      ) as string[],
      dataBaseTypeCode: item.value as string,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  function selectChange(value: AnyObject[]) {
    selections.value = value as Menu.MixSystemMenuElement[];
  }

  function openMenuDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (menuData as DialogData)[key] = data[key];
    });
    addOrEditMenuRef.value.dialogRef.open();
  }

  function refreshMenuTable(init = false) {
    tableRef?.value?.fetchList(init);
  }

  function addClick() {
    openMenuDialog({
      row: {
        sysId: sysId.value,
        sysName: sysName.value,
      },
      title: t('manageTable.dialog.addMenu', '新增菜单'),
    });
  }
  let sysIdRemoteMethod = debounce(fetchSystemList, 700); //动态查询系统
</script>
<template>
  <!-- 你的模板内容 -->
  <div class="flex h-full w-full flex-1 flex-col overflow-hidden">
    <div class="mb-5 flex justify-between">
      <span>
        <span class="mr-5">{{
          $t('manageTable.belonging.system', '所属系统')
        }}</span>
        <el-select
          v-model="sysId"
          :filterable="true"
          @change="changeSysId"
          :remote="true"
          :remote-method="sysIdRemoteMethod"
          clearable
          class="mr-5 w-72"
          :placeholder="
            t('global:placeholder.select.template', {
              name: t('manageTable.belonging.system', '所属系统'),
            })
          "
        >
          <el-option
            v-for="item in systems"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <span class="mr-5">{{ $t('global:enabledFlag') }}</span>
        <el-select
          v-model="enabledFlag"
          @change="
            () => {
              refreshMenuTable(true);
            }
          "
          class="mr-5 w-72"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="keyWord"
          @input="searchChange"
          clearable
          @keydown.enter="refreshMenuTable(true)"
          class="mr-5 w-72"
          :placeholder="$t('manageTable.input.placeholder', '请输入关键字查询')"
          :suffix-icon="Search"
        />
      </span>
      <span>
        <el-dropdown>
          <el-button :disabled="!selections.length" class="mr-5">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown v-if="selections.length">
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in props.menuList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="addClick">{{
          $t('global:add')
        }}</el-button>
      </span>
    </div>
    <pro-table
      ref="tableRef"
      class="flex h-full w-full flex-1 flex-col"
      row-key="menuId"
      :pagination="true"
      :fetch-data="fetchData"
      @selection-change="selectChange"
      :loading="loading"
      :columns="columns"
    />

    <AddOrEditMenu
      ref="addOrEditMenuRef"
      v-bind="menuData"
      @success="onClose"
    />
  </div>
</template>
