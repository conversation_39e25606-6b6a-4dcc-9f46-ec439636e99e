<script setup lang="ts">
  import { ref, useAttrs, watch, computed } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { debounce } from '@sun-toolkit/shared';
  import { ProForm, ProDialog } from 'sun-biz';
  import { ENABLED_FLAG, FLAG } from '@/utils/constant';
  import {
    updatePageElementById,
    queryMenuListByExample,
    addPageElement,
  } from '../../../api/menu';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common.ts';
  const { t } = useTranslation();

  type Props = {
    row?: {
      pageElementId: string;
      menuId: string;
      menuName: string;
      pageElementNo: string;
      pageElementName: string;
      defaultAllowUseFlag: number;
      enabledFlag: number;
      sort: number;
      spellNo?: string;
      wbNo?: string;
    };
    initSort: number;
  };

  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [];
  }>();
  const menuList = ref<SelectOptions[]>();
  const dialogRef = ref();
  const attrs = useAttrs();
  const formModel = ref();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  let elementRemoteMethod = debounce(fetchMenuListByExample, 700); //动态查询系统
  function getBaseInfoData(menuList: SelectOptions[] = []) {
    return [
      {
        name: 'pageElementNo',
        label: t('addOrEditElement.pageElementNo', '元素编码'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('addOrEditElement.pageElementNo', '元素编码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('addOrEditElement.pageElementNo', '元素编码'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'pageElementName',
        label: t('addOrEditElement.pageElementName', '元素名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('addOrEditElement.pageElementName', '元素名称'),
        }),
        autoConvertSpellNoAndWbNo: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('addOrEditElement.pageElementName', '元素名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'menuId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('addOrEditElement.pageElementName', '所属菜单'),
        }),
        label: t('addOrEditElement.pageElementName', '所属菜单'),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('addOrEditElement.pageElementName', '所属菜单'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          options: menuList,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            elementRemoteMethod(keyWord);
          },
        },
      },
      {
        name: 'defaultAllowUseFlag',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('addOrEditElement.defaultAllowUseFlag', '默认允许使用'),
        }),
        label: t('addOrEditElement.defaultAllowUseFlag', '默认允许使用'),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('addOrEditElement.defaultAllowUseFlag', '默认允许使用'),
            }),
            trigger: 'change',
          },
        ],
        defaultValue: FLAG.NO,
        extraProps: {
          options: [
            {
              value: FLAG.YES,
              label: t('global:yes', '是'),
            },
            {
              value: FLAG.NO,
              label: t('global:no', '否'),
            },
          ],
        },
      },

      {
        name: 'spellNo',
        component: 'input',
        placeholder: t('addOrEditMenu.spellNo.placeholder', '请输入拼音码'),
        label: t('global:spellNo'),
      },
      {
        name: 'wbNo',
        component: 'input',
        placeholder: t('addOrEditMenu.wbNo.placeholder', '请输入五笔码'),
        label: t('global:wbNo'),
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addOrEditMenu.enabledFlag.placeholder',
          '请输入启用状态',
        ),
        label: t('addOrEditMenu.enabledFlag', '启用状态'),
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
    ];
  }

  let baseInfoDescData = computed(() => {
    return getBaseInfoData(menuList.value);
  });

  /**
   * 查询菜单列表
   */
  async function fetchMenuListByExample(keyWord: string) {
    let [, result] = await queryMenuListByExample({
      enabledFlag: FLAG.YES,
      pageNumber: 1,
      pageSize: 50,
      keyWord,
    });
    if (result?.success) {
      menuList.value = result.data.map((item) => ({
        value: item.menuId,
        label: item.menuName,
      }));
    }
  }

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
      if (props.row?.menuId && props.row?.menuName) {
        menuList.value = [
          {
            value: props.row.menuId,
            label: props.row.menuName,
          },
        ];
      }
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        let model = formRef?.value?.model;
        if (valid) {
          let [, result] = props.row?.pageElementId
            ? await updatePageElementById({
                ...model,
                pageElementId: props.row?.pageElementId,
                sort: props.row?.sort || 99,
              } as Menu.UpdateElement)
            : await addPageElement({
                ...(model as unknown as Menu.AddElement),
                sort: props.initSort,
              });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.menuId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      :column="1"
      v-model="formModel"
      ref="formRef"
      :data="baseInfoDescData"
    />
  </ProDialog>
</template>
