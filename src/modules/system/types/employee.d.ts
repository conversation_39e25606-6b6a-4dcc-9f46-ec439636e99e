declare namespace Employee {
  interface Item {
    userId: string;
    userStatusCode: string;
    // ... other properties
  }

  // 临床权限列表项

  export interface CliPermissionReqParam {
    keyWord?: string; // 关键字
    enabledFlag?: number; // 启用标志
    cliPermissionIds?: number[]; // 临床权限标识集合
  }
  export interface CliPermission {
    cliPermissionId: string; // 临床权限标识
    cliPermissionName: string; // 临床权限名称
    codeSystemId: string; // 编码体系标识
    codeSystemNo: string; // 编码体系NO
    codeSystemNameDisplay: string; // 中文名称（语言环境）
    codeSystemName: string; // 中文名称
    codeSystem2ndName: string; // 辅助名称
    codeSystemExtName: string; // 扩展名称
    multiplyCheckFlag: number; // 多选标志
    enabledFlag: number; // 启用标志
    sort: number; // 排序
    cliPermissionValueList: ValuesOfPermissionRange[]; // 临床权限值域列表
  }

  // 临床权限值域列表项目
  export interface ValuesOfPermissionRange {
    cliPermissionValueId: string; // 临床权限值域标识
    cliPermissionValueName: string; // 临床权限值域名称
    dataValueId: string; // 值标识
    dataValueNo: string; // 值编码
    dataValueNameDisplay: string; // 值中文名称（语言环境）
    dataValueCnName: string; // 值中文名称
    dataValue2ndName: string; // 值辅助名称
    dataValueExtName: string; // 值扩展名称
    enabledFlag: number; // 启用标志
    sort: number; // 排序
  }

  // 用户对应临床权限
  export interface UserCliPermissionItem {
    userCliPermissionId: string; // 用户临床权限标识
    cliPermissionValueId: string; // 临床权限值域标识
    codeSystemId: string; // 编码体系标识
    dataValueId: string; // 值标识
  }

  // 保存用户临床权限入参
  export interface SaveUserCliPermissionReqParam {
    userId: string; // 用户标识
    userCliPermissionList?: Omit<
      UserCliPermissionItem,
      'userCliPermissionId'
    >[]; // 用户临床权限列表
  }

  export interface ValueRangeOption {
    label: string;
    value: string;
  }

  // 用户权限表单 form model 类型
  export interface UserCliPermissionFormModel {
    [key: string]: string[] | string;
  }
}
