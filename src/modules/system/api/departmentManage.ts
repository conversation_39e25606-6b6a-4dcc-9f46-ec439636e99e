import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10281-1]	根据条件查询科室列表
 * @param params
 * @returns
 */
export const queryDepartmentListByExampleTree = (
  params: DepartmentManage.RequestParameters,
) => {
  return dictRequest<DepartmentManage.getExampleTree[]>(
    '/organization/queryDepartmentListByExampleTree',
    params,
  );
};

/**
 * [1-10009-1]	根据标识停启用组织
 * @param params
 * @returns
 */
export const updateOrgEnabledFlagById = (
  params: DepartmentManage.RequestParametersUpdateOrgEnabledFlagById,
) => {
  return dictRequest<string>('/organization/updateOrgEnabledFlagById', params);
};
