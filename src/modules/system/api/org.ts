import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10002-1]新增组织
 * @param data
 * @returns
 */
export const addOrg = (params: Org.Item) => {
  return dictRequest<Org.AddResData, Org.Item>(
    '/organization/addOrganization',
    params,
    {
      successMsg: translation('global:create.success'),
    },
  );
};

/**
 * [1-10001-1]根据条件查询组织列表
 * @param params
 * @returns
 */
export const queryOrgList = (params: Partial<Org.queryReqParams>) => {
  return dictRequest<Org.Item[], Partial<Org.queryReqParams>>(
    '/organization/queryOrgListByExample',
    params,
  );
};

/**
 * [1-10140-1]根据条件查询组织列表（平铺)
 * @param params
 * @returns
 */
export const queryFlatOrgList = (params: Partial<Org.queryReqParams>) => {
  return dictRequest<Org.Item[], Partial<Org.queryReqParams>>(
    '/organization/queryOrgListByExampleFlat',
    params,
  );
};

/**
 * [1-10003-1]根据标识修改组织
 * @param params
 * @returns
 */
export const updateOrgItem = (params: Org.Item) => {
  return dictRequest<Org.AddResData, Org.Item>(
    '/organization/updateOrgById',
    params,
    {
      successMsg: translation('global:modify.success'),
    },
  );
};

/**
 * [1-10009-1]根据标识停启用组织
 * @param params
 * @returns
 */
export const updateOrgEnabledFlag = (params: {
  enabledFlag: 0 | 1;
  orgId: string;
}) => {
  return dictRequest<
    undefined,
    {
      enabledFlag: 0 | 1;
      orgId: string;
    }
  >('/organization/updateOrgEnabledFlagById', params, {
    successMsg: translation(
      params.enabledFlag ? 'global:enabled.success' : 'global:disabled.success',
    ),
  });
};

/**
 * [1-10004-1]根据标识修改组织排序
 * @param params
 * @returns
 */
export const updateOrgSort = (params: { orgId: string; sort: number }[]) => {
  return dictRequest<undefined, { orgId: string; sort: number }[]>(
    '/organization/updateOrgSortByIds',
    params,
    {
      successMsg: translation('global:modify.sort.success'),
    },
  );
};

/**
 * [1-10171]获取当前租户对应的集团及医院列表
 * @returns
 */
export const queryOrgAndHospitalList = () => {
  return dictRequest<Org.Item[]>('/organization/queryOrgAndHospitalList');
};

/**
 * [1-10154-1]根据条件查询科室列表
 * @param params
 * @returns
 */
export const queryDepartmentListByExample = (
  params: Org.queryDepartmentParams,
) => {
  return dictRequest<Org.Item[]>(
    '/organization/queryDepartmentListByExample',
    params,
  );
};

/**
 * [1-10155-1]根据条件查询病区列表
 * @param params
 * @returns
 */
export const queryWardListByExample = (params: Org.queryWardParams) => {
  return dictRequest<Org.Item[]>(
    '/organization/queryWardListByExample',
    params,
  );
};

/**
 * [1-10264-1]根据条件查询启用的库房列表
 * @param params
 * @returns
 */
export const queryStorageListByExample = (params: Org.queryStorageParams) => {
  return dictRequest<Org.Storage[]>(
    '/organization/queryStorageListByExample',
    params,
  );
};

/**
 * [1-10285-1]根据条件查询组织列表（平铺分页)
 * @param params
 * @returns
 */
export const queryOrgListByExampleFlat = (
  params: Partial<Org.queryReqFlatPageParams>,
) => {
  return dictRequest<Org.Item[], Partial<Org.queryReqFlatPageParams>>(
    '/organization/queryOrgListByExampleFlat',
    params,
  );
};
