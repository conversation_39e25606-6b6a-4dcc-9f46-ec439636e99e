import { dictRequest } from '@sun-toolkit/request';
// import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10014-1]新增用户
 * @param data
 * @returns
 */
export const addUser = (params: User.EditReqParams) => {
  return dictRequest<{ userId: string; personId: string }>(
    '/user/addUser',
    params,
  );
};

/**
 * [1-10013-1]根据条件查询用户列表
 * @param params
 * @returns
 */
export const queryUserList = (params: User.queryReqParams) => {
  return dictRequest<User.Item, User.queryReqParams>(
    '/user/queryUserListByExample',
    params,
  );
};

/**
 * [1-10013-1]根据条件查询用户列表
 * @param params
 * @returns
 */
export const updateUserById = (params: User.EditReqParams) => {
  return dictRequest('/user/updateUserById', params);
};

/**
 * [1-10238-1]初始化用户密码
 * @returns
 */
export const initPassword = (params: { userIds: string[] }) => {
  return dictRequest('/user/initPasswordByUserId', params);
};

/**
 * [1-10353-1]根据用户标识查询用户登录记录
 * @returns
 */
export const queryUserLoginRecordByUserId = (
  params: User.LoginRecordReqParams,
) => {
  return dictRequest<User.LoginRecordReqItem, User.LoginRecordReqParams>(
    '/user/queryUserLoginRecordByUserId',
    params,
  );
};
