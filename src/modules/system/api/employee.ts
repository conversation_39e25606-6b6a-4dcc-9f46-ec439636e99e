import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10141-1]根据条件查询职工信息列表
 * @param data
 * @returns
 */
export const queryEmployeeListByExample = (params: Employee.ReqParams) => {
  return dictRequest<Employee.Item, Employee.ReqParams>(
    '/employee/queryEmployeeListByExample',
    params,
  );
};

/**
 * [1-10142-1] 新增职工信息
 * @param data
 * @returns
 */
export const addEmployee = (params: Employee.Item) => {
  return dictRequest<Employee.Item, Employee.Item>(
    '/employee/addEmployee',
    params,
    {
      successMsg: translation('global:create.success'),
    },
  );
};

/**
 * [1-10143-1] 更新职工信息
 * @param data
 * @returns
 */
export const updateEmployee = (params: Employee.Item) => {
  return dictRequest<Employee.Item, Employee.Item>(
    '/employee/updateEmployeeById',
    params,
    {
      successMsg: translation('global:modify.success'),
    },
  );
};

/**
 * [1-10395-1] 根据条件查询临床权限及值域列表
 * @param {Employee.CliPermissionReqParam} params
 * @returns
 */
export const queryCliPermissionDictAndValueByExample = (
  params: Employee.CliPermissionReqParam = {},
) => {
  return dictRequest<Employee.CliPermission[], Employee.CliPermissionReqParam>(
    'cliPermission/queryCliPermissionDictAndValueByExample',
    params,
  );
};

/**
 * [1-10377-1] 保存用户临床权限
 * @param {Employee.SaveUserCliPermissionReqParam} params
 * @returns
 */
export const saveUserCliPermission = (
  params: Employee.SaveUserCliPermissionReqParam,
) => {
  return dictRequest<null, Employee.SaveUserCliPermissionReqParam>(
    'cliPermission/saveUserCliPermission',
    params,
  );
};

/**
 * [1-10378-1] 根据用户标识查询临床权限
 * @param { userId: string } params
 * @returns
 */
export const queryUserCliPermissionByUserId = (params: { userId: string }) => {
  return dictRequest<Employee.UserCliPermissionItem[], { userId: string }>(
    'cliPermission/queryUserCliPermissionByUserId',
    params,
  );
};
