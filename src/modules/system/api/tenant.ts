import { dictRequest } from '@sun-toolkit/request';
/**
 * [1-10083-1]根据条件查询租户管理列表
 * @param params
 * @returns
 */
export const queryTenantListByExample = (params: Tenant.QueryParams) => {
  return dictRequest<Tenant.UpsertTenantParams[]>(
    '/tenant/queryTenantListByExample',
    params,
  );
};
/**
 * [1-10085-1]根据标识修改租户信息
 * @param params
 * @returns
 */
export const updateTenantById = (params: Tenant.UpsertTenantParams) => {
  return dictRequest<{ tenantId: string }>('/tenant/updateTenantById', params);
};
/**
 * [1-10084-1]新增租户
 * @param params
 * @returns
 */
export const addTenant = (params: Tenant.UpsertTenantParams) => {
  return dictRequest<{ tenantId: string }>('/tenant/addTenant', params);
};
