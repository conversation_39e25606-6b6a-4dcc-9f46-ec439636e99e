import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10367-1]  根据条件查询临床服务列表及计费设置
 * @param data
 * @returns
 */
export const queryClinicalServiceListAndChargeSettingByExample = (
  params: MsChargeSetting.SearchParams & MsChargeSetting.Query,
) => {
  return dictRequest<
    MsChargeSetting.ServiceList,
    MsChargeSetting.SearchParams & MsChargeSetting.Query
  >(
    '/mschargetypesetting/queryClinicalServiceListAndChargeSettingByExample',
    params,
  );
};

/**
 * [1-10368-1]  根据条件查询服务的计费设置列表
 * @param data
 * @returns
 */
export const queryMsChargeSettingListByExample = (
  params: MsChargeSetting.SettingListReqParam,
) => {
  return dictRequest<
    MsChargeSetting.MsChargeTypeSetting[],
    MsChargeSetting.SettingListReqParam
  >('/mschargetypesetting/queryMsChargeSettingListByExample', params);
};

/**
 * [1-10369-1]  新增服务的计费设置
 * @param params
 * @returns
 */
export const addMsChargeSetting = (
  params: MsChargeSetting.MsChargeSaveReqParam,
) => {
  return dictRequest<
    { msChargeTypeSettingId: string /* 医疗服务计费方式设置标识 */ },
    MsChargeSetting.MsChargeSaveReqParam
  >('/mschargetypesetting/addMsChargeSetting', params);
};

/**
 * [1-10370-1]  根据标识停启用使用中的服务设置
 * @param params
 * @returns
 */
export const updateEnabledFlagById = (
  params: MsChargeSetting.IUpdateEnabledFlagReqParam,
) => {
  return dictRequest<null>(
    '/mschargetypesetting/updateEnabledFlagById',
    params,
  );
};

/**
 * [1-10371-1]  根据条件查询服务的计费对象列表
 * @param params
 * @returns
 */
export const queryMsChargeObjectListByExample = (
  params: MsChargeSetting.MsChargeObjectReqParam,
) => {
  return dictRequest<MsChargeSetting.MsChargeTypeSupported[]>(
    '/mschargetypesetting/queryMsChargeObjectListByExample',
    params,
  );
};

/**
 * [1-10147-1] 根据条件查询医院的收费项目列表(业务态)
 * @param data
 * @returns
 */
export const queryHospitalChargeItemListByExample = (
  params: MsChargeSetting.ChargeItemList,
) => {
  return dictRequest<MsChargeSetting.ListOfPaidDrugs[]>(
    '/chargeItem/queryHospitalChargeItemListByExample',
    params,
  );
};

/**
 * [1-10236-1] 根据条件查询医院的药品商品列表
 * @param data
 * @returns
 */
export const queryHospitalMedicineListByExample = (
  params: MsChargeSetting.ChargeItemList,
) => {
  return dictRequest<MsChargeSetting.ListOfPaidDrugs[]>(
    '/medicine/queryHospitalMedicineListByExample',
    params,
  );
};
