import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10031-1]根据条件查询系统角色列表
 * @param data
 * @returns
 */
export const querySystemRoleByExample = (params: {
  keyWord?: string;
  enabledFlag?: 0 | 1;
  roleIds?: string[];
  hospitalId: string;
}) => {
  return dictRequest<Role.SystemRoleInfo[]>(
    '/systemRole/querySystemRoleByExample',
    params,
  );
};

/**
 * [1-10032-1]新增系统角色
 * @param data
 * @returns
 */
export const addSystemRole = (params: Role.ReqSystemRole) => {
  return dictRequest<boolean>('/systemRole/addSystemRole', params);
};

/**
 * [1-10033-1]根据标识修改系统角色
 * @param data
 * @returns
 */
export const updateSystemRoleById = (params: Role.ReqUpdateSystemRoleById) => {
  return dictRequest<boolean>('/systemRole/updateSystemRoleById', params);
};

/**
 * [1-10034-1]保存系统角色的权限信息
 * @param data
 * @returns
 */
export const saveSystemRolePermission = (
  params: Role.ReqSaveSystemRolePermission,
) => {
  return dictRequest<Employee.Item>(
    '/systemRole/saveSystemRolePermission',
    params,
  );
};
