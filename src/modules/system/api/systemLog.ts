import { logRequest } from '@sun-toolkit/request';

/**
 * [94-10001-1]保存日志
 * @param params
 * @returns
 */
export const saveLog = (params: SystemLog.SaveLogReqParams) => {
  return logRequest<SystemLog.SaveLogReqItem>('/logManage/saveLog', params);
};

/**
 * [94-10002-1]根据条件查询日志列表
 * @param params
 * @returns
 */
export const queryLogListByExample = (params: SystemLog.LogReqParams) => {
  return logRequest<SystemLog.LogReqItem, SystemLog.LogReqParams>(
    '/logManage/queryLogListByExample',
    params,
  );
};

/**
 * [94-10003-1]根据条件清空日志
 * @param params
 * @returns
 */
// export const deleteLogByExample = (params: SystemLog.DeleteLogReqParams) => {
//   return logRequest<SystemLog.DeleteLogReqItem>('/logManage/deleteLogByExample', params);
// };

/**
 * [94-10004-1]根据条件下载日志
 * @param params
 * @returns
 */
export const downloadLogByExample = (
  params: SystemLog.DownloadLogReqParams,
) => {
  return logRequest<SystemLog.DownloadLogReqItem>(
    '/logManage/downloadLogByExample',
    params,
  );
};

/**
 * [94-10005-1]根据标识查询日志详情
 * @param params
 * @returns
 */
export const queryLogDetailById = (params: SystemLog.LogDetailReqParams) => {
  return logRequest<SystemLog.LogDetailReqItem>(
    '/logManage/queryLogDetailById',
    params,
  );
};

/**
 * [94-10006-1]保存日志()
 * @param params
 * @returns
 */
export const saveLogForClient = (
  params: SystemLog.SaveLogForClientReqParams,
) => {
  return logRequest<SystemLog.SaveLogForClientReqItem>(
    '/logManage/saveLogForClient',
    params,
  );
};
