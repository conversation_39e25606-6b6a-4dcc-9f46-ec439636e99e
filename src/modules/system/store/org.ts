import { defineStore } from 'pinia';
import { queryOrgList } from '@modules/system/api/org';

/**
 * @description 使用递归扁平化菜单，方便添加动态路由
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 */
export function getFlatOrgList(orgList: Org.Item[]): Org.Item[] {
  const newMenuList: Org.Item[] = JSON.parse(JSON.stringify(orgList));
  return newMenuList.flatMap((item) => [
    item,
    ...(item.subOrgList ? getFlatOrgList(item.subOrgList) : []),
  ]);
}

export const useOrgStore = defineStore({
  id: 'dict-orgManage',
  state: (): { orgList: Org.Item[] } => ({
    // 组织及联列表
    orgList: [],
  }),
  getters: {
    flatOrgList: (state) => getFlatOrgList(state.orgList),
  },
  actions: {
    // Get AuthButtonList
    async getOrgList(params: Partial<Org.queryReqParams>) {
      const [, res] = await queryOrgList(params);
      this.orgList = res ? res.data : [];
    },
    async setSuborgList(id: string, data: Org.Item[]) {
      this.orgList.forEach((item) => {
        if (item.orgId === id) {
          item.subOrgList = data;
        }
      });
    },
  },
});
