declare namespace RegistrationType {
  interface QueryParams {
    keyWord: string;
    enabledFlag?: number;
    regTypeIds?: string[];
    checkRegValidityPeriodFlag?: number;
  }
  interface SaveReqParams {
    regTypeId?: string;
    registrationTypeName: string;
    registrationType2ndName: string;
    registrationTypeExtName: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    defaultFlag: number;
    sort: number;
    validityPeriodCalcRuleCode: string;
    validityPeriodCalcRuleDesc: string;
    regValidityPeriod: number;
  }
  interface TableResResult extends SaveReqParams {
    regTypeId: string;
  }
  interface SortReqParams {
    regTypeId: string;
    sort: number;
  }
}
