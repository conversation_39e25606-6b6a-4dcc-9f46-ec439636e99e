declare namespace NoonType {
  interface NoonTypeReqParams {
    keyWord?: string;
    enabledFlag?: number;
    hospitalId: string;
    nooTypeSetId?: string;
    schedulingAt?: string;
  }

  interface NoonTypeReqItem {
    nooTypeSetId: string;
    nooTypeSetName: string;
    enabledFlag: number;
    startAt: string;
    endAt?: string;
    hospitalId: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    noonTypeList: {
      noonTypeId: string;
      noonTypeCode: string;
      noonTypeName: string;
      noonType2ndName?: string;
      noonTypeExtName?: string;
      noonTypeNameDisplay: string;
      forwordTime: number;
      startTime: string;
      endTime: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface NoonTypeItemReqItem {
    noonTypeId: string;
    noonTypeCode: string;
    noonTypeName: string;
    noonType2ndName?: string;
    noonTypeExtName?: string;
    noonTypeNameDisplay: string;
    forwordTime: number;
    startTime: string;
    endTime: string;
    enabledFlag: number;
    sort: number;
  }

  interface SaveNoonTypeSetReqParams {
    nooTypeSetId?: string;
    nooTypeSetName: string;
    enabledFlag: number;
    startAt: string;
    endAt?: string;
    hospitalId: string;
    noonTypeList: {
      noonTypeId?: string;
      noonTypeCode: string;
      noonTypeName: string;
      noonType2ndName?: string;
      noonTypeExtName?: string;
      forwordTime: number;
      startTime: string;
      endTime: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface SaveNoonTypeSetReqItem {
    nooTypeSetId: string;
  }

  interface UpdateNoonTypeSetReqParams {
    nooTypeSetId: string;
    enabledFlag: number;
  }

  interface UpdateNoonTypeReqParams {
    nooTypeId: string;
    enabledFlag: number;
  }
}
