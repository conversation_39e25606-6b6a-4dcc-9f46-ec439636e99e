declare namespace AcademicMajorCategory {
  interface QueryParams {
    keyWord: string;
    enabledFlag?: number;
  }
  interface SaveReqParams {
    esCategoryId?: string;
    esCategoryName: string;
    esCategory2ndName: string;
    esCategoryExtName: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    sort: number;
  }
  interface TableResResult extends SaveReqParams {
    sort: number;
    esCategoryId: string;
  }
}
