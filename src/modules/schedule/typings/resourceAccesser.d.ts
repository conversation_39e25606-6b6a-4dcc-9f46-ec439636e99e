declare namespace Channel {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    dictEncResAccesserIds?: string[];
  }
  interface DictEncResAccesser {
    dictEncResAccesserId?: string;
    dictEncResAccesserName?: string;
    dictEncResAccesser2ndName?: string;
    dictEncResAccesserExtName?: string;
    dictEncResAccesserNameDisplay?: string;
    editableFlag?: number;
    enabledFlag?: number;
    sort?: number;
    spellNo?: string;
    wbNo?: string;
    createdOrgLocationId?: string;
    createdOrgLocationName?: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt?: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    isCloudenv?: boolean;
  }
}
