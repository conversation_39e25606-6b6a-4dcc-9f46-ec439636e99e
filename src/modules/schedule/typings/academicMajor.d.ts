declare namespace AcademicMajor {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    hospitalId: string;
    academicMajorId?: string;
    // deptId?: string;
    deptIds?: string[];
    pageSize: number;
    pageNumber: number;
  }

  interface AcademicMajorInfo {
    academicMajorId?: string;
    academicMajorName?: string;
    academicMajor2ndName?: string;
    academicMajorExtName?: string;
    academicMajorNameDisplay?: string;
    enabledFlag?: number;
    deptId?: string;
    deptName?: string;
    sort?: number;
    spellNo?: string;
    wbNo?: string;
    createdOrgLocationId?: string;
    createdOrgLocationName?: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt?: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    esProviderList?: EsProviderInfo[];
  }

  interface EsProviderInfo {
    esProviderTypeCode?: string;
    esProviderTypeDesc?: string;
    providerId?: string;
    providerNo?: string;
    providerName?: string;
    sort?: number;
    encounterServiceList?: EncounterServiceInfo[];
  }

  interface EncounterServiceInfo {
    msId?: string;
    esProviderTypeDesc?: string;
    msNo?: string;
    msName?: string;
    ms2ndName?: string;
    msExtName?: string;
    msNameDisplay?: string;
    msTypeCode?: string;
    wbNo?: string;
    spellNo?: string;
    esChargeStandardId?: string;
    esChargeStandardNameDisplay?: string;
    createdOrgLocationId?: string;
    createdUserId?: string;
    createdAt?: string;
    modifiedOrgLocationId?: string;
    modifiedUserId?: string;
    modifiedAt?: string;
    enabledFlag?: number;
    sort?: number;
    limitEsSourceFlag?: number;
    autoFreeFlag?: number;
    esProvideTimePeriodList: EsProvideTimePeriodInfo[];
  }

  interface EsProvideTimePeriodInfo {
    timeList?: (string | undefined)[];
    esProvideTimePeriodId?: string;
    startTime?: string;
    endTime?: string;
  }

  interface AcademicMajorAddInfo extends EsProviderInfo {
    isAddCol?: boolean;
    isEditCol?: boolean;
    isAddRow?: boolean;
  }
}
