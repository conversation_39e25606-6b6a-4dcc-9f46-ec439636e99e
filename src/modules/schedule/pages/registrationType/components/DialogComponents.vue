<script setup lang="tsx">
  import { ref } from 'vue';
  import { ProDialog, ProForm, convertToWbNo, convertToSpellNo } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import {
    ENABLED_FLAG,
    VALIDITY_PERIOD_CALC_RULE_CODE,
  } from '@/utils/constant';
  import { type FormInstance, ElMessage } from 'element-sun';
  import { useSaveFormConfig } from '../config/useFormConfig.tsx';
  import { saveRegisterType } from '@/modules/schedule/api/registrationType.ts';
  const { t } = useTranslation();
  const props = defineProps<{
    mode: string;
    sort: number;
  }>();
  const dialogRef = ref();
  const innerForm = {
    regTypeId: '',
    registrationTypeName: '',
    registrationType2ndName: '',
    registrationTypeExtName: '',
    spellNo: '',
    wbNo: '',
    enabledFlag: ENABLED_FLAG.YES,
    defaultFlag: 0,
    sort: 1,
    validityPeriodCalcRuleCode: VALIDITY_PERIOD_CALC_RULE_CODE.DAY,
    regValidityPeriod: undefined,
  };
  const dialogForm = ref<RegistrationType.SaveReqParams>(
    innerForm as unknown as RegistrationType.SaveReqParams,
  );
  const formRef = ref<{ ref: FormInstance }>({} as { ref: FormInstance });
  const emits = defineEmits<{ success: [] }>();
  const handleConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid: unknown) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            sort: props.sort,
          };
          const [, res] = await saveRegisterType(params, props.mode);
          if (res?.success) {
            ElMessage.success(
              t(
                dialogForm.value.regTypeId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };
  const setFormData = (value: string) => {
    dialogForm.value = {
      ...dialogForm.value,
      spellNo: convertToSpellNo(value),
      wbNo: convertToWbNo(value),
    };
  };
  const handleClose = () => {
    dialogForm.value = {
      ...innerForm,
      registrationTypeName: '',
      enabledFlag: ENABLED_FLAG.YES,
      defaultFlag: 0,
    } as unknown as RegistrationType.SaveReqParams;
  };
  const formConfig = useSaveFormConfig(setFormData, dialogForm);
  defineExpose({ dialogRef, dialogForm });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${t(`global:${props.mode}`)}${t('registrationType.name', '挂号类别')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="handleConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
    @close="handleClose"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="3"
      :data="formConfig"
    />
  </ProDialog>
</template>
