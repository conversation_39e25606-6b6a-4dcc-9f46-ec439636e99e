<script setup lang="ts">
  import { ref } from 'vue';
  import {
    FLAG,
    ENABLED_FLAG,
    VALIDITY_PERIOD_CALC_RULE_CODE,
  } from '@/utils/constant';
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useTableConfig } from './config/useTableConfig';
  import { useSearchFormConfig } from './config/useFormConfig';
  import DialogComponents from './components/DialogComponents.vue';
  import {
    queryRegisterTypeListByExample,
    saveRegisterType,
    updateRegisterTypeSortById,
  } from '@/modules/schedule/api/registrationType.ts';
  const loading = ref(false);
  const { t } = useTranslation();
  const tableData = ref<RegistrationType.TableResResult[]>([]);
  const searchParams = ref<RegistrationType.QueryParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const dialogMode = ref('');
  const dialogRef = ref();
  const handleOpenDialog = (
    mode: string,
    data?: RegistrationType.SaveReqParams,
  ) => {
    dialogMode.value = mode;
    if (mode === 'edit') {
      dialogRef.value.dialogForm = JSON.parse(
        JSON.stringify({
          ...data,
          validityPeriodCalcRuleCode: data?.validityPeriodCalcRuleCode
            ? data.validityPeriodCalcRuleCode
            : VALIDITY_PERIOD_CALC_RULE_CODE.DAY,
        }),
      );
    }
    dialogRef.value.dialogRef.open();
  };
  const handleSearch = async (data?: RegistrationType.QueryParams) => {
    searchParams.value = {
      ...searchParams.value,
      ...(data ?? {}),
    };
    const params = {
      ...searchParams.value,
      checkRegValidityPeriodFlag: FLAG.NO,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryRegisterTypeListByExample(params);
    if (res?.success) {
      tableData.value = (res.data || []).sort((a, b) => a.sort - b.sort);
    }
  };
  const handleEnableSwitch = async (row: RegistrationType.SaveReqParams) => {
    ElMessageBox.confirm(
      t('global:confirm.switch', '是否确认切换状态？'),
      t('global:confirm', '提示'),
      {
        confirmButtonText: t('global:confirm', '确定'),
        cancelButtonText: t('global:cancel', '取消'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await saveRegisterType(params, 'edit');
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        await handleSearch();
      }
    });
  };
  const handleSortEnd = async (list: RegistrationType.TableResResult[]) => {
    const regTypeSortList = (list || []).map((item, index) => ({
      regTypeId: item.regTypeId,
      sort: index + 1,
    }));
    await updateRegisterTypeSortById({ regTypeSortList });
    ElMessage({
      type: 'success',
      message: t('global:modify.sort.success'),
    });
    await handleSearch();
  };
  handleSearch({ keyWord: '' });
  const searchConfig = useSearchFormConfig(handleSearch);
  const tableColumns = useTableConfig(handleEnableSwitch, handleOpenDialog);
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('registrationType.list.title', '挂号类别列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="handleSearch"
        />
      </div>
      <div>
        <el-button type="primary" @click="handleOpenDialog('add')">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <ProTable
      ref="tableRef"
      row-key="regTypeId"
      :loading="loading"
      :data="tableData"
      :columns="tableColumns"
      draggable
      @drag-end="handleSortEnd"
    />
    <DialogComponents
      :mode="dialogMode"
      ref="dialogRef"
      @success="handleSearch"
      :sort="tableData.length + 1"
    />
  </div>
</template>
