import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';
import {
  ENABLED_FLAG,
  VALIDITY_PERIOD_CALC_RULE_CODE,
  VALIDITY_PERIOD_CALC_RULE_CODE_NAME,
} from '@/utils/constant';
export function useSearchFormConfig(
  handleSearch: (params?: RegistrationType.QueryParams) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              handleSearch({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
}
export function useSaveFormConfig(
  callback: (value: string) => void,
  dialogForm: Ref<RegistrationType.SaveReqParams>,
) {
  return useFormConfig({
    dataSetCodes: [VALIDITY_PERIOD_CALC_RULE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('registrationType.registrationTypeName', '名称'),
        name: 'registrationTypeName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.registrationTypeName', '名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('registrationType.esCategoryName', '名称'),
            }),
            trigger: 'blur',
          },
        ],
        extraProps: {
          onblur: (e: Event) => {
            callback((e.target as HTMLInputElement).value);
          },
        },
      },
      {
        label: t('registrationType.registrationType2ndName', '辅助名称'),
        name: 'registrationType2ndName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.registrationType2ndName', '辅助名称'),
        }),
      },
      {
        label: t('registrationType.registrationTypeExtName', '扩展名称'),
        name: 'registrationTypeExtName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.registrationTypeExtName', '扩展名称'),
        }),
      },
      {
        label: t('registrationType.spellNo', '拼音码'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.spellNo', '拼音码'),
        }),
      },
      {
        label: t('registrationType.wbNo', '五笔码'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.wbNo', '五笔码'),
        }),
      },
      {
        label: t('registrationType.validityPeriodCalcRuleCode', '挂号效期规则'),
        name: 'validityPeriodCalcRuleCode',
        component: 'select',
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'registrationType.validityPeriodCalcRuleCode',
                '挂号效期规则',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'registrationType.validityPeriodCalcRuleCode',
            '挂号效期规则',
          ),
        }),
        extraProps: {
          options: dataSet?.value
            ? dataSet.value?.[VALIDITY_PERIOD_CALC_RULE_CODE_NAME]
            : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('registrationType.regValidityPeriod', '挂号有效期'),
        name: 'regValidityPeriod',
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('registrationType.regValidityPeriod', '挂号有效期'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            validator: (
              rule: unknown,
              value: string,
              callback: (error?: Error) => void,
            ) => {
              if (!value) {
                callback();
                return;
              }
              const num = Number(value);
              if (isNaN(num) || num <= 0 || !Number.isInteger(num)) {
                callback(
                  new Error(
                    t('global:validate.positiveInteger', '请输入正整数'),
                  ),
                );
              } else if (num > 99) {
                callback(
                  new Error(t('validate.maxValue.template', '输入最大值为99')),
                );
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        placeholder: t('global:placeholder.input.template', {
          content: t('registrationType.regValidityPeriod', '挂号有效期'),
        }),
        render: () => {
          return (
            <div class={'flex items-center'}>
              <el-input
                model-value={dialogForm.value.regValidityPeriod}
                onInput={(val: number) => {
                  dialogForm.value.regValidityPeriod = val;
                }}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'registrationType.regValidityPeriod',
                    '挂号有效期',
                  ),
                })}
              ></el-input>
              <span class={'ml-3 whitespace-nowrap'}>
                {dialogForm.value?.validityPeriodCalcRuleCode ===
                VALIDITY_PERIOD_CALC_RULE_CODE.DAY
                  ? t('day', '天')
                  : dialogForm.value?.validityPeriodCalcRuleCode ===
                      VALIDITY_PERIOD_CALC_RULE_CODE.HOUR
                    ? t('hour', '小时')
                    : ''}
              </span>
            </div>
          );
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag', '启用标志'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}
