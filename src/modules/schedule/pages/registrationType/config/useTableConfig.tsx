import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG, VALIDITY_PERIOD_CALC_RULE_CODE } from '@/utils/constant';
export function useTableConfig(
  handleEnableSwitch: (row: RegistrationType.SaveReqParams) => void,
  handleOpenDialog: (
    mode: string,
    data?: RegistrationType.SaveReqParams,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        minWidth: 60,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('registrationType.registrationTypeName', '名称'),
        minWidth: 100,
        prop: 'registrationTypeName',
      },
      {
        label: t('registrationType.registrationType2ndName', '辅助名称'),
        minWidth: 100,
        prop: 'registrationType2ndName',
      },
      {
        label: t('registrationType.registrationTypeExtName', '扩展名称'),
        minWidth: 100,
        prop: 'registrationTypeExtName',
      },
      {
        label: t('registrationType.spellNo', '拼音码'),
        minWidth: 100,
        prop: 'spellNo',
      },
      {
        label: t('registrationType.wbNo', '五笔码'),
        minWidth: 100,
        prop: 'wbNo',
      },
      {
        label: t('registrationType.regValidityPeriod', '挂号有效期'),
        minWidth: 100,
        prop: 'regValidityPeriod',
        render: (row: RegistrationType.SaveReqParams) => (
          <div>
            {row.regValidityPeriod && row.validityPeriodCalcRuleCode
              ? row.regValidityPeriod +
                (row.validityPeriodCalcRuleCode ===
                VALIDITY_PERIOD_CALC_RULE_CODE.DAY
                  ? t('day', '天')
                  : row.validityPeriodCalcRuleCode ===
                      VALIDITY_PERIOD_CALC_RULE_CODE.HOUR
                    ? t('hour', '小时')
                    : '')
              : '--'}
          </div>
        ),
      },
      {
        label: t('global:enabledFlag', '启用状态'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: RegistrationType.SaveReqParams) => (
          <>
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              before-change={() => handleEnableSwitch(row)}
            />
          </>
        ),
      },
      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        minWidth: 100,
        render: (row: RegistrationType.SaveReqParams) => (
          <>
            <el-button
              type="primary"
              link
              onClick={() => handleOpenDialog('edit', row)}
            >
              {t('global:edit')}
            </el-button>
          </>
        ),
      },
    ],
  });
}
