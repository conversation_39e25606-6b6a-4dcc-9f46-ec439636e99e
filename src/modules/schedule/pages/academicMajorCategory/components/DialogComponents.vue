<script setup lang="ts">
  import { ref } from 'vue';
  import { ProDialog, ProForm, convertToWbNo, convertToSpellNo } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { type FormInstance, ElMessage } from 'element-sun';
  import { useSaveFormConfig } from '../config/useFormConfig.tsx';
  import { saveEsCategory } from '@/modules/schedule/api/academicMajorCategory.ts';
  const { t } = useTranslation();
  const props = defineProps<{
    mode: string;
    sort: number;
  }>();
  const dialogRef = ref();
  const innerForm = {
    esCategoryId: '',
    esCategoryName: '',
    esCategory2ndName: '',
    esCategoryExtName: '',
    spellNo: '',
    wbNo: '',
    enabledFlag: ENABLED_FLAG.YES,
    sort: 1,
  };
  const dialogForm = ref<AcademicMajorCategory.SaveReqParams>(innerForm);
  const formRef = ref<{ ref: FormInstance }>({} as { ref: FormInstance });
  const emits = defineEmits<{ success: [] }>();
  const handleConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid: unknown) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            sort: dialogForm.value.sort || props.sort,
          };
          const [, res] = await saveEsCategory(params, props.mode);
          if (res?.success) {
            ElMessage.success(
              t(
                dialogForm.value.esCategoryId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };
  const setFormData = (value: string) => {
    dialogForm.value = {
      ...dialogForm.value,
      spellNo: convertToSpellNo(value),
      wbNo: convertToWbNo(value),
    };
  };
  const handleClose = () => {
    dialogForm.value = {
      ...innerForm,
      esCategoryName: '',
    };
  };
  const formConfig = useSaveFormConfig(setFormData);
  defineExpose({ dialogRef, dialogForm });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${t(`global:${props.mode}`)}${t('academicMajorCategory.name', '学科专业分类')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="handleConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
    @close="handleClose"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :columns="3"
      :data="formConfig"
    />
  </ProDialog>
</template>
