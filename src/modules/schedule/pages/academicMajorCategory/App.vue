<script setup lang="ts">
  import { ref } from 'vue';
  import { commonSort } from '@/api/common';
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useTableConfig } from './config/useTableConfig.tsx';
  import { useSearchFormConfig } from './config/useFormConfig.tsx';
  import DialogComponents from './components/DialogComponents.vue';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import {
    queryEsCategoryListByExample,
    saveEsCategory,
  } from '@/modules/schedule/api/academicMajorCategory';
  const loading = ref(false);
  const { t } = useTranslation();
  const tableData = ref<AcademicMajorCategory.TableResResult[]>([]);
  const searchParams = ref<AcademicMajorCategory.QueryParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const dialogMode = ref('');
  const dialogRef = ref();
  const handleOpenDialog = (
    mode: string,
    data?: AcademicMajorCategory.SaveReqParams,
  ) => {
    dialogMode.value = mode;
    if (mode === 'edit') {
      dialogRef.value.dialogForm = JSON.parse(JSON.stringify(data));
    }
    dialogRef.value.dialogRef.open();
  };
  const handleSearch = async (data?: AcademicMajorCategory.QueryParams) => {
    searchParams.value = {
      ...searchParams.value,
      ...(data ?? {}),
    };
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryEsCategoryListByExample(params);
    if (res?.success) {
      tableData.value = (res.data || []).sort((a, b) => a.sort - b.sort);
    }
  };
  const handleEnableSwitch = async (
    row: AcademicMajorCategory.SaveReqParams,
  ) => {
    ElMessageBox.confirm(
      t('global:confirm.switch', '是否确认切换状态？'),
      t('global:confirm', '提示'),
      {
        confirmButtonText: t('global:confirm', '确定'),
        cancelButtonText: t('global:cancel', '取消'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await saveEsCategory(params, 'edit');
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        await handleSearch();
      }
    });
  };
  const handleSortEnd = async (
    list: AcademicMajorCategory.TableResResult[],
  ) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.esCategoryId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_ES_CATEGORY,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await handleSearch();
    }
  };
  handleSearch({ keyWord: '' });
  const searchConfig = useSearchFormConfig(handleSearch);
  const tableColumns = useTableConfig(handleEnableSwitch, handleOpenDialog);
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('academicMajorCategory.list.title', '学科专业分类')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="handleSearch"
        />
      </div>
      <div>
        <el-button type="primary" @click="handleOpenDialog('add')">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <ProTable
      ref="tableRef"
      row-key="esCategoryId"
      :loading="loading"
      :data="tableData"
      :columns="tableColumns"
      draggable
      @drag-end="handleSortEnd"
    />
    <DialogComponents
      :mode="dialogMode"
      ref="dialogRef"
      @success="handleSearch"
      :sort="tableData.length + 1"
    />
  </div>
</template>
