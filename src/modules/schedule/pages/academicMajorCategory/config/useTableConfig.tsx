import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
export function useTableConfig(
  handleEnableSwitch: (row: AcademicMajorCategory.SaveReqParams) => void,
  handleOpenDialog: (
    mode: string,
    data?: AcademicMajorCategory.SaveReqParams,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        minWidth: 60,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('academicMajorCategory.esCategoryName', '名称'),
        minWidth: 100,
        prop: 'esCategoryName',
      },
      {
        label: t('academicMajorCategory.esCategory2ndName', '辅助名称'),
        minWidth: 100,
        prop: 'esCategory2ndName',
      },
      {
        label: t('academicMajorCategory.esCategoryExtName', '扩展名称'),
        minWidth: 100,
        prop: 'esCategoryExtName',
      },
      {
        label: t('academicMajorCategory.spellNo', '拼音码'),
        minWidth: 100,
        prop: 'spellNo',
      },
      {
        label: t('academicMajorCategory.wbNo', '五笔码'),
        minWidth: 100,
        prop: 'wbNo',
      },
      {
        label: t('global:enabledFlag', '启用状态'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: AcademicMajorCategory.SaveReqParams) => (
          <>
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              before-change={() => handleEnableSwitch(row)}
            />
          </>
        ),
      },
      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        minWidth: 100,
        render: (row: AcademicMajorCategory.SaveReqParams) => (
          <>
            <el-button
              type="primary"
              link
              onClick={() => handleOpenDialog('edit', row)}
            >
              {t('global:edit')}
            </el-button>
          </>
        ),
      },
    ],
  });
}
