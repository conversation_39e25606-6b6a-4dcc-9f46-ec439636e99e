import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
export function useSearchFormConfig(
  handleSearch: (params?: AcademicMajorCategory.QueryParams) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              handleSearch({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
}
export function useSaveFormConfig(callback: (value: string) => void) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('academicMajorCategory.esCategoryName', '名称'),
        name: 'esCategoryName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.esCategoryName', '名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('academicMajorCategory.esCategoryName', '名称'),
            }),
            trigger: 'blur',
          },
        ],
        extraProps: {
          onblur: (e: Event) => {
            callback((e.target as HTMLInputElement).value);
          },
        },
      },
      {
        label: t('academicMajorCategory.esCategory2ndName', '辅助名称'),
        name: 'esCategory2ndName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.esCategory2ndName', '辅助名称'),
        }),
      },
      {
        label: t('academicMajorCategory.esCategoryExtName', '扩展名称'),
        name: 'esCategoryExtName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.esCategoryExtName', '扩展名称'),
        }),
      },
      {
        label: t('academicMajorCategory.spellNo', '拼音码'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.spellNo', '拼音码'),
        }),
      },
      {
        label: t('academicMajorCategory.wbNo', '五笔码'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.wbNo', '五笔码'),
        }),
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}
