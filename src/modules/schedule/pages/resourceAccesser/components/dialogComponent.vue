<script setup lang="ts" name="dialogComponent">
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useChannelConfig } from '../config/useFormConfigData';
  import { type FormInstance } from 'element-sun';
  import { FLAG, ENABLED_FLAG } from '@/utils/constant';
  import { saveEncResAccesser } from '@modules/schedule/api/resourceAccesser';
  import { ProForm, ProDialog } from 'sun-biz';
  import { ref, nextTick, computed } from 'vue';

  type FormModelType = {
    dictEncResAccesserName: string | undefined;
    spellNo: string | undefined;
    wbNo: string | undefined;
    dictEncResAccesser2ndName: string | undefined;
    dictEncResAccesserExtName: string | undefined;
    enabledFlag: ENABLED_FLAG;
    editableFlag: FLAG;
  };

  const emit = defineEmits(['success']);
  const props = defineProps<{
    rowValue: Channel.DictEncResAccesser | undefined;
    dialogTitle: string;
    isCloudEnv: boolean;
  }>();

  const dialogRef = ref(); //弹窗dialogRef
  const formModelRef = ref<{
    ref: FormInstance;
  }>(); //formRef
  const formModel = ref<FormModelType>({
    dictEncResAccesserName: undefined,
    spellNo: undefined,
    wbNo: undefined,
    dictEncResAccesser2ndName: undefined,
    dictEncResAccesserExtName: undefined,
    enabledFlag: ENABLED_FLAG.YES,
    editableFlag: props.isCloudEnv ? FLAG.NO : FLAG.YES,
  });

  const isCloudEnv = computed(() => props.isCloudEnv);

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value.open();
      formModel.value = {
        dictEncResAccesserName: rowValue?.dictEncResAccesserName ?? undefined,
        spellNo: rowValue?.spellNo ?? undefined,
        wbNo: rowValue?.wbNo ?? undefined,
        dictEncResAccesser2ndName:
          rowValue?.dictEncResAccesser2ndName ?? undefined,
        dictEncResAccesserExtName:
          rowValue?.dictEncResAccesserExtName ?? undefined,
        enabledFlag: (rowValue?.enabledFlag ??
          ENABLED_FLAG.YES) as ENABLED_FLAG,
        editableFlag:
          rowValue?.editableFlag ?? (props.isCloudEnv ? FLAG.NO : FLAG.YES),
      };
    });
  };

  // 提交
  const handleConfirmSubmit = async () => {
    await formModelRef.value?.ref.validate();
    const params = {
      ...props.rowValue,
      ...formModel?.value,
    };
    return await saveEncResAccesser(params);
  };

  // form配置
  const channelConfig = useChannelConfig({ isCloudEnv: isCloudEnv });

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <ProForm
      ref="formModelRef"
      layout-mode="column"
      :column="3"
      v-model="formModel"
      :data="channelConfig"
    />
  </ProDialog>
</template>
