import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(options: {
  queryChannelData: (params: {
    keyWord?: string | undefined;
    enabledFlag?: ENABLED_FLAG;
  }) => Promise<void>;
}) {
  const { queryChannelData } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        className: 'mb-0',
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword.list'),
        className: 'w-60 mb-0',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryChannelData({
                keyWord: (e.target as HTMLInputElement).value,
              } as unknown as AcademicMajor.QueryParams);
            }
          },
        },
      },
    ],
  });
  return data;
}
