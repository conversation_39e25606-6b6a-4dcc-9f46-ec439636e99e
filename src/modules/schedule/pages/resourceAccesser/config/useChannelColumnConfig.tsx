import { FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

// 来源table的tsx
export function useChannelColumnConfig(
  isCloudEnv: boolean | undefined,
  handleEnableSwitch: (row: Channel.DictEncResAccesser) => Promise<void>,
  operationFn: (row: Channel.DictEncResAccesser) => Promise<void>,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
        width: 40,
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('channel.table.name', '号源使用渠道名称'),
        prop: 'dictEncResAccesserName',
        minWidth: 200,
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global:secondName'),
        prop: 'dictEncResAccesser2ndName',
        minWidth: 150,
      },
      {
        label: t('global:thirdName'),
        prop: 'dictEncResAccesserExtName',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('channel.table.allowEdit', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 150,
        render: (row: Channel.DictEncResAccesser) => {
          return (
            <el-checkbox
              modelValue={row.editableFlag === FLAG.YES}
              disabled={true}
            />
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: Channel.DictEncResAccesser) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 150,
        render: (row: Channel.DictEncResAccesser) => {
          return (
            <el-button
              onClick={() => operationFn(row)}
              type={
                isCloudEnv === false && row.editableFlag === FLAG.NO
                  ? ''
                  : 'primary'
              }
              link={true}
              disabled={isCloudEnv === false && row.editableFlag === FLAG.NO}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
