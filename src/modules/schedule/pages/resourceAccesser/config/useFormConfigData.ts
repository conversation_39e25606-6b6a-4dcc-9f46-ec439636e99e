import { ComputedRef } from 'vue';
import { useFormConfig } from 'sun-biz';
import { FLAG, ENABLED_FLAG } from '@/utils/constant';
export function useChannelConfig(options: {
  isCloudEnv: ComputedRef<boolean>;
}) {
  const { isCloudEnv } = options;

  //弹窗form
  return useFormConfig({
    getData: (t) => [
      {
        name: 'dictEncResAccesserName',
        label: t('global:name'),
        autoConvertSpellNoAndWbNo: true,
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        name: 'dictEncResAccesser2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'dictEncResAccesserExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'editableFlag',
        label: t('bizSource.table.allowEdit', '允许编辑'),
        component: 'checkbox',
        isHidden: !isCloudEnv.value,
        extraProps: {
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}
