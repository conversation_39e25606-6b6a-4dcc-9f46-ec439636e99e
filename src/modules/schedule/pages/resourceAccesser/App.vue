<script setup lang="ts" name="resourceAccesser">
  import { ENABLED_FLAG, DICT_ENC_RES_ACCESSER } from '@/utils/constant';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from './config/useSearchConfigData';
  import { useChannelColumnConfig } from './config/useChannelColumnConfig';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ref, onMounted, computed } from 'vue';
  import {
    saveEncResAccesser,
    queryDictEncResAccesserByExample,
    updateDictEncResAccesserSortByIds,
  } from '@/modules/schedule/api/resourceAccesser';

  import {
    Title,
    ProForm,
    ProTable,
    MAIN_APP_CONFIG,
    useAppConfigData,
    DmlButton,
  } from 'sun-biz';
  import dialogComponent from './components/dialogComponent.vue';

  export type searchFormType = {
    keyWord: string | undefined;
    enabledFlag: ENABLED_FLAG;
  };

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();

  const loading = ref(false); //加载状态
  const draggableFlag = ref(true); //是否拖拽
  const rowValue = ref<Channel.DictEncResAccesser>(); //当前行信息
  const bizData = ref<string[]>([]);
  /** 检索条件 */
  const searchFormModel = ref<searchFormType>({
    keyWord: undefined,
    enabledFlag: ENABLED_FLAG.ALL,
  });
  /** table数据 */
  const tableData = ref<Channel.DictEncResAccesser[]>([]);

  const dialogRef = ref(); //弹窗Ref

  // 标题
  const dialogTitle = computed(() => {
    return rowValue.value?.dictEncResAccesserId
      ? t('channel.manage.title.edit', '编辑号源使用渠道')
      : t('channel.manage.title.add', '新增号源使用渠道');
  });

  // 号源使用渠道列表
  const queryChannelData = async (
    val: {
      keyWord?: string | undefined;
      enabledFlag?: ENABLED_FLAG;
    } = {},
  ) => {
    searchFormModel.value = {
      ...searchFormModel.value,
      ...val,
    };
    const params = {
      keyWord: searchFormModel.value.keyWord,
      enabledFlag:
        searchFormModel.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchFormModel.value.enabledFlag,
    };
    loading.value = true;
    const [, res] = await queryDictEncResAccesserByExample(params);
    loading.value = false;
    if (res?.data) {
      tableData.value = res.data ?? [];
      draggableFlag.value =
        searchFormModel.value.keyWord ||
        searchFormModel.value.enabledFlag !== ENABLED_FLAG.ALL
          ? false
          : true;
    }
  };

  //拖拽排序
  const handleSortEnd = async (list: Channel.DictEncResAccesser[]) => {
    const arr: Channel.DictEncResAccesser[] = [];
    list?.forEach(async (item, index) => {
      arr.push({
        dictEncResAccesserId: item.dictEncResAccesserId,
        sort: index + 1,
      });
    });
    await updateDictEncResAccesserSortByIds({
      dictEncResAccesserList: arr,
    } as unknown as Channel.DictEncResAccesser[]);
    await queryChannelData();
  };

  // 启用状态切换
  const handleEnableSwitch = async (row: Channel.DictEncResAccesser) => {
    const params = {
      ...row,
      enabledFlag:
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES,
    };
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.dictEncResAccesserName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await saveEncResAccesser(params);
      if (res?.success) {
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
        await queryChannelData();
      }
    });
  };

  // 打开弹窗
  const operationFn = async (row: Channel.DictEncResAccesser) => {
    rowValue.value = row;
    dialogRef.value.open();
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryChannelData: queryChannelData,
  });
  const channelColumns = useChannelColumnConfig(
    isCloudEnv,
    handleEnableSwitch,
    operationFn,
  );

  //获取选中数据
  const selectionChange = (val: Channel.DictEncResAccesser[]) => {
    bizData.value = val.map((item) => {
      return item.dictEncResAccesserId || '';
    });
  };
  // 初始获取
  onMounted(async () => {
    await queryChannelData();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('channel.manage.title.list', '号源使用渠道列表')"
      class="mb-2"
    />
    <!-- 检索项 -->
    <div class="flex justify-between">
      <div :class="['flex-1', draggableFlag ? 'mb-2' : 'mb-4']">
        <ProForm
          v-model="searchFormModel"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryChannelData"
        />
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="DICT_ENC_RES_ACCESSER"
          class="mr-3"
        ></DmlButton>
        <el-button type="primary" @click="operationFn">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <!-- 号源使用渠道列表 -->
    <pro-table
      :data="tableData"
      :columns="channelColumns"
      :loading="loading"
      :draggable="draggableFlag"
      row-key="dictEncResAccesserId"
      @drag-end="handleSortEnd"
      @selection-change="selectionChange"
    />
    <dialogComponent
      ref="dialogRef"
      :row-value="rowValue"
      :dialog-title="dialogTitle"
      :is-cloud-env="isCloudEnv as boolean"
      @success="queryChannelData"
    />
  </div>
</template>
