import { ref } from 'vue';
import { dayjs } from '@sun-toolkit/shared';
import { ENCOUNTER_TYPE_CODE, FLAG } from '@/utils/constant';
import { queryRegisterTypeListByExample } from '@modules/baseConfig/api/registerType';
import { queryHospitalChargeItemListByExample } from '@/modules/finance/api/chargeItem';

/** 获取医院收费项目 */
export function useHospitalChargeItemList() {
  const esChargeItemList = ref<ChargeItem.HospitalChargeItemInfoItem[]>([]);
  const getHospitalChargeItemList = async (
    params: ChargeItem.QueryParamsHospitalChargeItem,
  ) => {
    const paramsCon = {
      ...{
        pageNumber: 1,
        pageSize: 100,
        encounterTypeCode: ENCOUNTER_TYPE_CODE.OUTPATIENT,
        priceAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      },
      ...params,
    };
    const [, res] = await queryHospitalChargeItemListByExample(paramsCon);
    if (res?.success) {
      esChargeItemList.value = res.data ?? [];
    }
  };
  return {
    esChargeItemList,
    getHospitalChargeItemList,
  };
}

/** 获取医院挂号类别 */
export function useRegisterTypeList() {
  const registerTypeList = ref<RegisterType.RegTypeInfo[]>([]);
  const getRegisterTypeList = async (keyWord: string = '') => {
    const [, res] = await queryRegisterTypeListByExample({
      keyWord,
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      registerTypeList.value = res.data ?? [];
    }
  };
  return {
    registerTypeList,
    getRegisterTypeList,
  };
}
