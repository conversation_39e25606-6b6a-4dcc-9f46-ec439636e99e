import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import {
  filterSelectData,
  formatDecimalNumber,
  dayjs,
} from '@sun-toolkit/shared';
import { ref, Ref, ComputedRef } from 'vue';

// 来源table的tsx
export function useEsChargeStandardColumnConfig(
  handleEnableSwitch: (
    row: EsChargeStandard.EsChargeStandardInfo,
  ) => Promise<void>,
  editRow: (row: EsChargeStandard.EsChargeStandardInfo) => Promise<void>,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('esChargeStandard.table.regTypeName', '挂号类型'),
        prop: 'regTypeName',
        minWidth: 200,
      },
      {
        label: t(
          'esChargeStandard.table.esChargeStandardNameDisplay',
          '收费标准名称',
        ),
        minWidth: 150,
        prop: 'esChargeStandardNameDisplay',
      },
      {
        label: t('global:secondName'),
        minWidth: 150,
        prop: 'esChargeStandard2ndName',
      },
      {
        label: t('global:thirdName'),
        minWidth: 150,
        prop: 'esChargeStandardExtName',
      },
      {
        label: t('global:amt'),
        minWidth: 150,
        prop: 'amt',
        autoFormatterNumber: true,
      },
      {
        label: t('esChargeStandard.table.commodityName', '收费项目明细'),
        prop: 'commodityName',
        width: 400,
        render: (row: EsChargeStandard.EsChargeStandardInfo) => {
          return (
            <>
              <div class="flex gap-2">
                {row.hospitalChargeItemList?.map((item) => (
                  <el-tag
                    type={
                      dayjs().isAfter(item.endAt) ||
                      item.enabledFlag === ENABLED_FLAG.NO
                        ? 'info'
                        : 'primary'
                    }
                    size="small"
                  >
                    {item.commodityName}*{item.num}
                  </el-tag>
                ))}
              </div>
            </>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: EsChargeStandard.EsChargeStandardInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 150,
        render: (row: EsChargeStandard.EsChargeStandardInfo) => {
          return (
            <el-button type="primary" link onClick={() => editRow(row)}>
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

// 收费明细
export function useEsChargeItemConfig(options: {
  tableRef: Ref<TableRef, TableRef>;
  data: Ref<EsChargeStandard.HospitalChargeItemInfo[] | undefined>;
  id: string;
  hospitalId: ComputedRef<string>;
  esChargeItemList: Ref<ChargeItem.HospitalChargeItemInfoItem[]>;
  getHospitalChargeItemList: (
    params: ChargeItem.QueryParamsHospitalChargeItem,
  ) => Promise<void>;
}) {
  const { t } = useTranslation();
  const data = ref(options.data);
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef: options.tableRef,
    data: data as Ref<
      (EsChargeStandard.HospitalChargeItemInfo & {
        editable: boolean;
      })[]
    >,
    id: options.id,
  });

  // 自定义校验收费项目名称
  const hospitalCommodityIdValidate = (
    rule: unknown,
    value: unknown,
    callback: (data?: Error | undefined) => void,
  ) => {
    const hospitalChargeItemList = data.value?.filter(
      (item) => item.hospitalCommodityId === value,
    );
    if (!value) {
      return callback(
        new Error(
          t('global:placeholder.select.template', {
            name: t(
              'esChargeStandard.table.dialog.commodityName',
              '收费项目名称',
            ),
          }),
        ),
      );
    }
    if ((hospitalChargeItemList ?? [])?.length > 1) {
      return callback(
        new Error(
          t(
            'esChargeStandard.table.dialog.commodityName.notReply',
            '收费项目名称不能重复',
          ),
        ),
      );
    }
    callback();
  };

  // 数量自定义校验
  const numValidate = (
    rule: unknown,
    value: unknown,
    callback: (data?: Error | undefined) => void,
  ) => {
    if (!value) {
      return callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('global:quantity'),
          }),
        ),
      );
    }
    if (!(Number.isInteger(Number(value)) && (Number(value) as number) > 0)) {
      return callback(
        new Error(
          t('esChargeStandard.table.dialog.num.valid', '数量需为正整数'),
        ),
      );
    }
    callback();
  };

  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('esChargeStandard.table.dialog.commodityName', '收费项目名称'),
        prop: 'hospitalCommodityId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            validator: hospitalCommodityIdValidate,
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
            price: string | number | null | undefined;
            unitId?: string;
            unitName?: string;
          },
        ) => {
          if (row.editable === true) {
            return (
              <el-select
                v-model={row.hospitalCommodityId}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'esChargeStandard.table.dialog.commodityName',
                    '收费项目名称',
                  ),
                })}
                onChange={(val: string) => {
                  row.hospitalCommodityId = val;
                  const obj = options.esChargeItemList.value.find(
                    (item) => item.hospitalCommodityId === val,
                  );
                  row.commodityName = obj?.commodityNameDisplay;
                  row.price = obj?.price;
                  row.unitId = obj?.unitId;
                  row.unitName = obj?.unitName;
                  row.num = 1;
                }}
                onClear={() => {
                  row.hospitalCommodityId = undefined;
                  row.commodityName = undefined;
                  row.price = undefined;
                  row.unitId = undefined;
                  row.unitName = undefined;
                  row.num = undefined;
                }}
                filterable={true}
                remote={true}
                remote-method={async (val: string) => {
                  if (options.hospitalId.value) {
                    await options.getHospitalChargeItemList({
                      hospitalId: options.hospitalId.value,
                      keyWord: val,
                    } as ChargeItem.QueryParamsHospitalChargeItem);
                  }
                }}
              >
                {filterSelectData(
                  options.esChargeItemList.value,
                  data.value?.filter(
                    (item) =>
                      item.hospitalCommodityId !== row.hospitalCommodityId,
                  ),
                  'hospitalCommodityId',
                )?.map((item: ChargeItem.HospitalChargeItemInfoItem) => (
                  <el-option
                    key={item?.hospitalCommodityId}
                    label={`${item?.commodityNameDisplay}(${formatDecimalNumber(item?.price as string | number) ?? '--'}/${item?.unitName ?? ''})`}
                    value={item?.hospitalCommodityId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.commodityName}</>;
          }
        },
      },
      {
        label: t('global:quantity', '数量'),
        prop: 'num',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            validator: numValidate,
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable === true) {
            return (
              <el-input
                v-model={row.num}
                type="number"
                clearable={false}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:quantity', '数量'),
                })}
              />
            );
          } else {
            return <>{row.num}</>;
          }
        },
      },
      {
        label: t('esChargeStandard.table.dialog.price', '单价'),
        prop: 'price',
        minWidth: 150,
        autoFormatterNumber: true,
      },
      {
        label: t('esChargeStandard.table.dialog.unit', '单位'),
        prop: 'unitName',
        minWidth: 150,
      },
      {
        label: t('esChargeStandard.table.dialog.startAt', '生效时间'),
        prop: 'startAt',
        minWidth: 260,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('esChargeStandard.table.dialog.startAt', '生效时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable === true) {
            return (
              <el-date-picker
                v-model={row.startAt}
                type="datetime"
                clearable={false}
                format={'YYYY-MM-DD HH:mm:ss'}
                value-format={'YYYY-MM-DD HH:mm:ss'}
                placeholder={t('global:placeholder.select.template', {
                  name: t('esChargeStandard.table.dialog.startAt', '生效时间'),
                })}
                disabled-date={(time: Date) => {
                  return (
                    row.endAt &&
                    dayjs(time).isAfter(
                      dayjs(row.endAt).format('YYYY-MM-DD HH:mm:ss'),
                      'second',
                    )
                  );
                }}
              />
            );
          } else {
            return <>{row.startAt}</>;
          }
        },
      },
      {
        label: t('esChargeStandard.table.dialog.endAt', '失效时间'),
        prop: 'endAt',
        minWidth: 260,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('esChargeStandard.table.dialog.endAt', '失效时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable === true) {
            return (
              <el-date-picker
                v-model={row.endAt}
                type="datetime"
                format={'YYYY-MM-DD HH:mm:ss'}
                value-format={'YYYY-MM-DD HH:mm:ss'}
                clearable={false}
                placeholder={t('global:placeholder.select.template', {
                  name: t('esChargeStandard.table.dialog.endAt', '失效时间'),
                })}
                disabled-date={(time: Date) => {
                  return (
                    row.startAt &&
                    dayjs(time).isBefore(
                      dayjs(row.startAt).format('YYYY-MM-DD HH:mm:ss'),
                      'second',
                    )
                  );
                }}
              />
            );
          } else {
            return <>{row.endAt}</>;
          }
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => {
                row.enabledFlag =
                  row.enabledFlag === ENABLED_FLAG.YES
                    ? ENABLED_FLAG.NO
                    : ENABLED_FLAG.YES;
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 150,
        render: (
          row: EsChargeStandard.HospitalChargeItemInfo & {
            editable: boolean;
          },
          $index: number,
        ) => {
          if (row.editable === true) {
            return (
              <div class={'flex items-center justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => toggleEdit(row)}
                >
                  {t('global:confirm')}
                </el-button>
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => cancelEdit(row, $index)}
                >
                  {t('global:cancel')}
                </el-button>
              </div>
            );
          } else {
            return (
              <el-button
                type="primary"
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
            );
          }
        },
      },
    ],
  });
  return { addItem, tableConfig };
}
