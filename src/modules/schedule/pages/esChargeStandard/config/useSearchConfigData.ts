import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(
  queryEsChargeStandardData: (
    params: EsChargeStandard.QueryParams,
  ) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:content'),
        }),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryEsChargeStandardData({
                keyWord: (e.target as HTMLInputElement).value,
              } as unknown as AcademicMajor.QueryParams);
            }
          },
        },
      },
    ],
  });
  return data;
}
