import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant';

export function useEsChargeStandardConfig(
  getRegisterTypeList: (keyWord: string) => Promise<void>,
  registerTypeList: Ref<RegisterType.RegTypeInfo[]>,
) {
  //弹窗form
  const esChargeStandardDialogForm = useFormConfig({
    getData: (t) => {
      return [
        {
          name: 'esChargeStandardName',
          label: t('global:name'),
          component: 'input',
          isFullWidth: true,
          placeholder: t('global:placeholder.input.template', {
            content: t('global:name'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('global:name'),
              }),
              trigger: 'change',
            },
          ],
        },
        {
          name: 'esChargeStandard2ndName',
          label: t('global:secondName'),
          component: 'input',
          isFullWidth: true,
          placeholder: t('global:placeholder.input.template', {
            content: t('global:secondName'),
          }),
        },
        {
          name: 'esChargeStandardExtName',
          label: t('global:thirdName'),
          component: 'input',
          isFullWidth: true,
          placeholder: t('global:placeholder.input.template', {
            content: t('global:thirdName'),
          }),
        },
        {
          name: 'regTypeId',
          label: t('esChargeStandard.dialog.regTypeLabel', '挂号类别'),
          component: 'select',
          isFullWidth: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('esChargeStandard.dialog.regTypeLabel', '挂号类别'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('esChargeStandard.dialog.regTypeLabel', '挂号类别'),
              }),
              trigger: 'change',
            },
          ],
          extraProps: {
            options: registerTypeList.value,
            remote: true,
            filterable: true,
            remoteShowSuffix: true,
            remoteMethod: getRegisterTypeList,
            props: {
              label: 'registrationTypeNameDisplay',
              value: 'regTypeId',
            },
          },
        },
        {
          name: 'enabledFlag',
          label: t('global:enabledFlag'),
          component: 'switch',
          isFullWidth: true,
          extraProps: {
            'inline-prompt': true,
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
          },
        },
      ];
    },
  });
  return {
    esChargeStandardDialogForm,
  };
}
