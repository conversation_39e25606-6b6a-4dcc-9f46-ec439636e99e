<script setup lang="ts" name="esChargeStandard">
  import { FLAG } from '@/utils/constant.ts';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ref, computed } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from './config/useSearchConfigData.ts';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useEsChargeStandardColumnConfig } from './config/useEsChargeStandardColumnConfig';
  import {
    queryEsChargeStandardListByExample,
    updateEsChargeStandardEnabledFlagById,
  } from '@/modules/schedule/api/esChargeStandard';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import dialogComponent from '@modules/schedule/pages/esChargeStandard/components/dialogComponent.vue';

  type searchForm = {
    hospitalId: string;
    keyWord: string | undefined;
  };

  const { t } = useTranslation();

  const loading = ref(false); //加载状态
  const rowValue = ref<EsChargeStandard.EsChargeStandardInfo>(); //当前行数据
  /** 查询条件 */
  const searchParams = ref<searchForm>({
    hospitalId: '',
    keyWord: undefined,
  });
  /** table数据 */
  const esChargeStandardList = ref<EsChargeStandard.EsChargeStandardInfo[]>([]);

  const dialogRef = ref(); //新增就诊服务收费标准弹窗

  /** 弹窗标题 */
  const dialogTitle = computed(() => {
    return rowValue.value?.esChargeStandardId
      ? t('edit.esChargeStandard.manage.title', '编辑就诊服务收费标准')
      : t('add.esChargeStandard.manage.title', '新增就诊服务收费标准');
  });

  /** 获取就诊服务收费标准列表 */
  const queryEsChargeStandardData = async (
    params: EsChargeStandard.QueryParams = {},
  ) => {
    searchParams.value = {
      ...searchParams.value,
      ...params,
    };

    loading.value = true;
    const [, res] = await queryEsChargeStandardListByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.data) {
      esChargeStandardList.value = res.data;
    }
  };
  /** 启用状态变化 */
  const handleEnableSwitch = async (
    row: EsChargeStandard.EsChargeStandardInfo,
  ) => {
    const params = {
      esChargeStandardId: row.esChargeStandardId as string,
      enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
    };
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.esChargeStandardNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await updateEsChargeStandardEnabledFlagById(params);
      if (res?.success) {
        row.enabledFlag = row.enabledFlag === 1 ? 0 : 1;
        ElMessage.success(
          t(
            row.enabledFlag === 1
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };
  /**编辑当前行 */
  const editRow = async (row: EsChargeStandard.EsChargeStandardInfo = {}) => {
    rowValue.value = cloneDeep(row);
    dialogRef.value.open();
  };

  /** 搜索form配置 */
  const searchConfig = useSearchFormConfig(queryEsChargeStandardData);
  /** table配置 */
  const esChargeStandardColumns = useEsChargeStandardColumnConfig(
    handleEnableSwitch,
    editRow,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      class="mb-2"
      :title="$t('esChargeStandard.manage.title.list', '就诊服务收费标准列表')"
    />
    <div class="flex justify-between">
      <ProForm
        v-model="searchParams"
        layout-mode="inline"
        :data="searchConfig"
        :show-search-button="true"
        @model-change="queryEsChargeStandardData"
      />
      <el-button type="primary" @click="() => editRow()">{{
        $t('global:add')
      }}</el-button>
    </div>
    <pro-table
      :data="esChargeStandardList"
      :columns="esChargeStandardColumns"
      :loading="loading"
    />
    <dialogComponent
      ref="dialogRef"
      :row-value="rowValue"
      :dialog-title="dialogTitle"
      :hospital-id="searchParams.hospitalId"
      @success="queryEsChargeStandardData"
    />
  </div>
</template>
