/** 职工列表查询条件配置文件 */
import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { EMPLOYEE_TYPE_CODE } from '@/utils/constant';

export function useSearchFormConfig(options: {
  orgList: Ref<Org.FlatOrgReqItem[]>;
  fetchData: () => Promise<void>;
  queryDepList: (keyWord?: string) => Promise<void>;
}) {
  const { queryDepList, fetchData, orgList } = options;

  const data = useFormConfig({
    dataSetCodes: [EMPLOYEE_TYPE_CODE],
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          className: 'w-72',
          clearable: false,
        },
      },
      {
        label: t('person.belongDeptName', '所属科室'),
        name: 'deptIds',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('person.belongDeptName', '所属科室'),
        }),
        extraProps: {
          className: 'w-72',
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: orgList.value,
          remoteMethod: queryDepList,
          props: {
            label: 'orgNameDisplay',
            value: 'orgId',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-72',
        extraProps: {
          prefixIcon: 'Search',
          onKeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              (e.target as HTMLInputElement).blur();
              await fetchData();
            }
          },
        },
      },
    ],
  });
  return data;
}
