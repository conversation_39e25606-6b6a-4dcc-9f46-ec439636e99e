import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { queryAcademicMajorByExample } from '@/modules/schedule/api/academicMajor';
import { queryEsChargeStandardListByExample } from '@/modules/schedule/api/esChargeStandard';

// 学科专业数据
export function useAcademicMajorData() {
  // 加载状态
  const loading = ref(false);
  //  学科专业
  const academicMajorData = ref<AcademicMajor.AcademicMajorInfo[]>([]);
  // 学科专业总数
  const total = ref<number>(0);
  // 查询学科专业列表;

  const queryAcademicMajorData = async (params: AcademicMajor.QueryParams) => {
    loading.value = true;
    const defaultParams = {
      pageNumber: 1,
      pageSize: ONE_PAGE_SIZE,
    };
    params = {
      ...defaultParams,
      ...params,
    };
    const [, res] = await queryAcademicMajorByExample(params);
    loading.value = false;
    if (res?.success) {
      academicMajorData.value = res.data;
      total.value = res.total;
    }
  };
  return {
    queryAcademicMajorData,
    academicMajorData,
    total,
    loading,
  };
}

// 收费标准数据
export function useEsChargeStandardData() {
  const loading = ref(false);
  const esChargeStandardList = ref<EsChargeStandard.EsChargeStandardInfo[]>([]);
  const getEsChargeStandardList = async (
    params: EsChargeStandard.QueryParams,
  ) => {
    params = {
      ...{
        pageNumber: 1,
        pageSize: ONE_PAGE_SIZE,
      },
      ...params,
    };
    loading.value = true;
    const [, res] = await queryEsChargeStandardListByExample(params);
    loading.value = false;
    if (res?.success) {
      esChargeStandardList.value = (res.data ?? []).filter(
        (item) => item.enabledFlag === FLAG.YES,
      );
    }
  };
  return {
    esChargeStandardList,
    getEsChargeStandardList,
    loading,
  };
}
