import { ref } from 'vue';
import { queryFlatOrgList } from '@/api/common.ts';
import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { ORG_TYPE_CODE, FLAG } from '@/utils/constant.ts';

export function useGetOrgFlatList() {
  /** 加载状态 */
  const loading = ref(false);
  /** 组织信息 */
  const orgList = ref<Org.FlatOrgReqItem[]>([]);
  const getFlatOrgList = async (
    params: Omit<Org.FlatOrgReqParams, 'pageNumber' | 'pageSize'>,
  ) => {
    loading.value = true;
    const defaultParams = {
      pageNumber: 1,
      pageSize: ONE_PAGE_SIZE,
      enabledFlag: FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.DEPARTMENT],
    };
    params = {
      ...defaultParams,
      ...params,
    };
    const [, res] = await queryFlatOrgList(params);
    loading.value = false;
    if (res?.success) {
      orgList.value = res.data;
    }
  };
  return {
    getFlatOrgList,
    orgList,
    loading,
  };
}
