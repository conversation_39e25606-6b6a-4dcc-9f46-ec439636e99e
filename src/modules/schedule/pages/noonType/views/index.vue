<script setup lang="ts" name="noonType">
  import { ref } from 'vue';
  import { FLAG } from '@/utils/constant.ts';
  import { useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from '../config/useSearchFormConfig.ts';
  import { type TableColumnCtx } from 'element-sun/es/components/table/src/table-column/defaults';
  import { useTableColumnsConfig } from '../config/useTableColumnsConfig.tsx';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    queryNoonTypeSetListByExample,
    updateNoonTypeSetEnabledFlagById,
    updateNoonTypeEnabledFlagById,
  } from '@/modules/schedule/api/noonType.ts';
  import { Title, ProForm, ProTable } from 'sun-biz';

  export type searchFormType = {
    hospitalId?: string;
    keyWord?: string | undefined;
  };

  type SpanMethodProps = {
    row: NoonType.NoonTypeReqItem & {
      spanNum: number;
    };
    column: TableColumnCtx<NoonType.NoonTypeReqItem>;
    rowIndex: number;
    columnIndex: number;
  };

  const router = useRouter();
  const { t } = useTranslation();

  const loading = ref<boolean>(false); // 加载状态
  const searchFormModel = ref<searchFormType>({
    hospitalId: '',
    keyWord: undefined,
  }); // 检索条件数据
  const tableData = ref<NoonType.NoonTypeReqItem[]>([]); // 表格数据

  // const hospitalId = computed(() => searchFormModel.value.hospitalId);

  // 合并方法
  const handleSpanMethod = (data: SpanMethodProps) => {
    const { row, columnIndex } = data;
    if (columnIndex < 5 || columnIndex === 11) {
      return [row.spanNum, 1];
    } else {
      return [1, 1];
    }
  };

  // 过滤午别数据
  const filterTableData = async (data: NoonType.NoonTypeReqItem[]) => {
    let arr: (NoonType.NoonTypeReqItem & {
      spanNum: number;
      indexSort: number;
      enabledFlagNoonType: number;
    })[] = [];
    data.map((item: NoonType.NoonTypeReqItem, index: number) => {
      if (item?.noonTypeList.length > 0) {
        item?.noonTypeList?.map((im, ix) => {
          arr.push({
            ...item,
            ...im,
            enabledFlag: item.enabledFlag,
            enabledFlagNoonType: im.enabledFlag,
            spanNum: ix === 0 ? item?.noonTypeList.length : 0,
            indexSort: index,
          });
        });
      } else {
        arr.push({
          ...item,
          spanNum: 1,
          indexSort: index,
          enabledFlagNoonType: FLAG.NO,
        });
      }
      return item;
    });
    return arr;
  };

  // 获取午别列表
  const queryNoonTypeData = async (
    params: searchFormType = {
      hospitalId: '',
      keyWord: undefined,
    },
  ) => {
    searchFormModel.value = {
      ...searchFormModel.value,
      ...params,
    };
    loading.value = true;
    const [, res] = await queryNoonTypeSetListByExample(
      searchFormModel.value as NoonType.NoonTypeReqParams,
    );
    loading.value = false;
    if (res?.success) {
      tableData.value = await filterTableData(res?.data ?? []);
    }
  };

  // 当前行午别集合切换
  const handleEnableSwitch = async (
    row: NoonType.NoonTypeReqItem,
    type: string,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          (type === 'enabledFlag'
            ? row.enabledFlag
            : (
                row as NoonType.NoonTypeReqItem & {
                  enabledFlagNoonType: number;
                }
              ).enabledFlagNoonType) === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name:
          type === 'enabledFlag'
            ? row.nooTypeSetName
            : (
                row as NoonType.NoonTypeReqItem & {
                  noonTypeName: string;
                }
              ).noonTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await switchFn(
        row,
        type as 'enabledFlag' | 'enabledFlagNoonType',
      );
      if (res?.success) {
        if (type === 'enabledFlag') {
          tableData.value.map((item) => {
            if (item.nooTypeSetId === row.nooTypeSetId) {
              item.enabledFlag =
                item.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
              row.enabledFlag = item.enabledFlag;
            }
          });
        } else if (type === 'enabledFlagNoonType') {
          (
            row as NoonType.NoonTypeReqItem & {
              enabledFlagNoonType: number;
            }
          ).enabledFlagNoonType =
            (
              row as NoonType.NoonTypeReqItem & {
                enabledFlagNoonType: number;
              }
            ).enabledFlagNoonType === FLAG.YES
              ? FLAG.NO
              : FLAG.YES;
        }
        ElMessage.success(
          t(
            (
              type === 'enabledFlag'
                ? row.enabledFlag === FLAG.YES
                : (
                    row as NoonType.NoonTypeReqItem & {
                      enabledFlagNoonType: number;
                    }
                  ).enabledFlagNoonType === FLAG.YES
            )
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  // 切换接口调用
  const switchFn = async (
    row: NoonType.NoonTypeReqItem,
    type: 'enabledFlag' | 'enabledFlagNoonType',
  ) => {
    switch (type) {
      case 'enabledFlag':
        return await updateNoonTypeSetEnabledFlagById({
          nooTypeSetId: row.nooTypeSetId,
          enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
        });
      case 'enabledFlagNoonType':
        return await updateNoonTypeEnabledFlagById({
          nooTypeId: (row as NoonType.NoonTypeReqItem & { noonTypeId: string })
            .noonTypeId,
          enabledFlag:
            (row as NoonType.NoonTypeReqItem & { enabledFlagNoonType: number })
              .enabledFlagNoonType === FLAG.YES
              ? FLAG.NO
              : FLAG.YES,
        });
    }
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryNoonTypeData: queryNoonTypeData,
  });
  // 表格配置数据
  const { tableColumns } = useTableColumnsConfig({
    handleEnableSwitch: handleEnableSwitch,
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <Title :title="$t('noonType.list', '午别列表')" class="mb-2" />
    <div class="flex justify-between">
      <ProForm
        ref="searchFormRef"
        layout-mode="inline"
        v-model="searchFormModel"
        :data="searchConfig"
        :show-search-button="true"
        @model-change="queryNoonTypeData"
      />
      <el-button
        type="primary"
        @click="
          router.push({
            path: '/detail',
            query: {
              id: 'add',
              hospitalId: searchFormModel.hospitalId,
            },
          })
        "
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      row-key="noonTypeId"
      ref="tableColumnsRef"
      :span-method="handleSpanMethod"
      :data="tableData"
      :loading="loading"
      :columns="tableColumns"
    />
  </div>
</template>
