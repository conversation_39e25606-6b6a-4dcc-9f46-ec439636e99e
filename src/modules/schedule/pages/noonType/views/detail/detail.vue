<script setup lang="ts" name="noonTypeDetail">
  import { FLAG } from '@/utils/constant.ts';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ref, Ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useBaseInfoFormConfig } from '../../config/useFormConfig.ts';
  import { useDetailTableColumnsConfig } from '../../config/useTableColumnsConfig.tsx';
  import {
    saveNoonTypeSet,
    queryNoonTypeSetListByExample,
  } from '@/modules/schedule/api/noonType.ts';
  import { Title, ProForm, ProTable, TableRef } from 'sun-biz';

  export type baseInfoType = {
    nooTypeSetName: string | undefined;
    startAt: string | undefined;
    endAt: string | undefined;
    enabledFlag: number;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();

  const noonTypeSetId = computed(() => route.query.id); //午别集合id
  const hospitalId = computed(() => route.query.hospitalId); //医院id
  const detailTitle = computed(() =>
    noonTypeSetId.value === 'add'
      ? t('noonType.add', '新增午别')
      : t('noonType.edit', '编辑午别'),
  );

  const loading = ref<boolean>(false); // 加载状态
  const baseInfoModel = ref<baseInfoType>({
    nooTypeSetName: undefined,
    startAt: undefined,
    endAt: undefined,
    enabledFlag: FLAG.YES,
  }); // 检索条件数据
  const tableData = ref<Partial<NoonType.NoonTypeItemReqItem>[]>([]); // 表格数据
  const rowData = ref<NoonType.NoonTypeReqItem>();

  const baseInfoFormRef = ref(); //基本信息Ref
  const tableColumnsRef = ref<TableRef>(); //tableRef

  // 查询午别
  const queryNoonType = async () => {
    const params = {
      hospitalId: hospitalId.value as string,
      nooTypeSetId: noonTypeSetId.value as string,
    };
    loading.value = true;
    const [, res] = await queryNoonTypeSetListByExample(params);
    loading.value = false;
    if (res?.success) {
      rowData.value = res.data[0];
      baseInfoModel.value = {
        nooTypeSetName: rowData.value?.nooTypeSetName,
        startAt: rowData.value?.startAt,
        endAt: rowData.value?.endAt,
        enabledFlag: rowData.value?.enabledFlag,
      };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (rowData.value.noonTypeList ?? []).map((item: any) => {
        item.startEndTime = [item.startTime, item.endTime];
        return item;
      });
      tableData.value = rowData.value.noonTypeList ?? [];
    }
  };
  // 基本信息变化
  const baseInfoFormModelChange = (data: unknown) => {
    baseInfoModel.value = {
      ...baseInfoModel.value,
      ...(data as baseInfoType),
    };
  };

  // 拖拽排序
  const handleSortEnd = async (
    data: Partial<NoonType.NoonTypeItemReqItem>[],
  ) => {
    tableData.value = data;
  };

  // 保存午别
  const handleSave = async () => {
    await Promise.all([
      baseInfoFormRef.value?.ref.validate(),
      tableColumnsRef.value?.formRef.validate(),
    ]);
    if (tableData.value.length === 0) {
      return ElMessage.error(t('noonType.addWarning', '请添加午别'));
    }
    tableData.value.map((item, index) => {
      item.forwordTime = Number(item.forwordTime);
      item.sort = index + 1;
      return item;
    });
    const params = {
      nooTypeSetId:
        noonTypeSetId.value === 'add' ? undefined : noonTypeSetId.value,
      hospitalId: hospitalId.value as string,
      ...baseInfoModel.value,
      noonTypeList: tableData.value,
    };
    const [, res] = await saveNoonTypeSet(
      params as unknown as NoonType.SaveNoonTypeSetReqParams,
    );
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      router.push('/');
    }
  };

  // 检索条件配置数据
  const baseInfoConfig = useBaseInfoFormConfig({
    baseInfoModel: baseInfoModel,
  });
  // 表格配置数据
  const { tableColumns, addItem } = useDetailTableColumnsConfig({
    tableRef: tableColumnsRef,
    data: tableData as Ref<NoonType.NoonTypeItemReqItem[]>,
    id: 'noonTypeId',
  });

  onMounted(async () => {
    if (noonTypeSetId.value !== 'add') {
      await queryNoonType();
    }
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <el-page-header @back="router.push('/')" class="pb-3">
      <template #content>
        <span class="text-base">
          {{ detailTitle }}
        </span>
      </template>
    </el-page-header>
    <Title class="mb-2" :title="$t('baseInfo', '基本信息')" />
    <ProForm
      ref="baseInfoFormRef"
      v-model="baseInfoModel"
      :data="baseInfoConfig"
      :column="4"
      @model-change="baseInfoFormModelChange"
    />
    <div class="flex justify-between pb-2">
      <Title :title="$t('noonType.list', '午别列表')" />
      <el-button
        type="primary"
        @click="
          () =>
            addItem({
              enabledFlag: FLAG.YES,
              editable: true,
            })
        "
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      ref="tableColumnsRef"
      row-key="noonTypeId"
      :editable="true"
      :draggable="true"
      :data="tableData"
      :loading="loading"
      :columns="tableColumns"
      @drag-end="handleSortEnd"
    />
    <div class="mt-3 text-right">
      <el-button @click="router.push('/')">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" @click="handleSave">{{
        $t('global:save')
      }}</el-button>
    </div>
  </div>
</template>
