import { Ref } from 'vue';
import { FLAG } from '@/utils/constant.ts';
import { dayjs } from '@sun-toolkit/shared';
import { baseInfoType } from '../views/detail/detail.vue';
import { useFormConfig } from 'sun-biz';

export function useBaseInfoFormConfig(options: {
  baseInfoModel: Ref<baseInfoType>;
}) {
  const { baseInfoModel } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('noonType.nooTypeSetName', '午别集合名称'),
        name: 'nooTypeSetName',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('noonType.nooTypeSetName', '午别集合名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('noonType.nooTypeSetName', '午别集合名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('noonType.startAt', '开始时间'),
        name: 'startAt',
        type: 'datetime',
        triggerModelChange: true,
        component: 'date-picker',
        placeholder: t('global:placeholder.select.template', {
          name: t('noonType.startAt', '开始时间'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('noonType.startAt', '开始时间'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          clearable: false,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
          disabledDate: (time: Date) => {
            return (
              baseInfoModel.value.endAt &&
              dayjs(time).isAfter(dayjs(baseInfoModel.value.endAt), 'second')
            );
          },
        },
      },
      {
        label: t('noonType.endAt', '结束时间'),
        name: 'endAt',
        type: 'datetime',
        triggerModelChange: true,
        component: 'date-picker',
        placeholder: t('global:placeholder.select.template', {
          name: t('noonType.endAt', '结束时间'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('noonType.endAt', '结束时间'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          clearable: false,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
          disabledDate: (time: Date) => {
            return (
              baseInfoModel.value.startAt &&
              dayjs(time).isBefore(dayjs(baseInfoModel.value.startAt), 'second')
            );
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        triggerModelChange: true,
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}
