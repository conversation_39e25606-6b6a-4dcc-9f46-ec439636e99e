import { useFormConfig } from 'sun-biz';
import { searchFormType } from '../views/index.vue';
export function useSearchFormConfig(options: {
  queryNoonTypeData: (params: searchFormType) => Promise<void>;
}) {
  const { queryNoonTypeData } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          className: 'w-52',
          clearable: false,
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-56',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryNoonTypeData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}
