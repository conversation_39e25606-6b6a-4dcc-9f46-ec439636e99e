import { Ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTranslation } from 'i18next-vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { filterSelectData } from '@sun-toolkit/shared';
import { FLAG, NOON_TYPE_CODE_NAME } from '@/utils/constant.ts';

export function useTableColumnsConfig(options: {
  handleEnableSwitch: (
    row: NoonType.NoonTypeReqItem,
    type: string,
  ) => Promise<void>;
}) {
  const router = useRouter();
  const { handleEnableSwitch } = options;
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (
          row: NoonType.NoonTypeReqItem & {
            indexSort: number;
          },
        ) => <>{row.indexSort + 1}</>,
      },
      {
        label: t('noonType.nooTypeSetName', '午别集合名称'),
        prop: 'nooTypeSetName',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: NoonType.NoonTypeReqItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row, 'enabledFlag')}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('noonType.startAt', '开始时间'),
        prop: 'startAt',
        minWidth: 180,
      },
      {
        label: t('noonType.endAt', '结束时间'),
        prop: 'endAt',
        minWidth: 180,
      },
      {
        label: t('noonType.noonTypeCode', '午别代码'),
        prop: 'noonTypeCode',
        minWidth: 150,
      },
      {
        label: t('noonType.noonTypeName', '午别名称'),
        prop: 'noonTypeNameDisplay',
        minWidth: 150,
      },
      {
        label: t('noonType.startTime', '开始时间'),
        prop: 'startTime',
        minWidth: 180,
      },
      {
        label: t('noonType.endTime', '结束时间'),
        prop: 'endTime',
        minWidth: 180,
      },
      {
        label: t('noonType.forwordTime', '提前放号时间(分钟)'),
        prop: 'forwordTime',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlagNoonType',
        minWidth: 150,
        render: (
          row: NoonType.NoonTypeReqItem & {
            enabledFlagNoonType: number;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlagNoonType}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() =>
                handleEnableSwitch(row, 'enabledFlagNoonType')
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: NoonType.NoonTypeReqItem) => (
          <>
            <el-button
              type="primary"
              link={true}
              onClick={() => {
                router.push({
                  path: '/detail',
                  query: {
                    id: row.nooTypeSetId,
                    hospitalId: row.hospitalId,
                  },
                });
              }}
            >
              {t('global:edit')}
            </el-button>
          </>
        ),
      },
    ],
  });
  return { tableColumns };
}

export function useDetailTableColumnsConfig(options: {
  tableRef: Ref<TableRef>;
  data: Ref<NoonType.NoonTypeItemReqItem[]>;
  id: string;
}) {
  const { t } = useTranslation();
  const { tableRef, data, id } = options;
  const { addItem, toggleEdit, cancelEdit, delItem } = useEditableTable({
    tableRef: tableRef,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: data as any,
    id: id as string,
  });

  const validTimeNum = (
    rule: unknown,
    value: string,
    callback: (data?: Error | undefined) => void,
  ) => {
    const isValid = /^\d+$/.test(value);
    if (!isValid) {
      callback(new Error(t('validTimeNum.validator', '请输入整数')));
    } else {
      callback();
    }
  };

  const tableColumns = useColumnConfig({
    dataSetCodes: [NOON_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('noonType.noonTypeCode', '午别代码'),
        prop: 'noonTypeCode',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('noonType.noonTypeCode', '午别代码'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
        ) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[NOON_TYPE_CODE_NAME]
            : [];
          (
            data.value as (NoonType.NoonTypeItemReqItem & {
              dataValueNo: string;
            })[]
          ).map(
            (
              item: NoonType.NoonTypeItemReqItem & {
                dataValueNo: string;
              },
            ) => {
              item.dataValueNo = item.noonTypeCode;
            },
          );

          const noonTypeCodeList = filterSelectData(
            dataSetList,
            data.value.filter(
              (item) => item?.noonTypeCode !== row?.noonTypeCode,
            ),
            'dataValueNo',
          );
          if (row?.editable) {
            return (
              <>
                <el-select
                  v-model={row.noonTypeCode}
                  onChange={(val: string) => {
                    const noonTypeObj = noonTypeCodeList.find(
                      (obj: { dataValueNo: string }) =>
                        obj?.dataValueNo === val,
                    );
                    row.noonTypeCode = noonTypeObj?.dataValueNo as string;
                    row.noonTypeName =
                      noonTypeObj?.dataValueNameDisplay as string;
                  }}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t('noonType.noonTypeCode', '午别代码'),
                  })}
                >
                  {noonTypeCodeList?.map(
                    (item: {
                      dataValueNo: string;
                      dataValueNameDisplay: string;
                    }) => (
                      <el-option
                        key={item.dataValueNo}
                        label={
                          item.dataValueNo + '-' + item.dataValueNameDisplay
                        }
                        value={item.dataValueNo}
                      />
                    ),
                  )}
                </el-select>
              </>
            );
          } else {
            return <>{row.noonTypeCode}</>;
          }
        },
      },
      {
        label: t('noonType.noonTypeName', '午别名称'),
        prop: 'noonTypeName',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('noonType.noonTypeName', '午别名称'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
        ) => {
          if (row?.editable) {
            return (
              <el-input
                type="text"
                v-model={row.noonTypeName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('noonType.noonTypeName', '午别名称'),
                })}
              ></el-input>
            );
          } else {
            return <>{row.noonTypeName}</>;
          }
        },
      },
      {
        label: t('global:secondName'),
        prop: 'noonType2ndName',
        minWidth: 120,
        editable: true,
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
        ) => {
          if (row?.editable) {
            return (
              <el-input
                type="text"
                v-model={row.noonType2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:secondName'),
                })}
              ></el-input>
            );
          } else {
            return <>{row.noonType2ndName}</>;
          }
        },
      },
      {
        label: t('global:thirdName'),
        prop: 'noonTypeExtName',
        minWidth: 120,
        editable: true,
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
        ) => {
          if (row?.editable) {
            return (
              <el-input
                type="text"
                v-model={row.noonTypeExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:thirdName'),
                })}
              ></el-input>
            );
          } else {
            return <>{row.noonTypeExtName}</>;
          }
        },
      },
      {
        label: t('noonType.forwordTime', '提前放号时间(分钟)'),
        prop: 'forwordTime',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('noonType.forwordTime', '提前放号时间(分钟)'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            validator: validTimeNum,
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
        ) => {
          if (row?.editable) {
            return (
              <el-input
                type="number"
                v-model={row.forwordTime}
                placeholder={t('global:placeholder.input.template', {
                  content: t('noonType.forwordTime', '提前放号时间(分钟)'),
                })}
              ></el-input>
            );
          } else {
            return <>{row.forwordTime}</>;
          }
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        editable: true,
        minWidth: 100,
        render: (row: NoonType.NoonTypeItemReqItem) => {
          return (
            <div class={'w-full'}>
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt={true}
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
                onChange={(val: number) => {
                  row.enabledFlag = val;
                }}
              />
            </div>
          );
        },
      },
      {
        label: t('noonType.startStop.time', '起止时间'),
        prop: 'startEndTime',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('noonType.startStop.time', '起止时间'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
            startEndTime: string[];
          },
        ) => {
          if (row?.editable) {
            return (
              <el-time-picker
                is-range={true}
                format={'HH:mm'}
                value-format={'HH:mm'}
                onChange={(val: string[]) => {
                  row.startTime = val[0];
                  row.endTime = val[1];
                }}
                v-model={row.startEndTime}
                range-separator={t('global:rangeSeparator')}
                end-placeholder={t('global:placeholder.endTime')}
                start-placeholder={t('global:placeholder.startTime')}
              />
            );
          } else {
            return <>{row.startTime + '-' + row.endTime}</>;
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (
          row: NoonType.NoonTypeItemReqItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button type="primary" link={true} onClick={() => c(row)}>
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:remove')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem };
}
