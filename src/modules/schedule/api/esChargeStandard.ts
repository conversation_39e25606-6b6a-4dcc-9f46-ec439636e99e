import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10057-1]根据条件查询就诊服务收费标准列表
 * @param params
 * @returns
 */
export const queryEsChargeStandardListByExample = (
  params: EsChargeStandard.QueryParams,
) => {
  return dictRequest<EsChargeStandard.EsChargeStandardInfo[]>(
    '/eschargestandard/queryEsChargeStandardListByExample',
    params,
  );
};

/**
 * [1-10058-1] 新增就诊服务收费标准
 * @param params
 * @returns
 */
export const addEsChargeStandard = (
  params: EsChargeStandard.EsChargeStandardInfo,
) => {
  return dictRequest<EsChargeStandard.EsChargeStandardInfo>(
    '/eschargestandard/addEsChargeStandard',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};

/**
 * [1-10059-1]根据标识修改就诊服务收费标准
 * @param params
 * @returns
 */
export const updateEsChargeStandardById = (
  params: EsChargeStandard.EsChargeStandardInfo,
) => {
  return dictRequest<EsChargeStandard.EsChargeStandardInfo>(
    '/eschargestandard/updateEsChargeStandardById',
    params,
    {
      successMsg: translation('global:modify.success'),
    },
  );
};

/**
 * [1-10062-1]根据标识停启用就诊服务收费标准
 * @param params
 * @returns
 */
export const updateEsChargeStandardEnabledFlagById = (
  params: EsChargeStandard.UpdateEsChargeStandardEnabledFlag,
) => {
  return dictRequest<EsChargeStandard.EsChargeStandardInfo>(
    '/eschargestandard/updateEsChargeStandardEnabledFlagById',
    params,
  );
};
