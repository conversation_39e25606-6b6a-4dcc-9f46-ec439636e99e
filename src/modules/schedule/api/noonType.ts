import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10074-1]根据条件查询午别集合列表
 * @param params
 * @returns
 */
export const queryNoonTypeSetListByExample = (
  params: NoonType.NoonTypeReqParams,
) => {
  return dictRequest<NoonType.NoonTypeReqItem[]>(
    '/noontype/queryNoonTypeSetListByExample',
    params,
  );
};

/**
 * [1-10075-1]保存午别集合
 * @param params
 * @returns
 */
export const saveNoonTypeSet = (params: NoonType.SaveNoonTypeSetReqParams) => {
  return dictRequest<NoonType.SaveNoonTypeSetReqItem>(
    '/noontype/saveNoonTypeSet',
    params,
  );
};

/**
 * [1-10076-1]根据标识停启用午别集合
 * @param params
 * @returns
 */
export const updateNoonTypeSetEnabledFlagById = (
  params: NoonType.UpdateNoonTypeSetReqParams,
) => {
  return dictRequest<NoonType.UpdateNoonTypeSetReqParams>(
    '/noontype/updateNoonTypeSetEnabledFlagById',
    params,
  );
};

/**
 * [1-10089-1]根据标识停启用午别
 * @param params
 * @returns
 */
export const updateNoonTypeEnabledFlagById = (
  params: NoonType.UpdateNoonTypeReqParams,
) => {
  return dictRequest<NoonType.UpdateNoonTypeReqParams>(
    '/noontype/updateNoonTypeEnabledFlagById',
    params,
  );
};
