import { dictRequest } from '@sun-toolkit/request';
/**
 * [1-10134-1] 根据条件查询学科专业分类列表
 * @param params
 * @returns
 */
export function queryEsCategoryListByExample(
  params: AcademicMajorCategory.QueryParams,
) {
  return dictRequest<
    AcademicMajorCategory.TableResResult[],
    AcademicMajorCategory.QueryParams
  >('/escategory/queryEsCategoryListByExample', params);
}

/**
 * [1-10135-1] [1-10136-1] 保存/修改 学科专业分类信息
 * @param params
 * @returns
 */
export function saveEsCategory(
  params: AcademicMajorCategory.SaveReqParams,
  mode: string,
) {
  return dictRequest<AcademicMajorCategory.SaveReqParams>(
    `/escategory/${mode === 'add' ? 'addEsCategory' : 'updateEsCategoryById'}`,
    params,
  );
}
/**
 * [1-10240-1] 排序，引用common.ts/commonSort
 */
