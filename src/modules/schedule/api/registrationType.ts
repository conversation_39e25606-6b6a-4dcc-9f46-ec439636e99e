import { dictRequest } from '@sun-toolkit/request';
/**
 * [1-10134-1] 根据条件查询挂号类别列表
 * @param params
 * @returns
 */
export function queryRegisterTypeListByExample(
  params: RegistrationType.QueryParams,
) {
  return dictRequest<
    RegistrationType.TableResResult[],
    RegistrationType.QueryParams
  >('/registertype/queryRegisterTypeListByExample', params);
}
/**
 * [1-10055-1] 保存/修改 挂号类别  改变启用状态也用这个接口
 * @param params
 * @returns
 */
export function saveRegisterType(
  params: RegistrationType.SaveReqParams,
  mode: string,
) {
  return dictRequest<
    RegistrationType.TableResResult[],
    RegistrationType.SaveReqParams
  >(
    `/registertype/${mode === 'add' ? 'addRegisterType' : 'updateRegisterTypeById'}`,
    params,
  );
}
/**
 * [1-10055-1] 保存/修改 挂号类别
 * @param params
 * @returns
 */
export function updateRegisterTypeSortById(params: {
  regTypeSortList: RegistrationType.SortReqParams[];
}) {
  return dictRequest<RegistrationType.TableResResult[]>(
    '/registertype/updateRegisterTypeSortById',
    params,
  );
}
