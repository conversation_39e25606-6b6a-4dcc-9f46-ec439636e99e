import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10069-1]根据条件查询号源使用渠道列表
 * @param params
 * @returns
 */
export const queryDictEncResAccesserByExample = (
  params: Channel.QueryParams,
) => {
  return dictRequest<Channel.DictEncResAccesser[]>(
    '/encResAccesser/queryEncResAccesserByExample',
    params,
  );
};

/**
 * [1-10070-1]保存号源使用渠道信息
 * @param params
 * @returns
 */
export const saveEncResAccesser = (params: Channel.DictEncResAccesser) => {
  return dictRequest<Channel.DictEncResAccesser[]>(
    '/encResAccesser/saveDictEncResAccesser',
    params,
  );
};

/**
 * [1-10072-1]根据标识修改号源使用渠道排序
 * @param params
 * @returns
 */
export const updateDictEncResAccesserSortByIds = (
  params: Channel.DictEncResAccesser[],
) => {
  return dictRequest<Channel.DictEncResAccesser[]>(
    '/encResAccesser/updateEncResAccesserSortById',
    params,
    {
      successMsg: translation('global:modify.sort.success'),
    },
  );
};
