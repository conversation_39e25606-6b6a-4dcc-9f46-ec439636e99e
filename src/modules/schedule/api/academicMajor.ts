import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10060-1]根据条件查询学科专业列表
 * @param params
 * @returns
 */
export const queryAcademicMajorByExample = (
  params: AcademicMajor.QueryParams,
) => {
  return dictRequest<
    AcademicMajor.AcademicMajorInfo,
    AcademicMajor.QueryParams
  >('/academicmajor/queryAcademicMajorByExample', params);
};

/**
 * [1-10061-1]保存学科专业信息
 * @param params
 * @returns
 */
export const saveAcademicMajor = (params: AcademicMajor.AcademicMajorInfo) => {
  return dictRequest<AcademicMajor.AcademicMajorInfo[]>(
    '/academicmajor/saveAcademicMajor',
    params,
  );
};

/**
 * [1-10063-1]根据标识停启用学科专业
 * @param params
 * @returns
 */
export const updateAcademicMajorEnabledFlagById = (
  params: AcademicMajor.AcademicMajorInfo,
) => {
  return dictRequest<AcademicMajor.AcademicMajorInfo[]>(
    '/academicmajor/updateAcademicMajorEnabledFlagById',
    params,
  );
};

/**
 * [1-10064-1]根据标识修改学科专业排序
 * @param params
 * @returns
 */
export const updateAcademicMajorSortById = (
  params: AcademicMajor.AcademicMajorInfo[],
) => {
  return dictRequest<AcademicMajor.AcademicMajorInfo[]>(
    '/academicmajor/updateAcademicMajorSortById',
    params,
    {
      successMsg: translation('global:modify.sort.success'),
    },
  );
};
