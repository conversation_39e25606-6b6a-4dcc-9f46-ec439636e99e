import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10311-1] 根据条件查询节假日
 * @param params
 * @returns
 */
export const queryHolidayByExample = (
  params: HolidaySetting.QueryHolidayParams,
) => {
  return dictRequest<HolidaySetting.HolidayInfo>(
    '/holiday/queryHolidayByExample',
    params,
  );
};

/**
 * [1-10312-1] 新增节假日
 * @param params
 * @returns
 */
export const addHoliday = (params: HolidaySetting.HolidayInfo) => {
  return dictRequest<HolidaySetting.HolidayInfo>('/holiday/addHoliday', params);
};

/**
 * [1-10313-1] 编辑节假日
 * @param params
 * @returns
 */
export const editHoliday = (params: HolidaySetting.HolidayInfo) => {
  return dictRequest<HolidaySetting.HolidayInfo>(
    '/holiday/editHoliday',
    params,
  );
};
/**
 * [1-10314-1] 删除节假日
 * @param params
 * @returns
 */
export const deleteHoliday = (params: HolidaySetting.HolidayInfo) => {
  return dictRequest<HolidaySetting.HolidayInfo>(
    '/holiday/deleteHoliday',
    params,
  );
};

/**
 * [1-10315-1] 根据条件查询节假日V1
 * @param params
 * @returns
 */
export const queryHolidayByExampleV1 = (
  params: HolidaySetting.QueryHolidayParams,
) => {
  return dictRequest<HolidaySetting.HolidayInfo>(
    '/holiday/queryHolidayByExampleV1',
    params,
  );
};
