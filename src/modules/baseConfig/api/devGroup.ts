import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';
import {
  DevGroupList,
  UpsertDevGroup,
} from '@/modules/baseConfig/typings/devGroup.ts';

/**
 * [1-10356-1] 根据条件查询开发组别
 * @param params
 * @returns
 */
export const queryDevGroupByExample = (params: UpsertDevGroup) => {
  return dictRequest<DevGroupList>('/devgroup/queryDevGroupByExample', params);
};

/**
 * [1-10379-1] 新增开发组别
 * @param params
 * @returns
 */
export const addDevGroup = (params: UpsertDevGroup) => {
  return dictRequest<UpsertDevGroup>('/devgroup/addDevGroup', params, {
    successMsg: translation('global:add.success'),
  });
};

/**
 * [1-10380-1] 编辑开发组别
 * @param params
 * @returns
 */
export const editDevGroup = (params: UpsertDevGroup) => {
  return dictRequest<UpsertDevGroup>('/devgroup/editDevGroup', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [1-10380-1] 删除开发组别
 * @param params
 * @returns
 */
export const deleteDevGroupById = (params: UpsertDevGroup) => {
  return dictRequest<UpsertDevGroup>('/devgroup/deleteDevGroup', params, {
    successMsg: translation('global:delete.success'),
  });
};
