import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10211-1]新增基础数据对照关系
 * @param params
 * @returns
 */
export const addBasicDataMapping = (
  params: ExBasicDataMapping.BasicDataMappingAddParams,
) => {
  return dictRequest<{ basicDataMappingId: string }>(
    '/basicdatamapping/addBasicDataMapping',
    params,
  );
};

/**
 * [1-10212-1]删除基础数据对照关系
 * @param params
 * @returns
 */
export const deleteBasicDataMapping = (params: {
  basicDataMappingId: string;
}) => {
  return dictRequest('/basicdatamapping/deleteBasicDataMapping', params);
};

/**
 * [1-10213-1]根据标识修改基础数据对照关系
 * @param params
 * @returns
 */
export const updateBasicDataMapping = (params: {
  basicDataMappingId: string;
  startAt: string;
  endAt: string;
}) => {
  return dictRequest('/basicdatamapping/updateBasicDataMapping', params);
};

/**
 * [1-10214-1]根据HIS数据标识查询对照关系
 * @param params
 * @returns
 */
export const queryBasicDataMappingByHisIds = (
  params: ExBasicDataMapping.BasicDataMappingByHisIdsQueryParams,
) => {
  return dictRequest<ExBasicDataMapping.HisDataAndMappingInfo[]>(
    '/basicdatamapping/queryBasicDataMappingByHisIds',
    params,
  );
};

/**
 * [1-10215-1]根据条件查询HIS基础数据及其对照关系
 * @param params
 * @returns
 */
export const queryHisDataAndMappingByExample = (
  params: ExBasicDataMapping.HisDataAndMappingQueryParams,
) => {
  return dictRequest<
    ExBasicDataMapping.HisDataAndMappingInfo,
    ExBasicDataMapping.HisDataAndMappingQueryParams
  >('/basicdatamapping/queryHisDataAndMappingByExample', params);
};

/**
 * [1-10216-1]根据条件查询HIS基础数据分类列表
 * @param params
 * @returns
 */
export const getHisBasicDataTypeList = (params: object) => {
  return dictRequest<ExBasicDataMapping.HisBasicDataType[]>(
    '/basicdatamapping/getHisBasicDataTypeList',
    params,
  );
};
