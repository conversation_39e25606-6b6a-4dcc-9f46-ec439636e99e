import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10195-1]根据条件查询票据配置
 * 1、查询DICT_INVOICE_SETTING（票据配置）
 * @param params
 * @returns
 */
export const queryInvoiceSettingList = (
  params: InvoiceSetting.QueryInvoiceSettingListParams,
) => {
  return dictRequest<InvoiceSetting.InvoiceSettingInfoItem[]>(
    '/invoicesetting/queryInvoiceSettingByExample',
    params,
  );
};
/**
 * [1-10196-1]根据标识修改票据配置
 * 1、删除表DICT_BIZ_LOCK(业务锁)数据
 * @param params
 * @returns
 */
export const upsertInvoiceSettingData = (
  params: InvoiceSetting.UpsertInvoiceSettingListParams,
) => {
  return dictRequest<InvoiceSetting.UpsertInvoiceSettingListParams>(
    '/invoicesetting/updateInvoiceSettingById',
    params,
  );
};
