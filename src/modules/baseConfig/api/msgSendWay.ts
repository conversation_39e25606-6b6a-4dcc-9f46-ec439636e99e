import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10282-1]根据条件查询消息发送渠道
 * @param params
 * @returns
 */
export const queryMsgSendWayList = (
  params?: MsgSendWay.QueryMsgSendWayList,
) => {
  return dictRequest<MsgSendWay.MsgSendWayList[]>(
    '/msgsendway/queryMsgSendWayByExample',
    params,
  );
};
/**
 * [1-10283-1]新增消息发送渠道
 * @param params
 * @returns
 */
export const addMsgSendWay = (params: MsgSendWay.UpsertMsgSendWayParams) => {
  return dictRequest<MsgSendWay.UpsertMsgSendWayParams>(
    '/msgsendway/addMsgSendWay',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10284-1]编辑消息发送渠道
 * @param params
 * @returns
 */
export const editMsgSendWay = (params: MsgSendWay.UpsertMsgSendWayParams) => {
  return dictRequest<MsgSendWay.UpsertMsgSendWayParams>(
    '/msgsendway/editMsgSendWay',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};
