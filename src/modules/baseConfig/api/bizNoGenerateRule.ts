import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10078-1]根据条件查询业务编码生成规则列表
 * @param params
 * @returns
 */
export const queryBizNoGenerateRuleListByExample = (
  params: BizNoGenerateRule.QueryParams,
) => {
  return dictRequest<BizNoGenerateRule.BizNoGenerateRuleInfo[]>(
    '/biznogeneraterule/queryBizNoGenerateRuleListByExample',
    params,
  );
};

/**
 * [1-10079-1]新增业务编码生成规则
 * @param params
 * @returns
 */
export const addBizNoGenerateRule = (
  params: BizNoGenerateRule.BizNoGenerateRuleUpsertParams,
) => {
  return dictRequest<BizNoGenerateRule.BizNoGenerateRuleInfo[]>(
    '/biznogeneraterule/addBizNoGenerateRule',
    params,
  );
};

/**
 * [1-10080-1]根据标识修改业务编码生成规则
 * @param params
 * @returns
 */
export const updateBizNoGenerateRuleById = (
  params: BizNoGenerateRule.BizNoGenerateRuleUpsertParams,
) => {
  return dictRequest<BizNoGenerateRule.BizNoGenerateRuleInfo[]>(
    '/biznogeneraterule/updateBizNoGenerateRuleById',
    params,
  );
};

/**
 * [1-10081-1]根据业务编码对象代码生成业务编码
 * @param params
 * @returns
 */
export const generateBizNoByCode = (params: {
  bizNoObjectCode?: string;
  hospitalId?: string;
  generateNum?: number;
  previewFlag?: string;
}) => {
  return dictRequest<string[]>(
    '/biznogeneraterule/generateBizNoByCode',
    params,
  );
};

/**
 * [1-10082-1]根据标识停启用业务编码规则
 * @param params
 * @returns
 */
export const updateEnabledFlagById = (params: {
  bizNoGenerateRuleId?: string;
  enabledFlag?: string;
}) => {
  return dictRequest<string[]>(
    '/biznogeneraterule/updateEnabledFlagById',
    params,
  );
};

/**
 * [1-10342-1]根据条件查询业务编码回收记录
 * @param params
 * @returns
 */
export const queryBizNoRecycleRecordByExample = (
  params: BizNoGenerateRule.RecyclingParameters,
) => {
  return dictRequest<string[]>(
    '/biznorecyclerecord/queryBizNoRecycleRecordByExample',
    params,
  );
};
