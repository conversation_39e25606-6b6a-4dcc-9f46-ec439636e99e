import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10286-1]根据条件查询联系方式信息
 * @param params
 * @returns
 */
export const queryContactInfoList = (
  params: ContactInfo.QueryContactInfoList,
) => {
  return dictRequest<ContactInfo.ContactInfoList[]>(
    '/contactinfo/queryContactinfoByExample',
    params,
  );
};
/**
 * [1-10287-1]根据条件查询联系方式信息
 * @param params
 * @returns
 */
export const addContactInfo = (params: ContactInfo.AddContactInfoParams) => {
  return dictRequest<ContactInfo.AddContactInfoParams>(
    '/contactinfo/addContactinfo',
    params,
  );
};
/**
 * [1-10288-1]编辑消息发送渠道
 * @param params
 * @returns
 */
export const editContactInfo = (
  params: ContactInfo.UpsertContactInfoParams,
) => {
  return dictRequest<ContactInfo.UpsertContactInfoParams>(
    '/contactinfo/editContactinfo',
    params,
  );
};
/**
 * [1-10289-1]删除联系方式信息
 * @param params
 * @returns
 */
export const deleteContactInfoById = (
  params: ContactInfo.DeleteContactInfoParams,
) => {
  return dictRequest<ContactInfo.DeleteContactInfoParams>(
    '/contactinfo/deleteContactinfo',
    params,
  );
};
