import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10067-1]根据条件查询业务事件列表
 * @param params
 * @returns
 */
export const queryDictBizEvent = (params: BizEvent.QueryParams) => {
  return dictRequest<BizEvent.BizEventInfo[]>(
    '/dictbizevent/queryDictBizEvent',
    params,
  );
};

/**
 * [1-10071-1]保存业务事件信息
 * @param params
 * @returns
 */
export const saveDictBizEvent = (params: BizEvent.UpsertEventParams) => {
  return dictRequest<{ bizEventId: string }>(
    '/dictbizevent/saveDictBizEvent',
    params,
  );
};

/**
 * [1-10290-1]新增事件的消息设置
 * @param params
 * @returns
 */
export const addEventMsgSetting = (params: BizEvent.UpsertEventParams) => {
  return dictRequest<{
    bizEventId: string;
    msgSendWayId: string;
    contactInfoId: string;
  }>('/dictbizevent/addEventMsgSetting', params);
};

/**
 * [1-10291-1]编辑事件的消息设置
 * @param params
 * @returns
 */
export const editEventMsgSetting = (params: BizEvent.UpsertEventParams) => {
  return dictRequest<{
    bizEventId: string;
    msgSendWayId: string;
    contactInfoId: string;
  }>('/dictbizevent/editEventMsgSetting', params);
};
/**
 * [1-10292-1]删除事件的消息设置
 * @param params
 * @returns
 */
export const deleteEventMsgSetting = (params: BizEvent.UpsertEventParams) => {
  return dictRequest<{ eventMsgSettingId: string }>(
    '/dictbizevent/deleteEventMsgSetting',
    params,
  );
};
