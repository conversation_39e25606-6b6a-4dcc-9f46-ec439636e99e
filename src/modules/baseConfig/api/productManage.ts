import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10256-1]根据条件查询代码仓库V1
 * @param params
 * @returns
 */
export const queryCodeRepositoryByExampleV1 = (
  params: ProductManage.QueryProductManageParams,
) => {
  return dictRequest<ProductManage.CodeRepositoryList[]>(
    '/codeRepository/queryCodeRepositoryByExampleV1',
    params,
  );
};

/**
 * [1-10383-1] 根据条件查询产品
 * @param params
 * @returns
 */
export const queryProductByExample = (
  params: ProductManage.QueryProductManageParams,
) => {
  return dictRequest<ProductManage.ProductList[]>(
    '/product/queryProductByExample',
    params,
  );
};
/**
 * [1-10384-1] 新增产品
 * @param params
 * @returns
 */
export const addProduct = (params: ProductManage.ProductList) => {
  return dictRequest<{ productId: string }>('/product/addProduct', params, {
    successMsg: translation('global:add.success'),
  });
};

/**
 * [1-10385-1] 编辑产品
 * @param params
 * @returns
 */
export const editProduct = (params: ProductManage.ProductList) => {
  return dictRequest<{ productId: string }>('/product/editProduct', params, {
    successMsg: translation('global:edit.success'),
  });
};

/**
 * [1-10386-1] 删除产品
 * @param params
 * @returns
 */
export const deleteProduct = (params: ProductManage.ProductList) => {
  return dictRequest<{ productId: string }>('/product/deleteProduct', params, {
    successMsg: translation('global:delete.success'),
  });
};

/**
 * [1-10399-1] 保存组织产品关系
 * @param params
 * @returns
 */
export const saveOrgXProd = (params: ProductManage.OrgList) => {
  return dictRequest<{ orgXProdId: string }>('/product/saveOrgXProd', params, {
    successMsg: translation('global:save.success'),
  });
};
/**
 * [1-10400-1] 新增医院产品的代码仓库
 * @param params
 * @returns
 */
export const addOrgXProdCodeRepository = (
  params: ProductManage.UpsertOrgXProdCodeRepository,
) => {
  return dictRequest<{ orgXProdId: string }>(
    '/product/addOrgXProdCodeRepository',
    params,
    {
      successMsg: translation('global:save.success'),
    },
  );
};
/**
 * [1-10401-1] 删除医院产品的代码仓库
 * @param params
 * @returns
 */
export const deleteOrgXProdCodeRepository = (
  params: ProductManage.UpsertOrgXProdCodeRepository,
) => {
  return dictRequest<{ orgXProdId: string }>(
    '/product/deleteOrgXProdCodeRepository',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};
/**
 * [1-10402-1] 删除医院产品关系
 * @param params
 * @returns
 */
export const deleteOrgXProd = (
  params: ProductManage.UpsertOrgXProdCodeRepository,
) => {
  return dictRequest<{ orgXProdId: string }>(
    '/product/deleteOrgXProd',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};
