import { dictRequest } from '@sun-toolkit/request';
/**
 * [1-10156-1]根据条件查询业务锁列表
 * 1、根据条件查询表DICT_BIZ_LOCK(业务锁),如果“超时解锁日期时间<当前时间”则有效标志=0，否则为1
 * @param params
 * @returns
 */
export const queryBizLockListData = (params: BizLock.QueryParams) => {
  return dictRequest<BizLock.BizLockInfo[]>(
    '/bizlock/queryBizLockListByExample',
    params,
  );
};
/**
 * [1-10157-1]根据标识解除业务锁
 * 1、删除表DICT_BIZ_LOCK(业务锁)数据
 * @param params
 * @returns
 */
export const unLockById = (params: BizLock.QueryParams) => {
  return dictRequest<BizLock.UpsertBizLockParams[]>(
    '/bizlock/unLockById',
    params,
  );
};
