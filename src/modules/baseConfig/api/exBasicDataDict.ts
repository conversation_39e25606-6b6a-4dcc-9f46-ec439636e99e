import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10198-1]根据条件查询外部目录列表
 * @param params
 * @returns
 */
export const queryExBasicDtaDictListByExample = (
  params: ExBasicDataDict.ExBasicDtaDictQueryParams,
) => {
  return dictRequest<ExBasicDataDict.ExBasicDtaDictInfo[]>(
    '/exbasicdtadict/queryExBasicDtaDictListByExample',
    params,
  );
};

/**
 * [1-10199-1]新增外部目录
 * @param params
 * @returns
 */
export const addExBasicDtaDict = (
  params: ExBasicDataDict.ExBasicDtaDictUpsertParams,
) => {
  return dictRequest<{ basicDataDictId: string }>(
    '/exbasicdtadict/addExBasicDtaDict',
    params,
  );
};

/**
 * [1-10200-1]根据标识修改外部目录
 * @param params
 * @returns
 */
export const updateExBasicDtaDictById = (
  params: ExBasicDataDict.ExBasicDtaDictUpsertParams,
) => {
  return dictRequest<ExBasicDataDict.ExBasicDtaDictInfo[]>(
    '/exbasicdtadict/updateExBasicDtaDictById',
    params,
  );
};

/**
 * [1-10201-1]根据标识停启用外部目录
 * @param params
 * @returns
 */
export const updateExBasicDtaDictEnabledFlagById = (params: {
  basicDataDictId: string;
  enabledFlag: number;
}) => {
  return dictRequest<ExBasicDataDict.ExBasicDtaDictInfo[]>(
    '/exbasicdtadict/updateExBasicDtaDictEnabledFlagById',
    params,
  );
};

/**
 * [1-10202-1]根据条件查询外部目录分类
 * @param params
 * @returns
 */
export const queryExBasicDtaDictTypeListByExample = (
  params: ExBasicDataDict.ExBasicDtaDictTypeQueryParams,
) => {
  return dictRequest<ExBasicDataDict.ExBasicDtaDictTypeInfo[]>(
    '/exbasicdtadict/queryExBasicDtaDictTypeListByExample',
    params,
  );
};

/**
 * [1-10203-1]根据标识设置贯标的外部目录
 * @param params
 * @returns
 */
export const updateExBasicDtaDictStandardFlagById = (params: {
  basicDataDictId: string;
}) => {
  return dictRequest<ExBasicDataDict.ExBasicDtaDictTypeInfo[]>(
    '/exbasicdtadict/updateExBasicDtaDictStandardFlagById',
    params,
  );
};

/**
 * [1-10204-1]根据条件查询目录分类的基础数据
 * @param params
 * @returns
 */
export const queryExBasicDtaListByExample = (
  params: ExBasicDataDict.ExBasicDtaQueryParams,
) => {
  return dictRequest<
    ExBasicDataDict.ExBasicDtaInfo,
    ExBasicDataDict.ExBasicDtaQueryParams
  >('/exbasicdtadict/queryExBasicDtaListByExample', params);
};

/**
 * [1-10205-1]保存目录分类的基础数据
 * @param params
 * @returns
 */
export const saveExBasicData = (
  params: ExBasicDataDict.SaveExBasicDataParams,
) => {
  return dictRequest<{ basicDataIds: string[] }>(
    '/exbasicdtadict/saveExBasicData',
    params,
  );
};

/**
 * [1-10206-1]保存目录分类的医保药品基础数据
 * @param params
 * @returns
 */
export const saveExMedicineBasicData = (
  params: ExBasicDataDict.SaveExMedicineBasicDataParams,
) => {
  return dictRequest<{ basicDataIds: string[] }>(
    '/exbasicdtadict/saveExMedicineBasicData',
    params,
  );
};

/**
 * [1-10207-1]保存目录分类的医保项目基础数据
 * @param params
 * @returns
 */
export const saveExServiceItemBasicData = (
  params: ExBasicDataDict.SaveExServiceItemBasicDataParams,
) => {
  return dictRequest<{ basicDataIds: string[] }>(
    '/exbasicdtadict/saveExServiceItemBasicData',
    params,
  );
};

/**
 * [1-10208-1]根据目录分类标识查询对应的最大版本号
 * @param params
 * @returns
 */
export const queryMaxVersionNoByIds = (params: {
  basicDataDictTypeIds: string[];
}) => {
  return dictRequest<{
    maxVersionNoList: { basicDataDictTypeId: string; versionNo?: string }[];
  }>('/exbasicdtadict/queryMaxVersionNoByIds', params);
};

/**
 * [1-10209-1]根据标识更新基础数据目录分类的下载状态
 * @param params
 * @returns
 */
export const updateDownloadStatusByIds = (params: {
  basicDataDictTypeId: string;
  dataDownloadStatusCode: string;
  dataDownloadStatusMemo?: string;
}) => {
  return dictRequest('/exbasicdtadict/updateDownloadStatusByIds', params);
};

/**
 * [1-10210-1]根据标识删除目录分类的基础数据
 * @param params
 * @returns
 */
export const deleteExBasicDataByDtaDictTypeId = (params: {
  basicDataDictTypeId: string;
}) => {
  return dictRequest(
    '/exbasicdtadict/deleteExBasicDataByDtaDictTypeId',
    params,
  );
};
