import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10065-1]根据条件查询业务来源列表
 * @param params
 * @returns
 */
export const queryBizSourceByExample = (params: Origin.QueryParams) => {
  return dictRequest<Origin.BizSourceInfo[]>(
    '/bizsource/queryBizSourceByExample',
    params,
  );
};

/**
 * [1-10066-1]根据标识修改或新增业务来源
 * @param params
 * @returns
 */
export const saveBizSource = (params: Origin.BizSourceInfo) => {
  return dictRequest<Origin.BizSourceInfo>('/bizsource/saveBizSource', params);
};

/**
 * [1-10068-1]根据标识修改业务来源排序
 * @param params
 * @returns
 */
export const updateBizSourceSortById = (params: Origin.BizSourceSortList) => {
  return dictRequest<Origin.BizSourceInfo[]>(
    '/bizsource/updateBizSourceSortById',
    params,
    {
      successMsg: translation('global:modify.sort.success'),
    },
  );
};
