import { dictRequest } from '@sun-toolkit/request';
import {
  AppEnvDefineAppEnvList,
  AppEnvDefineQueryParams,
  AppEnvDefineUpsertAppEnv,
} from '@/modules/baseConfig/typings/appEnvDefine.ts';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10293-1] 根据条件查询应用环境信息
 * @param params
 * @returns
 */
export const queryAppEnvByExample = (params: AppEnvDefineQueryParams) => {
  return dictRequest<AppEnvDefineAppEnvList[]>(
    '/appenv/queryAppEnvByExample',
    params,
  );
};

/**
 * [1-10294-1] 新增应用环境信息
 * @param params
 * @returns
 */
export const addAppEnv = (params: AppEnvDefineUpsertAppEnv) => {
  return dictRequest<{ appEnvId: string }>('/appenv/addAppEnv', params);
};

/**
 * [1-10295-1] 编辑应用环境信息
 * @param params
 * @returns
 */
export const editAppEnv = (params: AppEnvDefineUpsertAppEnv) => {
  return dictRequest<{ appEnvId: string }>('/appenv/editAppEnv', params);
};

/**
 * [1-10296-1] 删除应用环境信息
 * @param params
 * @returns
 */
export const deleteAppEnv = (params: AppEnvDefineUpsertAppEnv) => {
  return dictRequest<{ appEnvId: string }>('/appenv/deleteAppEnv', params, {
    successMsg: translation('global:delete.success'),
  });
};
