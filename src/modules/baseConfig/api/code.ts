import { dictRequest } from '@sun-toolkit/request';
import { CodeSystem, SystemInfo, CodeSystemType } from '@/typings/codeManage';
/**
 * [1-10010-1]根据条件查询数据标准管理平台的编码体系及值域
 * @param data
 * @returns
 */
export const queryExCodeSystemListByExample = (
  params: Code.PageInfo & { keyWord: string; version: string },
) => {
  return dictRequest<
    Code.ResQueryCodeSystemList,
    Code.PageInfo & { keyWord: string; version: string }
  >('/codeSystem/queryExCodeSystemListByExample', params);
};

/**
 * [1-10011-1]保存数据标准管理平台的编码体系及值域
 * @param data
 * @returns
 */
export const saveCodeSystemDataSets = (
  params: Code.ReqSaveCodeSystemDataSetsParams,
) => {
  return dictRequest<Code.ResSaveCodeSystemDataSets>(
    '/codeSystem/saveCodeSystemDataSets',
    params,
  );
};

/**
 * [1-10005-1]根据条件查询编码体系列表
 * @param data
 * @returns
 */
export const queryCodeSystemListByExample = (
  params: Code.ReqQueryCodeSystemListParams,
) => {
  return dictRequest<
    Code.ResQueryCodeSystemList,
    Code.ReqQueryCodeSystemListParams
  >('/codeSystem/queryCodeSystemListByExample', params);
};

/**
 * [1-10006-1]根据条件查询值域列表
 * @param data
 * @returns
 */
export const queryDataSetListByExample = (
  params: Code.ReqQueryDataSetListByExampleParams,
) => {
  return dictRequest<Code.CodeSystemInfo[]>(
    '/codeSystem/queryDataSetListByExample',
    params,
  );
};

/**
 * [1-10007-1]根据标识停启用值域
 * @param data
 * @returns
 */
export const updateDataSetEnabledFlagById = (params: {
  enabledFlag: 0 | 1;
  dataValueId: string;
}) => {
  return dictRequest('/codeSystem/updateDataSetEnabledFlagById', params);
};

/**
 * [1-10008-1]根据条件生成DML脚本
 * @param data
 * @returns
 */
export const exportDmlScriptByExample = (
  params: Code.ReqExportDmlScriptByExampleParams,
) => {
  return dictRequest<{
    data: string;
    fileName: string;
  }>('/dictCommon/exportDmlScriptByExample', params);
};

/**
 * [1-10009-1]根据标识停启用组织
 * @param data
 * @returns
 */
export const updateOrgEnabledFlagById = (params: {
  codeSystemId: string;
  enabledFlag: 0 | 1;
}) => {
  return dictRequest('/organization/updateOrgEnabledFlagById', params);
};

/**
 * [1-10012-1]根据标识修改值域排序
 * @param data
 * @returns
 */
export const updateDataSetSortByIds = (params: {
  dataSetSortList: {
    dataValueId: string;
    sort: number;
  }[];
}) => {
  return dictRequest('/codeSystem/updateDataSetSortByIds', params);
};

/**
 * [1-10012-1]获取值域列表
 * @param data
 * @returns
 */
type KeyOfArray<T> = T extends Array<infer U> ? U : never;
export const queryDataSetByCodeSystemCodes = (params: {
  codeSystemCodes: CodeSystemType[];
}) => {
  return dictRequest<{
    [key in KeyOfArray<typeof params.codeSystemCodes>]: CodeSystem[];
  }>('/codeSystem/queryDataSetByCodeSystemCodes', params, { cancel: false });
};

/**
 * [1-10105-1]字典数据综合检索接口
 * @param params
 * @returns
 */
export const queryDictDataListByExample = (params: {
  dataSearchBizIdTypeCode: string;
  codeSystemNo?: string;
  keyWord?: string;
  hospitalId: string;
  menuId: string;
}) => {
  return dictRequest<FormDesign.ReqDictDataListByExample>(
    '/dictCommon/queryDictDataListByExample',
    params,
  );
};

/**
 * [1-10237-1] 根据条件查询药理分类值域列表
 * @param data
 * @returns
 */
export const queryPharmacologyClassListByExample = (params: {
  keyWord?: string;
}) => {
  return dictRequest<
    (Code.CodeSystemInfo & { dataValueSubList: Code.CodeSystemInfo[] })[]
  >('/codeSystem/queryPharmacologyClassListByExample', params);
};

/**
 * [1-10017-1] 根据条件查询应用系统列表
 * @param data
 * @returns
 */
export const querySystemListByExample = (params: {
  keyWord?: string;
  enabledFlag?: number;
  sysId?: string;
  devGroupCode?: string;
  accessFlag?: number;
}) => {
  return dictRequest<SystemInfo[]>('/system/querySystemListByExample', params);
};

/**
 * [1-10453-1] 根据标识发布编码体系数据（按系统MDM地址）
 * @param data
 * @returns
 */
export const releaseCodeSystemByIds = (params: {
  codeSystemIds: string[];
  sysIds: string[];
}) => {
  return dictRequest('/codeSystem/releaseCodeSystemByIds', params);
};
