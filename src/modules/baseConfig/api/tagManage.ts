import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10180-1]根据条件查询标签分组列表
 * @param params
 * @returns
 */
export const queryTagGroupListByExample = (
  params: TagManage.ReqQueryTagGroupList,
) => {
  return dictRequest<TagManage.TagGroup[]>(
    '/tag/queryTagGroupListByExample',
    params,
  );
};

/**
 * [1-10180-1]根据条件查询标签分组列表
 * @param params
 * @returns
 */
export const queryTagListByExample = (params: TagManage.ReqQueryTagList) => {
  return dictRequest<TagManage.TagInfo[]>('/tag/queryTagListByExample', params);
};

/**
 * [1-10180-1]新增标签分组
 * @param params
 * @returns
 */
export const addTagGroup = (params: TagManage.ReqAddTagGroup) => {
  return dictRequest<{
    tagGroupId: string;
  }>('/tag/addTagGroup', params);
};

/**
 * [1-10180-1]新增标签
 * @param params
 * @returns
 */
export const addTag = (params: TagManage.ReqAddTag) => {
  return dictRequest<{
    tagGroupId: string;
  }>('/tag/addTag', params);
};

/**
 * [1-10184-1]根据标识修改标签分组
 * @param params
 * @returns
 */
export const updateTagGroupById = (params: TagManage.ReqUpdateTagGroup) => {
  return dictRequest('/tag/updateTagGroupById', params);
};

/**
 * [1-10185-1]根据标识修改标签
 * @param params
 * @returns
 */
export const updateTagById = (params: TagManage.ReqUpdateTag) => {
  return dictRequest('/tag/updateTagById', params);
};

/**
 * [1-10186-1]根据标识修改标签的排序
 * @param params
 * @returns
 */
export const updateTagSortByIds = (params: TagManage.ReqUpdateTagSort) => {
  return dictRequest('/tag/updateTagSortByIds', params);
};

/**
 * [1-10187-1]根据标识停启用标签分组
 * @param params
 * @returns
 */
export const updateTagGroupEnabledFlagById = (
  params: TagManage.ReqUpdateTagGroupEnabledFlag,
) => {
  return dictRequest('/tag/updateTagGroupEnabledFlagById', params);
};

/**
 * [1-10188-1]根据标识停启用标签
 * @param params
 * @returns
 */
export const updateTagEnabledFlagById = (
  params: TagManage.ReqUpdateTagEnabledFlag,
) => {
  return dictRequest('/tag/updateTagEnabledFlagById', params);
};
