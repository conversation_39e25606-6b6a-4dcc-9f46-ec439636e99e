import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10233-1] 根据条件查询药品商品列表
 * @param params
 * @returns
 */
export const queryMedicineListByExample = (
  params: Medicine.MedicineListQueryParams,
) => {
  return dictRequest<
    Medicine.MedicineListItem,
    Medicine.MedicineListQueryParams
  >('/medicine/queryMedicineListByExample', params);
};

/**
 * [1-10234-1] 新增药品商品
 * @param params
 * @returns
 */
export const addMedicine = (params: Medicine.MedicineUpsertParams) => {
  return dictRequest<{ commodityId: string }>('/medicine/addMedicine', params, {
    successMsg: translation('global:create.success'),
  });
};

/**
 * [1-10235-1] 根据标识修改药品商品
 * @param params
 * @returns
 */
export const updateMedicineById = (params: Medicine.MedicineUpsertParams) => {
  return dictRequest('/medicine/updateMedicineById', params, {
    successMsg: translation('global:modify.success'),
  });
};
