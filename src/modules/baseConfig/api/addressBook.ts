import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10373-1] 新增用户收藏信息
 * @param params
 * @returns
 */
export const addUserFavorit = (params: AddressBook.UpsertUserFavorit) => {
  return dictRequest<AddressBook.UpsertUserFavorit>(
    '/userfavorit/addUserFavorit',
    params,
    {
      successMsg: translation('addUserFavorit', '收藏成功'),
    },
  );
};
/**
 * [1-10374-1] 删除用户收藏信息
 * @param params
 * @returns
 */
export const deleteUserFavorit = (params: AddressBook.UpsertUserFavorit) => {
  return dictRequest<AddressBook.UpsertUserFavorit>(
    '/userfavorit/deleteUserFavorit',
    params,
    {
      successMsg: translation('deleteUserFavorit', '取消收藏成功'),
    },
  );
};

/**
 * [1-10375-1] 根据条件查询通讯录
 * @param params
 * @returns
 */
export const queryContactsByExample = (
  params: AddressBook.QueryAddressBookParams,
) => {
  return dictRequest<{ addressId: string }>(
    '/addressbook/queryContactsByExample',
    params,
  );
};
