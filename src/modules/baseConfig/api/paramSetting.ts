import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10037-1]根据条件查询参数配置列表（定义态）
 * @param data
 * @returns
 */
export const queryParamListByExample = (params: ParamSetting.ReqParams) => {
  return dictRequest<ParamSetting.ParamInfo[], ParamSetting.ReqParams>(
    '/parameter/queryParamListByExample',
    params,
  );
};

/**
 * [1-10038-1] 新增参数
 * @param data
 * @returns
 */
export const addParam = (params: ParamSetting.ReqAddParam) => {
  return dictRequest<boolean>('/parameter/addParam', params, {
    successMsg: translation('global:create.success'),
  });
};

/**
 * [1-10039-1] 根据标识修改参数配置
 * @param data
 * @returns
 */
export const updateParamById = (params: ParamSetting.ReqUpdateParam) => {
  return dictRequest<boolean>('/parameter/updateParamById', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [1-10040-1] 根据参数分类代码获取下一参数编码
 * @param data
 * @returns
 */
export const getNextParamNoByParamCategoryCode = (params: {
  paramCategoryCode: string;
}) => {
  return dictRequest<{
    nextParamNo: string;
  }>('/parameter/getNextParamNoByParamCategoryCode', params);
};

/**
 * [1-10041-1] 根据参数编码查询参数配置（业务态）
 * @param data
 * @returns
 */
export const queryParamListByNos = (params: {
  paramNos: string[];
  hospitalId: string;
}) => {
  return dictRequest<ParamSetting.ResQueryParamListInfo>(
    '/parameter/queryParamListByNos',
    params,
    {
      successMsg: translation('global:modify.success'),
    },
  );
};
