import { dictRequest } from '@sun-toolkit/request';
// import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10172-1]检验单据是否为票据并返回票据分配信息
 * @param params
 * @returns
 */
export const verifyInvoiceFlagAndConvertToAllot = (
  params: Receipt.VerifyInvoiceFlagAndConvertToAllotReqParams,
) => {
  return dictRequest<Receipt.VerifyInvoiceFlagAndConvertToAllotReqItem[]>(
    '/receipt/verifyInvoiceFlagAndConvertToAllot',
    params,
  );
};

/**
 * [1-10175-1]根据条件查询单据列表
 * @param params
 * @returns
 */
export const queryReceiptByExample = (params: Receipt.ReceiptReqParams) => {
  return dictRequest<Receipt.ReceiptReqItem[]>(
    '/receipt/queryReceiptByExample',
    params,
  );
};

/**
 * [1-10176-1]新增单据
 * @param params
 * @returns
 */
export const addReceipt = (params: Receipt.AddReceiptReqParams) => {
  return dictRequest<Receipt.AddReceiptReqItem>('/receipt/addReceipt', params);
};

/**
 * [1-10177-1]根据标识修改单据
 * @param params
 * @returns
 */
export const updateReceiptById = (params: Receipt.UpdateReceiptReqParams) => {
  return dictRequest<{
    receiptTemplateRuleList: Receipt.ReceiptTemplateRuleReqItem[];
  }>('/receipt/updateReceiptById', params);
};

/**
 * [1-10178-1]根据菜单标识查询菜单的单据
 * @param params
 * @returns
 */
export const queryMenuXReceiptBySysMenuId = (
  params: Receipt.MenuXReceiptReqParams,
) => {
  return dictRequest<Receipt.MenuXReceiptReqItem>(
    '/receipt/queryMenuXReceiptBySysMenuId',
    params,
  );
};

/**
 * [1-10179-1]保存菜单的单据信息
 * @param params
 * @returns
 */
export const saveMenuXReceipt = (params: Receipt.SaveMenuXReceiptReqParams) => {
  return dictRequest('/receipt/saveMenuXReceipt', params);
};
