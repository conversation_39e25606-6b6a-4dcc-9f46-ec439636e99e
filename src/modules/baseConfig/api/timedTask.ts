import { jobRequest, dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [5-10001-1]根据条件查询定时任务列表
 * @param params
 * @returns
 */
export const queryTimedTaskByExample = (params: TimedTask.QueryParams) => {
  return jobRequest<TimedTask.TimedTaskInfo[]>(
    '/timedtask/queryTimedTaskByExample',
    params,
  );
};

/**
 * [5-10002-1]新增定时任务
 * @param params
 * @returns
 */
export const addTimedTask = (params: TimedTask.UpsertTimedTaskParams) => {
  return dictRequest('/timedtask/addTimedTask', params, {
    successMsg: translation('global:create.success'),
  });
};

/**
 * [5-10003-1]根据标识修改定时任务
 * @param params
 * @returns
 */
export const updateTimedTaskById = (
  params: TimedTask.UpsertTimedTaskParams,
) => {
  return dictRequest('/timedtask/updateTimedTaskById', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [5-10004-1]根据标识停启用医院的定时任务
 * @param params
 * @returns
 */
export const updateTimedTaskInstanceEnabledFlagById = (params: {
  timedTaskInstanceId: string;
  enabledFlag: number;
}) => {
  return jobRequest(
    '/timedtask/updateTimedTaskInstanceEnabledFlagById',
    params,
    {
      successMsg: translation(
        params.enabledFlag
          ? 'global:enabled.success'
          : 'global:disabled.success',
      ),
    },
  );
};

/**
 * [5-10005-1]根据标识查询定时任务执行记录
 * @param params
 * @returns
 */
export const queryTimedTaskExecLogById = (
  params: TimedTask.ExecLogQueryParams,
) => {
  return jobRequest<TimedTask.TimedTaskExecLogInfo[]>(
    '/timedtask/queryTimedTaskExecLogById',
    params,
  );
};

/**
 * [5-10006-1] 执行单次任务
 * @param params
 * @returns
 */
export const execTimedTaskOnlyOnceById = (params: {
  timedTaskInstanceId: string;
  timedTaskParamList?: { timedTaskParamId: string; paramValue?: string }[];
}) => {
  return jobRequest('/timedtask/execTimedTaskOnlyOnceById', params, {
    successMsg: translation('timedTask.execute.success', '执行成功'),
  });
};

/**
 * [5-10007-1] 新增医院的定时任务
 * @param params
 * @returns
 */
export const addTimedTaskInstance = (
  params: TimedTask.UpsertTimedTaskInstanceParams,
) => {
  return jobRequest<{ timedTaskInstanceId: string }>(
    '/timedtask/addTimedTaskInstance',
    params,
    {
      successMsg: translation('global:create.success'),
    },
  );
};

/**
 * [5-10008-1] 根据标识修改医院的定时任务
 * @param params
 * @returns
 */
export const updateTimedTaskInstanceById = (
  params: TimedTask.UpsertTimedTaskInstanceParams,
) => {
  return jobRequest('/timedtask/updateTimedTaskInstanceById', params, {
    successMsg: translation('global:modify.success'),
  });
};
