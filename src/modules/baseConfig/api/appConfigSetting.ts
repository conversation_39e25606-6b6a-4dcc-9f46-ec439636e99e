import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10297-1] 根据条件查询软件配置节点
 * @param params
 * @returns
 */
export const queryAppConfigKey = (
  params: AppConfigSetting.QueryAppConfigParams,
) => {
  return dictRequest<AppConfigSetting.AppConfigKeyItem[]>(
    '/appconfigmanage/queryAppConfigKey',
    params,
  );
};

/**
 * [1-10298-1] 新增软件配置节点
 * @param params
 * @returns
 */
export const addAppConfigKey = (
  params: AppConfigSetting.AddOrUpdateAppConfigKey,
) => {
  return dictRequest<{ appConfigKeyId: string }>(
    '/appconfigmanage/addAppConfigKey',
    params,
  );
};

/**
 * [1-10299-1] 编辑软件配置节点
 * @param params
 * @returns
 */
export const editAppConfigKey = (
  params: AppConfigSetting.AddOrUpdateAppConfigKey,
) => {
  return dictRequest<{ appConfigKeyId: string }>(
    '/appconfigmanage/editAppConfigKey',
    params,
  );
};

/**
 * [1-10300-1] 删除软件配置节点
 * @param params
 * @returns
 */
export const deleteAppConfigKey = (
  params: AppConfigSetting.AddOrUpdateAppConfigKey,
) => {
  return dictRequest<{ appConfigKeyId: string }>(
    '/appconfigmanage/deleteAppConfigKey',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};

/**
 * [1-10301-1] 根据条件查询软件配置项
 * @param params
 * @returns
 */
export const queryAppConfigByExample = (
  params: AppConfigSetting.QueryAppConfigParams,
) => {
  return dictRequest<AppConfigSetting.AppConfigList[]>(
    '/appconfigmanage/queryAppConfigByExample',
    params,
  );
};

/**
 * [1-10302-1] 新增软件配置项
 * @param params
 * @returns
 */
export const addAppConfig = (params: AppConfigSetting.AddOrUpdateAppConfig) => {
  return dictRequest<{ appConfigId: string }>(
    '/appconfigmanage/addAppConfig',
    params,
  );
};

/**
 * [1-10303-1] 编辑软件配置项
 * @param params
 * @returns
 */
export const editAppConfig = (
  params: AppConfigSetting.AddOrUpdateAppConfig,
) => {
  return dictRequest<{ appConfigId: string }>(
    '/appconfigmanage/editAppConfig',
    params,
  );
};

/**
 * [1-10304-1] 删除软件配置项
 * @param params
 * @returns
 */
export const deleteAppConfig = (
  params: AppConfigSetting.AddOrUpdateAppConfig,
) => {
  return dictRequest<{ appConfigId: string }>(
    '/appconfigmanage/deleteAppConfig',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};

/**
 * [1-10305-1] 添加软件配置节点
 * @param params
 * @returns
 */
export const appendAppConfigKey = (
  params: AppConfigSetting.AppendAppConfigKey,
) => {
  return dictRequest<{ appConfigId: string }>(
    '/appconfigmanage/appendAppConfigKey',
    params,
  );
};

/**
 * [1-10306-1] 移除软件配置节点
 * @param params
 * @returns
 */
export const removeAppConfigKey = (
  params: AppConfigSetting.removeAppConfigKey,
) => {
  return dictRequest<{ appConfigId: string }>(
    '/appconfigmanage/removeAppConfigKey',
    params,
  );
};
