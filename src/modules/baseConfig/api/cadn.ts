import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10229-1] 根据条件查询药品通用名列表
 * @param params
 * @returns
 */
export const queryCadnListByExample = (params: Cadn.CadnListQueryParams) => {
  return dictRequest<Cadn.CadnListItem[]>(
    '/cadn/queryCadnListByExample',
    params,
  );
};

/**
 * [1-10230-1] 新增药品通用名
 * @param params
 * @returns
 */
export const addCadn = (params: Partial<Cadn.CadnListItem>) => {
  return dictRequest<{ cadnId: string }>('/cadn/addCadn', params, {
    successMsg: translation('global:create.success'),
  });
};

/**
 * [1-10231-1] 根据标识修改药品通用名
 * @param params
 * @returns
 */
export const updateCadnById = (params: Partial<Cadn.CadnListItem>) => {
  return dictRequest('/cadn/updateCadnById', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [1-10232-1] 根据条件查询药品服务列表
 * 是否分页	Y
 * @param params
 * @returns
 */
export const queryMedicineServiceListByExample = (
  params: Cadn.MedicineServiceListQueryParams,
) => {
  return dictRequest<Cadn.MedicineServiceItem[]>(
    '/cadn/queryMedicineServiceListByExample',
    params,
  );
};

/**
 * [1-10362-1] 根据条件查询药品通用名及规格列表--new
 * @param params
 * @returns
 */
export const queryCadnAndMedicineSpecListByExample = (
  params: Cadn.CadnAndMedicineSpecListQueryParams,
) => {
  return dictRequest<Cadn.CadnMedicineSpecItem[]>(
    '/cadn/queryCadnAndMedicineSpecListByExample',
    params,
  );
};
