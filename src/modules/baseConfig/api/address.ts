import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10117-1]根据条件查询地址列表（定义态）
 * @param params
 * @returns
 * addressLevelDesc 地址级别描述
 */
export const queryAddressListByExample = (params: Address.QueryParams) => {
  return dictRequest<Address.AddressInfo[]>(
    '/address/queryAddressListByExample',
    params,
  );
};

/**
 * [1-10119-1] 根据标识修改地址
 * @param params
 * @returns
 */
export const updateAddressById = (
  params: Address.UpdateAddressNativeFlagById,
) => {
  return dictRequest<{ addressId: string }>(
    '/address/updateAddressById',
    params,
    {
      successMsg: translation('address.addressTable.updateSuccess', '修改成功'),
    },
  );
};
/**
 * [1-10120-1] 启停用地址
 * @param params
 * @returns
 */
export const updateAddressEnabledFlagById = (
  params: Address.UpdateAddressEnabledFlagByIdParams,
) => {
  return dictRequest<{ addressId: string }>(
    '/address/updateAddressEnabledFlagById',
    params,
  );
};

/**
 * [1-10121-1] 根据标识修改地址排序
 * @param params
 * @returns
 */
export const updateAddressSortByIds = (params: Address.AddressSortList) => {
  return dictRequest<{ addressId: string }>(
    '/address/updateAddressSortByIds',
    params,
  );
};
