import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10100-1]根据条件查询原因
 * @param params
 * @returns
 */
export const queryReasonByExample = (params: ReasonManage.QueryParams) => {
  return dictRequest<ReasonManage.ReasonInfo[]>(
    '/reason/queryReasonByExample',
    params,
  );
};

/**
 * [1-10101-1]新增原因
 * @param params
 * @returns
 */
export const addReason = (params: ReasonManage.UpsertReasonParams) => {
  return dictRequest<ReasonManage.ReasonInfo[]>('/reason/addReason', params);
};

/**
 * [1-10102-1]根据标识编辑原因
 * @param params
 * @returns
 */
export const updateReasonById = (params: ReasonManage.UpsertReasonParams) => {
  return dictRequest<ReasonManage.ReasonInfo[]>(
    '/reason/updateReasonById',
    params,
  );
};

/**
 * [1-10101-1]根据标识编辑原因排序
 * @param params
 * @returns
 */
export const updateReasonSortById = (params: {
  reasonSortList: {
    reasonId: string;
    sort: number;
  }[];
}) => {
  return dictRequest<ReasonManage.ReasonInfo[]>(
    '/reason/updateReasonSortById',
    params,
  );
};
