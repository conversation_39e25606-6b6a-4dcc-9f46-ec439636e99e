import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10316-1]根据条件查询数据类型
 * @param params
 * @returns
 */
export const queryDataTypeManageList = (
  params?: DataTypeManage.QueryDataTypeManageList,
) => {
  return dictRequest<DataTypeManage.DataTypeManageList[]>(
    '/datatypemanage/queryDataTypeByExample',
    params,
  );
};
/**
 * [1-10317-1]新增数据类型
 * @param params
 * @returns
 */
export const addDataTypeManage = (
  params: DataTypeManage.AddDataTypeManageParams,
) => {
  return dictRequest<DataTypeManage.AddDataTypeManageParams>(
    '/datatypemanage/addDataType',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10318-1]编辑数据类型
 * @param params
 * @param enabledFlag
 * @param enabledVal
 * @returns
 */
export const editDataTypeManage = (
  params: DataTypeManage.UpsertDataTypeManageParams,
  enabledFlag?: boolean,
  enabledVal?: number,
) => {
  return dictRequest<DataTypeManage.UpsertDataTypeManageParams>(
    '/datatypemanage/editDataType',
    params,
    {
      successMsg: enabledFlag
        ? enabledVal
          ? translation('global:disabled.success')
          : translation('global:enabled.success')
        : translation('global:edit.success'),
    },
  );
};
/**
 * [1-10319-1]删除数据类型
 * @param params
 * @returns
 */
export const deleteDataTypeManageById = (
  params: DataTypeManage.UpsertDataTypeManageParams,
) => {
  return dictRequest<DataTypeManage.DataTypeManageList>(
    '/datatypemanage/deleteDataType',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};
