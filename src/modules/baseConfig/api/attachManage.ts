import { basicRequest, dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [4-10052-1] 上传文件
 * @param params
 * "1、文件分页总数、文件页码要么同时为空要么同时非空；
 * 2、非空时：
 * 1）都要大于0
 * 2）文件页码小于等于文件分页总数
 * 3）路径："
 * @returns
 */

export const uploadFile = (params: FormData) => {
  return basicRequest<{ filePosition: string }>('/untils/uploadFile', params);
};
/**
 * [4-10092-1] 删除文件
 * @param params
 * @returns
 */
export const deleteFile = (params: AttachManage.FileParams) => {
  return basicRequest<AttachManage.FileParams[]>('/utils/deleteFile', params, {
    successMsg: translation('global:delete.success'),
  });
};
/**
 * [1-10363-1]根据条件查询附件信息
 * @param params
 * @returns
 */
export const queryAttachByExample = (
  params: AttachManage.QueryAttachByExample,
) => {
  return dictRequest<AttachManage.AttachList[]>(
    '/attachmanage/queryAttachByExample',
    params,
  );
};
/**
 * [1-10364-1]新增附件
 * @param params
 * @returns
 */
export const addAttachManage = (params: AttachManage.AddAttachManage) => {
  return dictRequest<AttachManage.AttachList[]>(
    '/attachmanage/addAttach',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10365-1]删除附件
 * @param params
 * @returns
 */
export const deleteAttach = (params: AttachManage.DeleteAttach) => {
  return dictRequest<AttachManage.AttachList>(
    '/attachmanage/deleteAttach',
    params,
    {
      successMsg:
        params.deleteFlag === 1
          ? translation('global:delete.success')
          : translation(
              'attachManage.attachManageTable.yichuSuccess',
              '移除成功',
            ),
    },
  );
};
/**
 * [1-10366-1]编辑
 * @param params
 * @returns
 */
export const editAttach = (params: AttachManage.EditAttachManage) => {
  return dictRequest<AttachManage.EditAttachManage>(
    '/attachmanage/editAttach',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};
