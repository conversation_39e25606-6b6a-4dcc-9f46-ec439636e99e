<script setup lang="ts">
  import { ref, nextTick, useAttrs, watch, computed } from 'vue';
  import { ProForm, ProTable, Title, ProDialog } from 'sun-biz';
  import {
    addInterface,
    updateInterfaceById,
  } from '../../../api/interfaceManage';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { dayjs } from '@sun-toolkit/shared';
  import { INVOKE_TYPE_CODE, FLAG } from '@/utils/constant';
  import {
    useTableColumnConfig,
    useFromConfigData,
  } from '../config/useCreateOrEditConfig';

  import { useTranslation } from 'i18next-vue';
  const SUFFIX_DLL = '.dll';
  const initModel = {
    invokeTypeCode: INVOKE_TYPE_CODE.HTTP,
    enabledFlag: FLAG.YES,
    builtInFlag: FLAG.NO,
  };
  const { t } = useTranslation();
  type Props = {
    row?: InterfaceManage.UpdateInterface;
    hospitals: InterfaceManage.OrganizationInfo[];
  };

  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const tableRef = ref();
  const attrs = useAttrs();
  const hospitalList = ref<InterfaceManage.OrganizationInfo[]>([]);
  const formModel = ref<Partial<InterfaceManage.AddInterface>>(initModel);
  const tableData = ref<
    (InterfaceManage.HospitalInfo & { editable: boolean })[]
  >([]);
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const fromConfig = useFromConfigData(formModel);
  const { tableConfig, addItem } = useTableColumnConfig(
    tableRef,
    tableData,
    hospitalList,
  );

  watch(
    () => props.row,
    () => {
      let dllName = props.row?.dllName
        ? props.row?.dllName.endsWith(SUFFIX_DLL)
          ? props.row?.dllName.replace(SUFFIX_DLL, '')
          : props.row?.dllName
        : undefined;
      formModel.value = {
        ...initModel,
        ...(props.row || {}),
        dllName,
      };
      tableData.value = (props.row?.hospitalList || []).map((item) => ({
        ...item,
        editable: false,
      }));
    },
  );

  watch(
    () => props.hospitals,
    () => {
      hospitalList.value = props.hospitals;
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      Promise.all([
        formRef?.value?.ref.validateField(),
        tableRef?.value?.formRef?.validateField(),
      ])
        .then(async () => {
          let model = formRef?.value?.model;
          let dllName = model?.dllName
            ? model?.dllName.endsWith(SUFFIX_DLL)
              ? model?.dllName
              : `${model?.dllName}${SUFFIX_DLL}`
            : undefined;
          let [, result] = props.row?.interfaceId
            ? await updateInterfaceById({
                ...model,
                dllName,
                interfaceId: props.row?.interfaceId,
                hospitalList: tableData.value.map(
                  ({
                    beginDate = '',
                    endDate = '',
                    hospitalId = '',
                    interfaceXHospitalId,
                  }) => ({
                    beginDate,
                    endDate,
                    hospitalId,
                    interfaceXHospitalId,
                  }),
                ),
              } as InterfaceManage.UpdateInterface)
            : await addInterface({
                ...model,
                dllName,
                hospitalList: tableData.value.map(
                  ({ beginDate = '', endDate = '', hospitalId = '' }) => ({
                    beginDate,
                    endDate,
                    hospitalId,
                  }),
                ),
              } as InterfaceManage.AddInterface);
          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.interfaceId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        })
        .catch((error) => {
          console.log(error);
          reject(['', new Error('参数错误')]);
        });
    });
  }

  function addClick() {
    addItem({
      editable: true,
      endDate: '2099-12-31 00:00:00',
      beginDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  }

  defineExpose({
    dialogRef,
  });

  const disabledButton = computed(
    () => hospitalList.value.length === tableData.value.length,
  );
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm :column="2" v-model="formModel" ref="formRef" :data="fromConfig" />
    <Title
      :title="$t('addOrEditInterface.hospitals.used', '使用的医院')"
      class="mb-4"
    >
      <el-button
        :disabled="disabledButton"
        size="small"
        type="primary"
        @click="addClick"
        >{{ $t('global:add') }}</el-button
      >
    </Title>
    <ProTable
      :max-height="242"
      ref="tableRef"
      row-key="interfaceXHospitalId"
      :editable="true"
      :columns="tableConfig"
      :data="tableData"
    />
  </ProDialog>
</template>
