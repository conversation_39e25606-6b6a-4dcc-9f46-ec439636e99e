<script setup lang="ts">
  import { ref, nextTick, useAttrs, watch, computed } from 'vue';
  import {
    ProTable,
    Title,
    ProDialog,
    useAppConfigData,
    MAIN_APP_CONFIG,
    useFetchDataset,
  } from 'sun-biz';
  import { Search } from '@element-sun/icons-vue';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import {
    queryTransactionListByInterfaceId,
    saveInterfaceTransaction,
    queryTransactionListByInterfaceTypeCode,
  } from '../../../api/interfaceManage';
  import { ElMessage } from 'element-sun';
  import { debounce } from '@sun-toolkit/shared';
  import { useTableColumnConfig } from '../config/useTransactionColumnConfig';
  import { useTranslation } from 'i18next-vue';
  import type { TableColumnCtx } from 'element-sun/es/components/table/src/table-column/defaults';
  import QuoteTransaction from './QuoteTransaction.vue';
  import { TRANSACTION_CODE_NAME } from '@/utils/constant';
  import { useGetFormList } from '../hooks/useGetForm.ts';
  import { FLAG } from '@/utils/constant.ts';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  interface SpanMethodProps {
    row: InterfaceManage.InterfaceTransactionInfo & { editable: boolean };
    column: TableColumnCtx<
      InterfaceManage.InterfaceTransactionInfo & { editable: boolean }
    >;
    rowIndex: number;
    columnIndex: number;
  }
  const { t } = useTranslation();
  const { queryFormList, formList } = useGetFormList();

  type Props = {
    row?: InterfaceManage.InterfaceInfo;
  };
  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const tableRef = ref();
  const quoteTransactionRef = ref();
  const attrs = useAttrs();
  const keyWord = ref<string>('');
  const filterValue = ref<string>('');
  const loading = ref<boolean>(false);
  const transactions = ref<InterfaceManage.InterfaceTypeCodeTranInfo[]>([]);
  const tableData = ref<
    (InterfaceManage.InterfaceTransactionInfo & { editable: boolean })[]
  >([]);
  const dataSetList = useFetchDataset([TRANSACTION_CODE_NAME]);
  const transactionCodes = computed(() => {
    return dataSetList?.value?.[TRANSACTION_CODE_NAME] || [];
  });
  let inputChange = debounce((value) => {
    filterValue.value = value;
  }, 500);
  const { tableConfig, addItem } = useTableColumnConfig(
    tableRef,
    tableData,
    transactionCodes,
    queryFormList,
    formList,
    handleSwitch,
  );

  watch(
    () => props.row,
    () => {
      fetchDataList(props.row?.interfaceId);
      fetchTransactionList(props.row?.interfaceTypeCode);
    },
  );

  async function fetchTransactionList(interfaceTypeCode: string | undefined) {
    if (!interfaceTypeCode) return;
    let [, result] = await queryTransactionListByInterfaceTypeCode({
      interfaceTypeCode: props.row?.interfaceTypeCode || '',
    });
    if (result?.success) {
      transactions.value = result.data;
    }
  }

  async function fetchDataList(interfaceId: string | undefined) {
    if (!interfaceId) return;
    loading.value = true;
    let [, result] = await queryTransactionListByInterfaceId({ interfaceId });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  }

  /**
   * 点击确认后
   */
  function submit() {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<[never, unknown]>(async (resolve, reject) => {
      try {
        /** 表单校验 */
        await tableRef?.value?.formRef?.validate();

        // 校验必填项
        const hasEmptyFields = tableData.value?.some((item) =>
          item.tranXBizEventList?.some(
            (im) => !im.bizEventId || !im.communicateTypeCode,
          ),
        );
        if (hasEmptyFields) {
          const errorMsg = t('valid.tips', '存在未完成填写内容！');
          ElMessage.error(errorMsg);
          return reject(new Error(errorMsg));
        }

        // 校验未保存内容
        const hasUnsavedChanges = tableData.value?.some(
          (item) =>
            item.editable ||
            item.tranXBizEventList?.some(
              (im) =>
                (
                  im as InterfaceManage.TranXBizEventReqItem & {
                    editable: boolean;
                  }
                ).editable,
            ),
        );
        if (hasUnsavedChanges) {
          const errorMsg = t('valid.editable.tips', '存在未保存的内容！');
          ElMessage.error(errorMsg);
          return reject(new Error(errorMsg));
        }

        let [, result] = await saveInterfaceTransaction({
          interfaceId: props.row?.interfaceId || '',
          interfaceTransactionList: tableData.value.map(
            ({
              transactionId,
              transactionCode,
              transactionDesc,
              enabledFlag,
              formId,
              tranXBizEventList,
            }) => ({
              transactionId,
              transactionCode,
              transactionDesc,
              enabledFlag,
              formId,
              tranXBizEventList,
            }),
          ),
        });
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t(
              props.row?.interfaceId
                ? 'global:modify.success'
                : 'global:create.success',
            ),
          });
          resolve([] as unknown as [never, unknown]);
        } else {
          reject(['', new Error('接口错误')]);
        }
      } catch (error) {
        console.log(error);
        reject(['', new Error('参数错误')]);
      }
    });
  }
  function handleEnter() {}
  function addClick() {
    addItem({
      editable: true,
      enabledFlag: ENABLED_FLAG.YES,
      tranXBizEventList: [],
    });

    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  }

  const showData = computed(() => {
    return tableData.value.filter((item) => {
      return (
        (item.transactionCode || '').includes(filterValue.value) ||
        (item.transactionDesc || '').includes(filterValue.value)
      );
    });
  });

  /** 停启用切换 */
  function handleSwitch(row: InterfaceManage.InterfaceTransactionInfo) {
    row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
  }

  /**
   * 合并单元格
   */
  const mergeColumns = ({ row, columnIndex }: SpanMethodProps) => {
    if (row.editable && !row.transactionId) {
      if (columnIndex === 2) {
        return {
          rowspan: 1,
          colspan: 2,
        };
      }
      if (columnIndex === 3) {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  };

  function quote() {
    quoteTransactionRef.value?.dialogRef.open();
    nextTick(() => {
      quoteTransactionRef.value?.tableRef.proTableRef.toggleAllSelection();
    });
  }

  function quoteData(data: InterfaceManage.InterfaceTransactionInfo[]) {
    let addData = data.filter(
      (item) =>
        !tableData.value.find(
          (cur) => cur?.transactionCode === item?.transactionCode,
        ),
    );
    tableData.value = [
      ...tableData.value,
      ...addData.map((item) => ({
        ...item,
        editable: false,
        enabledFlag: ENABLED_FLAG.YES,
      })),
    ];
  }

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    class="w-3/5"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <Title title="交易列表" class="mb-4"> </Title>
    <span class="mb-4 flex justify-between">
      <el-input
        class="mr-5 w-72"
        :placeholder="t('global:placeholder.keyword')"
        v-model="keyWord"
        @input="inputChange"
        @keydown.enter="handleEnter"
        :suffix-icon="Search"
      />
      <div>
        <el-button :disabled="!isCloudEnv" size="small" @click="quote">{{
          $t('interfaceTransaction.quote', '引用')
        }}</el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="
            tableData.length === transactionCodes.length || !isCloudEnv
          "
          @click="addClick"
          >{{ $t('global:add') }}</el-button
        >
      </div>
    </span>
    <div style="height: 350px" class="flex flex-col">
      <ProTable
        :max-height="350"
        ref="tableRef"
        :span-method="mergeColumns"
        :loading="loading"
        :row-key="
          (row: any) => (row.transactionId ? row.transactionId : row.key)
        "
        :editable="true"
        :columns="tableConfig"
        :data="showData"
      />
    </div>
  </ProDialog>
  <QuoteTransaction
    @success="quoteData"
    ref="quoteTransactionRef"
    v-bind="{
      transactions,
      title: $t('quoteTransaction.dialog.title', '接口交易 - {{name}}', {
        name: props.row?.interfaceTypeDesc,
      }),
    }"
  />
</template>
