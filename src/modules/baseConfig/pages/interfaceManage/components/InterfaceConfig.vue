<script setup lang="ts">
  import { ref, nextTick, useAttrs, watch, computed } from 'vue';
  import {
    ProTable,
    Title,
    ProDialog,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { Search } from '@element-sun/icons-vue';
  import {
    queryInterfaceSettingListByKeys,
    saveInterfaceSetting,
  } from '../../../api/interfaceManage';
  import { ElMessage } from 'element-sun';
  import { debounce } from '@sun-toolkit/shared';
  import { useTableColumnConfig } from '../config/useInterfaceTableConfig';
  import { useTranslation } from 'i18next-vue';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  type Props = {
    row?: InterfaceManage.UpdateInterface;
  };

  const props = defineProps<Props>();
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const tableRef = ref();
  const attrs = useAttrs();
  const keyWord = ref<string>('');
  const filterValue = ref<string>('');
  const loading = ref<boolean>(false);
  const tableData = ref<
    (InterfaceManage.InterfaceConfigInfo & { editable: boolean })[]
  >([]);

  let inputChange = debounce((value) => {
    filterValue.value = value;
  }, 500);
  const { tableConfig, addItem } = useTableColumnConfig(
    tableRef,
    tableData,
    isCloudEnv,
  );

  watch(
    () => props.row,
    () => {
      fetchDataList(props.row?.interfaceId);
    },
  );

  async function fetchDataList(interfaceId: string | undefined) {
    if (!interfaceId) return;
    loading.value = true;
    let [, result] = await queryInterfaceSettingListByKeys({ interfaceId });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  }

  /**
   * 点击确认后
   */
  function submit() {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<[never, unknown]>(async (resolve, reject) => {
      try {
        await tableRef?.value?.formRef?.validate();
        let [, result] = await saveInterfaceSetting({
          interfaceId: props.row?.interfaceId || '',
          interfaceSettingList: tableData.value.map(
            ({
              interfaceSettingId,
              configKey,
              configDesc,
              configDefaultValue,
              configValue,
            }) => ({
              interfaceSettingId,
              configKey,
              configDesc,
              configDefaultValue,
              configValue,
            }),
          ),
        });
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t(
              props.row?.interfaceId
                ? 'global:modify.success'
                : 'global:create.success',
            ),
          });
          resolve([] as unknown as [never, unknown]);
        } else {
          reject(['', new Error('接口错误')]);
        }
      } catch (error) {
        console.log(error);
        reject(['', new Error('参数错误')]);
      }
    });
  }
  function handleEnter() {}
  function addClick() {
    addItem({
      editable: true,
    });
    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  }

  const showData = computed(() => {
    return tableData.value.filter((item) => {
      return (
        (item.configValue || '').includes(filterValue.value) ||
        (item.configDesc || '').includes(filterValue.value) ||
        (item.configKey || '').includes(filterValue.value)
      );
    });
  });

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <Title :title="$t('interfaceConfig.config.list', '配置列表')" class="mb-4">
    </Title>
    <span class="mb-4 flex justify-between">
      <el-input
        class="mr-5 w-72"
        :placeholder="t('global:placeholder.keyword')"
        v-model="keyWord"
        @input="inputChange"
        @keydown.enter="handleEnter"
        :suffix-icon="Search"
      />
      <el-button
        size="small"
        type="primary"
        :disabled="!isCloudEnv"
        @click="addClick"
        >{{ $t('global:add') }}</el-button
      >
    </span>
    <div style="height: 310px">
      <ProTable
        max-height="310"
        ref="tableRef"
        :loading="loading"
        row-key="interfaceId"
        :editable="true"
        :columns="tableConfig"
        :data="showData"
      />
    </div>
  </ProDialog>
</template>
