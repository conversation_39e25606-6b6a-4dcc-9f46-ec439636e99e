<script setup lang="ts">
  import { ref, watch } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { queryTransactionListByInterfaceId } from '../../../api/interfaceManage';
  import { useFromConfigData } from '../config/useCreateOrEditConfig';
  import { useTableColumnConfig } from '../config/useViewDetailConfig';

  type Props = {
    row?: InterfaceManage.UpdateInterface;
  };

  const props = defineProps<Props>();
  const loading = ref(false);
  const dialogVisible = ref(false);
  const tableRef = ref();
  const formModel = ref<Partial<InterfaceManage.AddInterface>>();
  const tableData = ref<InterfaceManage.InterfaceTransactionInfo[]>([]);
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const fromConfig = useFromConfigData(formModel);
  const tableConfig = useTableColumnConfig();

  watch(
    () => props.row,
    () => {
      formModel.value = {
        ...(props.row || {}),
      };
      if (props.row?.interfaceId) {
        fetchTableList(props.row?.interfaceId);
      }
    },
  );

  async function fetchTableList(interfaceId: string) {
    if (!interfaceId) return;
    loading.value = true;
    let [, result] = await queryTransactionListByInterfaceId({ interfaceId });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  }

  function openDialog() {
    dialogVisible.value = true;
  }

  defineExpose({
    openDialog,
  });
</script>

<template>
  <el-dialog v-model="dialogVisible" :title="900" width="900">
    <div v-loading="loading">
      <ProForm
        :disabled="true"
        :column="2"
        v-model="formModel"
        ref="formRef"
        :data="fromConfig"
      />
      <Title
        :title="$t('viewInterfaceDetail.transaction.title', '接口的交易')"
        class="mb-4"
      >
      </Title>
      <span class="mb-4 flex justify-between"></span>
      <div style="height: 200px">
        <ProTable
          :max-height="200"
          ref="tableRef"
          row-key="interfaceId"
          :editable="true"
          :columns="tableConfig"
          :data="tableData"
        />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{
          $t('global:close')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
