<script setup lang="ts" name="bizEvent">
  import { ref, nextTick, computed, toRef } from 'vue';
  import { Plus } from '@element-sun/icons-vue';
  import { useGetBizEventList } from '../hooks/useGetBizEvent';
  import { useBizEventConfig } from '../config/useBizEventTableConfig';
  import { ProTable, useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';

  const { rowValue } = defineProps<{
    rowValue: InterfaceManage.InterfaceTransactionInfo;
  }>();

  const loading = ref<boolean>(false);
  const tableData = toRef(() => rowValue.tranXBizEventList || []);

  /** 元素ref */
  const bizEventTableRef = ref();

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const { bizEventList, queryDictBizEventList } = useGetBizEventList();
  const orgId = computed(() => currentOrg?.orgId);

  /** 新增业务事件 */
  const handleAddFn = async () => {
    await addItem({
      editable: true,
    } as InterfaceManage.TranXBizEventReqItem & { editable: boolean });

    nextTick(() => {
      const row = bizEventTableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };

  const { tableConfig, addItem } = useBizEventConfig({
    tableRef: bizEventTableRef,
    data: tableData,
    bizEventList,
    queryDictBizEventList,
    hospitalId: orgId,
  });
</script>
<template>
  <div>
    <ProTable
      ref="bizEventTableRef"
      row-key="tranXBizEventId"
      max-height="300"
      :editable="true"
      :data="tableData"
      :loading="loading"
      :columns="tableConfig"
    />
    <el-button
      @click="() => handleAddFn()"
      :icon="Plus"
      class="w-full rounded-none border-t-0"
      >{{ $t('global:add') }}</el-button
    >
  </div>
</template>
