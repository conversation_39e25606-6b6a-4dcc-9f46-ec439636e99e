import { ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { queryDictBizEvent } from '@/modules/baseConfig/api/bizEvent';

// 获取号源渠道
export function useGetBizEventList() {
  const loading = ref(false);
  const bizEventList = ref<BizEvent.BizEventInfo[]>([]);

  const queryDictBizEventList = async (params: BizEvent.QueryParams) => {
    loading.value = true;
    const [, res] = await queryDictBizEvent({
      enabledFlag: ENABLED_FLAG.YES,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      bizEventList.value = res.data ?? [];
    }
  };

  return {
    loading,
    bizEventList,
    queryDictBizEventList,
  };
}
