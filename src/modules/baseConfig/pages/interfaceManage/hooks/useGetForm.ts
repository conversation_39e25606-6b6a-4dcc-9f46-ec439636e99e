import { ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { queryFormByExample } from '@/modules/componentManage/api/formDesign';

// 获取号源渠道
export function useGetFormList() {
  const loading = ref(false);
  const formList = ref<FormDesign.FormInfo[]>([]);

  const queryFormList = async (keyWord: string = '') => {
    loading.value = true;
    const [, res] = await queryFormByExample({
      keyWord,
      enabledFlag: ENABLED_FLAG.YES,
    });
    loading.value = false;
    if (res?.success) {
      formList.value = res.data ?? [];
    }
  };

  return {
    loading,
    formList,
    queryFormList,
  };
}
