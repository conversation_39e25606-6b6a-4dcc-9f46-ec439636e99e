import { Ref } from 'vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';

export function useTableColumnConfig(
  tableRef: Ref<TableRef>,
  data: Ref<(InterfaceManage.InterfaceConfigInfo & { editable: boolean })[]>,
  isCloudEnv: boolean | undefined,
) {
  const { toggleEdit, cancelEdit, delItem, addItem } = useEditableTable({
    tableRef,
    data,
    id: 'interfaceId',
  });

  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber', '序号'),
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('InterfaceTableConfig.configKey', '关键字'),
        prop: 'configKey',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('InterfaceTableConfig.configKey', '关键字'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.InterfaceConfigInfo & { editable?: boolean },
        ) => {
          return row.editable && isCloudEnv ? (
            <el-input
              maxLength={32}
              v-model={row.configKey}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTableConfig.configKey', '关键字'),
              })}
            ></el-input>
          ) : (
            <>{row.configKey}</>
          );
        },
      },
      {
        label: t('InterfaceTableConfig.configDesc', '描述'),
        prop: 'configDesc',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('InterfaceTableConfig.configDesc', '描述'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.InterfaceConfigInfo & { editable?: boolean },
        ) => {
          return row.editable && isCloudEnv ? (
            <el-input
              maxLength={256}
              v-model={row.configDesc}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTableConfig.configDesc', '描述'),
              })}
            ></el-input>
          ) : (
            <>{row.configDesc}</>
          );
        },
      },
      {
        label: t('InterfaceTableConfig.configDefaultValue', '默认值'),
        prop: 'configDefaultValue',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('InterfaceTableConfig.configDefaultValue', '默认值'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.InterfaceConfigInfo & { editable?: boolean },
        ) => {
          return row.editable && isCloudEnv ? (
            <el-input
              maxLength={1024}
              v-model={row.configDefaultValue}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTableConfig.configDefaultValue', '默认值'),
              })}
            ></el-input>
          ) : (
            <>{row.configDefaultValue}</>
          );
        },
      },
      {
        label: t('InterfaceTableConfig.configValue', '配置值'),
        prop: 'configValue',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('InterfaceTableConfig.configValue', '配置值'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.InterfaceConfigInfo & { editable?: boolean },
        ) => {
          return row.editable ? (
            <el-input
              maxLength={1024}
              v-model={row.configValue}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTableConfig.configValue', '配置值'),
              })}
            ></el-input>
          ) : (
            <>{row.configValue}</>
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'interfaceId',
        width: 150,
        render: (
          row: BizNoGenerateRule.BizNoFragment & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  <el-button
                    type="primary"
                    disabled={!isCloudEnv}
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:delete')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
    addItem,
  };
}
