import { useFormConfig } from 'sun-biz';
import {
  INTERFACE_TYPE_CODE_NAME,
  INVOKE_TYPE_CODE_NAME,
} from '@/utils/constant';
import { Ref } from 'vue';
export function useSearchBarConfig(
  hospitalList: Ref<InterfaceManage.OrganizationInfo[]>,
  fetchTableList: (init?: boolean) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [INTERFACE_TYPE_CODE_NAME, INVOKE_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('interfaceManage.search.interfaceTypeCode', '接口类型'),
        name: 'interfaceTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('interfaceManage.search.interfaceTypeCode', '接口类型'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-40',
          options: dataSet?.value
            ? dataSet.value?.[INTERFACE_TYPE_CODE_NAME]
            : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('interfaceManage.search.invokeTypeCode', '交互方式'),
        name: 'invokeTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('interfaceManage.search.invokeTypeCode', '交互方式'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-40',
          options: dataSet?.value ? dataSet.value?.[INVOKE_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'select',
        triggerModelChange: true,
        extraProps: {
          className: 'w-40',
          options: hospitalList?.value
            ? hospitalList.value.map((item) => {
                return {
                  value: item.orgId,
                  label: item.orgNameDisplay,
                };
              })
            : [],
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          className: 'w-36',
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              fetchTableList(true);
            }
          },
        },
      },
    ],
  });
  return data;
}
