import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
export function useTableColumnConfig() {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        render: (row: Menu.MenuInfo, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('InterfaceTransactionInfo.transactionCode', '交易代码'),
        prop: 'transactionCode',
        minWidth: 170,
      },
      {
        label: '接口交易',
        prop: 'transactionDesc',
        minWidth: 170,
      },
      {
        label: t('global:enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        fixed: 'right',
        render: (row: Menu.MenuInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
    ],
  });
}
