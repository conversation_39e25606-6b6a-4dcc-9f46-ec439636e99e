import { useColumnConfig, useFormConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant';
export function useTagFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'tagGroupName',
        label: t('addTagManage.tagGroup.Name.title', '标签分组'),
        component: 'input',
        extraProps: {
          disabled: true,
        },
        rules: [
          {
            required: true,
            message: t('addTagManage.tagGroupName.placeholder', '标签分组'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'tagName',
        label: t('addTagManage.tagName.title', '标签名称'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('addTagManage.tagName.title', '标签名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('addTagManage.tagName.title', '标签名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'tag2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'tagExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'spellNo',
        component: 'input',
        label: t('global:spellNo'),
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        component: 'input',
        label: t('global:wbNo'),
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addTagManage.enabledFlag.placeholder',
          '请选择启用状态',
        ),
        label: t('addTagManage.enabledFlag', '启用状态'),
        defaultValue: FLAG.YES,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'inline-prompt': true,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}

export function useTagColumnConfig(
  handleEnableSwitch: (row: TagManage.TagInfo) => void,
  openDialog: (data: { title: string; row: TagManage.TagInfo }) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
        },
        {
          label: t('global:sequenceNumber'),
          prop: 'sequenceNumber',
          render: (row: object, index: number) => <>{index + 1}</>,
          minWidth: 70,
        },
        {
          label: t('tagConfig.table.tagId.title', '标签标识'),
          prop: 'tagId',
          supportCopyAndTips: true,
          minWidth: 220,
        },
        {
          label: t('global:name'),
          prop: 'tagNameDisplay',
          supportCopyAndTips: true,
          minWidth: 100,
        },
        {
          label: t('global:secondName'),
          prop: 'tag2ndName',
          supportCopyAndTips: true,
          supportTextCopy: false,
          minWidth: 100,
        },
        {
          label: t('global:thirdName', '扩展名称'),
          prop: 'tagExtName',
          minWidth: 100,
        },
        {
          label: t('global:enableStatus'),
          prop: 'enableStatus',
          render: (row: TagManage.TagInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
          minWidth: 90,
        },
        {
          label: t('global:operation'),
          prop: 'action',
          fixed: 'right',
          render: (row: TagManage.TagInfo) => {
            return (
              <el-button
                link
                type="primary"
                onClick={() => {
                  openDialog({
                    title: t(
                      'tagConfig.table.editDialog.title',
                      '编辑标签 “{{name}}”',
                      {
                        name: row.tagNameDisplay,
                      },
                    ),
                    row: row,
                  });
                }}
              >
                {t('global:edit')}
              </el-button>
            );
          },
        },
      ];
    },
  });
}
