import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
export function useGroupInfoConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'tagGroupId',
        label: t('tagManage.groupInfo.tagGroupId', '标识'),
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'tagGroupNameDisplay',
        label: t('global:name'),
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'tagGroup2ndName',
        label: t('global:secondName'),
        component: 'text',
      },
      {
        name: 'tagGroupExtName',
        label: t('global:thirdName'),
        component: 'text',
      },
      {
        name: 'spellNo',
        supportCopyAndTips: true,
        label: t('global:spellNo'),
        component: 'text',
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: true,
        },
      },
    ],
  });
  return data;
}
