<script setup lang="tsx">
  import { ProTable, Title, type AnyObject, DmlButton } from 'sun-biz';
  import { onMounted, reactive, computed, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import {
    queryTagGroupListByExample,
    updateTagGroupEnabledFlagById,
  } from '@/modules/baseConfig/api/tagManage';
  import { debounce } from '@sun-toolkit/shared';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useTranslation } from 'i18next-vue';
  import { useTagGroupColumnConfig } from '../config/useTagGroupConfigData';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import AddOrEditGroup from './AddOrEditGroup.vue';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  type Props = {
    setSelectTagGroup: (row: TagManage.TagGroup) => void;
  };
  interface DialogData {
    title: string;
    row: Partial<TagManage.TagGroup>;
  }
  const props = defineProps<Props>();
  const tagGroups = ref<TagManage.TagGroup[]>([]);
  const tableRef = ref();
  const keyWord = ref<string>('');
  const selections = ref<TagManage.TagGroup[]>([]);
  const addOrEditGroupRef = ref();
  const loading = ref(false);
  const groupData = reactive<DialogData>({
    title: '',
    row: {},
  });
  onMounted(() => {
    fetchData();
  });

  function handleEnableSwitch(row: TagManage.TagGroup) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.tagGroupNameDisplay,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await updateTagGroupEnabledFlagById({
            enabledFlag:
              row.enabledFlag === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
            tagGroupId: row.tagGroupId,
          });
          if (result?.success) {
            resolve();
            fetchData();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  function currentChange(row: TagManage.TagGroup) {
    if (row) props.setSelectTagGroup(row);
  }

  async function fetchData() {
    loading.value = true;
    let [, result] = await queryTagGroupListByExample({
      keyWord: keyWord.value,
    });
    loading.value = false;
    if (result?.success) {
      if ((result.data || [])?.length) {
        tableRef.value.setCurrentRow(result.data[0]);
      }
      tagGroups.value = result.data;
    }
  }

  function openDialog(data: DialogData) {
    groupData.title = data.title;
    groupData.row = data.row;
    addOrEditGroupRef.value.dialogRef.open();
  }

  function selectChange(value: AnyObject[]) {
    selections.value = value as TagManage.TagGroup[];
  }

  function addGroup() {
    openDialog({
      title: t('add.tagGroup.dialog.title', '新增标签组'),
      row: {},
    });
  }
  const columns = useTagGroupColumnConfig(handleEnableSwitch, openDialog);
  const bizData = computed(() => {
    const list = selections.value?.map((item) => {
      return item.tagGroupId;
    });
    return list ?? [];
  });
  let inputChange = debounce(fetchData, 500);
</script>
<template>
  <div class="p-box flex h-full flex-1 flex-col">
    <Title :title="$t('tagManage.tag.group.list', '标签分组列表')" class="mb-5">
      <span>
        <el-input
          v-model="keyWord"
          @input="inputChange"
          @keydown.enter="fetchData"
          class="mr-5 w-72"
          :placeholder="t('global:placeholder.keyword')"
          :suffix-icon="Search"
        />
        <DmlButton
          :code="BIZ_ID_TYPE_CODE.DICT_TAG_GROUP"
          :biz-data="bizData"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button
          type="primary"
          :disabled="!isCloudEnv"
          class="ml-4"
          @click="addGroup"
          >{{ $t('global:add') }}</el-button
        >
      </span>
    </Title>
    <pro-table
      :highlight-current-row="true"
      @selection-change="selectChange"
      @current-change="currentChange"
      row-class-name="cursor-pointer"
      ref="tableRef"
      :loading="loading"
      :columns="columns"
      row-key="tagGroupId"
      :data="tagGroups"
    />
    <AddOrEditGroup
      v-bind="groupData"
      @success="fetchData"
      ref="addOrEditGroupRef"
    />
  </div>
</template>
