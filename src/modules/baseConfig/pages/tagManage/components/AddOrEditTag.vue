<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { ProForm, ProDialog } from 'sun-biz';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useTagFormConfig } from '../config/useTagConfigData';
  import { addTag, updateTagById } from '@/modules/baseConfig/api/tagManage';

  const { t } = useTranslation();
  export type Props = {
    row?: Partial<TagManage.TagInfo>;
    title?: string;
    sort: number;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const formModel = ref({});
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const data = useTagFormConfig();

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          let [, result] = props.row?.tagId
            ? await updateTagById({
                ...formModel.value,
                tagId: props.row?.tagId,
              } as unknown as TagManage.ReqUpdateTag)
            : await addTag({
                ...formModel.value,
                sort: props.sort,
              } as TagManage.ReqAddTag);
          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.tagId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="props.title"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="1" :data="data"
  /></ProDialog>
</template>
