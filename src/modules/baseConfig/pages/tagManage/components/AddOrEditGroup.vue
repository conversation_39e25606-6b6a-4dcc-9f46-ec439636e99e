<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { ProForm, ProDialog } from 'sun-biz';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useTagGroupFormConfig } from '../config/useTagGroupConfigData';
  import {
    addTagGroup,
    updateTagGroupById,
  } from '@/modules/baseConfig/api/tagManage';

  const { t } = useTranslation();
  export type Props = {
    row?: Partial<TagManage.TagGroup>;
    title?: string;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const formModel = ref({});
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();
  const data = useTagGroupFormConfig();

  watch(
    () => props.row,
    () => {
      formModel.value = props.row || {};
    },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          let [, result] = props.row?.tagGroupId
            ? await updateTagGroupById({
                ...formModel.value,
                tagGroupId: props.row?.tagGroupId,
              } as unknown as TagManage.ReqUpdateTagGroup)
            : await addTagGroup(formModel.value as TagManage.ReqAddTagGroup);
          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.tagGroupId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="props.title"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="1" :data="data"
  /></ProDialog>
</template>
