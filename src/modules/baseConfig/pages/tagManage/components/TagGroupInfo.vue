<script setup lang="tsx">
  import { Title, ProForm } from 'sun-biz';
  import { ref, watch } from 'vue';
  import { useGroupInfoConfig } from '../config/useGroupInfoConfig';

  type Props = {
    groupInfo: TagManage.TagGroup | null;
  };

  const props = withDefaults(defineProps<Props>(), {});
  const formModel = ref<TagManage.TagGroup>({} as TagManage.TagGroup);
  watch(
    () => props.groupInfo,
    () => {
      if (props.groupInfo) {
        formModel.value = props.groupInfo;
      }
    },
  );

  const show = ref(false);

  function handleClick() {
    show.value = !show.value;
  }
  const data = useGroupInfoConfig();
</script>
<template>
  <div class="shrink-0">
    <div :class="['mb-4', 'cursor-pointer', 'pt-2']" @click="handleClick">
      <Title :title="$t('tagManage.tag.group.info', '标签分组信息')">
        <span>
          <el-icon v-if="show"><CaretBottom /></el-icon>
          <el-icon v-else><CaretLeft /></el-icon>
        </span>
      </Title>
    </div>
    <div
      :class="[
        show ? 'h-36' : 'h-0',
        show ? 'mb-2' : '',
        'transition-all',
        'duration-300',
        'overflow-hidden',
        'ease-in',
      ]"
    >
      <ProForm ref="formRef" :column="3" v-model="formModel" :data="data" />
    </div>
    <hr class="mb-4" />
  </div>
</template>
