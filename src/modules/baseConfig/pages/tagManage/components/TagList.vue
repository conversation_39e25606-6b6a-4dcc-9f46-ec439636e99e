<script setup lang="tsx">
  import { ProTable, Title, type AnyObject, DmlButton } from 'sun-biz';
  import { Search } from '@element-sun/icons-vue';
  import { ref, watch, reactive, computed } from 'vue';
  import {
    queryTagListByExample,
    updateTagEnabledFlagById,
    updateTagSortByIds,
  } from '@/modules/baseConfig/api/tagManage';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { debounce } from '@sun-toolkit/shared';
  import { useTagColumnConfig } from '../config/useTagConfigData';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import AddOrEditTag from './AddOrEditTag.vue';
  const { t } = useTranslation();
  interface DialogData {
    title: string;
    row: Partial<TagManage.TagInfo> & {
      tagGroupName?: string;
      tagGroupId?: string;
    };
  }
  type Props = {
    groupInfo: TagManage.TagGroup | null;
  };
  const state = reactive({
    loading: false,
  });
  const props = defineProps<Props>();
  const addOrEditTagRef = ref();
  const tagData = reactive<DialogData>({
    title: '',
    row: {},
  });
  const tableData = ref<TagManage.TagInfo[]>([]);
  const tableRef = ref();
  const keyWord = ref<string>('');
  const selections = ref<TagManage.TagInfo[]>([]);

  watch(
    () => props.groupInfo,
    () => {
      if (props.groupInfo) {
        fetchData();
      }
    },
  );

  async function fetchData() {
    if (!props.groupInfo) return;
    state.loading = true;
    let [, result] = await queryTagListByExample({
      keyWord: keyWord.value,
      tagGroupIds: [props.groupInfo.tagGroupId],
    });
    state.loading = false;
    if (result?.success) {
      let { data } = result;
      tableData.value = data;
    }
  }

  function handleSelectChange(value: TagManage.TagInfo[]) {
    selections.value = value;
  }

  async function handleSortEnd(data: AnyObject[]) {
    let [, result] = await updateTagSortByIds({
      tagList: data.map((item, index) => {
        return {
          tagId: item.tagId,
          sort: index + 1,
        };
      }),
    });
    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      fetchData();
    }
  }

  function handleEnableSwitch(row: TagManage.TagInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.tagNameDisplay,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await updateTagEnabledFlagById({
            enabledFlag:
              row.enabledFlag === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
            tagId: row.tagId,
          });
          if (result?.success) {
            resolve();
            fetchData();
            ElMessage({
              type: 'success',
              message:
                row.enabledFlag === ENABLED_FLAG.YES
                  ? t('global:disabled.success')
                  : t('global:enabled.success'),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  function openDialog(data: DialogData) {
    tagData.title = data.title;
    tagData.row = {
      ...data.row,
      tagGroupName: props.groupInfo?.tagGroupName,
      tagGroupId: props.groupInfo?.tagGroupId,
    };
    addOrEditTagRef.value.dialogRef.open();
  }

  function addTag() {
    openDialog({
      title: t('add.tag.dialog.title', '新增标签'),
      row: {},
    });
  }

  let inputChange = debounce(fetchData, 500);
  const columns = useTagColumnConfig(handleEnableSwitch, openDialog);

  const bizData = computed(() => {
    const list = selections.value?.map((item) => {
      return item.tagId;
    });
    return list ?? [];
  });
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <Title :title="$t('tagManage.tag.list', '标签列表')">
      <span>
        <el-input
          v-model="keyWord"
          class="mr-5 w-72"
          :placeholder="t('global:placeholder.keyword')"
          @input="inputChange"
          @keydown.enter="fetchData"
          :suffix-icon="Search"
        />
        <DmlButton
          :code="BIZ_ID_TYPE_CODE.DICT_TAG"
          :biz-data="bizData"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button @click="addTag" type="primary" class="ml-4">{{
          $t('global:add')
        }}</el-button>
      </span></Title
    >
    <pro-table
      :loading="state.loading"
      :data="tableData"
      draggable
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
      ref="tableRef"
      :columns="columns"
      row-key="tagId"
    />
    <AddOrEditTag
      v-bind="tagData"
      :sort="tableData.length + 1"
      @success="fetchData"
      ref="addOrEditTagRef"
    />
  </div>
</template>
