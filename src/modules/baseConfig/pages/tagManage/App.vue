<script setup lang="ts" name="tagManage">
  import { reactive } from 'vue';
  import TagGroupList from './components/TagGroupList.vue';
  import TagList from './components/TagList.vue';
  import TagGroupInfo from './components/TagGroupInfo.vue';
  export type AppState = {
    groupInfo: TagManage.TagGroup | null;
  };

  const appState = reactive<AppState>({
    groupInfo: null,
  });

  function setSelectTagGroup(row: TagManage.TagGroup) {
    appState.groupInfo = row;
  }
</script>

<template>
  <el-row class="p-box h-full">
    <el-col :span="12" class="h-full border-r pr-5">
      <TagGroupList
        :set-select-tag-group="setSelectTagGroup"
        :select-scheme-row="appState.groupInfo"
      />
    </el-col>
    <el-col class="flex h-full flex-col overflow-hidden pl-5" :span="12">
      <TagGroupInfo :group-info="appState.groupInfo" />
      <TagList :group-info="appState.groupInfo"
    /></el-col>
  </el-row>
</template>

<style>
  micro-app-body {
    height: 100%;
  }
</style>
