<script lang="ts" name="dataTypeManage" setup>
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { useDataTypeManageFormConfig } from './config/useFormConfig.tsx';
  import { useDataTypeManageTableConfig } from './config/useTableConfig.tsx';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    addDataTypeManage,
    deleteDataTypeManageById,
    editDataTypeManage,
    queryDataTypeManageList,
  } from '@/modules/baseConfig/api/dataTypeManage.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { commonSort } from '@/api/common.ts';
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const dataTypeManageTableRef = ref();
  const selectTableData = ref<DataTypeManage.DataTypeManageList[]>([]);

  let queryDataTypeManageListParams =
    ref<DataTypeManage.QueryDataTypeManageList>({
      keyWord: '',
      dtUsageScopeCode: '',
      dataTypeIds: [],
      enabledFlag: undefined,
    });
  const dataTypeManageList = ref<DataTypeManage.DataTypeManageList[]>([]); // 表格数据
  const loading = ref(false);
  const mode = ref<string>('add');

  // 查询数据类型
  const queryDataTypeManageListData = async (
    params: DataTypeManage.QueryDataTypeManageList = {},
  ) => {
    loading.value = true;
    queryDataTypeManageListParams.value = {
      ...queryDataTypeManageListParams.value,
      ...params,
    };
    let [, res] = await queryDataTypeManageList({
      ...queryDataTypeManageListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      res.data.sort(
        (
          a: DataTypeManage.DataTypeManageList,
          b: DataTypeManage.DataTypeManageList,
        ) => Number(a.sort) - Number(b.sort),
      );
      dataTypeManageList.value = res.data || [];
    }
  };

  // 启用状态改变
  const changeSelect = async (
    data?: DataTypeManage.QueryDataTypeManageList,
  ) => {
    queryDataTypeManageListParams.value = {
      ...queryDataTypeManageListParams.value,
      ...data,
    };
    await queryDataTypeManageListData();
  };

  const canUpsertTableRow = () => {
    const isEditing = dataTypeManageList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  // 编辑
  const handleEdit = (data: DataTypeManage.UpsertDataTypeManageParams) => {
    if (!canUpsertTableRow()) return;
    mode.value = 'edit';
    toggleEdit(data);
  };
  const addNewDataTypeManage = async () => {
    if (!canUpsertTableRow()) return;
    const newRow = {
      dataTypeName: '',
      dataTypeDesc: '',
      dtUsageScopeCodes: [],
      enabledFlag: ENABLED_FLAG.YES,
      editable: true,
    };
    dataTypeManageList.value.push(newRow);
    mode.value = 'add';
    // 确保在下一个 tick 中调用 toggleEdit
    await nextTick(() => {
      toggleEdit(newRow);
    });
  };
  const saveRow = async (
    row: DataTypeManage.UpsertDataTypeManageParams,
    index: number,
  ) => {
    const isValid = await dataTypeManageTableRef?.value?.validateRow(index);
    if (!isValid) return;
    let result: object | undefined;
    if (!row.dataTypeId) {
      const [, res] = await addDataTypeManage(row);
      result = res;
    } else {
      const [, res] = await editDataTypeManage(row);
      result = res;
    }
    if (result?.success) {
      await queryDataTypeManageListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };
  const handleEnableSwitch = async (
    row: DataTypeManage.UpsertDataTypeManageParams,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.dataTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params: DataTypeManage.UpsertDataTypeManageParams = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editDataTypeManage(params, true, row.enabledFlag);
      if (res?.success) {
        queryDataTypeManageListData();
      }
    });
  };
  const deleteDataTypeManage = async (
    row: DataTypeManage.UpsertDataTypeManageParams,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row.dataTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteDataTypeManageById({
        dataTypeId: row.dataTypeId,
      });
      if (res?.success) {
        dataTypeManageTableRef?.value.proTableRef.clearSelection();
        selectTableData.value = [];
        queryDataTypeManageListData();
      }
    });
  };

  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.dataTypeId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: DataTypeManage.DataTypeManageList[]) => {
    selectTableData.value = val;
  };

  const searchConfig = useDataTypeManageFormConfig(queryDataTypeManageListData);
  const { tableColumns, toggleEdit } = useDataTypeManageTableConfig({
    id: 'dataTypeId',
    tableRef: dataTypeManageTableRef,
    data: dataTypeManageList,
    saveRow,
    handleEnableSwitch,
    deleteDataTypeManage,
    isCloudEnv,
    handleEdit,
    mode,
  });
  /** 拖拽排序 */
  const handleSortEnd = async (list: DataTypeManage.DataTypeManageList[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.dataTypeId as string,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_DATA_TYPE,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryDataTypeManageListData();
    }
  };
  onMounted(async () => {
    await queryDataTypeManageListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('dataTypeManage.list.title', '数据类型管理')" />
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryDataTypeManageListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button
          class="mr-2"
          type="primary"
          @click="queryDataTypeManageListData"
        >
          {{ $t('dataTypeManage.search', '搜索') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-2"
          type="primary"
          @click="addNewDataTypeManage"
        >
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_DATA_TYPE"
        class="ml-2"
        title="DML"
        @success="
          () => {
            dataTypeManageTableRef?.proTableRef.clearSelection();
            selectTableData.value = [];
          }
        "
      />
    </div>
    <ProTable
      ref="dataTypeManageTableRef"
      :columns="tableColumns"
      :data="dataTypeManageList"
      :draggable="true"
      :editable="true"
      :loading="loading"
      row-key="dataTypeId"
      @selection-change="selectionChange"
      @drag-end="handleSortEnd"
    />
  </div>
</template>
