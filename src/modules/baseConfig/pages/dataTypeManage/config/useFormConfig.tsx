import { useFormConfig } from 'sun-biz';
import { DT_USAGE_SCOPE_CODE } from '@/utils/constant.ts';

export function useDataTypeManageFormConfig(
  queryDataTypeManageListData: (
    params?: DataTypeManage.QueryDataTypeManageList,
  ) => Promise<void>,
) {
  const data = useFormConfig({
    dataSetCodes: [DT_USAGE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('dataTypeManage.form.keyWord', '关键字'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryDataTypeManageListData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
      {
        label: t('dataTypeManage.form.dtUsageScopeCode', '应用范围'),
        name: 'dtUsageScopeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('dataTypeManage.form.dtUsageScopeCode', '应用范围'),
        }),
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[DT_USAGE_SCOPE_CODE].map((item) => ({
                label: item.dataValueCnName,
                value: item.dataValueNo,
              }))
            : [],
          clearable: true,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}
