<script setup lang="ts" name="BizEventUpsertDialog">
  import { ref, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { saveDictBizEvent } from '@modules/baseConfig/api/bizEvent';
  import { useBizEventUpsertFormConfig } from '../config/useFormConfig.ts';
  import { ProForm, ProDialog } from 'sun-biz';
  const props = defineProps<{
    mode: string;
    data: BizEvent.UpsertEventParams;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: BizEvent.UpsertEventParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const disabled = ref(false);
  const bizEventForm = ref({});
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props,
    () => {
      disabled.value = props.mode === 'view';
      bizEventForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...bizEventForm.value,
            ...formRef?.value?.model,
          };
          const [, res] = await saveDictBizEvent(params);
          if (res?.success) {
            ElMessage.success(
              t(
                params.bizEventId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };
  const formConfig = useBizEventUpsertFormConfig(disabled);
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="`${$t(`global:${props.mode}`)}${$t('bizEvent.name', '业务事件')}`"
    :width="900"
    destroy-on-close
    :align-center="true"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="bizEventForm"
      :column="3"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
