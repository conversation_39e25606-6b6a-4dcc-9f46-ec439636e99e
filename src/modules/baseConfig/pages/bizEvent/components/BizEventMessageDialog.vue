<script lang="ts" name="BizEventUpsertDialog" setup>
  import { onMounted, ref, watch } from 'vue';
  import { ElMessage } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProTable, TableRef } from 'sun-biz';
  import { useBizEventMessageTableConfig } from '@/modules/baseConfig/pages/bizEvent/config/useTableConfig.tsx';
  import { SelectOptions } from '@/typings/common.ts';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { queryMsgSendWayList } from '@/modules/baseConfig/api/msgSendWay.ts';
  import { queryContactInfoList } from '@/modules/baseConfig/api/contactInfo.ts';
  import {
    addEventMsgSetting,
    deleteEventMsgSetting,
    editEventMsgSetting,
  } from '@modules/baseConfig/api/bizEvent';

  const props = defineProps<{
    data: BizEvent.UpsertEventParams;
  }>();

  const loading = ref(false);
  const messageTableRef = ref<TableRef>();

  const dialogRef = ref();
  const { t } = useTranslation();
  const bizEventMessageTableData = ref<BizEvent.EventMsgSettingList>({});
  const eventName = ref<string | undefined>('');
  const bizEventId = ref<string | undefined>('');
  const emits = defineEmits<{ success: [] }>();
  watch(
    () => props,
    () => {
      eventName.value = props.data?.bizEventName;
      bizEventId.value = props.data?.bizEventId;
      bizEventMessageTableData.value = cloneDeep(
        props?.data?.eventMsgSettingList,
      );
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const msgSendWayList = ref<SelectOptions[]>([]);
  const queryMsgSendWayListData = async () => {
    const [, res] = await queryMsgSendWayList({ pageNumber: 0 });
    if (res?.success) {
      let { data } = res;
      msgSendWayList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.msgSendWayId,
          label: item.msgSendWayName,
        }));
    }
  };

  interface SelectContact {
    msgSendWayId: string;
    value: string;
    label: string;
  }

  const contactInfoList = ref<SelectContact[]>([]);
  const queryContactInfoListData = async () => {
    const params = {
      pageNumber: 0,
    };
    let [, res] = await queryContactInfoList(params);
    loading.value = false;
    if (res?.success) {
      let { data } = res;
      contactInfoList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.contactInfoId,
          label: item.contactNo,
          msgSendWayId: item.msgSendWayId,
        }));
    }
  };

  const saveNewRow = async (row: BizEvent.EventMsgSettingList) => {
    const params = { ...row };
    if (row.eventMsgSettingId) {
      const [, res] = await editEventMsgSetting(params);
      if (res?.success) {
        (
          row as unknown as {
            editable: boolean;
          }
        ).editable = false;
        ElMessage.success(t('global:edit.success'));
        emits('success');
      }
    } else {
      const [, res] = await addEventMsgSetting({
        ...params,
        bizEventId: bizEventId.value,
      });
      if (res?.success) {
        row.bizEventId = bizEventId.value;
        row.eventMsgSettingId = res.data.eventMsgSettingId;
        (
          row as unknown as {
            editable: boolean;
          }
        ).editable = false;
        ElMessage.success(t('global:save.success'));
        emits('success');
      }
    }
  };

  const deleteRow = async (
    row: BizEvent.EventMsgSettingList,
    index: number,
  ) => {
    const params = {
      eventMsgSettingId: row.eventMsgSettingId,
    };
    const [, res] = await deleteEventMsgSetting(params);
    if (res?.success) {
      bizEventMessageTableData.value?.splice(index, 1);
      emits('success');
    }
  };

  const handleClose = () => {
    dialogRef.value.close();
  };
  const tableColumnsConfig = useBizEventMessageTableConfig({
    id: 'eventMsgSettingId',
    tableRef: messageTableRef,
    data: bizEventMessageTableData,
    deleteRow,
    saveNewRow,
    msgSendWayList,
    contactInfoList,
  });

  /**添加新行  */
  function addNewRow() {
    const newItem = {
      bizEventId: '',
      msgSendWayId: '',
      contactInfoId: '',
      editable: true,
    };
    if (bizEventMessageTableData.value) {
      bizEventMessageTableData.value.splice(
        bizEventMessageTableData.value.length,
        0,
        newItem,
      );
    } else {
      bizEventMessageTableData.value = [];
      bizEventMessageTableData.value.push(newItem);
    }
  }

  defineExpose({ dialogRef });
  onMounted(() => {
    queryMsgSendWayListData();
    queryContactInfoListData();
  });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :include-footer="false"
    :title="$t('bizEvent.messageDialog.name', '消息配置')"
    :width="600"
    destroy-on-close
    @success="emits('success')"
  >
    <div class="flex justify-between">
      <div class="el-form-item">事件：{{ eventName }}</div>
      <div>
        <el-button class="mr-2" type="primary" @click="addNewRow">
          {{ $t('global:add') }}
        </el-button>
        <el-button @click="handleClose">{{ $t('global:cancel') }}</el-button>
      </div>
    </div>
    <ProTable
      ref="messageTableRef"
      :columns="tableColumnsConfig"
      :data="bizEventMessageTableData"
      :editable="true"
      :loading="loading"
      :max-height="200"
      row-key="eventMsgSettingId"
    />
  </ProDialog>
</template>
