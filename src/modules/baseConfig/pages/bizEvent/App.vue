<script lang="ts" name="bizEvent" setup>
  import { ref, computed } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant';
  import { useBizEventTableConfig } from './config/useTableConfig.tsx';
  import { useBizEventSearchFormConfig } from './config/useFormConfig.ts';
  import {
    queryDictBizEvent,
    saveDictBizEvent,
  } from '@modules/baseConfig/api/bizEvent';
  import {
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    DmlButton,
    useAppConfigData,
  } from 'sun-biz';
  import BizEventUpsertDialog from '@/modules/baseConfig/pages/bizEvent/components/BizEventUpsertDialog.vue';
  import BizEventMessageDialog from '@/modules/baseConfig/pages/bizEvent/components/BizEventMessageDialog.vue';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const searchParams = ref<BizEvent.QueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const loading = ref(false);
  const bizEventList = ref<BizEvent.BizEventInfo[]>([]);
  const selections = ref<BizEvent.BizEventInfo[]>([]);
  const bizEventUpsertParams = ref<BizEvent.UpsertEventParams>({});
  const bizEventUpsertDialogRef = ref();
  const bizEventMessageDialogRef = ref();
  const bizEventUpsertDialogMode = ref('');
  const bizEventTableRef = ref();

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.bizEventId || '';
    });
  });

  async function queryBizEventData(data?: BizEvent.QueryParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryDictBizEvent(params);
    loading.value = false;
    if (res?.success) {
      bizEventList.value = res.data;
    }
  }

  const onOpenBizEventDialog = (mode: string, data?: BizEvent.BizEventInfo) => {
    bizEventUpsertDialogMode.value = mode;
    bizEventUpsertParams.value =
      mode === 'add' ? { enabledFlag: ENABLED_FLAG.YES } : { ...data };
    bizEventUpsertDialogRef.value.dialogRef.open();
  };
  const onOpenMessageDialog = (data?: BizEvent.BizEventInfo) => {
    bizEventUpsertParams.value = { ...data };
    bizEventMessageDialogRef.value.dialogRef.open();
  };

  const handleSelectChange = (value: BizEvent.BizEventInfo[]) => {
    selections.value = value;
  };

  /** 启用状态切换 */
  async function handleEnableSwitch(row: BizEvent.BizEventInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.bizEventName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await saveDictBizEvent(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryBizEventData();
      }
    });
  }

  queryBizEventData();
  const searchConfig = useBizEventSearchFormConfig(queryBizEventData);
  const tableColumnsConfig = useBizEventTableConfig(
    isCloudEnv,
    handleEnableSwitch,
    onOpenBizEventDialog,
    onOpenMessageDialog,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('bizEvent.list.title', '业务事件列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="queryBizEventData"
        />
      </div>
      <div>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-2"
          type="primary"
          @click="onOpenBizEventDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_BIZ_EVENT"
          @success="
            () => {
              bizEventTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="bizEventTableRef"
      :columns="tableColumnsConfig"
      :data="bizEventList"
      :loading="loading"
      row-key="bizEventId"
      @selection-change="handleSelectChange"
    />
  </div>
  <BizEventUpsertDialog
    ref="bizEventUpsertDialogRef"
    :data="bizEventUpsertParams"
    :mode="bizEventUpsertDialogMode"
    @success="queryBizEventData"
  />
  <BizEventMessageDialog
    ref="bizEventMessageDialogRef"
    :data="bizEventUpsertParams"
    @success="queryBizEventData"
  />
</template>
