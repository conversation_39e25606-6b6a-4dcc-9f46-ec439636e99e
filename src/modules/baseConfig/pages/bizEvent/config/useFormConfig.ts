import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';

export function useBizEventSearchFormConfig(
  queryBizEventData: (data?: BizEvent.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryBizEventData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryBizEventData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useBizEventUpsertFormConfig(disabled: Ref<boolean>) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('bizEvent.form.bizEventName', '名称'),
        name: 'bizEventName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('bizEvent.form.bizEventName', '名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bizEvent.form.bizEventName', '名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:secondName', '辅助名称'),
        name: 'bizEvent2ndName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:secondName', '辅助名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        label: t('global:thirdName', '扩展名称'),
        name: 'bizEventExtName',
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:thirdName', '扩展名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'bizEventDesc',
        isFullWidth: true,
        label: t('bizEvent.form.bizEventDesc', '描述'),
        component: 'input',
        type: 'textarea',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('bizEvent.form.bizEventDesc', '描述'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
    ],
  });
  return data;
}
