<script lang="ts" setup>
  import { computed } from 'vue';
  import mail from '../assets/images/mail.png';
  import mobile from '../assets/images/mobile.png';
  import phone from '../assets/images/telephone.png';
  import wechat from '../assets/images/wechat.png';
  import fax from '../assets/images/fax.png';
  import { CONTACT_TYPE_CODE_NAME } from '@/utils/constant.ts';
  // 接收单个属性
  type Props = {
    contactList: AddressBook.ContactsList[] | null;
    contactTypes: { CONTACT_TYPE_CODE: [] };
  };
  const props = defineProps<Props>();
  const localType = [
    { code: '4', icon: mail, desc: '邮件', color: 'rgba(45, 187, 214, 0.15)' },
    {
      code: '1',
      icon: mobile,
      desc: '手机',
      color: 'rgba(112, 127, 254, 0.15)',
    },
    {
      code: '2',
      icon: phone,
      desc: '固话',
      color: 'rgba(255, 84, 39, 0.15)',
    },
    { code: '3', icon: fax, desc: '传真', color: 'rgba(255, 160, 49, 0.15)' },
    { code: '99', icon: wechat, desc: '其他', color: 'rgba(0, 171, 68, 0.15)' },
  ];
  // 将 localType 的 icon 映射到 props.contactTypes 中
  const mergedContactTypes = computed(() => {
    return props.contactTypes[CONTACT_TYPE_CODE_NAME].map((type) => {
      const matchType = localType.find(
        (local) => local.code === type.dataValueNo,
      );
      return {
        ...type,
        icon: matchType ? matchType.icon : null,
        color: matchType ? matchType.color : null,
      };
    });
  });
  // 计算合并后的联系方式列表
  const processedContactList = computed(() => {
    // 先根据localType的顺序创建基础列表
    const orderedContactTypes = localType.map((local) => {
      // 查找mergedContactTypes中对应的类型
      const mergedType = mergedContactTypes.value.find(
        (type) => type.dataValueNo === local.code,
      );

      return {
        ...mergedType,
        // 确保保留localType中的图标和颜色定义
        icon: local.icon,
        color: local.color,
        desc: local.desc,
        contactNo: '', // 初始为空
      };
    });

    // 然后处理联系人数据，填充到对应的类型中
    const contactMap = new Map();
    props.contactList.forEach((contact: AddressBook.ContactList) => {
      const typeIndex = orderedContactTypes.findIndex(
        (t) => t.dataValueNo === contact.contactTypeCode,
      );

      if (typeIndex !== -1) {
        if (contactMap.has(contact.contactTypeCode)) {
          const existingContact = contactMap.get(contact.contactTypeCode);
          existingContact.contactNo = `${existingContact.contactNo}, ${contact.contactNo}`;
        } else {
          const contactData = {
            ...orderedContactTypes[typeIndex],
            contactNo: contact.contactNo,
          };
          contactMap.set(contact.contactTypeCode, contactData);
          // 更新orderedContactTypes中的对应项
          orderedContactTypes[typeIndex] = contactData;
        }
      }
    });

    return orderedContactTypes;
  });
</script>

<template>
  <span
    v-for="(contactItem, cIndex) in processedContactList"
    :key="cIndex"
    class="flex items-center"
    style="padding: 8px 0"
  >
    <span
      :style="`
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: ${contactItem.color}
          `"
      class="flex items-center justify-center"
    >
      <el-image
        :src="contactItem.icon"
        fit="fill"
        style="width: 16px; height: 16px"
      />
    </span>
    <span
      :style="`font-size: ${contactItem.contactNo.length > 11 ? '14px' : '16px'}`"
      class="ml-4 cursor-pointer overflow-hidden overflow-ellipsis whitespace-nowrap"
      style="width: 220px"
    >
      <el-tooltip :content="contactItem.contactNo" effect="light">
        {{ contactItem.contactNo }}
      </el-tooltip>
    </span>
  </span>
</template>
