<script lang="ts" name="addressBook" setup>
  import {
    MAIN_APP_CONFIG,
    ProForm,
    Title,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import { onMounted, ref } from 'vue';
  import { useAddressBookFormConfig } from './config/useFormConfig.tsx';
  import contactCard from './components/contactCard.vue';
  import avatar from './assets/images/avatar.png';
  import mail from './assets/images/mail.png';
  import mobile from './assets/images/mobile.png';
  import phone from './assets/images/telephone.png';
  import wechat from './assets/images/wechat.png';
  import fax from './assets/images/fax.png';
  import like from './assets/images/like.png';
  import unlike from './assets/images/unlike.png';
  import {
    addUserFavorit,
    deleteUserFavorit,
    queryContactsByExample,
  } from '@modules/baseConfig/api/addressBook';
  import { CONTACT_TYPE_CODE_NAME } from '@/utils/constant.ts';

  const { userInfo } = useAppConfigData([MAIN_APP_CONFIG.USER_INFO]);
  const contactTypes = useFetchDataset([CONTACT_TYPE_CODE_NAME]);
  const infiniteScrollRef = ref();

  const total = ref(0); // 数据总数
  const userList = ref<User.Item[]>(); // 表格数据
  let queryDataListParams = ref<AddressBook.QueryParams>({
    contactsCategoryCode: '1',
    keyWord: '',
    isFavorit: null,
    userId: null,
    pageNumber: 1,
    pageSize: 25,
  }); // 查询参数
  const loading = ref(false);
  const queryContactsByExampleListData = async (
    params?: AddressBook.QueryAddressBookParams,
  ) => {
    loading.value = true;
    queryDataListParams.value = {
      ...queryDataListParams.value,
      ...params,
      pageNumber: 1,
    };
    userList.value = [];
    let [, res] = await queryContactsByExample({
      ...queryDataListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      userList.value = res.data || [];
      total.value = res?.total;
    }
  };

  const searchConfig = useAddressBookFormConfig(queryContactsByExampleListData);

  const loadUser = async () => {
    loading.value = true;
    let [, res] = await queryContactsByExample({
      ...queryDataListParams.value,
      pageNumber: ++queryDataListParams.value.pageNumber,
    });
    loading.value = false;
    if (res?.success) {
      userList.value = userList.value?.concat(res.data);
    }
  };
  // 启用状态改变
  const changeSelect = async (data?: AddressBook.QueryAddressBookParams) => {
    userList.value = [];
    queryDataListParams.value = {
      ...queryDataListParams.value,
      ...data,
      pageNumber: 1,
      userId: data?.isFavorit === 1 ? userInfo?.userId : null,
    };
    await queryContactsByExampleListData();
    if (
      queryDataListParams.value.isFavorit === 1 &&
      queryDataListParams.value.keyWord.length > 0
    ) {
      userList.value = userList.value?.filter((item) =>
        item.contactsName.includes(queryDataListParams.value.keyWord),
      );
    }
  };
  const upsertLikeContact = async (data: AddressBook.UpsertUserFavorit) => {
    if (data.userFavoritId) {
      const params: AddressBook.UpsertUserFavorit = {
        userFavoritId: data.userFavoritId,
      };
      const [, res] = await deleteUserFavorit(params);
      if (res?.success) {
        data.userFavoritId = null;
      }
    } else {
      const params: AddressBook.UpsertUserFavorit = {
        userId: userInfo?.userId,
        bizId: data.contactsId,
        bizIdTypeCode:
          queryDataListParams.value.contactsCategoryCode === '1'
            ? 'DICT_EMPLOYEE'
            : 'DICT_DEPARTMENT',
      };
      const [, res] = await addUserFavorit(params);
      if (res?.success) {
        data.userFavoritId = 1;
      }
    }
  };

  onMounted(() => {
    queryContactsByExampleListData();
  });
</script>

<template>
  <div class="flex h-full flex-col" style="background: #c8d0e7">
    <div class="m-2 rounded bg-white pb-1 pl-4 pr-4 pt-4">
      <Title :title="$t('userList.list.title', '通讯录管理')" />
      <div class="mt-3 flex justify-between">
        <div class="el-form-item">
          <ProForm
            ref="proForm"
            v-model="queryDataListParams"
            :data="searchConfig"
            layout-mode="inline"
            @model-change="changeSelect"
          />
          <el-button
            class="mr-2"
            type="primary"
            @click="queryContactsByExampleListData"
          >
            {{ $t('contactInfo.search', '搜索') }}
          </el-button>
        </div>
      </div>
    </div>

    <div v-loading="loading" class="box overflow-y-auto">
      <div
        ref="infiniteScrollRef"
        v-infinite-scroll="loadUser"
        :infinite-scroll-immediate="true"
        class="user-list-container"
        infinite-scroll-distance="1"
      >
        <div v-for="(item, index) in userList" :key="index" class="user-item">
          <div
            class="flex justify-between bg-white"
            style="
              box-sizing: border-box;
              padding: 8px 8px 4px 16px;
              border-radius: 4px;
              box-shadow: 0 2px 10px 0 rgb(0 0 0 / 12%);
            "
          >
            <div class="flex justify-start">
              <div class="mb-2 flex items-center">
                <div class="flex items-center">
                  <el-avatar :size="32" :src="avatar" class="text-4xl" />
                </div>
                <div class="ml-2">
                  <div class="font-bold" style="font-size: 18px">
                    <el-tooltip
                      :content="item.contactsName"
                      class="box-item"
                      effect="light"
                      placement="top-start"
                    >
                      <span class="ml-2 size-5 cursor-pointer">{{
                        item.contactsName.length > 10
                          ? item.contactsName.substring(0, 10) + '...'
                          : item.contactsName
                      }}</span>
                    </el-tooltip>
                  </div>
                  <div class="ml-2" style="font-size: 14px; color: #666">
                    <span>
                      {{
                        queryDataListParams.contactsCategoryCode === '1'
                          ? '工号'
                          : '编号'
                      }}：
                    </span>

                    <span class="cursor-pointer">{{
                      item.contactsNo || ''
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-start justify-end">
              <el-image
                :src="item.userFavoritId ? like : unlike"
                class="cursor-pointer"
                fit="fill"
                style="width: 16px; height: 16px"
                @click="upsertLikeContact(item)"
              />
            </div>
          </div>

          <div
            class="flex flex-col"
            style="box-sizing: border-box; padding: 8px 16px 16px"
          >
            <div
              v-if="item.contactList && item.contactList.length > 0"
              class="flex flex-col"
            >
              <contactCard
                :contact-list="item.contactList"
                :contact-types="contactTypes"
              ></contactCard>
            </div>
            <div v-else>
              <div class="flex flex-col">
                <span
                  class="flex items-center"
                  style="box-sizing: border-box; padding: 8px 0"
                >
                  <span
                    class="flex items-center justify-center"
                    style="
                      width: 24px;
                      height: 24px;
                      background: rgb(45 187 214 / 15%);
                      border-radius: 50%;
                    "
                  >
                    <el-image
                      :src="mail"
                      fit="fill"
                      style="width: 16px; height: 16px"
                    />
                  </span>

                  <span class="ml-2"> </span>
                </span>

                <span
                  class="flex items-center"
                  style="box-sizing: border-box; padding: 8px 0"
                >
                  <span
                    class="flex items-center justify-center"
                    style="
                      width: 24px;
                      height: 24px;
                      background: rgb(112 127 254 / 15%);
                      border-radius: 50%;
                    "
                  >
                    <el-image
                      :src="mobile"
                      fit="fill"
                      style="width: 16px; height: 16px"
                    />
                  </span>

                  <span class="ml-2"></span>
                </span>
                <span
                  class="flex items-center"
                  style="box-sizing: border-box; padding: 8px 0"
                >
                  <span
                    class="flex items-center justify-center"
                    style="
                      width: 24px;
                      height: 24px;
                      background: rgb(255 84 39 / 15%);
                      border-radius: 50%;
                    "
                  >
                    <el-image
                      :src="phone"
                      fit="fill"
                      style="width: 16px; height: 16px"
                    />
                  </span>
                  <span class="ml-2"></span>
                </span>
                <span
                  class="flex items-center"
                  style="box-sizing: border-box; padding: 8px 0"
                >
                  <span
                    class="flex items-center justify-center"
                    style="
                      width: 24px;
                      height: 24px;
                      background: rgb(255 160 49 / 15%);
                      border-radius: 50%;
                    "
                  >
                    <el-image
                      :src="fax"
                      fit="fill"
                      style="width: 16px; height: 16px"
                    />
                  </span>
                  <span class="ml-2"></span>
                </span>
                <span
                  class="flex items-center"
                  style="box-sizing: border-box; padding: 8px 0"
                >
                  <span
                    class="flex items-center justify-center"
                    style="
                      width: 24px;
                      height: 24px;
                      background: rgb(0 171 68 / 15%);
                      border-radius: 50%;
                    "
                  >
                    <el-image
                      :src="wechat"
                      fit="fill"
                      style="width: 16px; height: 16px"
                    />
                  </span>
                  <span class="ml-2"></span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-backtop
      :bottom="10"
      :right="100"
      :target="infiniteScrollRef"
      :visibility-height="10"
    />
  </div>
</template>

<style lang="scss" scoped>
  .user-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    width: 100%;
    padding: 16px;
  }

  .user-item {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px 0 rgb(0 0 0 / 12%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px 0 rgb(0 0 0 / 15%);
      transform: translateY(-2px);
    }
  }

  @media screen and (width <= 1920px) {
    .user-list-container {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 12px;
      padding: 12px;
    }
  }

  @media screen and (width <= 1440px) {
    .user-list-container {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 10px;
      padding: 10px;
    }
  }

  @media screen and (width <= 1280px) {
    .user-list-container {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 8px;
      padding: 8px;
    }
  }
</style>
