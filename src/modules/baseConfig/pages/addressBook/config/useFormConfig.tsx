import { useFormConfig } from 'sun-biz';
import { CONTACTS_CATEGORY_CODE, FLAG } from '@/utils/constant.ts';

export function useAddressBookFormConfig(
  queryContactsByExampleListData: (params?: AddressBook.QueryParams) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [CONTACTS_CATEGORY_CODE],
    getData: (t, dataSet) => [
      {
        label: t('addressBook.searchFrom.contactsCategoryCode', '通讯录类别'),
        name: 'contactsCategoryCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('addressBook.searchFrom.contactsCategoryCode', '通讯录类别'),
        }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[CONTACTS_CATEGORY_CODE] : [],
          className: 'w-80',
          clearable: false,
        },
      },
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appEnvDefine.form.keyWord', '关键字'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          prefixIcon: 'Search',
          onClear: async () => {
            await queryContactsByExampleListData({
              keyWord: '',
            });
          },
        },
      },
      {
        label: t('addressBook.searchFrom.isFavorit', '是否收藏'),
        name: 'isFavorit',
        component: 'checkbox',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('addressBook.searchFrom.isFavorit', '是否收藏'),
        }),
        extraProps: {
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
        },
      },
    ],
  });
  return data;
}
