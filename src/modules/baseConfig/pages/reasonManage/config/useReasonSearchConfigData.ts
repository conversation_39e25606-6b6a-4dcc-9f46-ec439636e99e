import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';

export function useSearchFormConfig(
  reasonUseScopeList: Ref<SelectOptions[]>,
  queryReasonData: (data?: ReasonManage.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        label: t('reasonManage.search.reasonUseScopeCode', '应用范围'),
        name: 'reasonUseScopeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('reasonManage.search.reasonUseScopeCode', '应用范围'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: reasonUseScopeList.value,
          style: { width: '350px' },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '330px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryReasonData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryReasonData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
