import { Ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { ElMessage } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import {
  addReason,
  updateReasonById,
} from '@modules/baseConfig/api/reasonManage';
import { SelectOptions } from '@/typings/common';
import { useColumnConfig } from 'sun-biz';

export function useReasonColumnConfig(
  reasonUseScopeList: Ref<SelectOptions[]>,
  queryReasonData: (data?: ReasonManage.QueryParams) => void,
  onItemCancelClick: (item: ReasonManage.ReasonInfo, index: number) => void,
  isCloudEnv: boolean | undefined,
) {
  const { t } = useTranslation();

  async function onItemSaveClick(data: ReasonManage.ReasonInfo) {
    const params = data.form;
    if (!params.reasonName) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t('reasonManage.form.reasonName', '原因名称'),
        }),
      );
      return;
    }
    if (!params.reasonUseScopeCodes?.length) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('reasonManage.table.reasonUseScopeList', '应用范围'),
        }),
      );
      return;
    }
    let isSuccess = false;
    if (params.reasonId) {
      const [, res] = await updateReasonById(params);
      isSuccess = !!res?.success;
    } else {
      const [, res] = await addReason(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(params.reasonId ? 'global:edit.success' : 'global:add.success'),
      );
      queryReasonData();
    }
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ReasonManage.ReasonInfo, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('reasonManage.table.reasonName', '原因名称'),
        prop: 'reasonName',
        minWidth: 170,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.reasonName}
              placeholder={t('global:placeholder.input.template', {
                content: t('reasonManage.table.reasonName', '原因名称'),
              })}
            />
          ) : (
            <div>{row.reasonName || '--'}</div>
          );
        },
      },
      {
        label: t('reasonManage.table.reason2ndName', '辅助名称'),
        prop: 'reason2ndName',
        minWidth: 150,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.reason2ndName}
              placeholder={t('global:placeholder.input.template', {
                content: t('reasonManage.table.reason2ndName', '辅助名称'),
              })}
            />
          ) : (
            <div>{row.reason2ndName || '--'}</div>
          );
        },
      },
      {
        label: t('reasonManage.table.reasonExtName', '扩展名称'),
        prop: 'reasonExtName',
        minWidth: 130,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-input
              v-model={row.form.reasonExtName}
              placeholder={t('global:placeholder.input.template', {
                content: t('reasonManage.table.reasonExtName', '扩展名称'),
              })}
            />
          ) : (
            <div>{row.reasonExtName || '--'}</div>
          );
        },
      },
      {
        label: t('reasonManage.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-switch
              v-model={row.form.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          ) : (
            <div>
              {row.enabledFlag === FLAG.YES
                ? t('global:enabled')
                : t('global:disabled')}
            </div>
          );
        },
      },
      {
        label: t('reasonManage.table.displayFlag', '是否显示'),
        prop: 'displayFlag',
        minWidth: 100,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-checkbox
              v-model={row.form.displayFlag}
              true-value={1}
              false-value={0}
            />
          ) : (
            <div>
              {Number(row.displayFlag) === 1 ? t('global:yes') : t('global:no')}
            </div>
          );
        },
      },
      {
        label: t('reasonManage.table.editableFlag', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 100,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-checkbox
              v-model={row.form.editableFlag}
              true-value={1}
              false-value={0}
            />
          ) : (
            <div>
              {Number(row.editableFlag) === 1
                ? t('global:yes')
                : t('global:no')}
            </div>
          );
        },
      },
      {
        label: t('reasonManage.table.reasonUseScopeList', '应用范围'),
        prop: 'reasonUseScopeList',
        minWidth: 180,
        render: (row: ReasonManage.ReasonInfo) => {
          return row.isEdit ? (
            <el-select
              v-model={row.form.reasonUseScopeCodes}
              multiple={true}
              filterable={true}
              collapse-tags={true}
              collapse-tags-tooltip={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('reasonManage.table.reasonUseScopeList', '应用范围'),
              })}
            >
              {reasonUseScopeList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <div>
              {row.reasonUseScopeList?.length
                ? row.reasonUseScopeList
                    .map((item) => item.reasonUseScopeDesc)
                    .join(' ')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: ReasonManage.ReasonInfo, $index: number) => {
          return (
            <div class="flex justify-around">
              {!row.isEdit && (
                <el-button
                  onClick={() => (row.isEdit = true)}
                  link={true}
                  disabled={!isCloudEnv && !row.editableFlag}
                  type="primary"
                >
                  {t('global:edit')}
                </el-button>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemCancelClick(row, $index)}
                  link={true}
                  type="primary"
                >
                  {t('global:cancel')}
                </el-button>
              )}
              {row.isEdit && (
                <el-button
                  onClick={() => onItemSaveClick(row)}
                  link={true}
                  type="primary"
                >
                  {t('global:save')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
}
