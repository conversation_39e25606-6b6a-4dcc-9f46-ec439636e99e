<script setup lang="ts" name="reasonManage">
  import { ref, computed } from 'vue';
  import {
    queryReasonByExample,
    updateReasonSortById,
  } from '@modules/baseConfig/api/reasonManage';
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    type AnyObject,
    useFetchDataset,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import { CodeSystemType } from '@/typings/codeManage';
  import { FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useReasonColumnConfig } from './config/useReasonColumnConfig.tsx';
  import { useSearchFormConfig } from './config/useReasonSearchConfigData.ts';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const searchParams = ref<ReasonManage.QueryParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
    reasonUseScopeCode: undefined,
  });
  const tableRef = ref();
  const reasonList = ref<ReasonManage.ReasonInfo[]>([]);
  const loading = ref(false);
  const selections = ref<ReasonManage.ReasonInfo[]>([]);
  const dataSetList = useFetchDataset([CodeSystemType.REASON_USE_SCOPE_CODE]);
  const reasonUseScopeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.REASON_USE_SCOPE_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.reasonId || '';
    });
  });

  async function queryReasonData(data?: ReasonManage.QueryParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryReasonByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      res.data?.sort(
        (a: ReasonManage.ReasonInfo, b: ReasonManage.ReasonInfo) => {
          return a.sort - b.sort;
        },
      );
      reasonList.value = res.data.map((item: ReasonManage.ReasonInfo) => ({
        ...item,
        isEdit: false,
        form: {
          reasonId: item.reasonId,
          reasonName: item.reasonName,
          reason2ndName: item.reason2ndName,
          reasonExtName: item.reasonExtName,
          displayFlag: item.displayFlag,
          editableFlag: item.editableFlag,
          enabledFlag: item.enabledFlag,
          reasonUseScopeCodes: (item.reasonUseScopeList || []).map(
            (item) => item?.reasonUseScopeCode,
          ),
          sort: item.sort,
        },
      }));
    }
  }

  function onAddReasonClick() {
    reasonList.value.push({
      isEdit: true,
      form: {
        reasonName: '',
        reason2ndName: '',
        reasonExtName: '',
        displayFlag: 1,
        editableFlag: 1,
        enabledFlag: 1,
        reasonUseScopeCodes: [],
      },
    } as unknown as ReasonManage.ReasonInfo);
  }

  async function handleSortEnd(data: AnyObject[]) {
    let [, result] = await updateReasonSortById({
      reasonSortList: data.map((item, index) => {
        return {
          reasonId: item.reasonId,
          sort: index + 1,
        };
      }),
    });
    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryReasonData();
    }
  }

  function handleSelectChange(value: ReasonManage.ReasonInfo[]) {
    selections.value = value;
  }

  function onItemCancelClick(item: ReasonManage.ReasonInfo, index: number) {
    const data = { ...item };
    if (data.reasonId) {
      data.isEdit = false;
      reasonList.value.splice(index, 1, data);
    } else {
      reasonList.value.splice(index, 1);
    }
  }
  queryReasonData();
  const searchConfig = useSearchFormConfig(reasonUseScopeList, queryReasonData);
  const reasonColumns = useReasonColumnConfig(
    reasonUseScopeList,
    queryReasonData,
    onItemCancelClick,
    isCloudEnv,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('reasonManage.list.title', '原因列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryReasonData"
        />
      </div>
      <div>
        <el-button class="mr-3" type="primary" @click="onAddReasonClick">
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_REASON"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <pro-table
      ref="tableRef"
      row-key="reasonId"
      draggable
      :data="reasonList"
      :columns="reasonColumns"
      :loading="loading"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
