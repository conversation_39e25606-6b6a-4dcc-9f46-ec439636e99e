import { computed, Ref } from 'vue';
import { useFetchDataset, useFormConfig } from 'sun-biz';
import { CodeSystemType } from '@/typings/codeManage';
import { FLAG } from '@/utils/constant';

export function useDetailBaseInfoFormConfig(
  isCloudEnv: boolean | undefined,
  mode: Ref<string>,
) {
  const bizNoObjectCodeDataSetList = useFetchDataset([
    CodeSystemType.BIZ_NO_OBJECT_CODE,
  ]);
  const bizNoObjectCodeList = computed(() =>
    (
      bizNoObjectCodeDataSetList?.value?.[CodeSystemType.BIZ_NO_OBJECT_CODE] ||
      []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );

  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'bizNoObjectCode',
        label: t('bizNoGenerateRule.form.bizNoObjectCode', '业务编码对象'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizNoGenerateRule.form.bizNoObjectCode', '业务编码对象'),
        }),
        extraProps: {
          filterable: true,
          disabled: mode.value === 'edit',
          options: bizNoObjectCodeList.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizNoGenerateRule.form.bizNoObjectCode', '业务编码对象'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'ruleName',
        label: t('bizNoGenerateRule.form.ruleName', '规则名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizNoGenerateRule.form.ruleName', '规则名称'),
        }),
        extraProps: {
          disabled: !isCloudEnv && mode.value === 'edit',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bizNoGenerateRule.form.ruleName', '规则名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'maxCharSize',
        label: t('bizNoGenerateRule.form.maxCharSize', '最大长度'),
        component: 'input-number',
        placeholder: '',
        extraProps: {
          disabled: !isCloudEnv && mode.value === 'edit',
          min: 0,
          'controls-position': 'right',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('bizNoGenerateRule.form.maxCharSize', '最大长度'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'ruleDesc',
        label: t('bizNoGenerateRule.form.ruleDesc', '规则描述'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('bizNoGenerateRule.form.ruleDesc', '规则描述'),
        }),
        extraProps: {
          disabled: !isCloudEnv && mode.value === 'edit',
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          disabled: !isCloudEnv && mode.value === 'edit',
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        name: 'recycleFlag',
        label: t('bizNoGenerateRule.recyclingLogo', '回收标志'),
        component: 'switch',
        extraProps: {
          disabled: !isCloudEnv && mode.value === 'edit',
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('bizNoGenerateRule.use', '使用'),
          'inactive-text': t('bizNoGenerateRule.notUsed', '未使用'),
        },
      },
    ],
  });
  return data;
}
