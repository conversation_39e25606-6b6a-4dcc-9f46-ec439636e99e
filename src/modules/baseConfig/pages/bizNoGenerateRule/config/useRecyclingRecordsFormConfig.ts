import { useFormConfig } from 'sun-biz';
import { BIZ_NO_OBJECT_CODE_NAME, FLAG } from '@/utils/constant.ts';

export function useBusinessRecyclingFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [BIZ_NO_OBJECT_CODE_NAME],
    getData: (t, dataSet) => {
      return [
        {
          name: 'bizNoObjectCodes',
          label: t('bizNoGenerateRule.bizNoObjectCodes', '业务编码对象'),
          component: 'select',
          placeholder: t(
            'bizNoGenerateRule.pleaseSelectTheBusinessCodeObject',
            '请选择业务编码对象',
          ),
          triggerModelChange: true,
          extraProps: {
            multiple: true,
            filterable: true,
            collapseTags: true,
            collapseTagsTooltip: true,
            options: dataSet?.value
              ? dataSet.value[BIZ_NO_OBJECT_CODE_NAME]
              : [],
            className: 'w-60',
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
        {
          name: 'usedFlag',
          label: t('bizNoGenerateRule.form.usedFlag', '使用标识'),
          component: 'switch',
          triggerModelChange: true,
          extraProps: {
            'inline-prompt': true,
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('bizNoGenerateRule.use', '使用'),
            'inactive-text': t('bizNoGenerateRule.notUsed', '未使用'),
          },
        },
        {
          name: 'bizNoValue',
          label: t('bizNoGenerateRule.bizNoValue', '业务编码'),
          component: 'input',
          triggerModelChange: true,
          placeholder: t(
            'medicineManage.pleaseEnterTheBusinessCode',
            '请输入业务编码',
          ),
        },
      ];
    },
  });
  return data;
}
