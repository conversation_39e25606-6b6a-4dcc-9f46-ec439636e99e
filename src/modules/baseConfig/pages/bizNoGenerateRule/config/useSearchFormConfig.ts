import { useFormConfig } from 'sun-biz';

export function useSearchFormConfig(
  queryBizNoGenerateRuleData: (data?: BizNoGenerateRule.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: `${t('global:placeholder.input.template', {
          content: t('bizNoGenerateRule.form.ruleName', '规则名称'),
        })}${t('global:query')}`,
        extraProps: {
          style: { width: '350px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              queryBizNoGenerateRuleData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryBizNoGenerateRuleData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
