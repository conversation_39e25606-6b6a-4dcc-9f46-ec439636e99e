import { useColumnConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant.ts';
export function useRecyclingRecordsTable() {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('bizNoGenerateRule.dictBizNoRecycleRecordId', '回收记录标识'),
        prop: 'dictBizNoRecycleRecordId',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.bizNoObjectDesc', '编码对象'),
        prop: 'bizNoObjectDesc',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.bizNoValue', '编码值'),
        prop: 'bizNoValue',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.bizIdTypeDesc', '业务类型'),
        prop: 'bizIdTypeDesc',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.bizId', '业务标识'),
        prop: 'bizId',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.usedFlag', '使用标识'),
        prop: 'usedFlag',
        minWidth: 120,
        render: (row: BizNoGenerateRule.BusinessRecycling) => {
          return (
            <el-switch
              disabled
              v-model={row.usedFlag}
              active-color="#13ce66"
              inline-prompt={true}
              inactive-color="#ff4949"
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              active-text={t('bizNoGenerateRule.use', '使用')}
              inactive-text={t('bizNoGenerateRule.notUsed', '未使用')}
            ></el-switch>
          );
        },
      },
    ],
  });
}
