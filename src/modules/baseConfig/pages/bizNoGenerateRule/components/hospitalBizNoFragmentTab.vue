<script setup lang="ts" name="hospitalBizNoFragmentTab">
  import { ref, computed, watch } from 'vue';
  import { SelectOptions } from '@/typings/common';
  import { ProTable, type AnyObject, TableRef } from 'sun-biz';
  import { VALUE_TYPE_CODE, FULL_TYPE_CODE } from '../constant';
  import { useBizNoFragmentTableConfig } from '@/modules/baseConfig/pages/bizNoGenerateRule/config/useTableConfig';

  type Props = {
    formData: BizNoGenerateRule.BizNoGenerateRuleUpsertParams | undefined;
    hospitalItem: BizNoGenerateRule.BizNoGenerateRuleHospital;
    valueTypeCodeList: SelectOptions[];
    fullTypeCodeList: SelectOptions[];
    resetTypeCodeList: SelectOptions[];
  };
  const props = withDefaults(defineProps<Props>(), {
    formData: () => ({}) as BizNoGenerateRule.BizNoGenerateRuleUpsertParams,
    hospitalItem: () => ({}) as BizNoGenerateRule.BizNoGenerateRuleHospital,
    valueTypeCodeList: () => [],
    fullTypeCodeList: () => [],
    resetTypeCodeList: () => [],
  });
  const emit = defineEmits(['change']);

  const form = computed(() => props.formData);
  const valueTypeCodeList = computed(() => props.valueTypeCodeList);
  const fullTypeCodeList = computed(() => props.fullTypeCodeList);
  const resetTypeCodeList = computed(() => props.resetTypeCodeList);
  const bizNoFragmentList = computed(
    () => props.hospitalItem?.bizNoFragmentList,
  );
  const previewResult = ref('');
  const bizNoFragmentTableRef = ref<TableRef>();
  const handleResetBizNoFragmentListSort = () => {
    emit('change', {
      ...props.hospitalItem,
      bizNoFragmentList: props.hospitalItem.bizNoFragmentList.map(
        (item, index) => ({ ...item, sort: index + 1 }),
      ),
    });
  };
  const { bizNoFragmentTableConfig, addItem } = useBizNoFragmentTableConfig(
    bizNoFragmentTableRef,
    bizNoFragmentList,
    valueTypeCodeList,
    fullTypeCodeList,
    resetTypeCodeList,
    handleResetBizNoFragmentListSort,
  );

  watch(
    () => props.hospitalItem.bizNoFragmentList,
    () => {
      let str = '';
      if (props.hospitalItem?.bizNoFragmentList?.length > 0) {
        props.hospitalItem.bizNoFragmentList.forEach((item) => {
          switch (item.valueTypeCode) {
            case VALUE_TYPE_CODE.NUMERIC_TYPE: {
              // 数值型
              let value = item.currentValue;
              if (
                !isNaN(Number(item.currentValue)) &&
                !isNaN(Number(item.maxValue))
              ) {
                if (
                  Number(item.maxValue) &&
                  Number(item.currentValue) >= Number(item.maxValue)
                ) {
                  value = item.maxValue;
                } else {
                  value = item.currentValue;
                  if (
                    item.fullChar &&
                    [
                      FULL_TYPE_CODE.LEFT_PADDING,
                      FULL_TYPE_CODE.RIGHT_PADDING,
                    ].includes(item.fullTypeCode)
                  ) {
                    value =
                      item.fullTypeCode === FULL_TYPE_CODE.LEFT_PADDING
                        ? Array(
                            item.maxValue!.length -
                              item.currentValue!.length +
                              1,
                          ).join(item.fullChar) + item.currentValue
                        : item.currentValue +
                          Array(
                            item.maxValue!.length -
                              item.currentValue!.length +
                              1,
                          ).join(item.fullChar);
                  }
                }
              }
              str = `${str}${value || ''}`;
              break;
            }
            default:
              str = `${str}${item.currentValue || ''}`;
              break;
          }
        });
      }
      previewResult.value = str;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  watch(
    () => previewResult,
    () => {
      emit('change', {
        ...props.hospitalItem,
        previewResult: previewResult.value,
      });
    },
    { immediate: true },
  );

  const handleAddBizNoFragment = async () => {
    addItem({
      editable: true,
      sort: (props.hospitalItem?.bizNoFragmentList?.length || 0) + 1,
    });
  };

  const handleSortEnd = (data: AnyObject[]) => {
    emit('change', {
      ...props.hospitalItem,
      bizNoFragmentList: data.map((item, index) => ({
        ...item,
        sort: index + 1,
      })),
    });
  };

  defineExpose({ bizNoFragmentTableRef });
</script>
<template>
  <div class="flex h-full min-h-[500px] flex-col">
    <ProTable
      ref="bizNoFragmentTableRef"
      row-key="bizNoFragmentId"
      draggable
      :editable="true"
      :data="bizNoFragmentList"
      :columns="bizNoFragmentTableConfig"
      @drag-end="handleSortEnd"
    />
    <div
      @click="handleAddBizNoFragment"
      class="flex cursor-pointer items-center justify-center border-x-2 border-b-2 border-[#e5e7eb] py-2"
    >
      <el-icon size="20"><Plus /></el-icon>{{ $t('global:add') }}
    </div>

    <div
      :class="`p-4 ${
        form.maxCharSize && previewResult.length > form.maxCharSize
          ? 'text-red-400'
          : 'text-gray-400'
      }`"
    >
      <span class="text-lg">
        {{ $t('bizNoGenerateRule.tabs.preview', '预览：') + previewResult }}
      </span>
      <span
        v-if="form.maxCharSize && previewResult.length > form.maxCharSize"
        class="ml-2 text-sm"
      >
        {{ $t('bizNoGenerateRule.tabs.exceededMaxCharSize', '已超出最大长度') }}
      </span>
    </div>
  </div>
</template>
