<script lang="ts" name="bizNoGenerateRule" setup>
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import {
    ProForm,
    ProTable,
    Title,
    DmlButton,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { useSearchFormConfig } from '../config/useSearchFormConfig.ts';
  import { useBizNoGenerateRuleTableConfig } from '../config/useTableConfig.tsx';
  import {
    queryBizNoGenerateRuleListByExample,
    updateEnabledFlagById,
  } from '@modules/baseConfig/api/bizNoGenerateRule';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const router = useRouter();
  const searchParams = ref<BizNoGenerateRule.QueryParams>({
    keyWord: '',
  });
  const tableRef = ref();
  const loading = ref(false);
  const selections = ref<BizNoGenerateRule.BizNoGenerateRuleInfo[]>([]);
  const bizNoGenerateRuleList = ref<BizNoGenerateRule.BizNoGenerateRuleInfo[]>(
    [],
  );
  const { t } = useTranslation();

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.bizNoGenerateRuleId || '';
    });
  });

  async function queryBizNoGenerateRuleData(
    data?: BizNoGenerateRule.QueryParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryBizNoGenerateRuleListByExample(
      searchParams.value,
    );
    loading.value = false;
    if (res?.success) {
      bizNoGenerateRuleList.value = res.data;
    }
  }

  function onAddClick() {
    router.push({
      name: 'bizNoGenerateRuleDetail',
    });
  }

  function handleSelectChange(
    value: BizNoGenerateRule.BizNoGenerateRuleInfo[],
  ) {
    selections.value = value;
  }

  /** 启用状态切换 */
  async function handleEnableSwitch(
    row: BizNoGenerateRule.BizNoGenerateRuleInfo,
  ) {
    const params = {
      bizNoGenerateRuleId: row.bizNoGenerateRuleId,
      enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
    };
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.ruleName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await updateEnabledFlagById(params);
      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  }

  queryBizNoGenerateRuleData();
  const searchConfig = useSearchFormConfig(queryBizNoGenerateRuleData);
  const columnsConfig = useBizNoGenerateRuleTableConfig(handleEnableSwitch);
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('bizNoGenerateRule.list.title', '业务编码规则生成列表')"
    />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="queryBizNoGenerateRuleData"
        />
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_BIZ_NO_GENERATE_RULE"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
        <el-button
          :disabled="!isCloudEnv"
          class="ml-3"
          type="primary"
          @click="onAddClick"
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="tableRef"
      :columns="columnsConfig"
      :data="bizNoGenerateRuleList"
      :loading="loading"
      row-key="bizNoGenerateRuleId"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
