<script lang="ts" name="bizNoGenerateRuleDetail" setup>
  import { Plus } from '@element-sun/icons-vue';
  import { computed, ref, onMounted } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { VALUE_TYPE_CODE } from '../constant';
  import { useRouter, useRoute } from 'vue-router';
  import { ORG_TYPE_CODE } from '@/utils/constant';
  import { type FormInstance, ElMessage } from 'element-sun';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useDetailBaseInfoFormConfig } from '@/modules/baseConfig/pages/bizNoGenerateRule/config/useDetailFormConfig';
  import {
    queryBizNoGenerateRuleListByExample,
    addBizNoGenerateRule,
    updateBizNoGenerateRuleById,
  } from '@modules/baseConfig/api/bizNoGenerateRule';

  import {
    Title,
    ProForm,
    useAppConfigData,
    MAIN_APP_CONFIG,
    useFetchDataset,
  } from 'sun-biz';
  import hospitalBizNoFragmentTab from '@/modules/baseConfig/pages/bizNoGenerateRule/components/hospitalBizNoFragmentTab.vue';

  type HospitalBizNoFragmentListItem = {
    bizNoFragmentList: (BizNoGenerateRule.BizNoFragment & {
      editable?: boolean;
    })[];
    hospitalId: string;
    hospitalName: string;
    previewResult?: string;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const { isCloudEnv, currentOrg, hospitalList } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.HOSPITAL_LIST,
  ]);
  const loading = ref(false);
  const bizNoObjectCode = ref(route.query.bizNoObjectCode); // 业务编码对象,非空则为编辑模式,该值为业务主键，不能编辑
  const mode = ref(bizNoObjectCode.value ? 'edit' : 'add');
  const hospitalId = ref('1'); // 医院id，前端维护 默认='1'
  const bizNoGenerateRuleDetailData =
    ref<BizNoGenerateRule.BizNoGenerateRuleInfo>();
  const bizNoGenerateRuleUpsertParams =
    ref<BizNoGenerateRule.BizNoGenerateRuleUpsertParams>();
  const hospitalBizNoFragmentList = ref<HospitalBizNoFragmentListItem[]>([]);
  const originalHospitalSelectList = ref<Org.Item[]>([]);
  const hospitalSelectList = ref<Org.Item[]>([]);
  const submitLoading = ref(false);
  const baseInfoFormRef = ref<{
    ref: FormInstance;
    model: BizNoGenerateRule.BizNoGenerateRuleInfo;
  }>();
  const hospitalBizNoFragmentTabRef = ref();

  const dataSetList = useFetchDataset([
    CodeSystemType.VALUE_TYPE_CODE,
    CodeSystemType.FULL_TYPE_CODE,
    CodeSystemType.RESET_TYPE_CODE,
  ]);
  // 值类型代码数据
  const valueTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.VALUE_TYPE_CODE] || [])
      .filter(
        (item) =>
          ![VALUE_TYPE_CODE.BOOLEAN_TYPE, VALUE_TYPE_CODE.RANGE_TYPE].includes(
            item?.dataValueNo,
          ),
      )
      .map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      })),
  );
  // 补位方式代码数据
  const fullTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.FULL_TYPE_CODE] || []).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 重置方式代码数据
  const resetTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.RESET_TYPE_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const componentTitle = computed(() =>
    bizNoObjectCode.value
      ? t('bizNoGenerateRule.title.edit', '编辑业务编码规则')
      : t('bizNoGenerateRule.title.add', '新增业务编码规则'),
  );

  const getBizNoGenerateRuleDetail = async () => {
    if (bizNoObjectCode.value) {
      loading.value = true;
      const [, res] = await queryBizNoGenerateRuleListByExample({
        bizNoObjectCode: bizNoObjectCode.value as string,
      });
      loading.value = false;
      if (res?.success && res?.data?.length) {
        bizNoGenerateRuleDetailData.value = res.data[0];
        const {
          bizNoGenerateRuleId,
          bizNoObjectCode,
          enabledFlag,
          maxCharSize,
          ruleName,
          ruleDesc,
          recycleFlag,
        } = res.data[0];
        bizNoGenerateRuleUpsertParams.value = {
          bizNoGenerateRuleId,
          bizNoObjectCode,
          enabledFlag,
          maxCharSize: Number(maxCharSize),
          ruleName,
          ruleDesc,
          recycleFlag,
        };
        if (res.data[0].hospitalList?.length) {
          res.data[0].hospitalList.forEach(
            (item: BizNoGenerateRule.BizNoGenerateRuleHospital) => {
              if (item.bizNoFragmentList.length > 0) {
                item.bizNoFragmentList.sort(
                  (item1, item2) => item1.sort - item2.sort,
                );
              }
              if (!item.hospitalId && item.hospitalName === '默认') {
                item.hospitalId = '1';
              }
              hospitalBizNoFragmentList.value.push(item);
            },
          );
        } else {
          hospitalBizNoFragmentList.value = [
            { bizNoFragmentList: [], hospitalId: '1', hospitalName: '默认' },
          ];
        }
      }
    } else {
      bizNoGenerateRuleUpsertParams.value = {
        bizNoObjectCode: undefined,
        enabledFlag: '1',
        maxCharSize: undefined,
        ruleName: '',
        ruleDesc: '',
      };
      hospitalBizNoFragmentList.value = [
        { bizNoFragmentList: [], hospitalId: '1', hospitalName: '默认' },
      ];
    }
  };

  const handleAddHospital = async () => {
    // 过滤掉已选择过的
    hospitalSelectList.value = originalHospitalSelectList.value?.filter(
      (itemA) =>
        !hospitalBizNoFragmentList.value?.some(
          (itemB) => itemB.hospitalId === itemA.orgId,
        ),
    ) as Org.Item[];
  };

  const handleSelectHospital = async (item: Org.Item) => {
    hospitalBizNoFragmentList.value.push({
      hospitalId: item.orgId as string,
      hospitalName: item.orgName as string,
      bizNoFragmentList: [],
    });
    hospitalId.value = item.orgId as string;
  };

  const handleTabRemove = (targetName: string) => {
    const index = hospitalBizNoFragmentList.value.findIndex(
      (item) => item.hospitalId === targetName,
    );
    hospitalBizNoFragmentList.value?.splice(index, 1);
    hospitalId.value = '1';
  };

  const checkTabBeforeLeave = (isCheckAll: boolean = false) => {
    let isEditing = false;
    let isExceededMaxCharSize = false;
    if (isCheckAll) {
      isEditing = hospitalBizNoFragmentList.value.some((h) =>
        h.bizNoFragmentList.some((b) => b.editable === true),
      );
      isExceededMaxCharSize = hospitalBizNoFragmentList.value.some(
        (h) =>
          h.previewResult?.length &&
          bizNoGenerateRuleUpsertParams.value!.maxCharSize &&
          h.previewResult.length >
            bizNoGenerateRuleUpsertParams.value!.maxCharSize,
      );
    } else {
      const hospital = hospitalBizNoFragmentList.value.find(
        (item) => item.hospitalId === hospitalId.value,
      );
      isEditing = !!hospital?.bizNoFragmentList.some(
        (tabItem) => tabItem.editable === true,
      );
      isExceededMaxCharSize = !!(
        hospital?.previewResult?.length &&
        bizNoGenerateRuleUpsertParams.value!.maxCharSize &&
        hospital.previewResult.length >
          bizNoGenerateRuleUpsertParams.value!.maxCharSize
      );
    }
    if (isEditing) {
      ElMessage.warning(
        t(
          'bizNoGenerateRule.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的编码片段，请先确定！',
        ),
      );
      return false;
    } else if (isExceededMaxCharSize) {
      ElMessage.warning(
        t('bizNoGenerateRule.tabs.exceededMaxCharSize', '已超出最大长度'),
      );
    } else {
      return true;
    }
  };

  const onHospitalBizNoFragmentChange = (
    data: BizNoGenerateRule.BizNoGenerateRuleHospital,
  ) => {
    const index = hospitalBizNoFragmentList.value.findIndex(
      (item) => item.hospitalId === data.hospitalId,
    );
    hospitalBizNoFragmentList.value.splice(index, 1, data);
  };

  const handleSubmit = async () => {
    if (!checkTabBeforeLeave(true)) return;
    submitLoading.value = true;
    Promise.all([baseInfoFormRef.value?.ref?.validate()])
      .then(async () => {
        const bizNoFragmentList: BizNoGenerateRule.BizNoFragment[] = [];
        if (hospitalBizNoFragmentList.value.length > 0) {
          hospitalBizNoFragmentList.value.forEach((hospital) => {
            const list = hospital.bizNoFragmentList.map((item, index) => ({
              ...item,
              hospitalId:
                hospital.hospitalId === '1' ? null : hospital.hospitalId,
              hospitalName: hospital.hospitalName,
              sort: index + 1,
            }));
            bizNoFragmentList.push(...list);
          });
        }
        const params = {
          ...baseInfoFormRef.value?.model,
          bizNoFragmentList,
        };
        let isSuccess = false;
        if (mode.value === 'add') {
          const [, res] = await addBizNoGenerateRule(params);
          isSuccess = res?.success;
        } else {
          delete params.bizNoObjectCode;
          params.bizNoGenerateRuleId =
            bizNoGenerateRuleDetailData.value?.bizNoGenerateRuleId;
          const [, res] = await updateBizNoGenerateRuleById(params);
          isSuccess = res?.success;
        }
        if (isSuccess) {
          ElMessage.success(
            t(
              mode.value === 'add'
                ? 'global:add.success'
                : 'global:edit.success',
            ),
          );
          router.push('/');
        }
      })
      .finally(() => {
        submitLoading.value = false;
      });
  };

  const baseInfoFormConfig = useDetailBaseInfoFormConfig(isCloudEnv, mode);

  onMounted(async () => {
    hospitalSelectList.value =
      currentOrg?.orgTypeCode === ORG_TYPE_CODE.GROUP
        ? ((hospitalList ?? []) as unknown as Org.Item[])
        : [currentOrg as unknown as Org.Item];
    originalHospitalSelectList.value = [...hospitalSelectList.value];
    await getBizNoGenerateRuleDetail();
  });
</script>

<template>
  <div class="mr-2.5 flex h-full flex-col">
    <el-page-header @back="router.push('/')" class="pb-3">
      <template #content>
        <span class="text-base">
          {{ componentTitle }}
        </span>
      </template>
    </el-page-header>
    <div class="flex flex-col" v-loading="loading">
      <Title :title="$t('baseInfo', '基本信息')" class="mb-2" />
      <ProForm
        v-model="bizNoGenerateRuleUpsertParams"
        ref="baseInfoFormRef"
        :column="5"
        :label-width="110"
        :data="baseInfoFormConfig"
      />
      <Title
        class="mb-2"
        :title="$t('bizNoGenerateRule.title.bizNoFragmentList', '编码片段列表')"
      />
      <el-tabs
        v-model="hospitalId"
        class="hospital-tabs h-full"
        :before-leave="checkTabBeforeLeave"
        @tab-remove="handleTabRemove"
      >
        <el-tab-pane
          v-for="item in hospitalBizNoFragmentList"
          :key="item.hospitalId"
          :name="item.hospitalId"
          :closable="item.hospitalId !== '1'"
          :label="item.hospitalName"
          class="h-full"
        >
          <template #default>
            <hospitalBizNoFragmentTab
              class="h-full"
              ref="hospitalBizNoFragmentTabRef"
              :form-data="bizNoGenerateRuleUpsertParams"
              :hospital-item="item"
              :value-type-code-list="valueTypeCodeList"
              :full-type-code-list="fullTypeCodeList"
              :reset-type-code-list="resetTypeCodeList"
              @change="onHospitalBizNoFragmentChange"
            />
          </template>
        </el-tab-pane>
        <el-tab-pane>
          <template #label>
            <el-dropdown
              class="h-full"
              ref="dropdownRef"
              trigger="click"
              @command="handleSelectHospital"
            >
              <el-icon @click.stop="handleAddHospital" class="h-full" size="20">
                <Plus />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in hospitalSelectList"
                    :key="item.orgId"
                    :command="item"
                  >
                    {{ item.orgName }}
                  </el-dropdown-item>
                  <div
                    v-if="hospitalSelectList.length === 0"
                    class="mx-2 py-1 text-base"
                    style="font-size: 0.8rem; color: #d5d7de"
                  >
                    {{ $t('notFound.hospital', '暂无可选医院') }}
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="py-3.5 text-right">
      <el-button @click="() => router.push('/')">
        {{ $t('global:cancel') }}
      </el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
