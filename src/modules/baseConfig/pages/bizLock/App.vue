<script lang="ts" name="bizLock" setup>
  import { onMounted, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { queryUserList } from '@/modules/system/api/user';
  import { queryDataSetListByExample } from '@/modules/baseConfig/api/code';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { dayjs } from '@sun-toolkit/shared';
  import {
    MAIN_APP_CONFIG,
    PatientAccessResponse,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import {
    queryBizLockListData,
    unLockById,
  } from '@modules/baseConfig/api/bizLock';
  import { BIZ_LOCK_SCENE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useBizLockFormConfig } from './config/useFormConfig.tsx'; // ProFrom配置
  import { useBizLockTableConfig } from './config/useTableConfig.tsx'; // ProTable配置
  import { SelectOptions } from '@/typings/common.ts';

  const userInfo = useAppConfigData(MAIN_APP_CONFIG.USER_INFO);
  console.log(userInfo, 'userinfo');
  const { t } = useTranslation();
  const loading = ref(false);
  const bizLockList = ref<BizLock.BizLockInfo[]>([]);
  const allOperatorList = ref<
    { userId: string; userName: string; userNo: string }[]
  >([]);
  const rangList = ref<{ value: string; label: string }[]>([]);
  const operatorList = ref<SelectOptions[]>([]);
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const bizLockTableRef = ref();
  const patientInfo = ref<PatientAccessResponse['patientInfo']>(); // 患者信息

  const week = [
    dayjs().add(-6, 'd').endOf('day').format('YYYY-MM-DD 00:00:00'),
    dayjs().endOf('day').format('YYYY-MM-DD 23:59:59'),
  ];
  let searchParams = ref<BizLock.QueryParams>({
    bizIdTypeCode: '',
    lockResourceId: '',
    bizLockSceneCode: '',
    validFlag: ENABLED_FLAG.YES,
    lockUserId: userInfo?.userId,
    lockDate: week,
    lockBeginAt: week[0],
    lockEndAt: week[1],
  });

  const unlockItem = async (data: BizLock.BizLockInfoItem) => {
    ElMessageBox.confirm(
      t(
        'bizLock.confirmUnlockAskTitle',
        '当前业务锁在有效时间内，是否确认解锁？',
      ),
      t('bizLock.confirmUnlock', '确认解锁？'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          bizLockIds: data?.bizLockId?.split(','), // 业务锁ID
        };
        const [, res] = await unLockById(params);
        if (res?.success) {
          ElMessage({
            type: 'success',
            message: t('bizLock.unlock.success', '解锁成功'),
          });
          queryBizLockList();
        }
      })
      .catch(() => {});
  };

  // 查询业务锁列表
  async function queryBizLockList() {
    loading.value = true;
    const params = {
      ...searchParams.value,
      lockBeginAt: Array.isArray(searchParams.value.lockDate)
        ? searchParams.value.lockDate[0]
        : '',
      lockEndAt: Array.isArray(searchParams.value.lockDate)
        ? searchParams.value.lockDate[1]
        : '',
      bizIdTypeCode: searchParams.value.lockResourceId
        ? searchParams.value.bizIdTypeCode
        : '',
    };
    const [, res] = await queryBizLockListData(params);
    loading.value = false;
    if (res?.success) {
      bizLockList.value = res.data || [];
    }
  }

  // 获取用户列表
  async function fetchOperatorList(data?: BizLock.QueryUserListParams) {
    const QueryUserListParams = ref<BizLock.QueryUserListParams>({});
    if (data?.type === 'search') {
      if (data?.keyWord && data?.keyWord.length > 0) {
        QueryUserListParams.value = {
          enabledFlag: 1,
          keyWord: data?.keyWord,
        };
      }
    } else if (data?.type === 'load') {
      QueryUserListParams.value = {
        keyWord: '',
        enabledFlag: 1,
        pageNumber: 1,
        pageSize: 100,
      };
    } else {
      QueryUserListParams.value = {
        personId: userInfo?.userId,
        userNo: userInfo?.userNo,
        enabledFlag: 1,
      };
    }
    const [, res] = await queryUserList({
      enabledFlag: 1,
      keyWord: data?.keyWord,
    });
    if (res?.success) {
      allOperatorList.value = (res.data || []) as unknown as {
        userId: string;
        userName: string;
        userNo: string;
      }[];
      operatorList.value = allOperatorList.value.map((item) => ({
        label: `${item.userName}(${item.userNo})`,
        value: item.userId,
      }));
    }
  }

  // 获取场景列表
  async function fetchDataSetListByExample(codeSystemNo: string) {
    let [, result] = await queryDataSetListByExample({
      codeSystemNos: [codeSystemNo],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: 0,
    });
    if (result?.success) {
      let { data } = result;
      rangList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.dataValueNo,
          label: item.dataValueCnName,
        }));
    }
  }

  const isHidden = ref();

  // 业务场景改变
  const changeSelect = async (data?: BizLock.ProFormParams, key?: string) => {
    if (key === 'bizLockSceneCode') {
      if (data?.bizLockSceneCode === BIZ_LOCK_SCENE_CODE.OUTPATIENT) {
        searchParams.value.bizIdTypeCode = 'PER_PATIENT';
        isHidden.value = true;
      } else {
        searchParams.value.bizIdTypeCode = '';
        searchParams.value.lockResourceId = '';
        isHidden.value = false;
      }
      if (!searchParams.value.lockResourceId) return;
    }
    if (key === 'lockUserId') {
      searchParams.value.lockUserId = data?.lockUserId || '';
    }
    if (key === 'lockDate') {
      if (Array.isArray(data?.lockDate)) {
        searchParams.value.lockBeginAt = data.lockDate[0];
        searchParams.value.lockEndAt = data.lockDate[1];
      }
    }
    await queryBizLockList();
  };
  /** 处理患者进入组件change */
  const handlePatientChange = async (data: PatientAccessResponse) => {
    if (data) {
      patientInfo.value = data.patientInfo;
      searchParams.value.lockResourceId = patientInfo.value.patientId;
    } else {
      searchParams.value.lockResourceId = '';
    }

    await queryBizLockList();
  };

  const searchConfig = useBizLockFormConfig(
    operatorList,
    rangList,
    isHidden,
    fetchOperatorList,
    handlePatientChange,
  );
  watch(
    () => patientInfo.value,
    (newValue) => {
      console.log(newValue, 'newValue');
      console.log(newValue.patientId, 'patientId');
    },
  );
  const tableColumnsConfig = useBizLockTableConfig(unlockItem);

  onMounted(async () => {
    await fetchOperatorList(); // 确保先获取用户列表
    await fetchDataSetListByExample('BIZ_LOCK_SCENE_CODE');
    await queryBizLockList();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('bizLock.list.title', '业务锁查询')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
      </div>
      <el-button
        class="mr-2"
        icon="search"
        type="primary"
        @click="queryBizLockList"
      >
        {{ $t('global:search') }}
      </el-button>
    </div>

    <ProTable
      ref="bizLockTableRef"
      :columns="tableColumnsConfig"
      :data="bizLockList"
      :loading="loading"
      row-key="bizLockId"
    />
  </div>
</template>
