import { useColumnConfig } from 'sun-biz';

type tableRowData = BizLock.BizLockInfoItem;

export function useBizLockTableConfig(
  unlockItem: (data: tableRowData, index: number) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('bizLock.bizLockTable.bizLockId', '业务锁标识'),
        prop: 'bizLockId',
        minWidth: 150,
      },
      {
        label: t('bizLock.bizLockTable.bizLockSceneDesc', '锁定场景'),
        prop: 'bizLockSceneDesc',
        minWidth: 180,
      },
      {
        label: t('bizLock.bizLockTable.bizIdTypeDesc', '业务标识类型'),
        prop: 'bizIdTypeDesc',
        minWidth: 150,
      },
      {
        label: t('bizLock.bizLockTable.lockResourceId', '业务标识'),
        prop: 'lockResourceId',
        minWidth: 150,
      },
      {
        label: t('bizLock.bizLockTable.lockAt', '锁定日期时间'),
        prop: 'lockAt',
        minWidth: 180,
      },
      {
        label: t('bizLock.bizLockTable.bizIdTypeDesc', '锁定操作员'),
        prop: 'lockUserName',
        minWidth: 150,
        render: (row: tableRowData) => {
          return `${row.lockUserName}(${row.lockUserNo})`;
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: tableRowData, $index: number) => {
          return (
            <el-button
              type="primary"
              link={true}
              onClick={() => unlockItem(row, $index)}
            >
              {t('bizLock.unlock', '解锁')}
            </el-button>
          );
        },
      },
    ],
  });
}
