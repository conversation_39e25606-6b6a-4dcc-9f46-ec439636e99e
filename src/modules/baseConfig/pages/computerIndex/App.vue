<script lang="ts" name="computerIndex" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant';
  import { useComputerIndexTableConfig } from './config/useTableConfig';
  import { useComputerIndexSearchFormConfig } from './config/useFormConfig';
  import {
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    DmlButton,
    useAppConfigData,
  } from 'sun-biz';
  import {
    deleteComputerIndex,
    editComputerIndex,
    queryComputerIndexByExample,
  } from '@modules/baseConfig/api/computerManage';
  import ComputerIndexUpsertDialog from '@/modules/baseConfig/pages/computerIndex/components/ComputerIndexUpsertDialog.vue';

  const { t } = useTranslation();
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<ComputerManage.ComputerIndexQueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
    computerResTypeCode: '',
  });
  const loading = ref(false);
  const computerIndexList = ref<ComputerManage.ComputerIndexInfo[]>([]);
  const computerIndexUpsertParams =
    ref<ComputerManage.UpsertComputerIndexParams>({});
  const computerIndexUpsertDialogRef = ref();
  const computerIndexUpsertDialogMode = ref('');
  const computerIndexTableRef = ref();
  const selections = ref<ComputerManage.ComputerIndexInfo[]>([]);

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.computerIndexId || '';
    });
  });

  const queryComputerIndexList = async (
    data?: ComputerManage.ComputerIndexQueryParams,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryComputerIndexByExample(params);
    loading.value = false;
    if (res?.success) {
      computerIndexList.value = res.data ?? [];
    }
  };

  const onOpenComputerIndexDialog = (
    data?: ComputerManage.ComputerIndexInfo,
  ) => {
    computerIndexUpsertDialogMode.value = data ? 'edit' : 'add';
    if (computerIndexUpsertDialogMode.value === 'add') {
      computerIndexUpsertParams.value = {
        enabledFlag: ENABLED_FLAG.YES,
      };
    } else if (computerIndexUpsertDialogMode.value === 'edit' && data) {
      const {
        computerIndexId,
        computerIndexName,
        computerResTypeCode,
        criticalValueTypeCode,
        criticalValue,
        enabledFlag,
        logTimeValue,
        timeUnitCode,
      } = data;
      computerIndexUpsertParams.value = {
        computerIndexId,
        computerIndexName,
        computerResTypeCode,
        criticalValueTypeCode,
        criticalValue,
        enabledFlag,
        logTimeValue,
        timeUnitCode,
      };
    }
    computerIndexUpsertDialogRef.value.dialogRef.open();
  };

  const handleEnableSwitch = async (row: ComputerManage.ComputerIndexInfo) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.computerIndexName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const {
        computerIndexId,
        computerIndexName,
        computerResTypeCode,
        criticalValueTypeCode,
        criticalValue,
        logTimeValue,
        timeUnitCode,
      } = row;
      const params = {
        computerIndexId,
        computerIndexName,
        computerResTypeCode,
        criticalValueTypeCode,
        criticalValue,
        logTimeValue,
        timeUnitCode,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editComputerIndex(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryComputerIndexList();
      }
    });
  };

  const handleDeleteComputerIndex = async (
    row: ComputerManage.ComputerIndexInfo,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.computerIndexName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, res] = await deleteComputerIndex({
          computerIndexId: row.computerIndexId,
        });
        if (res?.success) {
          ElMessage.success(t('global:delete.success'));
          queryComputerIndexList();
        }
      })
      .catch(() => {});
  };

  const handleSelectChange = (value: ComputerManage.ComputerIndexInfo[]) => {
    selections.value = value;
  };

  const searchConfig = useComputerIndexSearchFormConfig(queryComputerIndexList);
  const tableColumnsConfig = useComputerIndexTableConfig(
    isCloudEnv,
    onOpenComputerIndexDialog,
    handleEnableSwitch,
    handleDeleteComputerIndex,
  );

  onMounted(async () => {
    await queryComputerIndexList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('computerIndex.list.title', '计算机指标列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="queryComputerIndexList"
        />
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_COMPUTER_INDEX"
          @success="
            () => {
              computerIndexTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
        <el-button
          class="ml-3"
          :disabled="!isCloudEnv"
          type="primary"
          @click="onOpenComputerIndexDialog()"
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="computerIndexTableRef"
      :columns="tableColumnsConfig"
      :data="computerIndexList"
      :loading="loading"
      row-key="computerIndexId"
      @selection-change="handleSelectChange"
    />
  </div>
  <ComputerIndexUpsertDialog
    ref="computerIndexUpsertDialogRef"
    :data="computerIndexUpsertParams"
    :mode="computerIndexUpsertDialogMode"
    @success="queryComputerIndexList"
  />
</template>
