import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';

export function useComputerIndexTableConfig(
  isCloudEnv: boolean | undefined,
  onOpenComputerIndexDialog: (data?: ComputerManage.ComputerIndexInfo) => void,
  handleEnableSwitch: (data: ComputerManage.ComputerIndexInfo) => void,
  handleDeleteComputerIndex: (data: ComputerManage.ComputerIndexInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t(
          'computerIndex.computerIndexable.computerIndexId',
          '计算机指标标识',
        ),
        prop: 'computerIndexId',
        minWidth: 180,
      },
      {
        label: t(
          'computerIndex.computerIndexable.computerIndexName',
          '计算机指标名称',
        ),
        prop: 'computerIndexName',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ComputerManage.ComputerIndexInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t(
          'computerIndex.computerIndexable.criticalValueTypeCodeDesc',
          '阈值类型',
        ),
        prop: 'criticalValueTypeCodeDesc',
        minWidth: 100,
      },
      {
        label: t('computerIndex.computerIndexable.criticalValue', '阈值'),
        prop: 'criticalValue',
        minWidth: 90,
      },
      {
        label: t(
          'computerIndex.computerIndexable.computerResTypeCodeDesc',
          '所属计算机资源',
        ),
        prop: 'computerResTypeCodeDesc',
        minWidth: 120,
      },
      {
        label: t(
          'computerIndex.computerIndexable.logTimeValueDisplay',
          '日志时间设置值',
        ),
        prop: 'logTimeValueDisplay',
        minWidth: 120,
        render: (row: ComputerManage.ComputerIndexInfo) => {
          return (
            <span>
              {row.logTimeValue}
              {row.timeUnitCodeDesc}
            </span>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (row: ComputerManage.ComputerIndexInfo) => {
          return (
            <div class={'flex justify-between'}>
              <el-button
                type="primary"
                link={true}
                disabled={!isCloudEnv}
                onClick={() => onOpenComputerIndexDialog(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                disabled={!isCloudEnv}
                onClick={() => handleDeleteComputerIndex(row)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
