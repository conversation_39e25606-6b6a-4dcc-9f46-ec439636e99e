import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  COMPUTER_RES_TYPE_CODE,
  CRITICAL_VALUE_TYPE_CODE,
  ENABLED_FLAG,
  TIME_UNIT_CODE,
} from '@/utils/constant';

export function useComputerIndexSearchFormConfig(
  queryComputerIndexList: (
    data?: ComputerManage.ComputerIndexQueryParams,
  ) => void,
) {
  const dataSetCodes = [COMPUTER_RES_TYPE_CODE];
  const data = useFormConfig({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryComputerIndexList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryComputerIndexList({ keyWord: '' });
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'computerResTypeCode',
        label: t('computerIndex.search.computerResTypeCode', '计算机资源'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('computerIndex.search.computerResTypeCode', '计算机资源'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-80',
          clearable: true,
          options: dataSet?.value ? dataSet.value[COMPUTER_RES_TYPE_CODE] : [],
        },
      },
    ],
  });
  return data;
}

export function useComputerIndexUpsertFormConfig(
  disabled: Ref<boolean>,
  changeComputerResTypeCode: (val: string) => void,
) {
  const dataSetCodes = [
    CRITICAL_VALUE_TYPE_CODE,
    COMPUTER_RES_TYPE_CODE,
    TIME_UNIT_CODE,
  ];
  return useFormConfig({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        label: t('computerIndex.form.computerIndexName', '计算机指标'),
        name: 'computerIndexName',
        component: 'input',
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('computerIndex.form.computerIndexName', '计算机指标'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerIndex.form.computerIndexName', '计算机指标'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'criticalValueTypeCode',
        label: t('computerIndex.form.criticalValueTypeCode', '阈值类型'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('computerIndex.form.criticalValueTypeCode', '阈值类型'),
            }),
        extraProps: {
          filterable: true,
          options: dataSet?.value
            ? dataSet.value[CRITICAL_VALUE_TYPE_CODE]
            : [],
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('computerIndex.form.criticalValueTypeCode', '阈值类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'criticalValue',
        label: t('computerIndex.form.criticalValue', '阈值'),
        component: 'input-number',
        placeholder: '',
        extraProps: {
          'controls-position': 'right',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerIndex.form.criticalValue', '阈值'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'computerResTypeCode',
        label: t('computerIndex.form.computerResTypeCode', '计算机资源'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('computerIndex.form.computerResTypeCode', '计算机资源'),
            }),
        extraProps: {
          onChange: (typeCode: string) => changeComputerResTypeCode(typeCode),
          options: dataSet?.value ? dataSet.value[COMPUTER_RES_TYPE_CODE] : [],
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('computerIndex.form.computerResTypeCode', '计算机资源'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'logTimeValue',
        label: t('computerIndex.form.logTimeValue', '日志时间'),
        component: 'input-number',
        placeholder: '',
        extraProps: {
          min: 0,
          step: 1,
          'step-strictly': true,
          max: 9999999,
          'controls-position': 'right',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerIndex.form.logTimeValue', '日志时间'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'timeUnitCode',
        label: t('computerIndex.form.timeUnitCode', '时间单位'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('computerIndex.form.timeUnitCode', '时间单位'),
            }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[TIME_UNIT_CODE] : [],
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('computerIndex.form.timeUnitCode', '时间单位'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
}
