<script lang="ts" name="ComputerIndexUpsertDialog" setup>
  import { ref, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProForm } from 'sun-biz';
  import {
    addComputerIndex,
    editComputerIndex,
  } from '@modules/baseConfig/api/computerManage';
  import { useComputerIndexUpsertFormConfig } from '../config/useFormConfig.ts';

  const props = defineProps<{
    mode: string;
    data: ComputerManage.UpsertComputerIndexParams;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: ComputerManage.UpsertComputerIndexParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const disabled = ref(false);
  const dialogForm = ref({});
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props,
    () => {
      disabled.value = props.mode === 'view';

      dialogForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addComputerIndex(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editComputerIndex(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const changeComputerResTypeCode = () => {
    formRef.value.model.timeUnitCode = undefined;
    formRef.value.model.logTimeValue = undefined;
  };

  const formConfig = useComputerIndexUpsertFormConfig(
    disabled,
    changeComputerResTypeCode,
  );

  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :title="`${$t(`global:${props.mode}`)}${$t('computerIndex.name', '计算机指标')}`"
    :width="680"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="2"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
