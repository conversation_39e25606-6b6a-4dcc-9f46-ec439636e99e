/** 参数分类 */
export enum PARAM_CATEGORY_CODE {
  /**
   * 全部
   */
  ALL = '9',
}

/** 值类型代码 */
export enum VALUE_TYPE_CODE {
  /**
   * 字符型
   */
  STRING = '2',
}

/** 参数影响范围代码 */
export enum PARAM_INFLUENCE_SCOPE_CODE {
  /**
   * 通用
   */
  GENERAL = '1',
  /**
   * 医院
   */
  HOSPITAL = '2',
}

/** 多值标志 非值域*/
export enum PARAM_MULTI_VALUE_FLAG {
  /**
   * 是
   */
  YES = '1',
  /**
   * 否
   */
  NO = '0',
}
