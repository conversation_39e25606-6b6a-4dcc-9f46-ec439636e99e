<script setup lang="ts" name="employeeManage">
  import { onMounted, nextTick, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { queryParamListByExample } from '@/modules/baseConfig/api/paramSetting.ts';
  import { useSearchFormConfig } from '../config/useSearchConfigData.ts';
  import { useConfigColumnConfig } from '../config/useTableColumnConfig.tsx';
  import type { TableColumnCtx } from 'element-sun/es/components/table/src/table-column/defaults';
  import { PARAM_CATEGORY_CODE } from '../constant.tsx';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { downloadFile } from '@sun-toolkit/shared';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  import {
    Title,
    ProForm,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  interface SpanMethodProps {
    row: ParamInfo;
    column: TableColumnCtx<ParamSetting.ParamInfo>;
    rowIndex: number;
    columnIndex: number;
  }

  interface ParamInfo
    extends ParamSetting.ParamInfo,
      ParamSetting.ParamUseScopeInfo,
      ParamSetting.ParamSettingInfo {
    paramInfluenceScopeCodeIndex?: number;
    paramValueIndex?: number;
    sort: number;
  }
  //isCloudEnv，true指云端，flase其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const tableRef = ref();
  const dmlList = useGetDMLList();
  // 检索条件配置数据
  const searchConfig = useSearchFormConfig();
  // 职工表头配置数据
  const configColumns = useConfigColumnConfig();
  const refreshIndex = ref(0);
  const router = useRouter();
  const loading = ref(false);
  // 修改后可以触发重新渲染from
  const searchModel = ref<Partial<ParamSetting.ReqParams>>({});
  /**
   * : 参数配置列表
   */
  const paramList = ref<ParamInfo[]>([]);
  const selections = ref<ParamSetting.ParamInfo[]>([]);
  // 系统总数
  const total = ref(0);
  // 查询参数配置条件
  const searchParams = ref({
    pageSize: 10,
    pageNumber: 1,
    paramCategoryCode: '',
  });
  /**
   * 查询参数配置列表
   * @param params 查询参数
   */
  const queryParamData = async (params: Partial<ParamSetting.ReqParams>) => {
    loading.value = true;
    if (params) {
      searchParams.value = {
        ...searchParams.value,
        ...params,
      };
    }
    let [, res] = await queryParamListByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.data) {
      let result: ParamInfo[] = [];
      (res.data as unknown as ParamSetting.ParamInfo[]).forEach(
        (item, index) => {
          let { paramUseScopeList = [] } = item;
          paramUseScopeList.forEach((cur, curIndex) => {
            let { paramSettingList = [] } = cur;
            paramSettingList.forEach((obj, objIndex) => {
              let paramValueIndex: number = 0;
              if (objIndex === 0 && curIndex === 0) {
                paramValueIndex = paramUseScopeList.reduce((acc, cur) => {
                  return acc + cur.paramSettingList.length;
                }, 0);
              }
              result.push({
                ...item,
                ...cur,
                ...obj,
                paramInfluenceScopeCodeIndex:
                  objIndex === 0 ? paramSettingList.length : 0,
                paramValueIndex:
                  objIndex === 0 && curIndex === 0 ? paramValueIndex : 0,
                sort: index + 1,
              });
            });
          });
        },
      );
      total.value = Number(res?.total);
      paramList.value = result;
      refreshIndex.value += 1;
    }
  };

  onMounted(() => {
    searchModel.value = {
      paramCategoryCode: PARAM_CATEGORY_CODE.ALL,
      pageSize: 10,
      pageNumber: 1,
    };
    queryParamData(searchModel.value);
  });

  /**
   * 合并单元格
   */
  const mergeColumns = ({ row, columnIndex }: SpanMethodProps) => {
    if (columnIndex <= 7 || columnIndex === 10) {
      if (row.paramValueIndex) {
        return {
          rowspan: row.paramValueIndex,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    } else if (columnIndex === 8) {
      if (row.paramInfluenceScopeCodeIndex) {
        return {
          rowspan: row.paramInfluenceScopeCodeIndex,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  };

  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_PARAMETER,
      bisIds: selections.value.map((item) => item.paramId),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  function selectChange(value: ParamSetting.ParamInfo[]) {
    selections.value = value as ParamSetting.ParamInfo[];
  }
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('employee.manage.title', '参数列表')" />
    <!-- 检索框 -->
    <div class="mt-3 flex justify-between">
      <div class="el-form-item flex-1">
        <ProForm
          :data="searchConfig"
          layout-mode="inline"
          v-model="searchModel"
          @model-change="
            () => {
              nextTick(() => {
                searchParams.pageNumber = 1;
                queryParamData(searchModel);
              });
            }
          "
        />
      </div>
      <span>
        <el-dropdown @click="clickDropdown">
          <el-button type="primary" :disabled="!selections.length">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown v-if="selections.length && !!dmlList.length">
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in dmlList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          class="ml-4"
          :disabled="!isCloudEnv"
          @click="
            () => {
              router.push('/detail/add');
            }
          "
          >{{ $t('global:add') }}</el-button
        >
      </span>
    </div>
    <pro-table
      :data="paramList"
      :span-method="mergeColumns"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :key="refreshIndex"
      ref="tableRef"
      row-key="paramId"
      @selection-change="selectChange"
      :columns="configColumns"
      :pagination="true"
      :loading="loading"
      @current-page-change="
        (val: number) => {
          queryParamData({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          queryParamData({ pageSize: val, pageNumber: 1 });
        }
      "
    />
  </div>
</template>
