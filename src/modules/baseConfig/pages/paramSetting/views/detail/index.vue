<script setup lang="ts" name="employeeDetail">
  import { ref, nextTick, computed, onBeforeMount } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { useRouter, useRoute } from 'vue-router';
  import { useParamInfoConfig } from '../../config/useParamSettingConfig';
  import { useTableColumnConfig } from '../../config/useTableColumnConfig';
  import { addParam, updateParamById } from '../../../../api/paramSetting';
  import { FLAG_STR, PARAMSETTING_DEFAULT_TYPE } from '@/utils/constant';
  import {
    queryParamListByExample,
    getNextParamNoByParamCategoryCode,
  } from '@/modules/baseConfig/api/paramSetting.ts';
  import { queryDataSetListByExample } from '@/modules/baseConfig/api/code';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import {
    PARAM_CATEGORY_CODE,
    VALUE_TYPE_CODE,
    PARAM_MULTI_VALUE_FLAG,
    PARAM_INFLUENCE_SCOPE_CODE,
  } from '../../constant.tsx';

  import {
    Title,
    ProForm,
    ProTable,
    AnyObject,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  type SearchModel = {
    paramMultiValueFlag: string;
    paramCategoryCode: string;
    valueTypeCode: string;
    paramId?: string;
    codeSystemNo?: string;
    paramNo: string;
  };
  const ADD = 'add';
  const PARAMCATEGORYCODE = 'paramCategoryCode';
  const VALUETYPECODE = 'valueTypeCode';
  const CODESYSTEMNO = 'codeSystemNo';
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const route = useRoute();
  const router = useRouter();
  const loading = ref(true);
  const submitLoading = ref(false);
  const rangList = ref<
    {
      value: string;
      label: string;
    }[]
  >([]);
  //isCloudenv，true指云端，flase其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const paramNo = computed(() => route.params.id);
  const paramId = ref<undefined | string>('');

  const hospitalList = useAppConfigData('hospitalList') || [];
  const formRef = ref<{
    ref: FormInstance;
    model: ParamSetting.ReqAddParam;
  }>();

  const tableRef = ref<{ formRef: FormInstance }>();

  // 参数配置信息
  const searchModel = ref<SearchModel>({
    paramMultiValueFlag: PARAM_MULTI_VALUE_FLAG.NO,
    paramCategoryCode: PARAM_CATEGORY_CODE.ALL,
    valueTypeCode: VALUE_TYPE_CODE.STRING,
    paramNo: '',
  });
  // 配置信息列表列表
  const settingList = ref<
    (Omit<ParamSetting.AddParamUseScopeInfo, 'paramValues'> & {
      editable?: boolean; //表格中是否可以使用from
      paramValues: {
        value: string | undefined;
        paramSettingId?: string | undefined;
      }[];
      isAdd?: boolean;
    })[]
  >([
    {
      paramInfluenceScopeCode: PARAM_INFLUENCE_SCOPE_CODE.GENERAL,
      paramValues: [{ value: '', paramSettingId: undefined }],
      editable: true,
    },
  ]);

  /**
   * 过滤出没有被选中的医院
   */
  const paramInfluenceScopeValueIds = computed(() => {
    return hospitalList.filter((cur: { orgId: string }) => {
      return !settingList.value.find(
        (item) =>
          item.paramInfluenceScopeValueId === cur.orgId && !item.editable,
      );
    });
  });
  // 是否为新增状态
  const isAdd = computed(() => paramNo.value === ADD);
  const { data: paramSetting, querySystemList } = useParamInfoConfig(
    isAdd.value,
    isCloudEnv,
    searchModel,
  );
  const tableColumn = useTableColumnConfig(
    paramInfluenceScopeValueIds,
    hospitalList,
    settingList,
    tableRef,
    formRef,
    isCloudEnv,
    rangList,
  );

  /**
   * 初始化
   */
  async function init() {
    if (!isAdd.value) {
      let [, result] = await queryParamListByExample({
        paramNos: [String(paramNo.value)],
        pageSize: 1,
        pageNumber: 1,
      });
      if (result?.success) {
        let data = result.data[0] as unknown as SearchModel & {
          paramUseScopeList?: ParamSetting.AddParamUseScopeInfo[];
        };
        paramId.value = data.paramId;
        searchModel.value = data;
        if (data.valueTypeCode === PARAMSETTING_DEFAULT_TYPE.CODESYSTEM) {
          querySystemList(data.codeSystemNo);
          fetchDataSetListByExample(data.codeSystemNo as string);
        }
        settingList.value = (data?.paramUseScopeList || []).map((item) => {
          return {
            ...item,
            paramValues: (item?.paramSettingList || []).map((cur) => ({
              value: cur.paramValue,
              paramSettingId: cur.paramSettingId,
            })),
          };
        });
        loading.value = false;
      }
    } else {
      loading.value = false;
      getNextParamNo(PARAM_CATEGORY_CODE.ALL);
    }
  }

  onBeforeMount(() => {
    init();
  });

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  // 保存
  const handleSubmit = async () => {
    Promise.all([
      formRef?.value?.ref.validateField(),
      tableRef?.value?.formRef?.validateField(),
    ]).then(async () => {
      submitLoading.value = true;
      let [, result] = isAdd.value
        ? await addParam({
            ...(formRef?.value?.model || {}),
            paramUseScopeList: settingList.value.map((item) => ({
              paramInfluenceScopeCode: item.paramInfluenceScopeCode,
              paramInfluenceScopeValueId: item.paramInfluenceScopeValueId,
              paramValues: item.paramValues.map((item) => item.value),
            })),
          } as ParamSetting.ReqAddParam)
        : await updateParamById({
            ...(formRef?.value?.model || {}),
            paramId: paramId.value,
            paramUseScopeList: settingList.value.map((item) => ({
              paramUseScopeId: item?.paramUseScopeId,
              paramInfluenceScopeCode: item.paramInfluenceScopeCode,
              paramInfluenceScopeValueId: item.paramInfluenceScopeValueId,
              paramValues: item.paramValues.map((item) => ({
                paramValue: item.value,
                paramSettingId: item.paramSettingId,
              })),
            })),
          } as ParamSetting.ReqUpdateParam);
      submitLoading.value = false;
      if (result?.success) {
        goBack();
      }
    });
  };

  /**
   * 新增配置信息列表
   */
  function addParamSetting() {
    settingList.value = [
      ...settingList.value,
      {
        paramInfluenceScopeCode: PARAM_INFLUENCE_SCOPE_CODE.HOSPITAL,
        paramInfluenceScopeValueId: '',
        paramValues: [
          {
            value:
              searchModel.value.valueTypeCode ===
              PARAMSETTING_DEFAULT_TYPE.SWTICH
                ? FLAG_STR.NO
                : undefined,
          },
        ],
        editable: true,
        isAdd: true,
      },
    ];
  }

  async function getNextParamNo(paramCategoryCode: string) {
    let [, result] = await getNextParamNoByParamCategoryCode({
      paramCategoryCode,
    });
    if (result?.success) {
      searchModel.value.paramNo = result?.data?.nextParamNo;
    }
  }
  async function fetchDataSetListByExample(codeSystemNo: string) {
    let [, result] = await queryDataSetListByExample({
      codeSystemNos: [codeSystemNo],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: 0,
    });
    if (result?.success) {
      let { data } = result;
      rangList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.dataValueNo,
          label: item.dataValueCnName,
        }));
    }
  }

  function clearConfigValue() {
    nextTick(() => {
      settingList.value = settingList.value.map((item) => {
        return {
          ...item,
          editable: true,
          paramValues: item.paramValues.map(() => ({
            value:
              searchModel.value.valueTypeCode ===
              PARAMSETTING_DEFAULT_TYPE.SWTICH
                ? FLAG_STR.NO
                : '',
          })),
        };
      });
    });
  }

  function handleModelChange(data: AnyObject, key = '') {
    switch (key) {
      case PARAMCATEGORYCODE:
        getNextParamNo(data.paramCategoryCode);
        break;
      case VALUETYPECODE:
        clearConfigValue();
        break;
      case CODESYSTEMNO:
        fetchDataSetListByExample(data.codeSystemNo);
        clearConfigValue();
        break;
      default:
        break;
    }
  }
</script>

<template>
  <div class="p-box h-full" v-loading="loading">
    <div v-if="!loading" class="flex h-full flex-col">
      <div>
        <el-page-header @back="goBack">
          <template #content>
            <span class="text-base">
              {{
                isAdd
                  ? $t('paramSetting.add', '参数配置')
                  : `${$t('global:edit', '编辑')}-${paramNo}`
              }}
            </span>
          </template>
        </el-page-header>
        <Title :title="$t('paramSetting.baseInfo', '参数信息')" class="my-3" />
        <ProForm
          ref="formRef"
          :column="3"
          v-model="searchModel"
          :disabled="!isCloudEnv"
          @model-change="handleModelChange"
          :data="paramSetting.configInfo"
        />

        <Title :title="$t('paramSetting.settingInfo', '配置信息')" class="my-3">
          <el-button type="primary" @click="addParamSetting">{{
            $t('global:add')
          }}</el-button>
        </Title>
      </div>
      <div class="flex h-full flex-1 flex-col overflow-hidden">
        <ProTable
          ref="tableRef"
          :columns="tableColumn"
          :editable="true"
          :data="settingList"
        />
        <div class="mt-5 text-right">
          <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            :disabled="submitLoading"
            @click="handleSubmit"
          >
            {{ $t('global:save') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
