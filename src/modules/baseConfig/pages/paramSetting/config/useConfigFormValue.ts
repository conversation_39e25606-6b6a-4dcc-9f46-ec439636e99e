import { useFormConfig } from 'sun-biz';
import { FLAG_STR, PARAMSETTING_DEFAULT_TYPE } from '@/utils/constant';
import { Ref } from 'vue';
export function useConfigFormValue(
  flag: PARAMSETTING_DEFAULT_TYPE,
  rangList: Ref<{ value: string; label: string }[]>,
  callback: () => void,
) {
  const data = useFormConfig({
    getData: (t) => {
      console.log(t);
      const COMPONENT_TYPE = {
        [PARAMSETTING_DEFAULT_TYPE.NUMBER]: {
          component: 'input-number',
          placeholder: t(
            'paramSetting.configValue.inputNumber',
            '请输入配置值',
          ),
          extraProps: {
            style: { width: '100%' },
            controlsPosition: 'right',
            onInput: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.INPUT]: {
          component: 'input',
          placeholder: t('paramSetting.configValue.input', '请输入配置值'),
          extraProps: {
            style: { width: '100%' },
            onInput: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.DATE_PICKER]: {
          component: 'date-picker',
          placeholder: t(
            'paramSetting.configValue.datePicker',
            '请输入配置日期',
          ),
          extraProps: {
            style: { width: '100%' },
            type: 'date',
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            onChange: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.TIME_PICKER]: {
          component: 'time-picker',
          placeholder: t(
            'paramSetting.configValue.timeSelect',
            '请输入配置时间',
          ),
          extraProps: {
            style: { width: '100%' },
            onChange: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.DATE_TIME_PICKER]: {
          component: 'date-picker',
          placeholder: t(
            'paramSetting.configValue.dateTimePicker',
            '请输入配置日期时间',
          ),
          extraProps: {
            style: { width: '100%' },
            type: 'datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            onChange: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.SWTICH]: {
          component: 'switch',
          placeholder: '',
          defaultValue: FLAG_STR.NO,
          extraProps: {
            width: '100%',
            'inline-prompt': true,
            'active-value': FLAG_STR.YES,
            'inactive-value': FLAG_STR.NO,
            onChange: callback,
          },
        },
        [PARAMSETTING_DEFAULT_TYPE.CODESYSTEM]: {
          component: 'select',
          placeholder: t('paramSetting.configValue.codesystem', '请选择值域'),
          extraProps: {
            style: { width: '100%' },
            options: rangList.value,
            onChange: callback,
          },
        },
      };
      return [
        {
          ...COMPONENT_TYPE[flag],
          triggerModelChange: true,
          isFullWidth: true,
          autofocus: true,
          name: 'value',
          label: '',
        },
      ];
    },
  });
  return data;
}
