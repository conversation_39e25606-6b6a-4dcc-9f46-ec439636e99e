import { useRouter } from 'vue-router';
import { ProForm, useColumnConfig, useFetchDataset } from 'sun-biz';
import { CodeSystemType } from '@/typings/codeManage';
import { Ref } from 'vue';
import { PARAMSETTING_DEFAULT_TYPE, FLAG_STR } from '@/utils/constant';
import { Plus, SemiSelect } from '@element-sun/icons-vue';
import { ElMessage, type FormInstance } from 'element-sun';

import {
  PARAM_MULTI_VALUE_FLAG,
  PARAM_INFLUENCE_SCOPE_CODE,
} from '../constant.tsx';
import { useConfigFormValue } from './useConfigFormValue';

type SettingInfoRow = Omit<ParamSetting.AddParamUseScopeInfo, 'paramValues'> & {
  editable?: boolean; //表格是否可以可以
  paramValues: { value: unknown }[];
  isAdd?: boolean;
  oldParam?:
    | (Omit<ParamSetting.AddParamUseScopeInfo, 'paramValues'> & {
        editable?: boolean;
        isAdd?: boolean;
        paramValues: { value: unknown }[];
      })
    | null;
};

export function useConfigColumnConfig() {
  const router = useRouter();
  const toggleEdit = (row: { paramNo: string }) => {
    router.push(`/detail/${row.paramNo}`);
  };
  return useColumnConfig({
    getData: (t) => [
      {
        prop: 'indexNo',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: { sort: number }) => <>{row.sort}</>,
      },
      {
        label: t('global:code'),
        prop: 'paramNo',
        supportCopyAndTips: true,
        minWidth: 80,
      },
      {
        label: t('params.config.paramDesc', '参数描述'),
        prop: 'paramDesc',
        minWidth: 170,
      },
      {
        label: t('params.config.paramCategoryDesc', '参数分类'),
        prop: 'paramCategoryDesc',
        minWidth: 110,
      },
      {
        label: t('params.config.valueTypeDesc', '值类型'),
        prop: 'valueTypeDesc',
        minWidth: 90,
      },
      {
        label: t('params.config.paramMultiValueFlag', '是否多值'),
        prop: 'paramMultiValueFlag',
        minWidth: 80,
        render: (row: { paramMultiValueFlag: PARAM_MULTI_VALUE_FLAG }) =>
          row.paramMultiValueFlag === PARAM_MULTI_VALUE_FLAG.YES
            ? t('global:yes')
            : t('global:no'),
      },
      {
        label: t('params.config.detailDesc', '详细描述'),
        prop: 'detailDesc',
        minWidth: 130,
      },
      {
        label: t('params.config.paramInfluenceScopeDesc', '影响范围'),
        prop: 'paramInfluenceScopeDesc',
        minWidth: 100,
      },
      {
        label: t('params.config.paramValue', '配置值'),
        prop: 'paramValue',
        minWidth: 150,
        render: (row: {
          paramValue: string;
          paramValueDesc: string;
          valueTypeCode: string;
        }) => {
          if (row.paramValueDesc) {
            return row.paramValueDesc;
          }
          if (row.valueTypeCode === PARAMSETTING_DEFAULT_TYPE.SWTICH) {
            return (
              <el-switch
                modelValue={row.paramValue}
                inline-prompt
                active-value={FLAG_STR.YES}
                inactive-value={FLAG_STR.NO}
                disabled
              />
            );
          }
          return row.paramValue;
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 70,
        fixed: 'right',
        render: (row: { paramNo: string }) => {
          return (
            <el-button
              onClick={(e: { preventDefault: () => void }) => {
                e.preventDefault();
                toggleEdit(row);
              }}
              type="primary"
              link
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

export function useTableColumnConfig(
  paramInfluenceScopeValueIds: Ref<
    { orgId: string; orgName: string }[],
    { orgId: string; orgName: string }[]
  >,
  hospitalList: { orgId: string; orgName: string }[],
  settingList: Ref<SettingInfoRow[], SettingInfoRow[]>,
  tableRef: Ref<
    | {
        formRef: FormInstance;
      }
    | undefined,
    | {
        formRef: FormInstance;
      }
    | undefined
  >,
  formRef: Ref<
    | {
        ref: FormInstance;
        model: ParamSetting.ReqAddParam;
      }
    | undefined,
    | {
        ref: FormInstance;
        model: ParamSetting.ReqAddParam;
      }
    | undefined
  >,
  isCloudEnv: boolean | undefined,
  rangList: Ref<{ value: string; label: string }[]>,
) {
  const dataSet = useFetchDataset([CodeSystemType.PARAM_INFLUENCE_SCOPE_CODE]);

  function renderDefaultValue(
    flag: PARAMSETTING_DEFAULT_TYPE,
    item: { value: unknown },
    validatorKey: string,
  ) {
    const data = useConfigFormValue(flag, rangList, () => {
      window.setTimeout(() => {
        tableRef?.value?.formRef?.validateField(validatorKey);
      });
    });
    return (
      <ProForm
        column={1}
        style={{
          width: 'calc(100% - 3rem)',
        }}
        v-model={item}
        class="mr-2"
        data={data.value}
      />
    );
  }

  function getConfigValue(value: string) {
    if (
      formRef?.value?.model.valueTypeCode ===
      PARAMSETTING_DEFAULT_TYPE.CODESYSTEM
    ) {
      const findObj = rangList.value.find((item) => item.value === value) || {
        label: '',
      };
      return findObj.label || value;
    }
    if (
      formRef?.value?.model.valueTypeCode === PARAMSETTING_DEFAULT_TYPE.SWTICH
    ) {
      return (
        <el-switch
          modelValue={value}
          inline-prompt
          active-value={FLAG_STR.YES}
          inactive-value={FLAG_STR.NO}
          disabled
        />
      );
    }
    return value;
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 120,
        render: (_row: SettingInfoRow, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('paramSetting.table.paramInfluenceScopeCode', '影响范围'),
        prop: 'paramInfluenceScopeCode',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'paramSetting.table.paramInfluenceScopeCode',
                '影响范围',
              ),
            }),
            trigger: 'change',
          },
        ],
        editable: true,
        render: (row: SettingInfoRow) => {
          const data =
            dataSet?.value?.[CodeSystemType.PARAM_INFLUENCE_SCOPE_CODE] || [];
          const found = data.find(
            (item) => item.dataValueNo === row.paramInfluenceScopeCode,
          ) || { dataValueCnName: '--' };
          return (
            <>
              {row.editable &&
              row.paramInfluenceScopeCode !==
                PARAM_INFLUENCE_SCOPE_CODE.GENERAL &&
              isCloudEnv ? (
                <el-select
                  v-model={row.paramInfluenceScopeCode}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'paramSetting.table.paramInfluenceScopeCode',
                      '影响范围',
                    ),
                  })}
                >
                  {data
                    .filter((item) => {
                      return item.dataValueNo !== '1';
                    })
                    .map((item) => {
                      return (
                        <el-option
                          key={item.dataValueNo}
                          label={item.dataValueCnName}
                          value={item.dataValueNo}
                        />
                      );
                    })}
                </el-select>
              ) : (
                <div class={'w-full'}>{found.dataValueCnName}</div>
              )}
            </>
          );
        },
      },
      {
        label: t('paramSetting.table.paramInfluenceScopeValueId', '影响范围值'),
        prop: 'paramInfluenceScopeValueId',
        render: (row: SettingInfoRow, index: number) => {
          if (
            row.paramInfluenceScopeCode === PARAM_INFLUENCE_SCOPE_CODE.GENERAL
          ) {
            return '--';
          }
          const found = hospitalList.find(
            (item: { orgId: string }) =>
              item.orgId === row.paramInfluenceScopeValueId,
          ) || { orgName: '--' };
          return (
            <>
              {row.editable ? (
                <el-form-item
                  style={{ marginBottom: '0' }}
                  class="overflow-hidden"
                  prop={`tableData.${index}.paramInfluenceScopeValueId`}
                  rules={[
                    {
                      trigger: 'change',
                      validator: async (
                        rule: string,
                        value: string,
                        callback: (arg0?: string | undefined) => void,
                      ) => {
                        const list = settingList.value.filter(
                          (item) =>
                            item.paramInfluenceScopeValueId &&
                            item.paramInfluenceScopeValueId ===
                              row.paramInfluenceScopeValueId,
                        );
                        if (
                          row.paramInfluenceScopeCode ===
                            PARAM_INFLUENCE_SCOPE_CODE.HOSPITAL &&
                          !row.paramInfluenceScopeValueId
                        ) {
                          return callback('');
                        }
                        if (list.length <= 1) {
                          callback();
                        } else {
                          ElMessage.warning(
                            t(
                              'paramSetting.message.error.hospitalRepeat',
                              '影响范围值存在重复的医院',
                            ),
                          );
                          callback('');
                        }
                      },
                    },
                  ]}
                >
                  <el-select
                    v-model={row.paramInfluenceScopeValueId}
                    filterable
                  >
                    {paramInfluenceScopeValueIds?.value.map((item) => (
                      <el-option
                        key={item.orgId}
                        label={item.orgName}
                        value={item.orgId}
                      />
                    ))}
                  </el-select>
                </el-form-item>
              ) : (
                found.orgName
              )}
            </>
          );
        },
      },
      {
        label: t('paramSetting.table.paramValues', '配置值'),
        prop: 'paramValues',
        render: (row: SettingInfoRow, index: number) => {
          return (
            <el-scrollbar class="w-full">
              <div class="max-h-52">
                {row.paramValues.map((item, i) => {
                  return (
                    <div class={i + 1 === row.paramValues.length ? '' : 'mb-4'}>
                      {row.editable ? (
                        <>
                          <el-form-item
                            style={{ marginBottom: '0' }}
                            class="overflow-hidden"
                            prop={`tableData.${index}.${i}.paramValues`}
                            rules={[
                              {
                                trigger: 'change',
                                validator: async (
                                  rule: string,
                                  value: string,
                                  callback: (arg0?: unknown) => void,
                                ) => {
                                  if (
                                    item.value ||
                                    ((item.value as number) === 0 &&
                                      formRef?.value?.model.valueTypeCode ===
                                        PARAMSETTING_DEFAULT_TYPE.NUMBER)
                                  ) {
                                    callback();
                                  } else {
                                    callback('');
                                  }
                                },
                              },
                            ]}
                          >
                            {renderDefaultValue(
                              formRef?.value?.model
                                .valueTypeCode as PARAMSETTING_DEFAULT_TYPE,
                              item,
                              `tableData.${index}.${i}.paramValues`,
                            )}
                            {formRef?.value?.model.paramMultiValueFlag ===
                              PARAM_MULTI_VALUE_FLAG.YES && (
                              <>
                                {i === 0 && (
                                  <el-icon
                                    onClick={() => {
                                      row.paramValues.unshift({
                                        value:
                                          formRef?.value?.model
                                            .valueTypeCode ===
                                          PARAMSETTING_DEFAULT_TYPE.SWTICH
                                            ? FLAG_STR.NO
                                            : '',
                                      });
                                    }}
                                    class="mr-2 cursor-pointer align-middle"
                                  >
                                    <Plus />
                                  </el-icon>
                                )}
                                {row.paramValues.length > 1 && (
                                  <el-icon
                                    onClick={() => {
                                      row.paramValues = row.paramValues.filter(
                                        (item, key) => key !== i,
                                      );
                                    }}
                                    class="cursor-pointer align-middle"
                                  >
                                    <SemiSelect />
                                  </el-icon>
                                )}
                              </>
                            )}
                          </el-form-item>
                        </>
                      ) : (
                        <>{getConfigValue(item.value as string)}</>
                      )}
                    </div>
                  );
                })}
              </div>
            </el-scrollbar>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'action',
        minWidth: 180,
        render: (row: SettingInfoRow, index: number) => {
          const found = hospitalList.find(
            (item: { orgId: string }) =>
              item.orgId === row.paramInfluenceScopeValueId,
          );
          return (
            <>
              {!row.editable && (
                <el-button
                  type={'primary'}
                  disabled={
                    row.paramInfluenceScopeCode ===
                      PARAM_INFLUENCE_SCOPE_CODE.HOSPITAL && !found
                  }
                  onClick={() => {
                    row.editable = true;
                    row.oldParam = {
                      ...row,
                      paramValues: row?.paramValues.map(
                        (item: { value: unknown }) => ({
                          value: item.value,
                        }),
                      ),
                    };
                  }}
                  link
                >
                  {t('global:edit')}
                </el-button>
              )}
              {!row.editable && (
                <el-button
                  disabled={
                    row.paramInfluenceScopeCode ===
                      PARAM_INFLUENCE_SCOPE_CODE.GENERAL ||
                    (row.paramInfluenceScopeCode ===
                      PARAM_INFLUENCE_SCOPE_CODE.HOSPITAL &&
                      !found)
                  }
                  onClick={() => {
                    settingList.value = settingList.value.filter(
                      (item, i: number) => i !== index,
                    );
                  }}
                  type={'primary'}
                  link
                >
                  {t('global:delete')}
                </el-button>
              )}

              {row.editable && (
                <el-button
                  onClick={async () => {
                    Promise.all(
                      [
                        `tableData.${index}.paramInfluenceScopeCode`,
                        `tableData.${index}.paramInfluenceScopeValueId`,
                        ...row.paramValues.map(
                          (o, i) => `tableData.${index}.${i}.paramValues`,
                        ),
                      ].map((item) =>
                        tableRef?.value?.formRef?.validateField(item),
                      ),
                    ).then(() => {
                      row.editable = false;
                      row.isAdd = false;
                      row.oldParam = null;
                      settingList.value = settingList.value.map((item) => {
                        if (
                          item.editable === true &&
                          item.paramInfluenceScopeValueId ===
                            row.paramInfluenceScopeValueId
                        ) {
                          return {
                            ...item,
                            paramInfluenceScopeValueId: '',
                          };
                        }
                        return item;
                      });
                    });
                  }}
                  type={'primary'}
                  link
                >
                  {t('global:confirm')}
                </el-button>
              )}
              {row.editable && (
                <el-button
                  onClick={() => {
                    if (row.isAdd) {
                      settingList.value = settingList.value.filter(
                        (item, i: number) => i !== index,
                      );
                    } else {
                      Object.keys(row.oldParam as SettingInfoRow).map(
                        (key: string) => {
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-expect-error
                          row[key] = (row?.oldParam || {})[key];
                        },
                      );
                      row.editable = false;
                    }
                  }}
                  disabled={
                    row.paramInfluenceScopeCode ===
                      PARAM_INFLUENCE_SCOPE_CODE.GENERAL && !row.oldParam
                  }
                  type={'primary'}
                  link
                >
                  {t('global:cancel')}
                </el-button>
              )}
            </>
          );
        },
      },
    ],
  });
}
