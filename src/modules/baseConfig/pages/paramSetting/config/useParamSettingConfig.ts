import {
  PARAM_CATEGORY_CODE,
  VALUE_TYPE_CODE,
  PARAMSETTING_DEFAULT_TYPE,
} from '@/utils/constant';
import { useFormConfig } from 'sun-biz';
import { PARAM_MULTI_VALUE_FLAG } from '../constant.tsx';
import { ref, Ref } from 'vue';
import { queryCodeSystemListByExample } from '@/modules/baseConfig/api/code';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
type SearchModel = {
  paramMultiValueFlag: string;
  paramCategoryCode: string;
  valueTypeCode: string;
  codeSystemNo?: string;
  codeSystemName?: string;
  paramId?: string;
  paramNo: string;
};

export function useParamInfoConfig(
  isAdd: boolean,
  isCloudEnv: boolean | undefined,
  searchModel: Ref<SearchModel, SearchModel>,
) {
  const dataSetCodes = <const>[PARAM_CATEGORY_CODE, VALUE_TYPE_CODE];
  const codeSystemList = ref<{ value: string; label: string }[]>([]);
  const querySystemList = async (keyWord?: string) => {
    const [, result] = await queryCodeSystemListByExample({
      pageNumber: 1,
      pageSize: 20,
      keyWord: keyWord,
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (result?.success) {
      codeSystemList.value = (result?.data || []).map((item) => ({
        value: item.codeSystemNo,
        label: item.codeSystemName,
      }));
    }
  };

  const data = useFormConfig<typeof dataSetCodes, ['configInfo']>({
    dataSetCodes,
    getData: (t, dataSet) => {
      return {
        configInfo: [
          {
            label: t('paramsConfig.paramCategoryCode', '参数分类'),
            name: 'paramCategoryCode',
            component: 'select',
            placeholder: t('global:placeholder.select'),
            extraProps: {
              options: dataSet?.value ? dataSet.value[PARAM_CATEGORY_CODE] : [],
              disabled: !isCloudEnv,
              props: {
                valueKey: 'dataValueId',
                label: 'dataValueNameDisplay',
              },
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select'),
                trigger: 'change',
              },
            ],
            triggerModelChange: true,
          },
          {
            label: t('paramsConfig.paramNo', '参数编码'),
            name: 'paramNo',
            component: 'input',
            placeholder: t('global:placeholder.input', {
              content: t('employee.code', '参数编码'),
            }),
            rules: [
              {
                required: true,
                message: t('global:placeholder.input', {
                  content: t('employee.code', '参数编码'),
                }),
                trigger: 'change',
              },
            ],
            extraProps: {
              disabled: !isAdd || !isCloudEnv,
            },
          },
          {
            label: t('paramsConfig.valueTypeCode', '参数值类型'),
            name: 'valueTypeCode',
            component: 'select',
            placeholder: t('global:placeholder.select'),
            extraProps: {
              options: dataSet?.value ? dataSet.value[VALUE_TYPE_CODE] : [],
              disabled: !isCloudEnv,
              props: {
                valueKey: 'dataValueId',
                label: 'dataValueNameDisplay',
              },
            },
            triggerModelChange: true,
            rules: [
              {
                required: true,
                message: t('global:placeholder.select'),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('paramsConfig.paramMultiValueFlag', '多值标志'),
            name: 'paramMultiValueFlag',
            component: 'select',
            placeholder: t('global:placeholder.select'),
            extraProps: {
              options: [
                {
                  value: PARAM_MULTI_VALUE_FLAG.YES,
                  label: t('global:yes'),
                },
                {
                  value: PARAM_MULTI_VALUE_FLAG.NO,
                  label: t('global:no'),
                },
              ],
              disabled: !isCloudEnv,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select'),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('paramsConfig.paramDesc', '参数描述'),
            name: 'paramDesc',
            component: 'input',
            placeholder: t('global:placeholder.input', {
              content: t('paramsConfig.paramDesc', '参数描述'),
            }),
            rules: [
              {
                required: true,
                message: t('global:placeholder.input', {
                  content: t('paramsConfig.paramDesc', '参数描述'),
                }),
                trigger: 'change',
              },
            ],
            extraProps: {
              disabled: !isCloudEnv,
            },
          },
          {
            label: t('formControl.codeSystemNo', '编码体系'),
            name: 'codeSystemNo',
            component: 'select',
            editable: true,
            isHidden:
              searchModel.value.valueTypeCode !==
              PARAMSETTING_DEFAULT_TYPE.CODESYSTEM,
            placeholder: t('global:placeholder.select.template', {
              name: t('formControl.codeSystemNo', '编码体系'),
            }),
            triggerModelChange: true,
            extraProps: {
              disabled: !isCloudEnv,
              options: codeSystemList.value,
              remote: true,
              filterable: true,
              remoteShowSuffix: true,
              remoteMethod: querySystemList,
            },
          },
          {
            label: t('paramsConfig.detailDesc', '详细描述'),
            name: 'detailDesc',
            component: 'input',
            type: 'textarea',
            isFullWidth: true,
            placeholder: t('global:placeholder.input'),
            extraProps: {
              resize: 'none',
              disabled: !isCloudEnv,
            },
          },
        ],
      };
    },
  });
  return {
    data,
    querySystemList,
  };
}
