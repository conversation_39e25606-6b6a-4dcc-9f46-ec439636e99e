import { PARAM_CATEGORY_CODE } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';

/**

/**
 * 使用搜索表单配置的自定义钩子
 *
 * 该钩子用于获取和配置搜索表单的数据和结构它主要依赖于部门数据的获取，
 * 并生成包含所属医院、所属科室、职工类型和关键字搜索等字段的表单配置
 *
 * @returns {Object} 返回一个包含表单配置数据的对象
 */
export function useSearchFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [PARAM_CATEGORY_CODE],
    getData: (t, data) => [
      {
        label: t('config.paramCategoryCode', '参数分类'),
        name: 'paramCategoryCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('config.paramCategoryCode', '参数分类'),
        }),
        triggerModelChange: true,
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[PARAM_CATEGORY_CODE] : [],
          style: { width: '170px' },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        formItemProps: {
          style: 'margin-right: 0;',
        },
        extraProps: {
          prefixIcon: 'Search',
        },
      },
    ],
  });
  return data;
}
