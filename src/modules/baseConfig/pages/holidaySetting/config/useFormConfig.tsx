import { useFormConfig } from 'sun-biz';

export function useHolidaySettingFormConfig() {
  const currentYear = new Date().getFullYear();
  const yearList = Array.from({ length: 11 }, (_, i) => {
    const year = currentYear - 5 + i;
    return { label: year, value: year };
  });
  return useFormConfig({
    getData: (t) => [
      {
        name: 'holidayYear',
        label: t('holidaySetting.form.holidayYear', '年份'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('holidaySetting.form.holidayYear', '年份'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-80',
          filterable: true,
          clearable: true,
          options: yearList,
        },
      },
    ],
  });
}
