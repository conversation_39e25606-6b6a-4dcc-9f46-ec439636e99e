<script lang="ts" name="holidaySetting" setup>
  import { ProForm, Title, useFetchDataset } from 'sun-biz';
  import { onMounted, ref } from 'vue';
  import { useHolidaySettingFormConfig } from './config/useFormConfig.tsx';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    addHoliday,
    deleteHoliday,
    editHoliday,
    queryHolidayByExample,
  } from '@/modules/baseConfig/api/holidaySetting.ts';
  import { CodeSystemType } from '@/typings/codeManage.ts';

  import 'bootstrap/dist/css/bootstrap.css';
  import 'bootstrap-icons/font/bootstrap-icons.css';
  import FullCalendar from '@fullcalendar/vue3';
  import multiMonthPlugin from '@fullcalendar/multimonth';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import zhCN from '@fullcalendar/core/locales/zh-cn';
  import bootstrap5Plugin from '@fullcalendar/bootstrap5';
  import type { EventClickArg } from '@fullcalendar/core';
  import { Calendar } from '@fullcalendar/core';
  import interactionPlugin from '@fullcalendar/interaction';

  const dataSetList = useFetchDataset([
    CodeSystemType.HOLIDAY_CODE,
    CodeSystemType.HOLIDAY_TYPE_CODE,
  ]);
  console.log(dataSetList, 'dataSetList');

  const { t } = useTranslation();

  let searchParams = ref<HolidaySetting.QueryHolidayParams>({
    holidayYear: new Date().getFullYear(),
    deleteFlag: null,
  }); // 查询参数
  let events = ref<HolidaySetting.HolidayEvent[]>([]); // 日历事件数据
  const holidaySettingList = ref<HolidaySetting.HolidayInfo[]>([]); // 表格数据
  const loading = ref(false);
  const calendarRef = ref<Calendar>();

  const handleDateSelect = (selectInfo) => {
    let title = prompt('Please enter a new title for your event');
    let calendarApi = selectInfo.view.calendar;

    calendarApi.unselect(); // clear date selection

    if (title) {
      calendarApi.addEvent({
        id: '',
        title,
        start: selectInfo.startStr,
        end: selectInfo.endStr,
        allDay: selectInfo.allDay,
      });
    }
  };
  const handleEventClick = (clickInfo) => {
    if (
      confirm(
        `Are you sure you want to delete the event '${clickInfo.event.title}'`,
      )
    ) {
      clickInfo.event.remove();
    }
  };
  // 查询联系方式列表
  const queryHolidaySettingListData = async (
    params?: HolidaySetting.QueryHolidayParams,
  ) => {
    loading.value = true;
    searchParams.value = {
      ...searchParams.value,
      ...params,
    };
    let [, res] = await queryHolidayByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.success) {
      // 清除现有事件
      // calendarRef.value?.removeAllEvents();

      // 添加新事件
      events.value = [];
      res.data.forEach((item: HolidaySetting.HolidayInfo) => {
        item.holidaySettingList?.forEach((setting) => {
          const event = {
            id: setting.holidayId,
            title: `${item.holidayCodeDesc}(${setting.holidayTypeCodeDesc})`,
            start: `${item.holidayYear}-${String(item.holidayMonth).padStart(2, '0')}-${String(setting.dayDate).padStart(2, '0')}`,
            allDay: true,
            backgroundColor:
              setting.holidayTypeCode === '1' ? '#ff4d4f' : '#52c41a',
            borderColor:
              setting.holidayTypeCode === '1' ? '#ff4d4f' : '#52c41a',
            textColor: '#fff',
          };
          events.value.push(event);
        });
      });
    }
  };

  const calendarOptions = {
    timeZone: 'Asia/Shanghai',
    locale: zhCN,
    firstDay: 0,
    themeSystem: 'bootstrap5',
    titleFormat: { year: 'numeric', month: 'long' },
    plugins: [
      multiMonthPlugin,
      dayGridPlugin,
      bootstrap5Plugin,
      interactionPlugin,
    ],
    initialView: 'multiMonthYear',
    dayMaxEvents: 3,
    moreLinkContent: '+更多',
    headerToolbar: {
      left: 'prev,today,next',
      center: 'title',
      right: 'multiMonthYear,dayGridMonth',
    },
    editable: true,
    selectable: true,
    selectMirror: true,
    weekends: true,
    showNonCurrentDates: true,
    dayCellClassNames: ['calendar-cell', 'other-month'],

    select: (selectInfo) => {
      handleDateSelect(selectInfo);
    },
    eventClick: (info: EventClickArg) => {
      handleEventClick(info);
    },
    handleWindowResize: true, //是否随浏览器窗口大小变化而自动变化。
    lazyFetching: true, //是否开启懒加载，即滚动条滚动到某一位置时才加载对应的事件。
    selectEventOverlap: false, //是否允许事件重叠。
    eventAdd: (info: {
      event: {
        id: string;
        title: string;
        start: string;
        end: string;
        allDay: boolean;
        backgroundColor: string;
        borderColor: string;
        textColor: string;
      };
    }) => {
      // 处理事件添加逻辑
      console.log('Event added:', info.event);
    },
    eventChange: (info: {
      event: {
        id: string;
        title: string;
        start: string;
        end: string;
        allDay: boolean;
        backgroundColor: string;
        borderColor: string;
        textColor: string;
      };
    }) => {
      // 处理事件更改逻辑
      console.log('Event changed:', info.event);
    },
    eventRemove: (info) => {
      // 处理事件删除逻辑
      console.log('Event removed:', info.event);
    },
    addEventSource: (source: { events: HolidaySetting.HolidayEvent[] }) => {
      source.events = events.value;
      // 添加事件源
      console.log('Event source added:', source.events);
    },
    events: events.value, // 绑定事件数据
  };

  const addNewHolidaySetting = async () => {
    const newHoliday: HolidaySetting.HolidayInfo = {
      holidayCode: '',
      holidayYear: '',
      holidayMonth: '',
      holidaySettingList: [],
      editable: true,
    };
    holidaySettingList.value.push(newHoliday);
  };
  const saveHoliday = async (row: HolidaySetting.HolidayInfo) => {
    let result: object | undefined;
    if (row.holidayId === '') {
      const [, res] = await addHoliday({ ...row });
      result = res;
    } else {
      const [, res] = await editHoliday({ ...row });
      result = res;
    }
    if (result?.success) {
      await queryHolidaySettingListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };

  const deleteHolidaySetting = async (row: HolidaySetting.HolidayInfo) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row.holidayCodeDesc,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      console.log(row);
      const [, res] = await deleteHoliday({ ...row });
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:success'),
        });
        queryHolidaySettingListData();
      }
    });
  };

  const searchConfig = useHolidaySettingFormConfig();

  onMounted(async () => {
    await queryHolidaySettingListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('holiday.title', '节假日设置')" />

    <div class="mt-3 flex justify-start">
      <ProForm
        ref="proForm"
        v-model="searchParams"
        :data="searchConfig"
        layout-mode="inline"
        @model-change="queryHolidaySettingListData"
      />

      <div class="flex gap-2">
        <el-button type="primary" @click="queryHolidaySettingListData">
          {{ $t('global.search', '搜索') }}
        </el-button>
        <el-button type="primary" @click="addNewHolidaySetting">
          {{ $t('global.add', '新增') }}
        </el-button>
        <el-button type="primary" @click="saveHoliday">
          {{ $t('global.dml', '保存') }}
        </el-button>
        <el-button type="primary" @click="deleteHolidaySetting">
          {{ $t('global.delete', 'DML') }}
        </el-button>
      </div>
    </div>

    <div class="mt-2 flex" style="height: calc(100vh - 200px)">
      <FullCalendar
        ref="calendarRef"
        :options="calendarOptions"
        style="width: 100vw"
      >
        <template #eventContent="arg">
          <b>{{ arg.timeText }}</b>
          <i>{{ arg.event.title }}</i>
        </template>
      </FullCalendar>
    </div>
  </div>
</template>

<style scoped>
  .calendar-cell {
    border: 1px solid #e0e0e0;
  }

  .other-month {
    color: #999;
    background-color: #f5f5f5;
  }

  .calendar-event {
    padding: 2px 4px;
    margin: 1px 0;
    cursor: pointer;
    border-radius: 4px;
  }

  :deep(.fc) {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  :deep(.fc-theme-standard td) {
    border: 1px solid #e0e0e0;
  }

  :deep(.fc-theme-standard th) {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
  }

  :deep(.fc-day-today) {
    background-color: #e8f4ff !important;
  }

  :deep(.fc-event) {
    color: white;
    background-color: #4a90e2;
    border-color: #4a90e2;
  }

  :deep(.fc-event:hover) {
    background-color: #357abd;
    border-color: #357abd;
  }
</style>
