<script lang="ts" name="holidaySetting" setup>
  import { ProForm, Title, useFetchDataset } from 'sun-biz';
  import { onMounted, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { dayjs } from '@sun-toolkit/shared';

  import {
    addHoliday,
    deleteHoliday,
    editHoliday,
    queryHolidayByExample,
  } from '@/modules/baseConfig/api/holidaySetting.ts';
  import { CodeSystemType } from '@/typings/codeManage.ts';
  import { useHolidaySettingFormConfig } from './config/useFormConfig.tsx';

  interface HolidaySettingInfoWithList extends HolidaySetting.HolidayInfo {
    holidaySettingList: HolidaySettingListWithId[];
    forEach?: (
      callbackfn: (
        value: HolidaySettingInfoWithList,
        index: number,
        array: HolidaySettingInfoWithList[],
      ) => void,
      thisArg?: unknown,
    ) => void;
  }

  interface HolidaySettingListWithId extends HolidaySetting.HolidaySettingList {
    holidayId: string;
  }

  interface HolidayEvent {
    holidayId: string;
    holidaySettingId: string;
    title: string;
    start: string;
    end?: string;
    allDay: boolean;
    highlight?: {
      color: string;
      fillMode: string;
    };
    dot?: {
      color: string;
    };
    bar?: {
      color: string;
    };
    textColor?: string;
  }

  interface PageAddress {
    day?: number;
    week?: number;
    month: number;
    year: number;
  }

  interface CalendarAttribute {
    key?: string;
    highlight?: boolean | object;
    dates:
      | Date
      | Date[]
      | { start: Date | string; end: Date | string }
      | string;
    dot?: { color: string } | boolean;
    popover?: { label: string; visibility?: string; customData?: unknown };
    bar?: { color: string } | boolean;
    content?: string;
    contentStyle?: {
      color?: string;
      background?: string;
      [key: string]: unknown;
    };

    [key: string]: unknown;
  }

  const dataSetList = useFetchDataset([
    CodeSystemType.HOLIDAY_CODE,
    CodeSystemType.HOLIDAY_TYPE_CODE,
  ]);
  console.log(dataSetList, 'dataSetList');

  const { t } = useTranslation();

  // 节假日类型颜色配置
  const holidayTypeColors = {
    '1': {
      highlight: {
        color: 'red', // 红色
        fillMode: 'light',
      },
      dot: {
        color: 'red',
      },
    },
    '2': {
      highlight: {
        color: 'blue', // 蓝色
        fillMode: 'light',
      },
      dot: {
        color: 'blue',
      },
    },
    default: {
      highlight: {
        color: 'gray', // 灰色
        fillMode: 'light',
      },
      dot: {
        color: 'gray',
      },
    },
  };

  let searchParams = ref<HolidaySetting.QueryHolidayParams>({
    holidayYear: dayjs().year(),
    deleteFlag: 0, // 只查询未删除的节假日
  }); // 查询参数
  let events = ref<HolidayEvent[]>([]); // 日历事件数据
  const holidaySettingList = ref<HolidaySettingInfoWithList[]>([]); // 表格数据
  const loading = ref(false);

  // 日历配置
  const calendarConfig = ref({
    isExpanded: true,
    firstDayOfWeek: 0,
    rows: 3,
    columns: 4,
    showWeekNumbers: true,
    showDayPopover: true,
    masks: {
      weekdays: 'WWW',
      title: 'MMMM', // 只显示月份
    },
    titlePosition: 'top', // 标题位置
  });

  // 选中的日期
  const selectedDate = ref('');
  const currentYear = ref(dayjs().year()); // 当前年份

  const attributesConfig = ref<CalendarAttribute[]>([]);

  const pageAddress = ref<PageAddress>({
    year: searchParams.value.holidayYear,
    month: 1, // 从1月开始
  });

  // 弹窗相关
  const dialogVisible = ref(false);
  const selectedDay = ref('');
  const isEditMode = ref(false);
  const holidayForm = ref({
    holidayId: '',
    holidaySettingId: '',
    holidayCode: '',
    holidayTypeCode: '',
    dayDate: '',
    holidayYear: '',
    holidayMonth: '',
    holidayName: '',
  });

  // 监听年份变化
  watch(
    () => searchParams.value.holidayYear,
    (newYear) => {
      if (newYear) {
        currentYear.value = newYear;
        pageAddress.value = {
          year: newYear,
          month: 1, // 从1月开始
        };
        // 重新加载数据
        queryHolidaySettingListData();
      }
    },
  );

  // 查询联系方式列表
  const queryHolidaySettingListData = async (
    params?: HolidaySetting.QueryHolidayParams,
  ) => {
    loading.value = true;
    searchParams.value = {
      ...searchParams.value,
      ...params,
    };
    let [, res] = await queryHolidayByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.success) {
      events.value = [];
      res.data.forEach((item: HolidaySettingInfoWithList) => {
        if (item.holidayYear && item.holidayMonth && item.holidaySettingList) {
          item.holidaySettingList.forEach(
            (setting: HolidaySettingListWithId) => {
              if (setting.dayDate) {
                const eventDate = dayjs()
                  .year(Number(item.holidayYear))
                  .month(Number(item.holidayMonth) - 1)
                  .date(Number(setting.dayDate));

                const colors =
                  holidayTypeColors[
                    setting.holidayTypeCode as keyof typeof holidayTypeColors
                  ] || holidayTypeColors.default;

                const event: HolidayEvent = {
                  holidayId: item.holidayId,
                  holidaySettingId: setting.holidaySettingId,
                  title: `${item.holidayCodeDesc || ''}(${setting.holidayTypeCodeDesc})`,
                  start: eventDate.format('YYYY-MM-DD'),
                  allDay: true,
                  ...colors,
                };
                events.value.push(event);
              }
            },
          );
        }
      });

      // 更新日历属性，包括事件标记
      attributesConfig.value = [
        // 添加默认的 popover 属性，使每个日期都可以显示 popover
        {
          key: 'default-popover',
          dates: {
            start: dayjs(currentYear.value).format('YYYY-01-01'),
            end: dayjs(currentYear.value).endOf('year').format('YYYY-MM-DD'),
          },
          popover: {
            label: '',
            visibility: 'click',
            customData: null,
          },
        },
        // 添加节假日事件标记
        ...events.value.map((event, index) => ({
          key: `event-${index}-${event.id}`,
          dates: event.start,
          highlight: event.highlight,
          dot: event.dot,
          popover: {
            label: event.title,
            visibility: 'click',
            customData: event,
          },
        })),
      ];

      // 确保页面从1月开始显示
      pageAddress.value = {
        year: searchParams.value.holidayYear || dayjs().year(),
        month: 1,
      };
    }
  };

  const addNewHolidaySetting = async () => {
    const newHoliday: HolidaySettingInfoWithList = {
      holidayCode: '',
      holidayYear: '',
      holidayMonth: '',
      holidaySettingList: [],
      editable: true,
    };
    holidaySettingList.value.push(newHoliday);
  };
  const saveHoliday = async (row: HolidaySettingInfoWithList) => {
    let result: object | undefined;
    if (row.holidayId === '') {
      const [, res] = await addHoliday({ ...row });
      result = res;
    } else {
      const [, res] = await editHoliday({ ...row });
      result = res;
    }
    if (result?.success) {
      await queryHolidaySettingListData();
    }
  };

  const deleteHolidaySetting = async (row: HolidaySettingInfoWithList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row.holidayCodeDesc,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteHoliday({ ...row });
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:success'),
        });
        queryHolidaySettingListData();
      }
    });
  };

  // 处理节假日事件点击
  const handleEdit = (event: HolidayEvent) => {
    if (!event) return;
    isEditMode.value = true;
    const date = dayjs(event.start);
    // 从事件标题中提取节假日名称和类型
    const titleMatch = event.title.match(/(.*?)\((.*?)\)/);
    const holidayName = titleMatch ? titleMatch[1] : '';
    const holidayTypeDesc = titleMatch ? titleMatch[2] : '';

    // 查找对应的节假日类型编码
    const holidayType = dataSetList[CodeSystemType.HOLIDAY_TYPE_CODE]?.find(
      (item) => item.dataValueNameDisplay === holidayTypeDesc,
    );

    holidayForm.value = {
      holidayId: event.holidayId,
      holidaySettingId: event.holidaySettingId,
      holidayCode: '',
      holidayTypeCode: holidayType?.dataValueId || '',
      dayDate: date.date().toString(),
      holidayYear: date.year().toString(),
      holidayMonth: (date.month() + 1).toString(),
      holidayName: holidayName,
    };
    selectedDay.value = event.start;
    dialogVisible.value = true;
  };

  // 处理添加节假日
  const handleAddHoliday = (date: string) => {
    selectedDay.value = date;
    isEditMode.value = false;
    const dayjsDate = dayjs(date);
    holidayForm.value = {
      holidayId: '',
      holidaySettingId: '',
      holidayCode: '',
      holidayTypeCode: '',
      dayDate: dayjsDate.date().toString(),
      holidayYear: dayjsDate.year().toString(),
      holidayMonth: (dayjsDate.month() + 1).toString(),
      holidayName: '',
    };
    dialogVisible.value = true;
  };

  // 处理删除节假日
  const handleDelete = async (event: HolidayEvent) => {
    if (!event) return;
    try {
      await ElMessageBox.confirm('确定要删除该节假日吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      const params = {
        holidayId: event.holidayId,
        holidaySettingId: event.holidaySettingId,
      };

      const [, res] = await deleteHoliday(params);
      if (res?.success) {
        ElMessage.success('删除成功');
        await queryHolidaySettingListData();
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除节假日失败:', error);
        ElMessage.error('删除失败');
      }
    }
  };

  // 保存节假日
  const saveHolidaySetting = async () => {
    try {
      const params = {
        holidayId: holidayForm.value.holidayId,
        holidayCode: holidayForm.value.holidayCode,
        holidayYear: holidayForm.value.holidayYear,
        holidayMonth: holidayForm.value.holidayMonth,
        holidaySettingList: [
          {
            holidaySettingId: holidayForm.value.holidaySettingId,
            dayDate: holidayForm.value.dayDate,
            holidayTypeCode: holidayForm.value.holidayTypeCode,
          },
        ],
      };

      let res;
      if (isEditMode.value) {
        [, res] = await editHoliday(params);
      } else {
        [, res] = await addHoliday(params);
      }

      if (res?.success) {
        ElMessage.success(isEditMode.value ? '修改成功' : '保存成功');
        dialogVisible.value = false;
        await queryHolidaySettingListData();
      }
    } catch (error) {
      console.error('保存节假日失败:', error);
      ElMessage.error('保存失败');
    }
  };

  const searchConfig = useHolidaySettingFormConfig();
  onMounted(async () => {
    await queryHolidaySettingListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('holiday.title', '节假日设置')" />

    <div class="mt-3 flex justify-start">
      <ProForm
        ref="proForm"
        v-model="searchParams"
        :data="searchConfig"
        layout-mode="inline"
        @model-change="queryHolidaySettingListData"
      />

      <div class="flex gap-2">
        <el-button type="primary" @click="queryHolidaySettingListData">
          {{ $t('global.search', '搜索') }}
        </el-button>
        <el-button type="primary" @click="addNewHolidaySetting">
          {{ $t('global.add', '新增') }}
        </el-button>
        <el-button type="primary" @click="saveHoliday">
          {{ $t('global.dml', '保存') }}
        </el-button>
        <el-button type="primary" @click="deleteHolidaySetting">
          {{ $t('global.delete', 'DML') }}
        </el-button>
      </div>
    </div>

    <div class="mt-2 flex flex-col" style="height: calc(100vh - 100px)">
      <div class="mb-4 text-center text-2xl font-bold">{{ currentYear }}年</div>
      <VCalendar
        v-model="pageAddress"
        :attributes="attributesConfig"
        :columns="calendarConfig.columns"
        :expanded="calendarConfig.isExpanded"
        :first-day-of-week="calendarConfig.firstDayOfWeek"
        :masks="calendarConfig.masks"
        :rows="calendarConfig.rows"
        :selected="selectedDate"
        :show-day-popover="calendarConfig.showDayPopover"
        :show-week-numbers="calendarConfig.showWeekNumbers"
        :title-position="calendarConfig.titlePosition"
      >
        <template #day-popover="{ day, attributes }">
          <div class="px-2 py-1 text-sm" style="pointer-events: auto">
            <div class="text-xs mt-1 text-gray-600">
              {{ dayjs(day.date).format('MM月DD日 星期') }}
              {{
                ['日', '一', '二', '三', '四', '五', '六'][
                  dayjs(day.date).day()
                ]
              }}
            </div>
            <div class="mt-1 space-y-2 overflow-hidden text-sm">
              <div
                v-if="
                  attributes.length > 0 && attributes[0].popover?.customData
                "
              >
                <div
                  v-for="attr in attributes"
                  :key="attr.key"
                  class="flex items-center justify-between rounded p-1 text-gray-700 hover:bg-gray-100"
                >
                  <div class="flex items-center gap-2">
                    <span
                      :style="{
                        backgroundColor:
                          attr.highlight?.base.color || '#8c8c8c',
                      }"
                      class="h-2 w-2 rounded-full"
                    ></span>
                    <span class="text-xs">{{ attr.popover?.label }}</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <el-button
                      link
                      size="small"
                      type="primary"
                      @click="handleEdit(attr.popover?.customData)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      link
                      size="small"
                      type="danger"
                      @click="handleDelete(attr.popover?.customData)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
              <el-button
                class="w-full justify-center text-sm"
                link
                type="primary"
                @click="handleAddHoliday(day.date)"
              >
                添加节假日
              </el-button>
            </div>
          </div>
        </template>
      </VCalendar>
    </div>

    <!-- 节假日设置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :title="isEditMode ? '编辑节假日' : '设置节假日'"
      width="500px"
    >
      <el-form :model="holidayForm" label-width="100px">
        <el-form-item label="当前日期">
          <el-date-picker
            v-model="selectedDay"
            format="YYYY-MM-DD"
            placeholder="选择日期"
            type="date"
            value-format="YYYY-MM-DD"
            @change="
              (val) => {
                const date = dayjs(val);
                holidayForm.dayDate = date.date().toString();
                holidayForm.holidayYear = date.year().toString();
                holidayForm.holidayMonth = (date.month() + 1).toString();
              }
            "
          />
        </el-form-item>
        <el-form-item label="节假日类型">
          <el-select
            v-model="holidayForm.holidayTypeCode"
            placeholder="请选择节假日类型"
          >
            <el-option
              v-for="item in dataSetList[CodeSystemType.HOLIDAY_TYPE_CODE]"
              :key="item.dataValueNo"
              :label="item.dataValueNameDisplay"
              :value="item.dataValueNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="节假日性质">
          <el-select
            v-model="holidayForm.holidayCode"
            placeholder="请选择节假日类型"
          >
            <el-option
              v-for="item in dataSetList[CodeSystemType.HOLIDAY_CODE]"
              :key="item.dataValueNo"
              :label="item.dataValueNameDisplay"
              :value="item.dataValueNo"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveHolidaySetting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
  .calendar-cell {
    border: 1px solid #e0e0e0;
  }

  .other-month {
    color: #999;
    background-color: #f5f5f5;
  }

  .calendar-event {
    padding: 2px 4px;
    margin: 1px 0;
    cursor: pointer;
    border-radius: 4px;
  }

  :deep(.vc-container) {
    --vc-accent-50: #f0f9ff;
    --vc-accent-100: #e0f2fe;
    --vc-accent-200: #bae6fd;
    --vc-accent-300: #7dd3fc;
    --vc-accent-400: #38bdf8;
    --vc-accent-500: #0ea5e9;
    --vc-accent-600: #0284c7;
    --vc-accent-700: #0369a1;
    --vc-accent-800: #075985;
    --vc-accent-900: #0c4a6e;

    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  :deep(.vc-day-content) {
    font-weight: 500;
  }

  :deep(.vc-day-content:hover) {
    background-color: var(--vc-accent-100);
    border-radius: 50%;
  }

  :deep(.vc-day.is-today) {
    background-color: transparent !important;
  }

  :deep(.vc-day.is-today .vc-day-content) {
    font-weight: bold;
    color: #1890ff !important;
  }

  :deep(.vc-dot) {
    width: 5px;
    height: 5px;
    border-radius: 50%;
  }

  :deep(.vc-bar) {
    height: 4px;
    border-radius: 2px;
  }

  :deep(.vc-day.is-selected) {
    background-color: transparent !important;
  }

  :deep(.vc-day.is-selected .vc-day-content) {
    font-weight: normal;
    color: inherit;
  }

  :deep(.vc-header) {
    padding: 10px 0;
    margin-bottom: 12px;
  }

  :deep(.vc-title) {
    font-size: 1.1em;
    font-weight: 500;
  }

  :deep(.vc-popover-content) {
    color: #000;
  }
  /* 只保留外部边框 */
  :deep(.vc-weeks) {
    margin: 4px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
  }

  :deep(.vc-week) {
    border-bottom: none;
  }

  :deep(.vc-day) {
    border-right: none;
  }
</style>
