<script setup lang="ts" name="TimedTaskDialog">
  import { ref, computed, Ref, nextTick } from 'vue';
  import {
    ProForm,
    ProDialog,
    Title,
    ProTable,
    useFetchDataset,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { CodeSystemType } from '@/typings/codeManage';
  import { useTranslation } from 'i18next-vue';
  import type { FormInstance } from 'element-sun';
  import { TRIGGER_PERIOD_CODE } from '../constant';
  import { FLAG } from '@/utils/constant';
  import { UserReqItem } from '@/api/types';
  import { useGetUserInfo } from '@/hooks/useGetUserList';
  import {
    addTimedTask,
    updateTimedTaskById,
    addTimedTaskInstance,
    updateTimedTaskInstanceById,
    queryTimedTaskExecLogById,
    execTimedTaskOnlyOnceById,
  } from '@modules/baseConfig/api/timedTask';
  import {
    useTimedTaskUpsertFormConfig,
    useTimedTaskInstanceUpsertFormConfig,
    useTimedTaskExecLogSearchFormConfig,
  } from '../config/useFormConfig';
  import {
    useTimedTaskParamTableConfig,
    useTimedTaskExecLogTableConfig,
  } from '../config/useTableConfig';
  import { useApiTableConfig } from '../config/useApiTableConfig';
  import { useApiList } from '../hooks/useApiList';

  type OptionItem = { label?: string; value: string };
  type TriggerPeriodMap = {
    [key: string]: {
      triggerPeriodCode: string;
      triggerMainTime: string;
      triggerSubTime: string;
    };
  };

  const formRef = ref<{
    ref: FormInstance;
    model:
      | TimedTask.TimedTaskInfo
      | TimedTask.TimedTaskInstanceListItem
      | TimedTask.ExecLogQueryParams;
  }>();
  const mode = ref();
  const dialogRef = ref();
  const { t } = useTranslation();
  const dialogTitle = ref('');
  const confirmLoading = ref(false);
  const timedTaskForm = ref<TimedTask.TimedTaskInfo>();
  const timedTaskInstanceForm = ref<TimedTask.TimedTaskInstanceListItem>();
  const timedTaskParamTableRef = ref();
  const paramList = ref<TimedTask.TimedTaskParamListItem[]>([]);
  const selectApi = ref<CodeRepositoryManageAPI.ApiList>();
  const emits = defineEmits<{ success: [] }>();

  const minuteTriggerMainTimeSelections = ref<OptionItem[]>([]);
  const hourTriggerMainTimeSelections = ref<OptionItem[]>([]);
  const weekHourTriggerMainTimeSelections = ref<OptionItem[]>([]);
  const monthHourTriggerMainTimeSelections = ref<OptionItem[]>([]);

  const { userList, getUserList } = useGetUserInfo();

  const timedTaskExecLogSearchForm = ref<TimedTask.ExecLogQueryParams>({
    hospitalId: '',
    execBeginAt: '',
    execEndAt: '',
    pageNumber: 1,
    pageSize: 25,
  });
  const timedTaskExecLogTableLoading = ref(false);
  const timedTaskExecLogTotal = ref(0);
  const timedTaskExecLogList = ref<TimedTask.TimedTaskExecLogInfo[]>([]);
  const timedTaskExecLogTableRef = ref();
  const timeEnd = ref('00:30');

  // 定时任务触发规则map
  const triggerPeriodMap = ref<TriggerPeriodMap>();

  const dataSetList = useFetchDataset([CodeSystemType.TRIGGER_PERIOD_CODE]);
  const triggerPeriodCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.TRIGGER_PERIOD_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const selectLabel = computed(() => {
    return (
      (selectApi.value?.apiNo ?? '--') +
      ' | ' +
      (selectApi.value?.apiName ?? '--') +
      ' | ' +
      (selectApi.value?.apiCategoryName ?? '--') +
      ' | ' +
      (selectApi.value?.codeRepositoryName ?? '--')
    );
  });

  const { hospitalList, userInfo } = useAppConfigData([
    MAIN_APP_CONFIG.HOSPITAL_LIST,
    MAIN_APP_CONFIG.USER_INFO,
  ]);

  const { loading: apiLoading, apiList, queryApiList } = useApiList();

  const queryTimedTaskExecLogData = async (
    data?: TimedTask.ExecLogQueryParams,
  ) => {
    timedTaskExecLogTableLoading.value = true;
    if (data) {
      timedTaskExecLogSearchForm.value = {
        ...timedTaskExecLogSearchForm.value,
        ...data,
      };
    }
    const params = {
      ...timedTaskExecLogSearchForm.value,
      timedTaskId: timedTaskForm.value!.timedTaskId,
      timedTaskInstanceId: timedTaskInstanceForm.value!.timedTaskInstanceId,
      execBeginAt: timedTaskExecLogSearchForm.value.execAt?.[0],
      execEndAt: timedTaskExecLogSearchForm.value.execAt?.[1],
      execAt: undefined,
    };
    const [, res] = await queryTimedTaskExecLogById(params);
    timedTaskExecLogTableLoading.value = false;
    if (res?.success) {
      timedTaskExecLogList.value = res.data;
      timedTaskExecLogTotal.value = res.total;
    }
  };

  const onExecute = async () => {
    try {
      confirmLoading.value = true;
      const [, res] = await execTimedTaskOnlyOnceById({
        timedTaskInstanceId: timedTaskInstanceForm.value!.timedTaskInstanceId,
        timedTaskParamList: paramList.value,
      });
      if (res?.success) {
        dialogRef.value?.close();
        emits('success');
      }
    } finally {
      confirmLoading.value = false;
    }
  };

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          let request;
          switch (mode.value) {
            case 'addTimedTask':
              request = addTimedTask({
                ...timedTaskForm.value,
                ...formRef?.value?.model,
                timedTaskParamList: paramList.value,
              });
              break;
            case 'editTimedTask':
              request = updateTimedTaskById({
                ...timedTaskForm.value,
                ...formRef?.value?.model,
                timedTaskParamList: paramList.value,
              });
              break;
            case 'addTimedTaskInstance':
              request = addTimedTaskInstance({
                timedTaskId: timedTaskForm.value?.timedTaskId,
                ...timedTaskInstanceForm.value,
                ...formRef?.value?.model,
                ...triggerPeriodMap.value?.[
                  timedTaskInstanceForm.value!.triggerPeriodCode
                ],
                timedTaskParamList: paramList.value,
              });
              break;
            case 'editTimedTaskInstance':
              request = updateTimedTaskInstanceById({
                timedTaskId: timedTaskForm.value?.timedTaskId,
                ...timedTaskInstanceForm.value,
                ...formRef?.value?.model,
                ...triggerPeriodMap.value?.[
                  timedTaskInstanceForm.value!.triggerPeriodCode
                ],
                timedTaskParamList: paramList.value,
              });
              break;
            default:
              break;
          }
          if (!request) {
            reject(['', new Error('参数错误')]);
            return;
          }
          const [, res] = await request;
          if (res?.success) {
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const editTimedTask = async (timedTask?: TimedTask.TimedTaskInfo) => {
    await queryApiList();

    const obj = (apiList.value ?? []).find(
      (item) => item.apiId === timedTask?.apiId,
    );
    if (obj) {
      selectApi.value = obj;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (timedTaskForm.value as any)!.apiIdRow = obj;
      setTimeout(() => {
        tableSelectRef.value?.setCurrentRow(obj);
      }, 100);
    }
  };

  const initDetail = (
    dialogMode: string,
    timedTask?: TimedTask.TimedTaskInfo,
    taskInstance?: TimedTask.TimedTaskInstanceListItem,
  ) => {
    mode.value = dialogMode;
    formRef?.value?.ref?.resetFields();
    timedTaskExecLogSearchForm.value = {
      hospitalId: '',
      execBeginAt: '',
      execEndAt: '',
      pageNumber: 1,
      pageSize: 25,
    };
    timedTaskForm.value = (timedTask || {
      timedTaskName: '',
      allowMultiInstanceFlag: FLAG.NO,
    }) as TimedTask.TimedTaskInfo;
    timedTaskInstanceForm.value = (taskInstance || {
      triggerPeriodCode: TRIGGER_PERIOD_CODE.MINUTE,
      bindingUserId: userInfo?.userId,
      bindingUserName: userInfo?.userName,
    }) as TimedTask.TimedTaskInstanceListItem;
    if (
      timedTaskInstanceForm.value.bindingUserId &&
      !userList.value.some(
        (item) => item.userId === timedTaskInstanceForm.value!.bindingUserId,
      )
    ) {
      userList.value.push({
        userId: timedTaskInstanceForm.value!.bindingUserId,
        userName: timedTaskInstanceForm.value!.bindingUserName,
      } as unknown as UserReqItem);
    }

    paramList.value = [];
    if (!minuteTriggerMainTimeSelections.value?.length) {
      for (let i = 1; i <= 60; i++) {
        minuteTriggerMainTimeSelections.value.push({
          value: String(i),
          label: String(i),
        });
        if (i <= 31) {
          monthHourTriggerMainTimeSelections.value.push({
            value: String(i),
            label: String(i),
          });
          if (i <= 24) {
            hourTriggerMainTimeSelections.value.push({
              value: String(i),
              label: String(i),
            });
            if (i <= 7) {
              weekHourTriggerMainTimeSelections.value.push({
                value: String(i),
              });
            }
          }
        }
      }
      [
        t('timedTaskDialog.triggerPeriod.sun', '日'),
        t('timedTaskDialog.triggerPeriod.mon', '一'),
        t('timedTaskDialog.triggerPeriod.tue', '二'),
        t('timedTaskDialog.triggerPeriod.wed', '三'),
        t('timedTaskDialog.triggerPeriod.thu', '四'),
        t('timedTaskDialog.triggerPeriod.fri', '五'),
        t('timedTaskDialog.triggerPeriod.sat', '六'),
      ].forEach((label, index) => {
        weekHourTriggerMainTimeSelections.value[index].label = label;
      });
    }

    if (dialogMode === 'editTimedTask' || dialogMode === 'addTimedTask') {
      editTimedTask(timedTask);
    }
    switch (dialogMode) {
      case 'addTimedTask':
        dialogTitle.value = `${t('global:add')}${t('timedTask.name', '定时任务')}`;
        paramList.value = timedTask?.timedTaskParamList || [];
        break;
      case 'editTimedTask':
        dialogTitle.value = `${t('global:edit')}${t('timedTask.name', '定时任务')}`;
        paramList.value = timedTask?.timedTaskParamList || [];
        break;
      case 'addTimedTaskInstance': {
        dialogTitle.value = `${t('global:add')}${t('taskInstance.name', '实例')}（${timedTaskForm.value.timedTaskName}）`;
        paramList.value =
          timedTask!.timedTaskParamList?.length > 0
            ? timedTask!.timedTaskParamList.map((item) => ({
                ...item,
                paramValue: item.paramValue || item.defaultValue,
                editable: true,
              }))
            : [];
        const map: TriggerPeriodMap = {};
        triggerPeriodCodeList.value?.forEach((item) => {
          map[item?.value] = {
            triggerPeriodCode: item?.value,
            triggerMainTime: '',
            triggerSubTime: '00:00',
          };
        });
        triggerPeriodMap.value = map;
        break;
      }
      case 'editTimedTaskInstance': {
        dialogTitle.value = `${t('global:edit')}${t('taskInstance.name', '实例')}（${timedTaskForm.value.timedTaskName}）`;
        paramList.value =
          taskInstance!.timedTaskParamList?.length > 0
            ? taskInstance!.timedTaskParamList.map((item) => ({
                ...item,
                editable: true,
              }))
            : [];
        const map: TriggerPeriodMap = {};
        triggerPeriodCodeList.value?.forEach((item) => {
          map[item?.value] = {
            triggerPeriodCode: item?.value,
            triggerMainTime:
              item?.value === timedTaskInstanceForm.value!.triggerPeriodCode
                ? timedTaskInstanceForm.value!.triggerMainTime || ''
                : '',
            triggerSubTime:
              item?.value === timedTaskInstanceForm.value!.triggerPeriodCode
                ? timedTaskInstanceForm.value!.triggerSubTime || ''
                : '00:00',
          };
        });
        triggerPeriodMap.value = map;
        break;
      }
      case 'execTimedTaskOnlyOnce':
        dialogTitle.value = `${t('taskInstance.execTimedTaskOnlyOnce', '单次执行')}（${timedTaskForm.value.timedTaskName}-${taskInstance!.hospitalName}）`;
        paramList.value =
          taskInstance!.timedTaskParamList?.length > 0
            ? taskInstance!.timedTaskParamList.map((item) => ({
                ...item,
                paramValue: item.paramValue || item.defaultValue,
                editable: true,
              }))
            : [];
        break;
      case 'timedTaskExecLog':
        dialogTitle.value = t('taskInstance.timedTaskExecLog', '执行记录');
        timedTaskExecLogSearchForm.value.hospitalId = taskInstance?.hospitalId;
        queryTimedTaskExecLogData();
        break;
      default:
        dialogTitle.value = '';
        break;
    }
  };

  const handleOpen = (
    dialogMode: string,
    timedTask?: TimedTask.TimedTaskInfo,
    taskInstance?: TimedTask.TimedTaskInstanceListItem,
  ) => {
    nextTick(() => {
      initDetail(dialogMode, timedTask, taskInstance);
    });
    dialogRef.value.open();
  };

  const handleDialogOpened = () => {
    timeEnd.value = '23:59';
  };

  const handleDialogClosed = () => {
    timeEnd.value = '00:30';
  };

  const handleClose = () => {
    dialogRef.value.close();
    mode.value = '';
  };

  const changeRow = (row: CodeRepositoryManageAPI.ApiList) => {
    selectApi.value = row;
    (timedTaskForm.value as TimedTask.TimedTaskInfo).apiId =
      row?.apiId as string;
  };

  const handleTimedTaskInstanceFormChange = (
    formData: TimedTask.TimedTaskInstanceListItem,
    name?: string,
  ) => {
    switch (name) {
      case 'hospitalId':
        if (formData?.hospitalId) {
          nextTick(() => {
            getUserList({
              pageSize: 100,
              pageNumber: 1,
              hospitalId: formData.hospitalId || '',
            });
            if (!formData.timedTaskInstanceDesc) {
              const hospital = hospitalList?.find(
                (item: Org.Item) => item.orgId === formData.hospitalId,
              );
              formData.timedTaskInstanceDesc = `${hospital?.orgName || ''}-${timedTaskForm.value?.timedTaskName || ''}`;
            }
          });
        }
        break;
      default:
        break;
    }
  };

  const apiTableConfig = useApiTableConfig();

  const { data: timedTaskFormConfig, tableSelectRef } =
    useTimedTaskUpsertFormConfig({
      selectLabel,
      tableColumns: apiTableConfig,
      apiList,
      apiLoading,
      changeRow,
      queryApiList,
    });
  const timedTaskInstanceFormConfig = useTimedTaskInstanceUpsertFormConfig(
    timedTaskInstanceForm,
    userList,
    getUserList,
  );
  const timedTaskExecLogSearchFormConfig = useTimedTaskExecLogSearchFormConfig(
    queryTimedTaskExecLogData,
  );
  const { timedTaskParamTableConfig, addItem } = useTimedTaskParamTableConfig(
    timedTaskParamTableRef,
    paramList as Ref<
      (TimedTask.TimedTaskParamListItem & {
        editable: boolean;
      })[]
    >,
    mode,
  );
  const timedTaskExecLogTableColumn = useTimedTaskExecLogTableConfig();

  const handleAddParam = () => {
    addItem({
      mustInputFlag: 1,
      editable: true,
    } as unknown as TimedTask.TimedTaskParamListItem & {
      editable: boolean;
    });
  };

  defineExpose({ dialogRef, open: handleOpen });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    destroy-on-close
    :title="dialogTitle"
    :align-center="true"
    :confirm-fn="onConfirm"
    :width="mode === 'timedTaskExecLog' ? 1280 : 1080"
    :include-footer="
      !['execTimedTaskOnlyOnce', 'timedTaskExecLog'].includes(mode)
    "
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @opened="handleDialogOpened"
    @closed="handleDialogClosed"
    @success="emits('success')"
  >
    <div>
      <ProForm
        v-if="['addTimedTask', 'editTimedTask'].includes(mode)"
        ref="formRef"
        v-model="timedTaskForm"
        :column="4"
        :data="timedTaskFormConfig"
      />
      <ProForm
        v-else-if="
          ['addTimedTaskInstance', 'editTimedTaskInstance'].includes(mode)
        "
        ref="formRef"
        v-model="timedTaskInstanceForm"
        :data="timedTaskInstanceFormConfig"
        :column="2"
        @model-change="handleTimedTaskInstanceFormChange"
      />
      <ProForm
        v-else-if="mode === 'timedTaskExecLog'"
        ref="formRef"
        v-model="timedTaskExecLogSearchForm"
        layout-mode="inline"
        :data="timedTaskExecLogSearchFormConfig"
        @model-change="queryTimedTaskExecLogData"
      />
      <div
        v-if="
          [
            'addTimedTask',
            'editTimedTask',
            'addTimedTaskInstance',
            'editTimedTaskInstance',
            'execTimedTaskOnlyOnce',
          ].includes(mode)
        "
        class="mb-4"
      >
        <div class="mb-2 flex items-center justify-between">
          <Title
            :title="$t('timedTaskDialog.ParamList.title', '定时任务参数')"
          />
          <el-button
            v-if="['addTimedTask', 'editTimedTask'].includes(mode)"
            type="primary"
            @click="handleAddParam"
          >
            {{ $t('global:add') }}
          </el-button>
        </div>
        <ProTable
          ref="timedTaskParamTableRef"
          :editable="true"
          :data="paramList"
          :columns="timedTaskParamTableConfig"
        />
      </div>
      <div
        v-if="
          ['addTimedTaskInstance', 'editTimedTaskInstance'].includes(mode) &&
          timedTaskInstanceForm &&
          triggerPeriodCodeList?.length > 0 &&
          triggerPeriodMap
        "
        class="mb-4"
      >
        <Title
          :title="$t('timedTaskDialog.triggerPeriod.title', '定时任务触发规则')"
        />
        <el-radio-group
          v-model="timedTaskInstanceForm.triggerPeriodCode"
          class="mt-4"
        >
          <el-radio
            v-for="item in triggerPeriodCodeList"
            :key="item.value"
            :value="item.value"
            class="mb-4 w-full"
          >
            <div
              v-if="item.value === TRIGGER_PERIOD_CODE.MINUTE"
              class="flex items-center"
            >
              <span>{{ $t('timedTaskDialog.interval', '间隔') }}</span>
              <el-select
                v-if="minuteTriggerMainTimeSelections.length > 0"
                v-model="triggerPeriodMap[item.value].triggerMainTime"
                class="mx-2 w-40"
              >
                <el-option
                  v-for="minute in minuteTriggerMainTimeSelections"
                  :key="`minute-option-${minute.value}`"
                  :label="minute.label"
                  :value="minute.value"
                />
              </el-select>
              <span>
                {{ $t('timedTaskDialog.triggerPeriod.minute', '分钟') }}
              </span>
              <span>
                {{ `${$t('timedTaskDialog.execute', '执行')}，` }}
              </span>
              <span>
                {{
                  $t(
                    'timedTaskDialog.triggerPeriod.triggerSubTime',
                    '第一次执行时间',
                  )
                }}
              </span>
              <el-time-select
                v-model="triggerPeriodMap[item.value].triggerSubTime"
                class="mx-2 w-40"
                start="00:00"
                step="00:01"
                :end="timeEnd"
              />
            </div>
            <div
              v-else-if="item.value === TRIGGER_PERIOD_CODE.HOUR"
              class="flex items-center"
            >
              <span>{{ $t('timedTaskDialog.interval', '间隔') }}</span>
              <el-select
                v-model="triggerPeriodMap[item.value].triggerMainTime"
                class="mx-2 w-40"
              >
                <el-option
                  v-for="hour in hourTriggerMainTimeSelections"
                  :key="`hour-option-${hour.value}`"
                  :label="hour.label"
                  :value="hour.value"
                />
              </el-select>
              <span>
                {{ $t('timedTaskDialog.triggerPeriod.hour', '小时') }}
              </span>
              <span>
                {{ `${$t('timedTaskDialog.execute', '执行')}，` }}
              </span>
              <span>
                {{
                  $t(
                    'timedTaskDialog.triggerPeriod.triggerSubTime',
                    '第一次执行时间',
                  )
                }}
              </span>
              <el-time-select
                v-model="triggerPeriodMap[item.value].triggerSubTime"
                class="mx-2 w-40"
                start="00:00"
                step="00:01"
                :end="timeEnd"
              />
            </div>
            <div
              v-else-if="item.value === TRIGGER_PERIOD_CODE.DAY_HOUR"
              class="flex items-center"
            >
              <span>
                {{ $t('timedTaskDialog.triggerPeriod.dayHour', '每天') }}
              </span>
              <el-time-select
                v-model="triggerPeriodMap[item.value].triggerSubTime"
                class="mx-2 w-40"
                start="00:00"
                step="00:01"
                :end="timeEnd"
              />
              <span>
                {{ `${$t('timedTaskDialog.execute', '执行')}` }}
              </span>
            </div>
            <div
              v-else-if="item.value === TRIGGER_PERIOD_CODE.WEEK_HOUR"
              class="flex items-center"
            >
              <span>
                {{ $t('timedTaskDialog.triggerPeriod.weekHour', '每周') }}
              </span>
              <el-select
                v-model="triggerPeriodMap[item.value].triggerMainTime"
                class="mx-2 w-40"
              >
                <el-option
                  v-for="weekHour in weekHourTriggerMainTimeSelections"
                  :key="`weekHour-option-${weekHour.value}`"
                  :label="weekHour.label"
                  :value="weekHour.value"
                />
              </el-select>
              <el-time-select
                v-model="triggerPeriodMap[item.value].triggerSubTime"
                class="mr-2 w-40"
                start="00:00"
                step="00:01"
                :end="timeEnd"
              />
              <span>
                {{ `${$t('timedTaskDialog.execute', '执行')}` }}
              </span>
            </div>
            <div
              v-else-if="item.value === TRIGGER_PERIOD_CODE.MONTH_HOUR"
              class="flex items-center"
            >
              <span>
                {{ $t('timedTaskDialog.triggerPeriod.monthHour', '每月') }}
              </span>
              <el-select
                v-model="triggerPeriodMap[item.value].triggerMainTime"
                class="mx-2 w-40"
              >
                <el-option
                  v-for="monthHour in monthHourTriggerMainTimeSelections"
                  :key="`monthHour-option-${monthHour.value}`"
                  :label="monthHour.label"
                  :value="monthHour.value"
                />
              </el-select>
              <el-time-select
                v-model="triggerPeriodMap[item.value].triggerSubTime"
                class="mr-2 w-40"
                start="00:00"
                step="00:01"
                :end="timeEnd"
              />
              <span>
                {{ `${$t('timedTaskDialog.execute', '执行')}` }}
              </span>
            </div>
            <span v-else-if="item.value === TRIGGER_PERIOD_CODE.NONE">
              {{ $t('timedTaskDialog.triggerPeriod.none', '不自动触发') }}
            </span>
          </el-radio>
        </el-radio-group>
      </div>
      <ProTable
        v-else-if="mode === 'timedTaskExecLog'"
        ref="timedTaskExecLogTableRef"
        row-key="timedTaskExecLogId"
        :loading="timedTaskExecLogTableLoading"
        :data="timedTaskExecLogList"
        :columns="timedTaskExecLogTableColumn"
        :pagination="true"
        :page-info="{
          total: timedTaskExecLogTotal,
          pageNumber: timedTaskExecLogSearchForm.pageNumber,
          pageSize: timedTaskExecLogSearchForm.pageSize,
        }"
        :max-height="400"
        layout="prev, pager, next, sizes, jumper"
        @current-page-change="
          (val: number) => {
            queryTimedTaskExecLogData({
              pageNumber: val,
            });
          }
        "
        @size-page-change="
          (val: number) => {
            queryTimedTaskExecLogData({
              pageSize: val,
            });
          }
        "
      />
      <div v-if="mode === 'execTimedTaskOnlyOnce'" class="mt-4 text-right">
        <el-button @click="handleClose">{{ $t('global:cancel') }}</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="onExecute">
          {{ $t('timedTaskDialog.execute', '执行') }}
        </el-button>
      </div>
      <div v-else-if="mode === 'timedTaskExecLog'" class="mt-4 text-right">
        <el-button type="primary" @click="handleClose">
          {{ $t('global:close') }}
        </el-button>
      </div>
    </div>
  </ProDialog>
</template>
