<script setup lang="ts" name="TimedTaskInstanceTable">
  import { ref, watch } from 'vue';
  import { ProTable } from 'sun-biz';
  import { ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { useTimedTaskInstanceTableConfig } from '../config/useTableConfig';
  import { updateTimedTaskInstanceEnabledFlagById } from '@modules/baseConfig/api/timedTask';

  const props = defineProps<{
    timedTask: TimedTask.TimedTaskInfo;
  }>();
  const { t } = useTranslation();
  const timedTaskInstanceList = ref<TimedTask.TimedTaskInstanceListItem[]>([]);
  const timedTaskInstanceTableRef = ref();
  const emits = defineEmits(['openDialog']);

  watch(
    () => props.timedTask,
    () => {
      timedTaskInstanceList.value =
        props.timedTask?.timedTaskInstanceList || [];
    },
    {
      deep: true,
      immediate: true,
    },
  );

  /** 启用状态切换 */
  async function handleEnableSwitch(row: TimedTask.TimedTaskInstanceListItem) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: `${props.timedTask.timedTaskName}-${row.hospitalName}`,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        timedTaskInstanceId: row.timedTaskInstanceId,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateTimedTaskInstanceEnabledFlagById(params);
      if (res?.success) {
        row.enabledFlag = params.enabledFlag;
      }
    });
  }

  const onOpenTimedTaskDialog = (
    mode: string,
    taskInstance?: TimedTask.TimedTaskInstanceListItem,
  ) => {
    emits('openDialog', mode, props.timedTask, taskInstance);
  };

  const tableColumnsConfig = useTimedTaskInstanceTableConfig(
    onOpenTimedTaskDialog,
    handleEnableSwitch,
  );
</script>
<template>
  <div v-if="props.timedTask" class="p-box">
    <ProTable
      ref="timedTaskInstanceTableRef"
      row-key="timedTaskInstanceId"
      :data="timedTaskInstanceList"
      :columns="tableColumnsConfig"
    />
  </div>
</template>
