/**
 * 定时任务触发周期代码
 */
export enum TRIGGER_PERIOD_CODE {
  // 按分自动触发
  MINUTE = 'minute',
  // 按时自动触发
  HOUR = 'hour',
  // 按日自动触发
  DAY_HOUR = 'dayHour',
  // 按周自动触发
  WEEK_HOUR = 'weekHour',
  // 按月自动触发
  MONTH_HOUR = 'monthHour',
  // 不自动触发
  NONE = 'none',
}

export const TRIGGER_PERIOD_CODE_MAP = {
  [TRIGGER_PERIOD_CODE.MINUTE]: TRIGGER_PERIOD_CODE.MINUTE,
  [TRIGGER_PERIOD_CODE.HOUR]: TRIGGER_PERIOD_CODE.HOUR,
  [TRIGGER_PERIOD_CODE.DAY_HOUR]: TRIGGER_PERIOD_CODE.DAY_HOUR,
  [TRIGGER_PERIOD_CODE.WEEK_HOUR]: TRIGGER_PERIOD_CODE.WEEK_HOUR,
  [TRIGGER_PERIOD_CODE.MONTH_HOUR]: TRIGGER_PERIOD_CODE.MONTH_HOUR,
  [TRIGGER_PERIOD_CODE.NONE]: TRIGGER_PERIOD_CODE.NONE,
};
