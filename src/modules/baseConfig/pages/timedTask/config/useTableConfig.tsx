import { Ref } from 'vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { ENABLED_FLAG, FLAG } from '@/utils/constant';
import TimedTaskInstanceTable from '@/modules/baseConfig/pages/timedTask/components/TimedTaskInstanceTable.vue';

export function useTimedTaskTableConfig(
  isCloudEnv: boolean | undefined,
  onOpenTimedTaskDialog: (
    mode: string,
    timedTask?: TimedTask.TimedTaskInfo,
    taskInstance?: TimedTask.TimedTaskInstanceListItem,
  ) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        type: 'expand',
        prop: 'timedTaskInstanceList',
        render: (row: TimedTask.TimedTaskInfo) =>
          row?.timedTaskId ? (
            <TimedTaskInstanceTable
              timedTask={row}
              onOpenDialog={(
                dialogMode: string,
                dialogTimedTask?: TimedTask.TimedTaskInfo,
                dialogTaskInstance?: TimedTask.TimedTaskInstanceListItem,
              ) =>
                onOpenTimedTaskDialog(
                  dialogMode,
                  dialogTimedTask,
                  dialogTaskInstance,
                )
              }
            />
          ) : (
            <span></span>
          ),
      },
      {
        label: t('timedTask.timedTaskTable.timedTaskName', '定时任务'),
        prop: 'timedTaskName',
        minWidth: 350,
        align: 'left',
      },
      {
        label: t(
          'timedTask.timedTaskTable.allowMultiInstanceFlag',
          '允许多实例',
        ),
        prop: 'allowMultiInstanceFlag',
        minWidth: 220,
        align: 'left',
        render: (row: TimedTask.TimedTaskInfo) => {
          return (
            <>
              {row.allowMultiInstanceFlag === FLAG.YES
                ? t('global:yes')
                : t('global:no')}
            </>
          );
        },
      },
      {
        label: t('timedTask.timedTaskTable.apiName', '对应API'),
        prop: 'apiName',
        minWidth: 350,
        align: 'left',
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 320,
        render: (row: TimedTask.TimedTaskInfo) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                disabled={!isCloudEnv}
                onClick={() => onOpenTimedTaskDialog('editTimedTask', row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() =>
                  onOpenTimedTaskDialog('addTimedTaskInstance', row)
                }
              >
                {t('timedTask.timedTaskTable.addTimedTaskInstance', '新增实例')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenTimedTaskDialog('timedTaskExecLog', row)}
              >
                {t('timedTask.timedTaskTable.openTimedTaskExecLog', '执行记录')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}

type TimedTaskParamTableRow = TimedTask.TimedTaskParamListItem & {
  editable: boolean;
};

export function useTimedTaskParamTableConfig(
  tableRef: Ref<TableRef>,
  data: Ref<TimedTaskParamTableRow[]>,
  mode: Ref<string>,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'timedTaskParamId',
  });

  const onItemDelete = (index: number) => {
    delItem(index);
  };

  const onItemConfirm = (data: TimedTaskParamTableRow) => {
    toggleEdit(data);
  };

  const timedTaskParamTableConfig = useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:sequenceNumber', '序号'),
          minWidth: 60,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('timedTask.timedTaskParamTable.paramKey', '关键字'),
          prop: 'paramKey',
          minWidth: 120,
          editable: ['addTimedTask', 'editTimedTask'].includes(mode.value),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('timedTask.timedTaskParamTable.paramKey', '关键字'),
              }),
              trigger: ['blur', 'change'],
            },
          ],
          render: (row: TimedTaskParamTableRow) => {
            return (
              <div class={'w-full'}>
                {row.editable &&
                ['addTimedTask', 'editTimedTask'].includes(mode.value) ? (
                  <el-input
                    v-model={row.paramKey}
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'timedTask.timedTaskParamTable.paramKey',
                        '关键字',
                      ),
                    })}
                  ></el-input>
                ) : (
                  <>{row.paramKey}</>
                )}
              </div>
            );
          },
        },
        {
          label: t('timedTask.timedTaskParamTable.paramDesc', '描述'),
          prop: 'paramDesc',
          minWidth: 120,
          editable: ['addTimedTask', 'editTimedTask'].includes(mode.value),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('timedTask.timedTaskParamTable.paramDesc', '描述'),
              }),
              trigger: ['blur', 'change'],
            },
          ],
          render: (row: TimedTaskParamTableRow) => {
            return (
              <div class={'w-full'}>
                {row.editable &&
                ['addTimedTask', 'editTimedTask'].includes(mode.value) ? (
                  <el-input
                    v-model={row.paramDesc}
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'timedTask.timedTaskParamTable.paramDesc',
                        '描述',
                      ),
                    })}
                  ></el-input>
                ) : (
                  <>{row.paramDesc}</>
                )}
              </div>
            );
          },
        },
        {
          label: t('timedTask.timedTaskParamTable.mustInputFlag', '必填'),
          prop: 'mustInputFlag',
          minWidth: 120,
          editable: ['addTimedTask', 'editTimedTask'].includes(mode.value),
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('timedTask.timedTaskParamTable.mustInputFlag', '必填'),
              }),
              trigger: ['blur', 'change'],
            },
          ],
          render: (row: TimedTaskParamTableRow) => {
            return (
              <div class={'w-full'}>
                {row.editable &&
                ['addTimedTask', 'editTimedTask'].includes(mode.value) ? (
                  <el-select
                    v-model={row.mustInputFlag}
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'timedTask.timedTaskParamTable.mustInputFlag',
                        '必填',
                      ),
                    })}
                  >
                    <el-option
                      key={ENABLED_FLAG.YES}
                      label={t('global:yes')}
                      value={ENABLED_FLAG.YES}
                    />
                    <el-option
                      key={ENABLED_FLAG.NO}
                      label={t('global:no')}
                      value={ENABLED_FLAG.NO}
                    />
                  </el-select>
                ) : (
                  <>
                    {row.mustInputFlag === ENABLED_FLAG.YES
                      ? t('global:yes')
                      : t('global:no')}
                  </>
                )}
              </div>
            );
          },
        },
        {
          label: t('timedTask.timedTaskParamTable.defaultValue', '默认值'),
          prop: 'defaultValue',
          minWidth: 100,
          editable: true,
          render: (row: TimedTaskParamTableRow) => {
            return (
              <div class={'w-full'}>
                {row.editable ? (
                  <el-input
                    v-model={row.defaultValue}
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'timedTask.timedTaskParamTable.defaultValue',
                        '默认值',
                      ),
                    })}
                  />
                ) : (
                  <>{row.defaultValue}</>
                )}
              </div>
            );
          },
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          minWidth: 100,
          render: (row: TimedTaskParamTableRow, $index: number) => {
            return (
              <>
                {row.editable ? (
                  <div class={'flex justify-around'}>
                    <el-button
                      type="danger"
                      link={true}
                      onClick={() => cancelEdit(row, $index, false)}
                    >
                      {t('global:cancel')}
                    </el-button>
                    <el-button
                      type="primary"
                      link={true}
                      onClick={() => onItemConfirm(row)}
                    >
                      {t('global:confirm')}
                    </el-button>
                  </div>
                ) : (
                  <div class={'flex justify-around'}>
                    <el-button
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:edit')}
                    </el-button>
                    <el-button
                      type="danger"
                      link={true}
                      onClick={() => onItemDelete($index)}
                    >
                      {t('global:delete')}
                    </el-button>
                  </div>
                )}
              </>
            );
          },
        },
      ];
      if (
        [
          'addTimedTaskInstance',
          'editTimedTaskInstance',
          'execTimedTaskOnlyOnce',
        ].includes(mode.value)
      ) {
        data.splice(4, 2, {
          label: t('timedTask.timedTaskParamTable.paramValue', '配置值'),
          prop: 'paramValue',
          minWidth: 100,
          editable: true,
          render: (row: TimedTaskParamTableRow) => {
            return (
              <div class={'w-full'}>
                {row.editable ? (
                  <el-input
                    v-model={row.paramValue}
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'timedTask.timedTaskParamTable.paramValue',
                        '配置值',
                      ),
                    })}
                  />
                ) : (
                  <>{row.paramValue}</>
                )}
              </div>
            );
          },
        });
      }
      return data;
    },
  });
  return {
    timedTaskParamTableConfig,
    addItem,
    delItem,
  };
}

export function useTimedTaskInstanceTableConfig(
  onOpenTimedTaskDialog: (
    mode: string,
    data?: TimedTask.TimedTaskInstanceListItem,
  ) => void,
  handleEnableSwitch: (data: TimedTask.TimedTaskInstanceListItem) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        prop: 'hospitalName',
        minWidth: 150,
      },
      {
        label: t(
          'timedTask.timedTaskInstanceTable.timedTaskInstanceDesc',
          '定时任务实例描述',
        ),
        prop: 'timedTaskInstanceDesc',
        minWidth: 150,
      },
      {
        label: t(
          'timedTask.timedTaskInstanceTable.triggerPeriodDesc',
          '触发周期',
        ),
        prop: 'triggerPeriodDesc',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskInstanceTable.cron', '调度表达式'),
        prop: 'cron',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskInstanceTable.cronDesc', '表达式描述'),
        prop: 'cronDesc',
        minWidth: 150,
      },
      {
        label: t(
          'timedTask.timedTaskInstanceTable.firstExceAt',
          '首次执行时间',
        ),
        prop: 'firstExceAt',
        minWidth: 170,
      },
      {
        label: t('timedTask.timedTaskInstanceTable.execAt', '最近执行时间'),
        prop: 'execAt',
        minWidth: 170,
      },
      {
        label: t('timedTask.timedTaskInstanceTable.nextExecAt', '下次执行时间'),
        prop: 'nextExecAt',
        minWidth: 170,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: TimedTask.TimedTaskInstanceListItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 320,
        fixed: 'right',
        render: (row: TimedTask.TimedTaskInstanceListItem) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() =>
                  onOpenTimedTaskDialog('editTimedTaskInstance', row)
                }
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() =>
                  onOpenTimedTaskDialog('execTimedTaskOnlyOnce', row)
                }
              >
                {t(
                  'timedTask.timedTaskInstanceTable.execTimedTaskOnlyOnce',
                  '执行一次',
                )}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenTimedTaskDialog('timedTaskExecLog', row)}
              >
                {t(
                  'timedTask.timedTaskInstanceTable.openTimedTaskExecLog',
                  '执行记录',
                )}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}

export function useTimedTaskExecLogTableConfig() {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber', '序号'),
        prop: 'indexNo',
        render: (row: TimedTask.TimedTaskExecLogInfo, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('person.belongHospital', '所属医院'),
        prop: 'hospitalName',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskExecLogTable.execAt', '执行时间'),
        prop: 'execAt',
        minWidth: 170,
      },
      {
        label: t(
          'timedTask.timedTaskExecLogTable.timedTaskParam',
          '定时任务参数',
        ),
        prop: 'timedTaskParam',
        minWidth: 180,
      },
      {
        label: t('timedTask.timedTaskExecLogTable.completeAt', '完成时间'),
        prop: 'completeAt',
        minWidth: 170,
      },
      {
        label: t('timedTask.timedTaskExecLogTable.successFlag', '执行结果'),
        prop: 'successFlag',
        minWidth: 100,
        render: (row: TimedTask.TimedTaskExecLogInfo) => (
          <span class={row.successFlag ? '' : 'text-red-500'}>
            {row.successFlag ? t('global:success') : t('global:fail')}
          </span>
        ),
      },
      {
        label: t(
          'timedTask.timedTaskExecLogTable.bizInterfaceInParam',
          '业务入参',
        ),
        prop: 'bizInterfaceInParam',
        minWidth: 180,
      },
      {
        label: t(
          'timedTask.timedTaskExecLogTable.bizInterfaceOutParam',
          '业务出参',
        ),
        prop: 'bizInterfaceOutParam',
        minWidth: 180,
      },
    ],
  });
}
