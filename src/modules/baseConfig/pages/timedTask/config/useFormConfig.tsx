import { ref, Ref, ComputedRef } from 'vue';
import { useFormConfig, ColumnProps } from 'sun-biz';
import { FLAG } from '@/utils/constant';
import { UserReqItem, UserReqParams } from '@/api/types';
import HospitalSelect from '@/modules/project/components/HospitalSelect/index.vue';
import TableSelect from '@/components/table-select/index.vue';
export function useTimedTaskSearchFormConfig(
  queryTimedTaskData: (data?: TimedTask.QueryParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          clearable: true,
          className: 'w-52',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryTimedTaskData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryTimedTaskData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useTimedTaskInstanceUpsertFormConfig(
  timedTaskInstanceForm: Ref<TimedTask.TimedTaskInstanceListItem | undefined>,
  userList: Ref<UserReqItem[]>,
  getUserList: (params: UserReqParams) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        formItemProps: { 'label-width': '75px' },
        extraProps: {
          clearable: false,
          className: 'w-52',
          // disabled: mode.value === 'editTimedTaskInstance',
        },
      },
      {
        label: t(
          'timedTask.timedTaskInstanceForm.timedTaskInstanceDesc',
          '定时任务实例描述',
        ),
        name: 'timedTaskInstanceDesc',
        formItemProps: { 'label-width': '135px' },
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t(
            'timedTask.timedTaskInstanceForm.timedTaskInstanceDesc',
            '定时任务实例描述',
          ),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'timedTask.timedTaskInstanceForm.timedTaskInstanceDesc',
                '定时任务实例描述',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'bindingUserId',
        label: t('timedTask.timedTaskInstanceForm.bindingUserId', '绑定用户'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('timedTask.timedTaskInstanceForm.bindingUserId', '绑定用户'),
        }),
        formItemProps: { 'label-width': '80px' },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'timedTask.timedTaskInstanceForm.bindingUserId',
                '绑定用户',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          className: 'w-52',
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          options: userList.value,
          remoteMethod: (keyWord: string) => {
            getUserList({
              keyWord: keyWord,
              pageSize: 100,
              pageNumber: 1,
              hospitalId: timedTaskInstanceForm?.value?.hospitalId || '',
            });
          },
          props: {
            label: 'userName',
            value: 'userId',
          },
        },
      },
    ],
  });
  return data;
}

export function useTimedTaskUpsertFormConfig(options: {
  selectLabel: ComputedRef<string>;
  tableColumns: Ref<ColumnProps[]>;
  apiList: Ref<CodeRepositoryManageAPI.ApiList[]>;
  apiLoading: Ref<boolean>;
  changeRow: (row: CodeRepositoryManageAPI.ApiList) => void;
  queryApiList: (params?: CodeRepositoryManageAPI.QueryParams) => Promise<void>;
}) {
  const {
    selectLabel,
    tableColumns,
    apiList,
    apiLoading,
    changeRow,
    queryApiList,
  } = options;

  const tableSelectRef = ref();

  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('timedTask.timedTaskForm.timedTaskName', '定时任务'),
        name: 'timedTaskName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('timedTask.form.timedTaskName', '名称'),
        }),
        span: 3,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('timedTask.form.timedTaskName', '名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'allowMultiInstanceFlag',
        component: 'checkbox',
        extraProps: {
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
          label: t(
            'timedTask.timedTaskForm.allowMultiInstanceFlag',
            '允许多实例',
          ),
        },
      },
      {
        label: t('timedTask.timedTaskForm.apiId', '对应API'),
        name: 'apiIdRow',
        placeholder: t('global:placeholder.select.template', {
          name: t('timedTask.timedTaskForm.apiId', '对应API'),
        }),
        triggerModelChange: true,
        isFullWidth: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('timedTask.timedTaskForm.apiId', '对应API'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: () => {
          return (
            <TableSelect
              ref={tableSelectRef}
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              clearable={false}
              filterable={true}
              remote={true}
              remote-method={(keyWord: string) => queryApiList({ keyWord })}
              selectLabel={selectLabel.value}
              rowKey="apiId"
              columns={tableColumns.value}
              data={apiList.value}
              loading={apiLoading.value}
              placeholder={t('global:placeholder.select.template', {
                name: t('timedTask.timedTaskForm.apiId', '对应API'),
              })}
              onChange={(row: CodeRepositoryManageAPI.ApiList) =>
                changeRow(row)
              }
            ></TableSelect>
          );
        },
      },
    ],
  });
  return { data, tableSelectRef };
}

export function useTimedTaskExecLogSearchFormConfig(
  queryTimedTaskExecLogData: (data?: TimedTask.ExecLogQueryParams) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        // triggerModelChange: true,
        // component: 'hospitalSelect',
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
        render: () => (
          <HospitalSelect
            automaticallySetValue={false}
            onChange={(val: string) =>
              queryTimedTaskExecLogData({ hospitalId: val })
            }
          />
        ),
      },
      {
        label: t('timedTask.timedTaskExecLogSearch.execAt', '执行时间'),
        name: 'execAt',
        component: 'datePicker',
        placeholder: t('global:placeholder.select.template', {
          name: t('timedTask.timedTaskExecLogSearch.execAt', '执行时间'),
        }),
        triggerModelChange: true,
        extraProps: {
          type: 'datetimerange',
          'start-placeholder': t('global:startDate'),
          'end-placeholder': t('global:endDate'),
          format: 'YYYY-MM-DD HH:mm:ss',
          'range-separator': t('global:rangeSeparator'),
          'value-format': 'YYYY-MM-DD HH:mm:ss',
          className: 'w-80',
        },
      },
    ],
  });
}
