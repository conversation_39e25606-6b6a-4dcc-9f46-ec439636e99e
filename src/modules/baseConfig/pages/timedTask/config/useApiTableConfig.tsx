import { useColumnConfig } from 'sun-biz';

export function useApiTableConfig() {
  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('timedTask.timedTaskTable.apiNo', 'API编号'),
        prop: 'apiNo',
        minWidth: 100,
      },
      {
        label: t('timedTask.timedTaskTable.apiName', 'API名称'),
        prop: 'apiName',
        minWidth: 200,
      },
      {
        label: t('timedTask.timedTaskTable.apiCategoryName', 'API类别名称'),
        prop: 'apiCategoryName',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskTable.codeRepositoryName', '代码仓库名称'),
        prop: 'codeRepositoryName',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskTable.className', '类名'),
        prop: 'className',
        minWidth: 150,
      },
      {
        label: t('timedTask.timedTaskTable.methodName', '方法名'),
        prop: 'methodName',
        minWidth: 150,
      },
    ],
  });
  return data;
}
