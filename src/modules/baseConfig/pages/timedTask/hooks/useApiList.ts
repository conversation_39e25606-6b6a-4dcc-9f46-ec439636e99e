import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { queryApiByExample } from '@/modules/project/api/codeRepositoryManageAPI';

// 打印接口相关hooks
export function useApiList() {
  const loading = ref(false);
  const apiList = ref<CodeRepositoryManageAPI.ApiList[]>([]);
  const queryApiList = async (params?: CodeRepositoryManageAPI.QueryParams) => {
    loading.value = true;
    const defaultParams = {
      enabledFlag: FLAG.YES,
      deletedFlag: FLAG.NO,
      pageNumber: 1,
      pageSize: ONE_PAGE_SIZE,
    };

    const [, res] = await queryApiByExample({
      ...defaultParams,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      apiList.value = res.data ?? [];
    }
  };
  return {
    loading,
    apiList,
    queryApiList,
  };
}
