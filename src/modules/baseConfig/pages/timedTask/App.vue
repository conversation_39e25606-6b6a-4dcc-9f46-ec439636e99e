<script setup lang="ts" name="timedTask">
  import { ref, computed } from 'vue';
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useTimedTaskTableConfig } from './config/useTableConfig';
  import { useTimedTaskSearchFormConfig } from './config/useFormConfig';
  import { queryTimedTaskByExample } from '@modules/baseConfig/api/timedTask';
  import TimedTaskDialog from '@/modules/baseConfig/pages/timedTask/components/TimedTaskDialog.vue';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<TimedTask.QueryParams>({
    hospitalId: '',
    keyWord: '',
    pageNumber: 1,
    pageSize: 25,
  });
  const loading = ref(false);
  const total = ref(0);
  const timedTaskList = ref<TimedTask.TimedTaskInfo[]>([]);
  const selections = ref<TimedTask.TimedTaskInfo[]>([]);
  const timedTaskDialogRef = ref();
  const timedTaskTableRef = ref();

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.timedTaskId || '';
    });
  });

  const queryTimedTaskData = async (data?: TimedTask.QueryParams) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryTimedTaskByExample(searchParams.value);
    loading.value = false;
    if (res?.success) {
      timedTaskList.value = res.data;
      total.value = res.total;
    }
  };

  const handleSelectChange = (value: TimedTask.TimedTaskInfo[]) => {
    selections.value = value;
  };

  const onOpenTimedTaskDialog = (
    mode: string,
    timedTask?: TimedTask.TimedTaskInfo,
    taskInstance?: TimedTask.TimedTaskInstanceListItem,
  ) => {
    timedTaskDialogRef.value.open(
      mode,
      cloneDeep(timedTask),
      cloneDeep(taskInstance),
    );
  };

  // queryTimedTaskData();
  const searchConfig = useTimedTaskSearchFormConfig(queryTimedTaskData);
  const tableColumnsConfig = useTimedTaskTableConfig(
    isCloudEnv,
    onOpenTimedTaskDialog,
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('timedTask.list.title', '定时任务列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryTimedTaskData"
        />
      </div>
      <div>
        <el-button
          class="mr-3"
          type="primary"
          :disabled="!isCloudEnv"
          @click="onOpenTimedTaskDialog('addTimedTask')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_TIMED_TASK"
          @success="
            () => {
              timedTaskTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="timedTaskTableRef"
      row-key="timedTaskId"
      :loading="loading"
      :data="timedTaskList"
      :columns="tableColumnsConfig"
      :pagination="true"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      layout="prev, pager, next, sizes, jumper, total"
      @current-page-change="
        (val: number) => {
          queryTimedTaskData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryTimedTaskData({
            pageSize: val,
          });
        }
      "
      @selection-change="handleSelectChange"
    />
    <TimedTaskDialog ref="timedTaskDialogRef" @success="queryTimedTaskData" />
  </div>
</template>
