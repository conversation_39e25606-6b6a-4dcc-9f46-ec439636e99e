<script lang="ts" name="msgSendWay" setup>
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { computed, onMounted, ref } from 'vue';
  import { useMsgSendWayFormConfig } from './config/useFormConfig.tsx';
  import { useMsgSendWayTableConfig } from './config/useTableConfig.tsx';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    editMsgSendWay,
    queryMsgSendWayList,
  } from '@/modules/baseConfig/api/msgSendWay.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import UpsertMsgSendWayDialog from './components/upsertMsgSendWayDialog.vue';

  const upsertMsgSendWayDialogRef = ref();
  const upsertMsgSendWayDialogMode = ref('');

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const msgSendWayTableRef = ref();
  const selectTableData = ref<MsgSendWay.MsgSendWayList[]>([]);

  let queryMsgSendWayListParams = ref<MsgSendWay.QueryMsgSendWayList>(); // 查询参数
  const sendWayList = ref<MsgSendWay.MsgSendWayList[]>([]); // 表格数据
  const loading = ref(false);
  const currentRow = ref<MsgSendWay.UpsertMsgSendWayParams>({});
  // 查询票据配置列表
  const queryMsgSendWayListData = async (
    params: MsgSendWay.QueryMsgSendWayList = {},
  ) => {
    loading.value = true;
    queryMsgSendWayListParams.value = {
      ...queryMsgSendWayListParams.value,
      ...params,
    };
    let [, res] = await queryMsgSendWayList({
      ...queryMsgSendWayListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      sendWayList.value = res.data || [];
    }
  };

  // 启用状态改变
  const changeSelect = async (
    data?: MsgSendWay.QueryMsgSendWayList,
    key?: string,
  ) => {
    if (key === 'enabledFlag') {
      queryMsgSendWayListParams.value = {
        ...queryMsgSendWayListParams.value,
        ...data,
      };
      await queryMsgSendWayListData();
    }
  };

  const addNewSendWay = async () => {
    currentRow.value = {
      enabledFlag: ENABLED_FLAG.YES,
      timeUnitCode: 'SECOND',
    };
    upsertMsgSendWayDialogMode.value = 'add';
    upsertMsgSendWayDialogRef.value.dialogRef.open();
  };
  const openDialog = (row = null) => {
    currentRow.value = { ...row, timeUnitCode: row.timeUnitCode || 'SECOND' };
    upsertMsgSendWayDialogMode.value = 'edit';
    upsertMsgSendWayDialogRef.value.dialogRef.open();
  };

  const handleEnableSwitch = async (row: MsgSendWay.MsgSendWayList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.msgSendWayName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params: MsgSendWay.MsgSendWayList = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editMsgSendWay(params);
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:success'),
        });
        queryMsgSendWayListData();
      }
    });
  };

  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.msgSendWayId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: PayWay.PayWayReqItem[]) => {
    selectTableData.value = val;
  };

  const searchConfig = useMsgSendWayFormConfig(queryMsgSendWayListData);
  const { tableColumns } = useMsgSendWayTableConfig({
    handleEnableSwitch,
    openDialog,
  });
  onMounted(async () => {
    await queryMsgSendWayListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('msgSendWay.list.title', '消息发送渠道')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryMsgSendWayListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button class="mr-2" type="primary" @click="queryMsgSendWayListData">
          {{ $t('msgSendWay.search', '搜索') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-2"
          type="primary"
          @click="addNewSendWay"
        >
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_MSG_SEND_WAY"
        @success="
          () => {
            msgSendWayTableRef?.proTableRef.clearSelection();
          }
        "
      />
    </div>
    <ProTable
      ref="msgSendWayTableRef"
      :columns="tableColumns"
      :data="sendWayList"
      :editable="true"
      :loading="loading"
      row-key="msgSendWayId"
      @selection-change="selectionChange"
    />
    <UpsertMsgSendWayDialog
      ref="upsertMsgSendWayDialogRef"
      :interface-list="interfaceList"
      :mode="upsertMsgSendWayDialogMode"
      :row-data="currentRow"
      @success="queryMsgSendWayListData"
    />
  </div>
</template>
