<script lang="ts" setup>
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    MAIN_APP_CONFIG,
    ProDialog,
    ProForm,
    useAppConfigData,
  } from 'sun-biz';
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { useMsgSendWayUpsertFormConfig } from '../config/useFormConfig.tsx';
  import {
    addMsgSendWay,
    editMsgSendWay,
  } from '@/modules/baseConfig/api/msgSendWay.ts';
  import { ENABLED_FLAG, FLAG } from '@/utils/constant.ts';
  import { queryInterfaceListByExample } from '@/api/common.ts';
  import { cloneDeep } from '@sun-toolkit/shared';

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const { t } = useTranslation();

  const props = defineProps<{
    mode: string;
    rowData?: MsgSendWay.UpsertMsgSendWayParams | undefined;
  }>();
  const emits = defineEmits<{ success: [] }>();
  const dialogRef = ref();
  const formRef = ref();
  const dialogForm = ref({});
  const isHidden = ref(false);

  const interfaceList = ref<{ value: string; label: string }[]>([]);
  const getInterfaceList = async () => {
    const params = {
      pageNumber: 1,
      pageSize: 9999,
      keyWord: '',
      hospitalId: currentOrg?.orgId || '',
      interfaceTypeCode: '8',
      enabledFlag: FLAG.YES,
    };
    const [, res] = await queryInterfaceListByExample(params);
    if (res) {
      let { data } = res;
      interfaceList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.interfaceId,
          label: item.interfaceName,
        }));
    }
  };
  const changedTimeUnitCode = (value: string) => {
    isHidden.value = value === '1';
    if (value === '1') {
      nextTick(() => {
        dialogForm.value = {
          ...dialogForm.value,
          timePeriod: '',
          timeUnitCode: '',
          msgCountLimit: '',
        };
      });
    } else {
      nextTick(() => {
        dialogForm.value = {
          ...dialogForm.value,
          timePeriod: '1',
          timeUnitCode: 'SECOND',
          msgCountLimit: 1,
        };
      });
    }
  };
  const formConfig = useMsgSendWayUpsertFormConfig(
    interfaceList,
    dialogForm,
    changedTimeUnitCode,
    isHidden,
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addMsgSendWay(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editMsgSendWay(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };
  const closeDialog = () => {
    dialogForm.value = {};
    isHidden.value = false;
    dialogRef.value?.close();
  };
  watch(
    () => props.rowData,
    (val) => {
      let tmp = cloneDeep(val);
      console.log(val?.msgSendLimitCode);
      if (val?.msgSendLimitCode === '1') {
        nextTick(() => {
          isHidden.value = true;
          dialogForm.value = {
            ...tmp,
            timePeriod: '',
            timeUnitCode: '',
            msgCountLimit: undefined,
          };
        });
      } else {
        nextTick(() => {
          dialogForm.value = { ...tmp };
        });
      }
    },
    { immediate: true, deep: true },
  );
  defineExpose({ dialogRef });
  onMounted(() => {
    getInterfaceList();
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :title="
      (props.mode === 'add' ? '新增' : '编辑') +
      t('msgSendWay.upsertMsgSendWayDialogTitle', '消息发送渠道')
    "
    :width="700"
    destroy-on-close
    @close="closeDialog"
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="2"
      :data="formConfig"
    />
  </ProDialog>
</template>
