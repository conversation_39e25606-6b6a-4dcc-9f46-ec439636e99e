import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';

export function useMsgSendWayTableConfig(options: {
  handleEnableSwitch: (row: MsgSendWay.UpsertMsgSendWayParams) => Promise<void>;
  openDialog: (row: MsgSendWay.MsgSendWayList) => void;
}) {
  const { handleEnableSwitch, openDialog } = options;

  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('msgSendWay.msgSendWayTable.msgSendWayName', '发送渠道名称'),
        prop: 'msgSendWayName',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.interfaceId', '消息发送接口'),
        prop: 'interfaceName',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: MsgSendWay.UpsertMsgSendWayParams) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t(
          'msgSendWay.msgSendWayTable.msgSendLimitCodeDesc',
          '消息发送限制',
        ),
        prop: 'msgSendLimitCodeDesc',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.timeUnitCodeDesc', '时间周期'),
        prop: 'timePeriod',
        minWidth: 100,
      },
      {
        label: t('msgSendWay.msgSendWayTable.timeUnitCodeDesc', '时间单位'),
        prop: 'timeUnitCodeDesc',
        minWidth: 100,
      },
      {
        label: t('msgSendWay.msgSendWayTable.msgCountLimit', '限制数量'),
        prop: 'msgCountLimit',
        minWidth: 100,
      },
      {
        label: t('msgSendWay.msgSendWayTable.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.createdAt', '创建时间'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.modifiedUserName', '最后修改人'),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('msgSendWay.msgSendWayTable.modifiedAt', '最后修改时间'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 80,
        fixed: 'right',
        render: (
          row: MsgSendWay.UpsertMsgSendWayParams & {
            editable: boolean;
          },
        ) => {
          return (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => openDialog(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}
