import { useFormConfig } from 'sun-biz';
import {
  ENABLED_FLAG,
  MSG_SEND_LIMIT_CODE,
  TIME_UNIT_CODE,
} from '@/utils/constant.ts';
import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';

export function useMsgSendWayFormConfig(
  queryMsgSendWayListData: (
    params?: MsgSendWay.QueryMsgSendWayList,
  ) => Promise<void>,
) {
  const enabledFlagList = [
    { value: ENABLED_FLAG.YES, label: '启用' },
    { value: ENABLED_FLAG.NO, label: '停用' },
  ];
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('msgSendWay.msgSendWayUpsertForm.keyword', '发送渠道名称'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-80',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryMsgSendWayListData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
      {
        label: t('global:enabledFlag', '启用标志'),
        name: 'enabledFlag',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag', '启用标志'),
        }),
        extraProps: {
          options: enabledFlagList,
          clearable: true,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}

export function useMsgSendWayUpsertFormConfig(
  interfaceList: Ref<SelectOptions[]>,
  dialogForm: Ref<MsgSendWay.UpsertMsgSendWayParams>,
  changedTimeUnitCode: (val: string) => void,
  isHidden: Ref<boolean>,
) {
  return useFormConfig({
    dataSetCodes: [TIME_UNIT_CODE, MSG_SEND_LIMIT_CODE],
    getData: (t, dataSet) => [
      {
        label: t(
          'msgSendWay.msgSendWayUpsertForm.msgSendWayName',
          '发送渠道名称',
        ),
        name: 'msgSendWayName',
        component: 'input',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dbgridColumns.msgSendWayName', '发送渠道名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        placeholder: t('global:placeholder.input.template', {
          content: t('msgSendWay.msgSendWayName', '发送渠道名称'),
        }),
      },
      {
        name: 'enabledFlag',
        label: t('msgSendWay.msgSendWayUpsertForm.enabledFlag', '是否启用'),
        component: 'switch',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dbgridColumns.enabledFlag', '是否启用'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('msgSendWay.msgSendWayUpsertForm.interfaceId', '消息发送接口'),
        name: 'interfaceId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'msgSendWay.msgSendWayUpsertForm.interfaceId',
            '消息发送接口',
          ),
        }),
        extraProps: {
          filterable: true,
          options: interfaceList.value,
          clearable: true,
        },
      },
      {
        label: t(
          'msgSendWay.msgSendWayUpsertForm.msgSendLimitCode',
          '消息限制类型',
        ),
        name: 'msgSendLimitCode',
        component: 'select',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dbgridColumns.msgSendLimitCode', '消息限制类型'),
            }),
            trigger: 'change',
          },
        ],
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'msgSendWay.msgSendWayUpsertForm.msgSendLimitCode',
            '消息限制类型',
          ),
        }),
        extraProps: {
          filterable: true,
          options: dataSet?.value ? dataSet.value[MSG_SEND_LIMIT_CODE] : [],
          clearable: false,
          onChange: (val: string) => {
            changedTimeUnitCode(val);
          },
        },
      },
      {
        label: t('msgSendWay.msgSendWayUpsertForm.timePeriod', '每'),
        name: 'timePeriod',
        isHidden: isHidden.value,
        render: () => {
          return (
            <>
              <el-input-number
                min={1}
                max={999999999}
                step={1}
                step-strictly={true}
                precision={0}
                v-model={dialogForm.value.timePeriod}
                controls-position="right"
                class="w-20"
              />
              <el-select
                class="w-20"
                v-model={dialogForm.value.timeUnitCode}
                filterable
                placeholder={t(
                  'msgSendWay.msgSendWayUpsertForm.timeUnitCode',
                  '单位',
                )}
              >
                {(dataSet?.value ? dataSet.value[TIME_UNIT_CODE] : [])?.map(
                  (item) => (
                    <el-option
                      key={item.dataValueNo}
                      label={item.dataValueNameDisplay}
                      value={item.dataValueNo}
                    />
                  ),
                )}
              </el-select>
            </>
          );
        },
      },
      {
        label: t(
          'msgSendWay.msgSendWayUpsertForm.msgCountLimit',
          '最多发送（条）',
        ),
        name: 'msgCountLimit',
        isHidden: isHidden.value,
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'msgSendWay.msgSendWayUpsertForm.msgCountLimit',
            '消息限制类型',
          ),
        }),
        render: () => {
          return (
            <>
              <el-input-number
                v-model={dialogForm.value.msgCountLimit}
                controls-position="right"
                class="w-20"
                min={1}
                max={999999999}
                step={1}
                step-strictly={true}
                precision={0}
              />
            </>
          );
        },
      },
    ],
  });
}
