<script setup lang="ts" name="ExBasicDtaDictUpsertDialog">
  import { ref, watch, Ref } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common';
  import { ENABLED_FLAG } from '@/utils/constant';
  import {
    addExBasicDtaDict,
    updateExBasicDtaDictById,
  } from '@/modules/baseConfig/api/exBasicDataDict';
  import { useExBasicDtaDictTypeEditTableConfig } from '../config/useTableConfig.tsx';
  import { useExBasicDtaDictUpsertFormConfig } from '@/modules/baseConfig/pages/exBasicDataDict/config/useFormConfig.ts';
  import { Title, ProForm, ProTable, ProDialog, TableRef } from 'sun-biz';

  type BasicDataDictTypeListItem = {
    basicDataDictTypeId?: string;
    basicDataDictTypeName?: string;
    basicDataDictTypeNo?: string;
    basicDataSourceCode?: string;
    dataDownloadStatusCode?: string;
    dataDownloadStatusMemo?: string;
    enabledFlag?: number;
    hisBasicDataTypeCode?: string;
    mappingFlag?: number;
    sort?: number;
  };
  const props = defineProps<{
    mode: string;
    interfaceList: SelectOptions[];
    data: ExBasicDataDict.ExBasicDtaDictUpsertParams;
    basicDataSourceCodeList: SelectOptions[];
    hisBasicDataTypeCodeList: SelectOptions[];
    dataDownloadStatusCodeList: SelectOptions[];
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: ExBasicDataDict.ExBasicDtaDictUpsertParams;
  }>();
  const dialogRef = ref();
  const { t } = useTranslation();
  const keyWord = ref('');
  const disabled = ref(false);
  const interfaceListData = ref<SelectOptions[]>([]);
  const exBasicDataDictForm = ref<ExBasicDataDict.ExBasicDtaDictUpsertParams>(
    {},
  );
  const basicDataDictTypeList = ref<BasicDataDictTypeListItem[]>([]);
  const dialogExBasicDataDictTypeTableRef = ref<TableRef>();
  const basicDataSourceCodeListData = ref<SelectOptions[]>([]);
  const hisBasicDataTypeCodeListData = ref<SelectOptions[]>([]);
  const dataDownloadStatusCodeListData = ref<SelectOptions[]>([]);
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props,
    () => {
      keyWord.value = '';
      disabled.value = props.mode === 'view';
      exBasicDataDictForm.value = cloneDeep(props.data);
      basicDataDictTypeList.value =
        exBasicDataDictForm.value.basicDataDictTypeList || [];
      interfaceListData.value = cloneDeep(props.interfaceList);
      basicDataSourceCodeListData.value = props.basicDataSourceCodeList;
      hisBasicDataTypeCodeListData.value = props.hisBasicDataTypeCodeList;
      dataDownloadStatusCodeListData.value = props.dataDownloadStatusCodeList;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...exBasicDataDictForm.value,
            ...formRef?.value?.model,
          };
          if (params.basicDataDictTypeList?.length) {
            const isEditing = params.basicDataDictTypeList.some(
              (item) => item.editable === true,
            );
            if (isEditing) {
              ElMessage.warning(
                t(
                  'exBasicDataDict.errorTip.toggleIsEditingWarning',
                  '存在编辑状态中的目录分类，请先确定！',
                ),
              );
              reject(['', new Error('参数错误')]);
              return;
            }
            params.basicDataDictTypeList.forEach((item, index) => {
              item.sort = index + 1;
            });
          }
          let isSuccess = false;
          if (params.basicDataDictId) {
            const [, res] = await updateExBasicDtaDictById(params);
            isSuccess = !!res?.success;
          } else {
            const [, res] = await addExBasicDtaDict(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                params.basicDataDictId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const onInputKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      const keyWord = (e.target as HTMLInputElement).value;
      if (keyWord) {
        basicDataDictTypeList.value =
          exBasicDataDictForm.value.basicDataDictTypeList!.filter(
            (item) =>
              item.basicDataDictTypeName?.includes(keyWord) ||
              item.basicDataDictTypeNo?.includes(keyWord),
          );
      } else {
        basicDataDictTypeList.value =
          exBasicDataDictForm.value.basicDataDictTypeList!;
      }
    }
  };

  const formConfig = useExBasicDtaDictUpsertFormConfig(
    interfaceListData,
    disabled,
  );
  const { exBasicDtaDictTypeTableConfig, addItem } =
    useExBasicDtaDictTypeEditTableConfig(
      dialogExBasicDataDictTypeTableRef,
      basicDataDictTypeList as Ref<
        (BasicDataDictTypeListItem & { editable: boolean })[]
      >,
      disabled,
      basicDataSourceCodeListData,
      hisBasicDataTypeCodeListData,
      dataDownloadStatusCodeListData,
    );

  const onAddClick = () => {
    addItem({
      editable: true,
      enabledFlag: ENABLED_FLAG.YES,
      mappingFlag: ENABLED_FLAG.NO,
    });
  };

  defineExpose({ dialogRef, dialogExBasicDataDictTypeTableRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :width="1150"
    destroy-on-close
    :close-on-click-modal="false"
    :confirm-fn="onConfirm"
    :title="`${$t(`global:${mode}`)}${$t('exBasicDataDict.title', '目录')}`"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <div class="p-box">
      <ProForm
        ref="formRef"
        v-model="exBasicDataDictForm"
        :column="4"
        :data="formConfig"
      />
      <Title
        :title="$t('exBasicDataDict.exBasicDtaDictTypeList.title', '目录分类')"
      />
      <div class="mb-2 flex justify-between">
        <div>
          <el-input
            v-if="mode !== 'add'"
            v-model="keyWord"
            prefix-icon="Search"
            :placeholder="$t('global:placeholder.keyword')"
            @keydown="onInputKeydown"
          />
        </div>
        <div>
          <el-button v-if="mode !== 'view'" type="primary" @click="onAddClick">
            {{ $t('global:add') }}
          </el-button>
        </div>
      </div>
      <ProTable
        ref="dialogExBasicDataDictTypeTableRef"
        row-key="basicDataDictTypeId"
        :editable="true"
        :data="basicDataDictTypeList"
        :columns="exBasicDtaDictTypeTableConfig"
      />
    </div>
  </ProDialog>
</template>
