<script lang="ts" name="exBasicDtaList" setup>
  import { ref, watch, computed, Ref, onBeforeUnmount } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { CodeSystemType } from '@/typings/codeManage';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG, FLAG } from '@/utils/constant';
  import { SelectOptions } from '@/typings/common';
  import {
    BASIC_DATA_SOURCE_CODE,
    DATA_DOWNLOAD_STATUS_CODE,
    HIS_BASIC_DATA_TYPE_CODE,
  } from '../constant';
  import {
    useExBasicGenericDtaTableConfig,
    useExBasicProjectDtaTableConfig,
    useExBasicDrugDtaTableConfig,
  } from '../config/useTableConfig.tsx';
  import { useExBasicDtaSearchFormConfig } from '../config/useFormConfig.ts';
  import {
    updateDownloadStatusByIds,
    queryExBasicDtaListByExample,
    queryExBasicDtaDictTypeListByExample,
  } from '@modules/baseConfig/api/exBasicDataDict';
  import {
    Title,
    ProForm,
    ProTable,
    useFetchDataset,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { DATA_TRADE_CODE } from '@sun-toolkit/enums';
  import { interfaceInvoke } from '@sun-toolkit/request';

  type ExBasicDtaDictTypeItem = ExBasicDataDict.ExBasicDtaDictTypeInfo & {
    label?: string;
    value?: string;
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const basicDataDictId = ref<string>(
    (route.params.basicDataDictId as string) || '',
  );
  const basicDataDictName = ref(history.state.basicDataDictName || '');
  const exBasicDtaDictTypeList = ref<ExBasicDtaDictTypeItem[]>([]);
  const currentExBasicDtaDictType = ref<ExBasicDtaDictTypeItem>({});
  const searchParams = ref<ExBasicDataDict.ExBasicDtaQueryParams>({
    basicDataDictId: basicDataDictId.value,
    basicDataDictTypeId: (route.query.basicDataDictTypeId as string) || '',
    enabledFlag: ENABLED_FLAG.ALL,
    keyword: '',
    pageNumber: 1,
    pageSize: 25,
  });
  const total = ref(0);
  const tableType = ref('');
  const loading = ref(false);
  const exBasicDtaGenericTableRef = ref();
  const exBasicDtaProjectTableRef = ref();
  const exBasicDtaDrugTableRef = ref();
  const exBasicDtaTableLoading = ref(false);
  const exBasicDtaList = ref<ExBasicDataDict.ExBasicDtaInfo[]>([]);
  const reDownloadBtnLoading = ref(false);
  const incrementalDownloadBtnLoading = ref(false);
  const intervalId = ref<ReturnType<typeof setInterval> | null>(null); // 定时任务
  const { menuId } = useAppConfigData([MAIN_APP_CONFIG.MENU_ID]);

  const dataSetList = useFetchDataset([CodeSystemType.BASIC_DATA_SOURCE_CODE]);
  const basicDataSourceCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.BASIC_DATA_SOURCE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  // 停止定时任务
  const stopInterval = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
      // console.log('定时器已停止');
    }
  };

  const queryExBasicDtaDictTypeList = async () => {
    const [, res] = await queryExBasicDtaDictTypeListByExample({
      basicDataDictId: basicDataDictId.value,
    });
    if (res?.success) {
      exBasicDtaDictTypeList.value = (res.data || []).map(
        (item: ExBasicDataDict.ExBasicDtaDictTypeInfo) => ({
          ...item,
          label: item?.basicDataDictTypeName,
          value: item?.basicDataDictTypeId,
        }),
      );
      if (
        exBasicDtaDictTypeList.value.length &&
        !searchParams.value.basicDataDictTypeId
      ) {
        searchParams.value.basicDataDictTypeId =
          exBasicDtaDictTypeList.value[0].value;
      }
    }
  };

  const queryExBasicDta = async (
    data?: Partial<ExBasicDataDict.ExBasicDtaQueryParams>,
    changeName?: string,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    currentExBasicDtaDictType.value = searchParams.value.basicDataDictTypeId
      ? (exBasicDtaDictTypeList.value.find(
          (item) =>
            item.basicDataDictTypeId === searchParams.value.basicDataDictTypeId,
        ) as ExBasicDtaDictTypeItem)
      : {};
    switch (currentExBasicDtaDictType.value.hisBasicDataTypeCode) {
      case HIS_BASIC_DATA_TYPE_CODE.DRUG:
        tableType.value = 'drug';
        break;
      case HIS_BASIC_DATA_TYPE_CODE.PROJECT:
        tableType.value = 'project';
        break;
      default:
        tableType.value = 'generic';
        break;
    }
    if (changeName === 'basicDataDictTypeId') {
      searchParams.value = {
        ...searchParams.value,
        pageNumber: 1,
        pageSize: 25,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryExBasicDtaListByExample(params);
    loading.value = false;
    if (res?.success) {
      exBasicDtaList.value = res.data || [];
      total.value = res.total;
    }
  };

  watch(
    () => route.params.basicDataDictId,
    async () => {
      basicDataDictId.value = route.params.basicDataDictId as string;
      await queryExBasicDtaDictTypeList();
      await queryExBasicDta();
    },
    {
      immediate: true,
    },
  );

  const checkDownloading = async () => {
    await queryExBasicDtaDictTypeList();
    currentExBasicDtaDictType.value = searchParams.value.basicDataDictTypeId
      ? (exBasicDtaDictTypeList.value.find(
          (item) =>
            item.basicDataDictTypeId === searchParams.value.basicDataDictTypeId,
        ) as ExBasicDtaDictTypeItem)
      : {};
    if (
      currentExBasicDtaDictType.value?.dataDownloadStatusCode ===
      DATA_DOWNLOAD_STATUS_CODE.DOWNLOADED
    ) {
      ElMessage.success(
        t(
          'exBasicDataDict.exBasicDtaDictTypeTable.downloadSuccess',
          '下载完成！',
        ),
      );
      stopInterval();
    }
  };

  // 点击下载后每隔 30s 更新列表
  const startInterval = () => {
    stopInterval();
    intervalId.value = setInterval(checkDownloading, 30000);
    // console.log('定时器已开始');
  };

  const handleExBasicDtaDictTypeStopDownload = async (type: string) => {
    if (['reDownload', 'incrementalDownload'].includes(type)) {
      if (!route.query.interfaceId || !route.query.basicDataDictTypeNo) {
        ElMessage.warning(
          t(
            'exBasicDataDict.downloadErrorTip.missingFields',
            '缺失参数，请返回目录列表重新操作',
          ),
        );
        return;
      }
      if (type === 'reDownload') {
        reDownloadBtnLoading.value = true;
      } else {
        incrementalDownloadBtnLoading.value = true;
      }
      const [, res] = await interfaceInvoke({
        interfaceId: route.query.interfaceId as string,
        menuId: menuId as string,
        tradeCode: DATA_TRADE_CODE.LOAD,
        params: {
          basicDataDictTypeId:
            currentExBasicDtaDictType.value.basicDataDictTypeId,
          basicDataDictTypeNo: route.query.basicDataDictTypeNo,
          reDownloadFlag: type === 'reDownload' ? FLAG.YES : FLAG.NO,
        },
      });
      if (res?.success) {
        ElMessage.success(
          t(
            'exBasicDataDict.exBasicDtaDictTypeTable.downloading',
            '正在下载...',
          ),
        );
        checkDownloading();
        startInterval();
      } else {
        reDownloadBtnLoading.value = false;
        incrementalDownloadBtnLoading.value = false;
      }
    } else if (type === 'terminateDownload') {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action: t(
            'exBasicDataDict.ExBasicDtaList.terminateDownload',
            '终止下载',
          ),
          name: currentExBasicDtaDictType.value.basicDataDictTypeName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          basicDataDictTypeId:
            currentExBasicDtaDictType.value.basicDataDictTypeId,
          dataDownloadStatusCode: DATA_DOWNLOAD_STATUS_CODE.TERMINATED,
        };
        const [, res] = await updateDownloadStatusByIds(params);
        if (res?.success) {
          ElMessage.success(t('global:save.success'));
          currentExBasicDtaDictType.value.dataDownloadStatusCode =
            DATA_DOWNLOAD_STATUS_CODE.TERMINATED;
          queryExBasicDtaDictTypeList();
        }
      });
    }
  };

  const searchConfig = useExBasicDtaSearchFormConfig(
    exBasicDtaDictTypeList as Ref<SelectOptions[]>,
    queryExBasicDta,
  );
  const exBasicGenericDtaTableConfig = useExBasicGenericDtaTableConfig(
    exBasicDtaGenericTableRef,
    exBasicDtaList as Ref<
      (ExBasicDataDict.ExBasicDtaInfo & {
        editable: boolean;
      })[]
    >,
    basicDataSourceCodeList,
  );
  const exBasicProjectDtaTableConfig = useExBasicProjectDtaTableConfig(
    exBasicDtaProjectTableRef,
    exBasicDtaList as Ref<
      (ExBasicDataDict.ExBasicDtaInfo & {
        editable: boolean;
      })[]
    >,
    basicDataSourceCodeList,
  );
  const exBasicDrugDtaTableConfig = useExBasicDrugDtaTableConfig(
    exBasicDtaDrugTableRef,
    exBasicDtaList as Ref<
      (ExBasicDataDict.ExBasicDtaInfo & {
        editable: boolean;
      })[]
    >,
    basicDataSourceCodeList,
  );

  onBeforeUnmount(() => {
    stopInterval();
  });
</script>

<template>
  <div class="flex h-full flex-col">
    <el-page-header
      @back="
        router.push({ name: 'exBasicDataDict', query: { basicDataDictId } })
      "
      class="pb-3"
    >
      <template #content>
        <span class="text-base">
          {{
            `${$t('exBasicDataDict.ExBasicDtaList.title', '数据管理')}${basicDataDictName ? `（${basicDataDictName}）` : ''}`
          }}
        </span>
      </template>
    </el-page-header>
    <div class="flex flex-col" v-loading="loading">
      <Title
        :title="$t('exBasicDataDict.ExBasicDtaList.list', '数据列表')"
        class="mb-2"
      />
      <ProForm
        v-model="searchParams"
        layout-mode="inline"
        :show-search-button="true"
        :data="searchConfig"
        @model-change="queryExBasicDta"
      />
      <div class="mb-2 flex items-center justify-between">
        <div class="flex">
          <el-button
            v-if="
              currentExBasicDtaDictType.basicDataSourceCode ===
              BASIC_DATA_SOURCE_CODE.DOWNLOAD
            "
            class="mr-2"
            type="primary"
            :loading="reDownloadBtnLoading"
            :disabled="reDownloadBtnLoading || incrementalDownloadBtnLoading"
            @click="handleExBasicDtaDictTypeStopDownload('reDownload')"
          >
            {{ $t('exBasicDataDict.ExBasicDtaList.reDownload', '重新下载') }}
          </el-button>
          <el-button
            v-if="
              currentExBasicDtaDictType.basicDataSourceCode ===
              BASIC_DATA_SOURCE_CODE.DOWNLOAD
            "
            :loading="incrementalDownloadBtnLoading"
            :disabled="reDownloadBtnLoading || incrementalDownloadBtnLoading"
            class="mr-2"
            type="primary"
            @click="handleExBasicDtaDictTypeStopDownload('incrementalDownload')"
          >
            {{
              $t(
                'exBasicDataDict.ExBasicDtaList.incrementalDownload',
                '增量下载',
              )
            }}
          </el-button>
          <el-button
            v-if="
              currentExBasicDtaDictType.basicDataSourceCode ===
                BASIC_DATA_SOURCE_CODE.DOWNLOAD &&
              currentExBasicDtaDictType.dataDownloadStatusCode ===
                DATA_DOWNLOAD_STATUS_CODE.DOWNLOADING
            "
            type="danger"
            class="mr-2"
            @click="handleExBasicDtaDictTypeStopDownload('terminateDownload')"
          >
            {{
              $t('exBasicDataDict.ExBasicDtaList.terminateDownload', '终止下载')
            }}
          </el-button>
          <el-button
            v-if="
              currentExBasicDtaDictType.basicDataSourceCode ===
              BASIC_DATA_SOURCE_CODE.IMPORT
            "
            disabled
            type="primary"
            class="mr-2"
          >
            {{ $t('exBasicDataDict.ExBasicDtaList.import', '导入') }}
          </el-button>
          <el-button
            v-if="
              currentExBasicDtaDictType.basicDataSourceCode ===
              BASIC_DATA_SOURCE_CODE.MANUAL_MAINTENANCE
            "
            type="primary"
            disabled
          >
            {{ $t('global:add') }}
          </el-button>
        </div>
        <div
          v-if="
            currentExBasicDtaDictType.basicDataSourceCode ===
            BASIC_DATA_SOURCE_CODE.DOWNLOAD
          "
          class="flex items-center"
        >
          <div class="mr-3">
            {{
              $t('exBasicDataDict.ExBasicDtaList.downloadStatus', '下载状态：')
            }}
            <el-tag type="danger">
              {{ currentExBasicDtaDictType.dataDownloadStatusDesc || '-' }}
            </el-tag>
          </div>
          <div>
            {{
              $t('exBasicDataDict.ExBasicDtaList.downloadMemo', '下载备注：')
            }}
            <el-tag type="primary">
              {{ currentExBasicDtaDictType.dataDownloadStatusMemo || '-' }}
            </el-tag>
          </div>
        </div>
      </div>
      <div v-if="tableType === 'generic'">
        <ProTable
          ref="exBasicDtaGenericTableRef"
          row-key="basicDataId"
          :data="exBasicDtaList"
          :loading="exBasicDtaTableLoading"
          :columns="exBasicGenericDtaTableConfig"
          :page-info="{
            total,
            pageNumber: searchParams.pageNumber,
            pageSize: searchParams.pageSize,
          }"
          :pagination="true"
          @current-page-change="
            (val: number) => {
              queryExBasicDta({
                pageNumber: val,
              });
            }
          "
          @size-page-change="
            (val: number) => {
              queryExBasicDta({
                pageSize: val,
              });
            }
          "
        />
      </div>
      <div v-else-if="tableType === 'project'">
        <ProTable
          ref="exBasicDtaProjectTableRef"
          row-key="basicDataId"
          :data="exBasicDtaList"
          :loading="exBasicDtaTableLoading"
          :columns="exBasicProjectDtaTableConfig"
          :page-info="{
            total,
            pageNumber: searchParams.pageNumber,
            pageSize: searchParams.pageSize,
          }"
          :pagination="true"
          @current-page-change="
            (val: number) => {
              queryExBasicDta({
                pageNumber: val,
              });
            }
          "
          @size-page-change="
            (val: number) => {
              queryExBasicDta({
                pageSize: val,
              });
            }
          "
        />
      </div>
      <div v-else-if="tableType === 'drug'">
        <ProTable
          ref="exBasicDtaDrugTableRef"
          row-key="basicDataId"
          :data="exBasicDtaList"
          :loading="exBasicDtaTableLoading"
          :columns="exBasicDrugDtaTableConfig"
          :page-info="{
            total,
            pageNumber: searchParams.pageNumber,
            pageSize: searchParams.pageSize,
          }"
          :pagination="true"
          @current-page-change="
            (val: number) => {
              queryExBasicDta({
                pageNumber: val,
              });
            }
          "
          @size-page-change="
            (val: number) => {
              queryExBasicDta({
                pageSize: val,
              });
            }
          "
        />
      </div>
    </div>
  </div>
</template>
