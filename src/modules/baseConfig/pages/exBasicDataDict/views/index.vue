<script setup lang="ts" name="exBasicDataDict">
  import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { SelectOptions } from '@/typings/common';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { CodeSystemType } from '@/typings/codeManage';
  import { DATA_DOWNLOAD_STATUS_CODE } from '../constant';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { useExBasicDtaDictSearchFormConfig } from '../config/useFormConfig.ts';
  import { useExBasicDtaDictTypeTableConfig } from '../config/useTableConfig.tsx';
  import { queryInterfaceListByExample } from '@/modules/baseConfig/api/interface';
  import {
    DmlButton,
    Title,
    ProForm,
    ProTable,
    useFetchDataset,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    queryExBasicDtaDictListByExample,
    queryExBasicDtaDictTypeListByExample,
    updateExBasicDtaDictEnabledFlagById,
    updateExBasicDtaDictStandardFlagById,
    updateDownloadStatusByIds,
  } from '@modules/baseConfig/api/exBasicDataDict';
  import { interfaceInvoke } from '@sun-toolkit/request';
  import { DATA_TRADE_CODE } from '@sun-toolkit/enums';
  import ExBasicDtaDictUpsertDialog from '@/modules/baseConfig/pages/exBasicDataDict/components/ExBasicDtaDictUpsertDialog.vue';

  const route = useRoute();
  const router = useRouter();
  const { isCloudEnv, menuId } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.MENU_ID,
  ]);

  const { t } = useTranslation();
  const searchParams = ref<ExBasicDataDict.ExBasicDtaDictQueryParams>({
    keyWord: '',
    interfaceId: '',
    enabledFlag: ENABLED_FLAG.ALL,
    standardFlag: '',
  });
  const loading = ref(false);
  const activeName = ref(route.query.basicDataDictId || '');
  const exBasicDataDictList = ref<ExBasicDataDict.ExBasicDtaDictInfo[]>([]);
  const currentExBasicDataDict = ref<ExBasicDataDict.ExBasicDtaDictInfo>();
  const exBasicDataDictUpsertParams =
    ref<ExBasicDataDict.ExBasicDtaDictUpsertParams>({});
  const exBasicDtaDictUpsertDialogRef = ref();
  const exBasicDtaDictUpsertDialogMode = ref('add');
  const exBasicDataDictTypeTableRef = ref();
  const basicDataDictTypeTableLoading = ref(false);
  const exBasicDtaDictTypeList = ref<ExBasicDataDict.ExBasicDtaDictTypeInfo[]>(
    [],
  );
  const filterBasicDataDictTypeName = ref<string>('');
  const interfaceList = ref<SelectOptions[]>([]);
  const intervalId = ref<ReturnType<typeof setInterval> | null>(null); // 定时任务
  const downloadingBasicDataDictTypeIds = ref<string[]>([]); // 下载中的目录分类标识集合

  const bizData = computed(() => {
    return [activeName.value as string];
  });

  const displayExBasicDtaDictTypeList = computed(() =>
    exBasicDtaDictTypeList.value.filter((item) =>
      item.basicDataDictTypeName?.includes(filterBasicDataDictTypeName.value),
    ),
  );

  const dataSetList = useFetchDataset([
    CodeSystemType.BASIC_DATA_SOURCE_CODE,
    CodeSystemType.HIS_BASIC_DATA_TYPE_CODE,
    CodeSystemType.DATA_DOWNLOAD_STATUS_CODE,
  ]);
  const basicDataSourceCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.BASIC_DATA_SOURCE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const hisBasicDataTypeCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.HIS_BASIC_DATA_TYPE_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  const dataDownloadStatusCodeList = computed(() =>
    (dataSetList?.value?.[CodeSystemType.DATA_DOWNLOAD_STATUS_CODE] || []).map(
      (item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );

  // 停止定时任务
  const stopInterval = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
      downloadingBasicDataDictTypeIds.value = [];
      // console.log('定时器已停止');
    }
  };

  const queryInterfaceList = async () => {
    const [, res] = await queryInterfaceListByExample({ enabledFlag: 1 });
    if (res?.success) {
      interfaceList.value = (res.data || []).map((item) => ({
        label: item?.interfaceName as string,
        value: item?.interfaceId as string,
      }));
    }
  };

  const queryExBasicDtaDictTypeData = async () => {
    basicDataDictTypeTableLoading.value = true;
    const [, res] = await queryExBasicDtaDictTypeListByExample({
      basicDataDictId: activeName.value,
    });
    basicDataDictTypeTableLoading.value = false;
    if (res?.success) {
      filterBasicDataDictTypeName.value = '';
      exBasicDtaDictTypeList.value = res.data;
    }
  };

  const queryExBasicDataDictData = async (
    data?: ExBasicDataDict.ExBasicDtaDictQueryParams,
  ) => {
    stopInterval();
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryExBasicDtaDictListByExample(params);
    loading.value = false;
    if (res?.success) {
      exBasicDataDictList.value = res.data;
      if (exBasicDataDictList.value.length) {
        currentExBasicDataDict.value =
          exBasicDataDictList.value.find(
            (item) => item.basicDataDictId === activeName.value,
          ) || exBasicDataDictList.value[0];
        activeName.value = currentExBasicDataDict.value.basicDataDictId;
        queryExBasicDtaDictTypeData();
      }
    }
  };

  const handleTabChange = (val: string) => {
    currentExBasicDataDict.value = exBasicDataDictList.value.find(
      (item) => item.basicDataDictId === val,
    );
    stopInterval();
    queryExBasicDtaDictTypeData();
  };

  const onOpenExBasicDtaDictUpsertDialog = (mode: string) => {
    exBasicDtaDictUpsertDialogMode.value = mode;
    exBasicDataDictUpsertParams.value =
      mode === 'add'
        ? {
            enabledFlag: ENABLED_FLAG.YES,
            standardFlag: ENABLED_FLAG.NO,
            basicDataDictTypeList: [],
          }
        : {
            ...currentExBasicDataDict.value,
            basicDataDictTypeList: JSON.parse(
              JSON.stringify(exBasicDtaDictTypeList.value),
            ),
          };
    exBasicDtaDictUpsertDialogRef.value.dialogRef.open();
  };

  const onDataManageExBasicDataDictClick = () => {
    stopInterval();
    router.push({
      name: 'exBasicDtaList',
      params: {
        basicDataDictId: currentExBasicDataDict.value!.basicDataDictId,
      },
      state: {
        basicDataDictName: currentExBasicDataDict.value!.basicDataDictName,
      },
    });
  };

  /** 启用状态切换 */
  const handleEnableSwitch = async () => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          currentExBasicDataDict.value!.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: currentExBasicDataDict.value!.basicDataDictName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        basicDataDictId: currentExBasicDataDict.value!.basicDataDictId,
        enabledFlag:
          currentExBasicDataDict.value!.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateExBasicDtaDictEnabledFlagById(params);
      if (res?.success) {
        currentExBasicDataDict.value!.enabledFlag =
          currentExBasicDataDict.value!.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            currentExBasicDataDict.value!.enabledFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  /** 贯标状态切换 */
  const handleStandardFlagCheck = async () => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('exBasicDataDict.table.standard', '贯标'),
        name: currentExBasicDataDict.value!.basicDataDictName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        basicDataDictId: currentExBasicDataDict.value!.basicDataDictId,
      };
      const [, res] = await updateExBasicDtaDictStandardFlagById(params);
      if (res?.success) {
        ElMessage.success(t('global:save.success'));
        exBasicDataDictList.value.forEach((item) => {
          item.standardFlag = ENABLED_FLAG.NO;
        });
        currentExBasicDataDict.value!.standardFlag = ENABLED_FLAG.YES;
      }
    });
  };

  const checkDownloading = async () => {
    if (!downloadingBasicDataDictTypeIds.value.length) return;
    await queryExBasicDtaDictTypeData();
    downloadingBasicDataDictTypeIds.value.forEach((id) => {
      const item = exBasicDtaDictTypeList.value.find(
        (item) => item.basicDataDictTypeId === id,
      );
      if (
        !item ||
        item.dataDownloadStatusCode === DATA_DOWNLOAD_STATUS_CODE.DOWNLOADED
      ) {
        ElMessage.success(
          t(
            'exBasicDataDict.exBasicDtaDictTypeTable.downloadSuccess',
            '下载完成！',
          ),
        );
        nextTick(() => {
          downloadingBasicDataDictTypeIds.value =
            downloadingBasicDataDictTypeIds.value.filter((_id) => _id !== id);
        });
      }
    });
  };

  // 点击下载后每隔 30s 更新列表
  const startInterval = () => {
    stopInterval();
    intervalId.value = setInterval(checkDownloading, 30000);
    // console.log('定时器已开始');
  };

  const handleExBasicDtaDictTypeDownloadOrStop = async (
    data: ExBasicDataDict.ExBasicDtaDictTypeInfo & { downloading: boolean },
  ) => {
    if (data.dataDownloadStatusCode === DATA_DOWNLOAD_STATUS_CODE.DOWNLOADING) {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action: t(
            'exBasicDataDict.exBasicDtaDictTypeTable.stopDownload',
            '终止下载',
          ),
          name: data.basicDataDictTypeName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          const params = {
            basicDataDictTypeId: data.basicDataDictTypeId,
            dataDownloadStatusCode: DATA_DOWNLOAD_STATUS_CODE.TERMINATED,
          };
          const [, res] = await updateDownloadStatusByIds(params);
          if (res?.success) {
            ElMessage.success(t('global:save.success'));
            queryExBasicDtaDictTypeData();
          }
        })
        .catch(() => {});
      return;
    }
    data.downloading = true;
    const [, res] = await interfaceInvoke({
      interfaceId: currentExBasicDataDict.value?.interfaceId || '',
      menuId: menuId as string,
      tradeCode: DATA_TRADE_CODE.LOAD,
      params: {
        basicDataDictTypeId: data.basicDataDictTypeId,
        basicDataDictTypeNo: data.basicDataDictTypeNo,
        reDownloadFlag: FLAG.NO,
      },
    });
    if (res?.success) {
      ElMessage.success(
        t('exBasicDataDict.exBasicDtaDictTypeTable.downloading', '正在下载...'),
      );
      downloadingBasicDataDictTypeIds.value = Array.from(
        new Set([
          ...downloadingBasicDataDictTypeIds.value,
          data.basicDataDictTypeId as string,
        ]),
      );
      checkDownloading();
      startInterval();
    } else {
      data.downloading = false;
    }
  };

  const handleView = (data: ExBasicDataDict.ExBasicDtaDictTypeInfo) => {
    stopInterval();
    router.push({
      name: 'exBasicDtaList',
      params: {
        basicDataDictId: currentExBasicDataDict.value!.basicDataDictId,
      },
      query: {
        basicDataDictTypeId: data.basicDataDictTypeId,
        basicDataDictTypeNo: data.basicDataDictTypeNo,
        interfaceId: currentExBasicDataDict.value?.interfaceId,
      },
      state: {
        basicDataDictName: currentExBasicDataDict.value!.basicDataDictName,
      },
    });
  };

  const searchConfig = useExBasicDtaDictSearchFormConfig(
    interfaceList,
    queryExBasicDataDictData,
  );
  const basicDataDictTypeTableColumnsConfig = useExBasicDtaDictTypeTableConfig(
    handleExBasicDtaDictTypeDownloadOrStop,
    handleView,
  );

  onMounted(() => {
    stopInterval();
    queryInterfaceList();
    queryExBasicDataDictData();
  });

  onBeforeUnmount(() => {
    stopInterval();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('exBasicDataDict.list.title', '目录列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :show-search-button="true"
          :data="searchConfig"
          @model-change="
            (...args: any) => {
              activeName = '';
              queryExBasicDataDictData(...args);
            }
          "
        />
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_EX_BASIC_DATA_DICTIONARY"
        />
        <el-button
          class="ml-3"
          type="primary"
          :disabled="!isCloudEnv"
          @click="onOpenExBasicDtaDictUpsertDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <el-tabs v-model="activeName" class="mt-2" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in exBasicDataDictList"
        :key="item.basicDataDictId"
        :label="item.basicDataDictName"
        :name="item.basicDataDictId"
        class="h-full"
      >
        <div class="flex items-center justify-between">
          <div v-if="currentExBasicDataDict" class="flex items-center">
            <div class="mr-5">
              {{
                $t('exBasicDataDict.tableDetail.basicDataDictId', '目录标识：')
              }}
              {{ currentExBasicDataDict.basicDataDictId }}
            </div>
            <div class="mr-5">
              {{ $t('exBasicDataDict.tableDetail.standardFlag', '是否贯标：') }}
              <el-checkbox
                v-model="currentExBasicDataDict.standardFlag"
                :disabled="
                  currentExBasicDataDict.standardFlag === ENABLED_FLAG.YES
                "
                :true-value="ENABLED_FLAG.YES"
                :false-value="ENABLED_FLAG.NO"
                @change="handleStandardFlagCheck"
              />
            </div>
            <div class="mr-5">
              {{
                $t('exBasicDataDict.tableDetail.interfaceName', '对应接口：')
              }}
              {{ currentExBasicDataDict.interfaceName }}
            </div>
            <div>
              {{ $t('exBasicDataDict.tableDetail.enabledFlag', '启用标志：') }}
              <el-switch
                v-model="currentExBasicDataDict.enabledFlag"
                inline-prompt
                :active-value="ENABLED_FLAG.YES"
                :inactive-value="ENABLED_FLAG.NO"
                :active-text="$t('global:enabled')"
                :inactive-text="$t('global:disabled')"
                :before-change="handleEnableSwitch"
              />
            </div>
          </div>
          <div class="flex justify-around">
            <el-button
              link
              type="primary"
              @click="onOpenExBasicDtaDictUpsertDialog('view')"
            >
              {{ $t('global:view') }}
            </el-button>
            <el-button
              link
              class="ml-5"
              type="primary"
              :disabled="!isCloudEnv"
              @click="onOpenExBasicDtaDictUpsertDialog('edit')"
            >
              {{ $t('global:edit') }}
            </el-button>
            <el-button
              link
              class="ml-5"
              type="primary"
              @click="onDataManageExBasicDataDictClick"
            >
              {{ $t('exBasicDataDict.tableDetail.dataManage', '数据管理') }}
            </el-button>
          </div>
        </div>
        <ProTable
          ref="exBasicDataDictTypeTableRef"
          row-key="basicDataDictTypeId"
          :loading="basicDataDictTypeTableLoading"
          :columns="basicDataDictTypeTableColumnsConfig"
          :data="displayExBasicDtaDictTypeList"
        >
          <template #basicDataDictTypeNameHeader>
            {{
              $t(
                'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeName',
                '目录分类名称',
              )
            }}
            <el-popover placement="top" :width="300" trigger="click">
              <template #reference>
                <el-icon class="absolute right-2 cursor-pointer"
                  ><Filter
                /></el-icon>
              </template>
              <el-input
                v-model="filterBasicDataDictTypeName"
                :placeholder="
                  $t('global:placeholder.input.template', {
                    content: $t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeNameKeyWord',
                      '目录分类名称关键字',
                    ),
                  })
                "
              ></el-input>
            </el-popover>
          </template>
        </ProTable>
      </el-tab-pane>
    </el-tabs>
  </div>
  <ExBasicDtaDictUpsertDialog
    ref="exBasicDtaDictUpsertDialogRef"
    :mode="exBasicDtaDictUpsertDialogMode"
    :interface-list="interfaceList"
    :data="exBasicDataDictUpsertParams"
    :basic-data-source-code-list="basicDataSourceCodeList"
    :his-basic-data-type-code-list="hisBasicDataTypeCodeList"
    :data-download-status-code-list="dataDownloadStatusCodeList"
    @success="queryExBasicDataDictData"
  />
</template>
