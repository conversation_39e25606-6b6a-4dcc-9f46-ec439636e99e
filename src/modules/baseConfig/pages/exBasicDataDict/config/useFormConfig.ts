import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';

export function useExBasicDtaDictSearchFormConfig(
  interfaceList: Ref<SelectOptions[]>,
  queryExBasicDtaDictData: (
    data: ExBasicDataDict.ExBasicDtaDictQueryParams,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('exBasicDataDict.search.interfaceId', '对应接口'),
        name: 'interfaceId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataDict.search.interfaceId', '对应接口'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: interfaceList.value,
          className: 'w-80',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryExBasicDtaDictData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryExBasicDtaDictData({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useExBasicDtaDictTypeMemoFormConfig() {
  return useFormConfig({
    getData: (t) => [
      {
        label: t(
          'exBasicDtaDict.downloadForm.dataDownloadStatusMemo',
          '下载状态备注',
        ),
        name: 'dataDownloadStatusMemo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t(
            'exBasicDtaDict.downloadForm.dataDownloadStatusMemo',
            '下载状态备注',
          ),
        }),
        isFullWidth: true,
        extraProps: {
          type: 'textarea',
          autosize: { minRows: 3, maxRows: 6 },
        },
      },
    ],
  });
}

export function useExBasicDtaDictUpsertFormConfig(
  interfaceList: Ref<SelectOptions[]>,
  disabled: Ref<boolean>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('exBasicDtaDict.form.basicDataDictName', '目录名称'),
        name: 'basicDataDictName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('exBasicDtaDict.form.basicDataDictName', '目录名称'),
        }),
        extraProps: {
          maxLength: 64,
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('exBasicDtaDict.form.basicDataDictName', '目录名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('exBasicDtaDict.form.interfaceId', '对应接口'),
        name: 'interfaceId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDtaDict.search.interfaceId', '对应接口'),
        }),
        extraProps: {
          className: 'w-60',
          options: interfaceList.value,
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('exBasicDtaDict.form.releaseHospitalList', '对应接口'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          disabled: disabled.value,
        },
      },
      {
        name: 'standardFlag',
        label: t('exBasicDtaDict.form.standardFlag', '是否贯标'),
        component: 'radio-group',
        extraProps: {
          options: [
            { label: t('global:no'), value: ENABLED_FLAG.NO },
            { label: t('global:yes'), value: ENABLED_FLAG.YES },
          ],
          disabled: disabled.value,
        },
      },
    ],
  });
  return data;
}

export function useExBasicDtaSearchFormConfig(
  exBasicDtaDictTypeList: Ref<SelectOptions[]>,
  queryExBasicDta: (
    data: Partial<ExBasicDataDict.ExBasicDtaQueryParams>,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('exBasicDta.search.basicDataDictTypeId', '外部目录分类'),
        name: 'basicDataDictTypeId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDta.search.basicDataDictTypeId', '外部目录分类'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: exBasicDtaDictTypeList.value,
          clearable: false,
          className: 'w-80',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-80',
        },
      },
      {
        name: 'keyword',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryExBasicDta({
                keyword: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryExBasicDta({
              keyword: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
