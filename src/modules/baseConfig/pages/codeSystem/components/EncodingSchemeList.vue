<script setup lang="tsx">
  import { computed, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import UpdateEncodingScheme from './UpdateEncodingScheme.vue';
  import PublishEncodingScheme from './PublishEncodingScheme.vue';
  import { queryCodeSystemListByExample } from '@/modules/baseConfig/api/code';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant';
  import {
    updateOrgEnabledFlagById,
    exportDmlScriptByExample,
  } from '@/modules/baseConfig/api/code';
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  import { type AnyObject, Title, ProTable } from 'sun-biz';
  const getColumns = function () {
    return [
      {
        prop: 'indexNo',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'sequenceNumber',
        render: (row: object, index: number) => <>{index + 1}</>,
      },
      {
        label: t('global:code'),
        prop: 'codeSystemNo',
        supportCopyAndTips: true,
        supportTextCopy: false,
      },
      {
        label: t('global:name'),
        prop: 'codeSystemName',
        supportCopyAndTips: true,
        supportTextCopy: false,
      },
      {
        label: t('global:secondName'),
        prop: 'codeSystem2ndName',
        editable: false,
        supportCopyAndTips: true,
        supportTextCopy: false,
      },
      {
        label: t('global:enableStatus'),
        prop: 'action',
        fixed: 'right',
        render: (row: Code.CodeSystemInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
    ];
  };

  type Props = {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    setSelectSchemeRow: Function;
    selectSchemeRow: object | null;
    menuList: { value: string; label: string }[];
  };

  const props = defineProps<Props>();

  function handleEnableSwitch(row: Code.CodeSystemInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.codeSystemName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await updateOrgEnabledFlagById({
            enabledFlag:
              row.enabledFlag === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
            codeSystemId: row.codeSystemId,
          });
          if (result?.success) {
            resolve();
            handleEnter();
            ElMessage({
              type: 'success',
              message: t(
                row.enabledFlag === ENABLED_FLAG.YES
                  ? 'global:disabled.success'
                  : 'global:enabled.success',
              ),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  function currentChange(row: object) {
    props.setSelectSchemeRow(row);
  }

  const tableRef = ref();
  const keyWord = ref<string>('');
  const selections = ref<Code.CodeSystemInfo[]>([]);
  function onClose() {
    tableRef.value.fetchList();
  }
  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_CODE_SYSTEM,
      bisIds: selections.value.map((item) => item.codeSystemId),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      clearSelection();
    }
  }

  async function fetchData(params: Code.PageInfo) {
    let [, result] = await queryCodeSystemListByExample({
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
      keyWord: keyWord.value,
    });
    if (result?.success) {
      if ((result.data || [])?.length) {
        tableRef.value.setCurrentRow(result.data[0]);
      }
      return {
        data: result.data,
        total: result.total,
      };
    }
  }

  function handleEnter() {
    tableRef?.value?.fetchList();
  }
  let inputChange = debounce(handleEnter, 500);

  function selectChange(value: AnyObject[]) {
    selections.value = value as Code.CodeSystemInfo[];
  }
  const columns = computed(() => getColumns());

  function clearSelection() {
    tableRef?.value?.proTableRef.clearSelection();
  }
</script>
<template>
  <div class="p-box flex h-full flex-1 flex-col">
    <Title :title="$t('encoding.scheme.list', '编码体系列表')" class="mb-5">
      <span>
        <el-input
          v-model="keyWord"
          @input="inputChange"
          @keydown.enter="handleEnter"
          class="mr-3 w-60"
          :placeholder="t('global:placeholder.keyword')"
          :suffix-icon="Search"
        />
        <UpdateEncodingScheme @success="onClose" />
        <el-dropdown @click="clickDropdown">
          <el-button type="primary" :disabled="!selections.length">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template
            #dropdown
            v-if="selections.length && !!props.menuList.length"
          >
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in props.menuList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <PublishEncodingScheme
          :selections="selections"
          @success="clearSelection"
        />
      </span>
    </Title>
    <pro-table
      :pagination="true"
      component-no="1000"
      :highlight-current-row="true"
      @selection-change="selectChange"
      @current-change="currentChange"
      row-class-name="cursor-pointer"
      ref="tableRef"
      :columns="columns"
      row-key="codeSystemId"
      :fetch-data="fetchData"
    />
  </div>
</template>
