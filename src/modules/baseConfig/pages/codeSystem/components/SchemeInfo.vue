<script setup lang="tsx">
  import { computed, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { Title, ProForm } from 'sun-biz';
  const { t } = useTranslation();

  function getBaseInfoData(initData: Code.CodeSystemInfo) {
    return [
      {
        name: 'codeSystemNo',
        label: t('global:code'),
        defaultValue: initData?.codeSystemNo,
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'codeSystemName',
        label: t('global:name'),
        defaultValue: initData?.codeSystemName,
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'codeSystem2ndName',
        label: t('global:secondName'),
        defaultValue: initData?.codeSystem2ndName,
        supportCopyAndTips: true,
        component: 'text',
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        supportCopyAndTips: true,
        defaultValue: initData?.spellNo,
        component: 'text',
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        supportCopyAndTips: true,
        defaultValue: initData?.wbNo,
        component: 'text',
      },
      {
        name: 'version',
        label: t('encoding.scheme.version', '版本号'),
        defaultValue: initData?.version,
        component: 'text',
      },
      {
        name: 'sourceName',
        label: t('encoding.scheme.sourceName', '来源'),
        component: 'text',
        defaultValue: initData?.sourceName || '--',
      },
      {
        name: 'beginDate',
        label: t('encoding.scheme.beginDate', '生效时间'),
        defaultValue: initData?.beginDate || '--',
        component: 'text',
      },
      {
        name: 'endDate',
        label: t('encoding.scheme.endDate', '失效时间'),
        defaultValue: initData?.endDate || '--',
        component: 'text',
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'text',
        defaultValue:
          initData?.enabledFlag === ENABLED_FLAG.YES
            ? t('global:enabled')
            : t('global:disabled'),
      },
      {
        name: 'description',
        label: t('encoding.scheme.description', '描述'),
        defaultValue: initData?.description,
        component: 'text',
      },
    ];
  }

  type Props = {
    selectSchemeRow: Code.CodeSystemInfo | null;
  };

  const props = withDefaults(defineProps<Props>(), {});

  const show = ref(false);

  function handleClick() {
    show.value = !show.value;
  }
  const baseInfoDescData = computed(() =>
    getBaseInfoData(props.selectSchemeRow || ({} as Code.CodeSystemInfo)),
  );
</script>
<template>
  <div class="shrink-0">
    <div :class="['mb-4', 'cursor-pointer', 'pt-2']" @click="handleClick">
      <Title :title="$t('encoding.scheme.info', '编码体系信息')">
        <span>
          <el-icon v-if="show"><CaretBottom /></el-icon>
          <el-icon v-else><CaretLeft /></el-icon>
        </span>
      </Title>
    </div>
    <div
      :class="[
        show ? 'h-52' : 'h-0',
        show ? 'mb-2' : '',
        'transition-all',
        'duration-300',
        'overflow-hidden',
        'ease-in',
      ]"
    >
      <ProForm
        :key="`${props.selectSchemeRow?.codeSystemNo}`"
        ref="formRef"
        :column="3"
        :data="baseInfoDescData"
      />
    </div>

    <hr class="mb-4" />
  </div>
</template>
