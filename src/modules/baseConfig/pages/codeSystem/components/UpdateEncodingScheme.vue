<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<script setup lang="tsx">
  import { ref, computed, reactive } from 'vue';
  import { queryExCodeSystemListByExample } from '@/modules/baseConfig/api/code';
  import { saveCodeSystemDataSets } from '@/modules/baseConfig/api/code';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import {
    ProTable,
    type AnyObject,
    ProDialog,
    CopyTextWithTooltip,
  } from 'sun-biz';
  import { MAIN_APP_CONFIG, useAppConfigData } from 'sun-biz';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const ALL_HIS_EXISTS_FLAG = -1;
  function empty(value: string) {
    return value || '--';
  }
  function getColumns() {
    return [
      {
        type: 'selection',
        width: 40,
      },
      {
        label: ' ',
        align: 'left',
        render: () => {
          return ' ';
        },
        width: 40,
      },
      {
        label: t('global:code'),
        prop: 'codeSystemNo',
        supportCopyAndTips: true,
        render: (row: Code.ResQueryCodeSystemList) => {
          return (
            <CopyTextWithTooltip
              supportTextCopy={true}
              align="text-center"
              text={row.parentId ? row.dataValueNo : row.codeSystemNo || '--'}
            />
          );
        },
        minWidth: 120,
      },
      {
        label: t('global:name'),
        prop: 'codeSystemName',
        editable: false,
        supportCopyAndTips: true,
        render: (row: Code.ResQueryCodeSystemList) => {
          return (
            <CopyTextWithTooltip
              supportTextCopy={true}
              align="text-center"
              text={
                row.parentId ? row.dataValueCnName : row.codeSystemName || '--'
              }
            />
          );
        },
        minWidth: 120,
      },
      {
        label: t('global:secondName'),
        prop: 'codeSystem2ndName',
        editable: false,
        supportCopyAndTips: true,
        render: (row: Code.ResQueryCodeSystemList) => {
          return (
            <CopyTextWithTooltip
              supportTextCopy={true}
              align="text-center"
              text={
                row.parentId
                  ? row.dataValue2ndName
                  : row.codeSystem2ndName || '--'
              }
            />
          );
        },
        minWidth: 120,
      },
      {
        label: t('encoding.scheme.description', '描述'),
        prop: 'description',
        editable: false,
        render: (row: Code.ResQueryCodeSystemList) => {
          return empty(
            row.parentId ? row.dataValueDescription : row.description,
          );
        },
        minWidth: 150,
      },
      {
        label: t('encoding.scheme.version', '版本号'),
        prop: 'version',
        editable: false,
        render: (row: Code.ResQueryCodeSystemList) => {
          return empty(row.version);
        },
      },
      {
        label: t('encoding.scheme.sourceName', '来源'),
        prop: 'sourceName',
        editable: false,
        render: (row: Code.ResQueryCodeSystemList) => {
          return empty(row.sourceName);
        },
      },
      {
        label: t('encoding.scheme.hisExistsFlag', 'HIS存在'),
        prop: 'hisExistsFlag',
        render: (row: Code.ResQueryCodeSystemList) => {
          return row.hisExistsFlag === ENABLED_FLAG.YES ? (
            <span>{t('global:yes')}</span>
          ) : (
            <span>{t('global:no')}</span>
          );
        },
        editable: false,
      },
      {
        label: t('encoding.scheme.publishDate', '发布时间'),
        prop: 'publishDate',
        editable: false,
        minWidth: 200,
        render: (row: Code.ResQueryCodeSystemList) => {
          return empty(row.publishDate);
        },
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        render: (row: Code.ResQueryCodeSystemList) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => {
                row.enabledFlag =
                  row.enabledFlag === ENABLED_FLAG.YES
                    ? ENABLED_FLAG.NO
                    : ENABLED_FLAG.YES;
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
        fixed: 'right',
        width: 100,
      },
    ];
  }

  const options = computed(() => {
    return [
      {
        label: t('global:all'),
        value: ALL_HIS_EXISTS_FLAG,
      },
      {
        label: t('global:yes'),
        value: 1,
      },
      {
        label: t('global:no'),
        value: 0,
      },
    ];
  });

  type State = {
    keyWord: string;
    version: string;
    hisExistsFlag: number;
  };

  const tableRef = ref();
  let selections = ref();
  let state = reactive<State>({
    keyWord: '',
    version: '',
    hisExistsFlag: ALL_HIS_EXISTS_FLAG,
  });
  const emits = defineEmits<{
    success: [];
  }>();
  async function fetchData(params: Omit<Code.PageInfo, 'total'>) {
    let [, result] = await queryExCodeSystemListByExample({
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
      keyWord: state.keyWord,
      version: state.version,
    });
    if (result?.success) {
      return {
        total: result.total,
        data: result.data.map((item: Code.ResQueryCodeSystemList) => {
          //     item.dataSetList = item.dataSetList.slice(0, 200);
          return {
            ...item,
            id: Math.random(),
            children: item.dataSetList.map((cur) => ({
              ...cur,
              parentId: item.codeSystemId,
              id: Math.random(),
            })),
          };
        }),
      };
    }
  }

  function submit() {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<[never, unknown]>(async (resolve, reject) => {
      if (!selections.value) {
        return resolve([] as unknown as [never, unknown]);
      }
      let data = selections.value.sort((item: { parentId: any }) =>
        item.parentId ? 1 : -1,
      ); //父级放前面
      let codeSystemList: Code.ResQueryCodeSystemList[] = [];
      data.forEach((item: Code.ResQueryCodeSystemList) => {
        if (item.parentId) {
          codeSystemList.forEach((data) => {
            if (data.codeSystemId === item.parentId) {
              data.dataSetList.push({
                ...item,
                parentId: undefined,
              });
            }
          });
        } else {
          let cur = Object.assign({}, item, { dataSetList: [] });
          Reflect.deleteProperty(cur, 'codeSystemList');
          Reflect.deleteProperty(cur, 'children');
          codeSystemList.push(cur);
        }
      });
      let [, result] = await saveCodeSystemDataSets({
        codeSystemList,
      });
      if (result?.success) {
        ElMessage({
          type: 'success',
          message: t('global:update.success'),
        });
        resolve([] as unknown as [never, unknown]);
      } else {
        reject(['', new Error('参数错误')]);
      }
    });
  }

  function handleSearch() {
    tableRef?.value.fetchList();
  }

  function handleEnter() {
    tableRef?.value.fetchList();
  }

  function select(val: AnyObject[]) {
    let parentIds = val.filter((item) => !item.parentId);
    let childs = val.filter((item) => item.parentId);
    if (childs.length) {
      childs.forEach((item) => {
        let found = parentIds.find((cur: AnyObject) => {
          return cur.codeSystemId === item.parentId;
        });
        if (!found) {
          let parent = tableRef.value.tableData.find((cur: AnyObject) => {
            return cur.codeSystemId === item.parentId;
          });
          if (parent) {
            val = [...val, parent];
            tableRef.value!.proTableRef.toggleRowSelection(parent, true);
            (parent?.children || []).forEach((cur: AnyObject) => {
              let found = childs.find(
                (item) => cur.dataValueId === item.dataValueId,
              );
              if (!found) {
                tableRef.value!.proTableRef.toggleRowSelection(cur, false);
              }
            });
          }
        }
      });
    }
    selections.value = val;
  }

  function handleInput(value: string) {
    // 允许数字和一个小数点，并限制长度为10位以内
    let sanitizedValue = value.replace(/[^0-9.]/g, ''); // 移除非数字和非小数点字符
    const decimalIndex = sanitizedValue.indexOf('.');

    // 确保只有一个小数点
    if (decimalIndex !== -1) {
      sanitizedValue =
        sanitizedValue.slice(0, decimalIndex + 1) +
        sanitizedValue.slice(decimalIndex + 1).replace(/\./g, '');
    }

    // 限制长度为10位（包括小数点）
    state.version = sanitizedValue.slice(0, 10);
  }

  const tableColumns = computed(() => getColumns());

  function clear() {
    selections.value = [];
    state.hisExistsFlag = ALL_HIS_EXISTS_FLAG;
    state.keyWord = '';
    state.version = '';
  }

  defineExpose({
    submit,
  });
</script>
<template>
  <ProDialog
    :confirm-fn="submit"
    :include-button="true"
    @success="emits('success')"
    @close="clear"
    :width="1100"
    destroy-on-close
    button-class="mr-3"
    :disabled="!isCloudEnv"
    :title="$t('update.encoding.scheme', '编码体系更新')"
    :button-text="$t('global:update')"
  >
    <div style="height: 425px" class="flex flex-col overflow-hidden">
      <div class="mb-4">
        {{ $t('encoding.scheme.hisExistsFlag', ' HIS 存在') }}
        <el-select
          v-model="state.hisExistsFlag"
          class="ml-2 mr-5 w-48"
          placeholder="Select"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        {{ $t('encoding.scheme.version', '版本号') }}
        <el-input
          class="ml-2 mr-5 w-48"
          v-model="state.version"
          @input="handleInput"
          maxlength="10"
          @keydown.enter="handleEnter"
          :placeholder="
            t('global:placeholder.input.template', {
              content: t('encoding.scheme.version', '版本号'),
            })
          "
        />

        {{ $t('encoding.scheme.content', '编码体系内容') }}
        <el-input
          class="ml-2 mr-5 w-48"
          v-model="state.keyWord"
          @keydown.enter="handleEnter"
          :placeholder="
            t('global:placeholder.input.template', {
              content: $t('encoding.scheme.content', '编码体系内容'),
            })
          "
        />
        <el-button type="primary" @click="handleSearch">
          {{ $t('global:search') }}
        </el-button>
      </div>
      <pro-table
        :columns="tableColumns"
        ref="tableRef"
        :filter-obj="{
          hisExistsFlag:
            state.hisExistsFlag === ALL_HIS_EXISTS_FLAG
              ? ''
              : state.hisExistsFlag,
        }"
        @select="select"
        @select-all="select"
        :default-query="false"
        row-key="id"
        :pagination="true"
        :page-sizes="[10, 100, 1000]"
        :fetch-data="fetchData"
      /></div
  ></ProDialog>
</template>

<style scoped>
  /* 你的样式代码 */
</style>
