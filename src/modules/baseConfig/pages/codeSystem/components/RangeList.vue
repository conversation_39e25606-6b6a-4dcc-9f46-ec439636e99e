<script setup lang="tsx">
  import { Search } from '@element-sun/icons-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { ref, watch, onBeforeMount, reactive, computed } from 'vue';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { queryDataSetListByExample } from '@/modules/baseConfig/api/code';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    updateDataSetEnabledFlagById,
    updateDataSetSortByIds,
  } from '@/modules/baseConfig/api/code';
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import {
    Title,
    ProTable,
    type AnyObject,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  function getColumns() {
    return [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        editable: false,
        render: (row: object, index: number) => <>{index + 1}</>,
      },
      {
        label: t('global:code'),
        prop: 'dataValueNo',
        editable: false,
        supportCopyAndTips: true,
      },
      {
        label: t('global:name'),
        prop: 'dataValueCnName',
        editable: false,
        supportCopyAndTips: true,
      },
      {
        label: t('global:secondName'),
        prop: 'dataValue2ndName',
        editable: false,
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        render: (row: Code.CodeSystemInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'action',
        fixed: 'right',
        render: () => {
          return (
            <el-button link type="primary" disabled onClick={() => {}}>
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ];
  }
  const columns = computed(() => getColumns());
  type Props = {
    selectSchemeRow: Code.CodeSystemInfo | null;
    menuList: { value: string; label: string }[];
  };
  const state = reactive({
    loading: false,
  });
  const props = defineProps<Props>();
  const tableData = ref<Code.CodeSystemInfo[]>([]);
  const tableRef = ref();
  function handleEnableSwitch(row: Code.CodeSystemInfo) {
    return new Promise<void>((resolve, reject) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.dataValueCnName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          let [, result] = await updateDataSetEnabledFlagById({
            enabledFlag:
              row.enabledFlag === ENABLED_FLAG.YES
                ? ENABLED_FLAG.NO
                : ENABLED_FLAG.YES,
            dataValueId: row.dataValueId,
          });
          if (result?.success) {
            resolve();
            handleEnter();
            ElMessage({
              type: 'success',
              message:
                row.enabledFlag === ENABLED_FLAG.YES
                  ? t('global:disabled.success')
                  : t('global:enabled.success'),
            });
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  }

  const keyWord = ref<string>('');
  const selections = ref<Code.CodeSystemInfo[]>([]);
  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_DATA_SETS,
      bisIds: selections.value.map((item) => item.dataValueId),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  onBeforeMount(() => {
    if (props.selectSchemeRow) {
      fetchData();
    }
  });

  watch(
    () => props.selectSchemeRow,
    () => {
      fetchData();
    },
  );

  async function fetchData() {
    if (!props.selectSchemeRow) return;
    state.loading = true;
    let [, result] = await queryDataSetListByExample({
      keyWord: keyWord.value,
      codeSystemIds: [props.selectSchemeRow.codeSystemId],
      codeSystemNos: [props.selectSchemeRow.codeSystemNo],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: 0,
    });
    state.loading = false;
    if (result?.success) {
      let { data } = result;
      tableData.value = data;
    }
  }

  function handleEnter() {
    fetchData();
  }
  let inputChange = debounce(handleEnter, 500);
  function handleSelectChange(value: AnyObject[]) {
    selections.value = value as Code.CodeSystemInfo[];
  }

  async function handleSortEnd(data: AnyObject[]) {
    let [, result] = await updateDataSetSortByIds({
      dataSetSortList: data.map((item, index) => {
        return {
          dataValueId: item.dataValueId,
          sort: index + 1,
        };
      }),
    });
    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      handleEnter();
    }
  }
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <Title :title="$t('range.list', '值域列表')">
      <span>
        <el-input
          v-model="keyWord"
          class="mr-3 w-60"
          :placeholder="t('global:placeholder.keyword')"
          @input="inputChange"
          @keydown.enter="handleEnter"
          :suffix-icon="Search"
        />
        <el-dropdown>
          <el-button type="primary" :disabled="!selections.length">
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown v-if="selections.length && props.menuList.length">
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in props.menuList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span></Title
    >
    <pro-table
      :loading="state.loading"
      :data="tableData"
      draggable
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
      ref="tableRef"
      :columns="columns"
      row-key="dataValueId"
    />
  </div>
</template>
