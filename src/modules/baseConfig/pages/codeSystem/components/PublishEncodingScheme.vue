<script setup lang="tsx">
  import { computed, ref, onMounted } from 'vue'; // Import onMounted
  import { ElMessage } from 'element-sun';
  import { ArrowDown } from '@element-sun/icons-vue'; // Assuming ArrowDown is needed
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();

  import {
    querySystemListByExample, // Import system query API
    releaseCodeSystemByIds, // Import publish API
  } from '@/modules/baseConfig/api/code';
  import { ENABLED_FLAG } from '@/utils/constant'; // Import constant

  // Define props to receive selected encoding schemes
  const props = defineProps<{
    selections: Code.CodeSystemInfo[];
  }>();

  // Define emitted events
  const emit = defineEmits(['success']);

  // State for the Publish feature
  const systemList = ref<{ value: string; label: string; mdmApiUrl: string }[]>(
    [],
  );
  const selectedSystems = ref<{ value: string; label: string }[]>([]);

  // Fetch and filter systems
  async function fetchSystems() {
    let [, result] = await querySystemListByExample({
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (result?.success) {
      const enabledSystemsWithMdm = (result.data || [])
        .filter((system) => system.mdmApiUrl) // Filter for systems with mdmApiUrl
        .map((system) => ({
          // Map to { value, label } format
          value: system.sysId, // Assuming systemId is the identifier
          label: system.sysName, // Assuming systemName is the display name
          mdmApiUrl: system.mdmApiUrl,
        }));
      systemList.value = enabledSystemsWithMdm;
      selectedSystems.value = [...enabledSystemsWithMdm]; // Select all by default
    } else {
      systemList.value = [];
      selectedSystems.value = [];
    }
  }

  // Handle system selection in the dropdown
  function handleSystemSelect(system: { value: string; label: string }) {
    const index = selectedSystems.value.findIndex(
      (s) => s.value === system.value,
    );
    if (index > -1) {
      selectedSystems.value.splice(index, 1); // Deselect
    } else {
      selectedSystems.value.push(system); // Select
    }
  }

  // Handle the Publish action
  async function clickPublishDropdown() {
    const codeSystemIds = props.selections.map((item) => item.codeSystemId);
    const sysIds = selectedSystems.value.map((item) => item.value);

    let [, result] = await releaseCodeSystemByIds({ codeSystemIds, sysIds });

    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('encoding.scheme.publish.success', '发布成功'),
      });
      emit('success'); // Emit success event
    }
  }

  // Computed property to control Publish button disabled state
  const publishButtonDisabled = computed(() => {
    return props.selections.length === 0 || selectedSystems.value.length === 0;
  });

  // Computed property for the text displayed on the button
  const buttonText = computed(() => {
    const baseText = t('encoding.scheme.publish', '发布');
    const count = selectedSystems.value.length;
    return `${baseText} (${count})`;
  });

  // Fetch systems when the component is mounted
  onMounted(() => {
    fetchSystems();
  });
</script>
<template>
  <!-- New Publish Dropdown Button -->
  <el-button
    class="ml-3"
    v-permission="'ZYGL-FB'"
    type="primary"
    :disabled="publishButtonDisabled"
    @click="clickPublishDropdown"
  >
    {{ buttonText }}
    <el-dropdown class="ml-1" :hide-on-click="false">
      <el-icon @click.stop class="text-white"><arrow-down /></el-icon>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="system in systemList"
            :key="system.value"
            @click.stop="handleSystemSelect(system)"
          >
            <el-checkbox
              :model-value="
                selectedSystems.some((s) => s.value === system.value)
              "
            >
              {{ system.label }}
            </el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-button>
  <!-- End New Publish Dropdown Button -->
</template>
