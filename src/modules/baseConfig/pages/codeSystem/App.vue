<script setup lang="ts" name="codeManage">
  import { reactive } from 'vue';
  import EncodingSchemeList from './components/EncodingSchemeList.vue';
  import RangeList from './components/RangeList.vue';
  import SchemeInfo from './components/SchemeInfo.vue';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  export type AppState = {
    showSchemeInfo: boolean;
    selectSchemeRow: Code.CodeSystemInfo | null;
  };

  const dmlList = useGetDMLList();
  const appState = reactive<AppState>({
    showSchemeInfo: false,
    selectSchemeRow: null,
  });

  function setSelectSchemeRow(row: Code.CodeSystemInfo) {
    appState.selectSchemeRow = row;
  }
</script>

<template>
  <el-row class="p-box h-full">
    <el-col :span="12" class="h-full border-r pr-5">
      <EncodingSchemeList
        :set-select-scheme-row="setSelectSchemeRow"
        :select-scheme-row="appState.selectSchemeRow"
        :menu-list="dmlList"
      />
    </el-col>
    <el-col class="flex h-full flex-col overflow-hidden pl-5" :span="12">
      <SchemeInfo :select-scheme-row="appState.selectSchemeRow" />
      <RangeList
        :select-scheme-row="appState.selectSchemeRow"
        :menu-list="dmlList"
    /></el-col>
  </el-row>
</template>

<style>
  micro-app-body {
    height: 100%;
  }
</style>
