<script setup lang="ts" name="operationDialog">
  import { useMenuList } from '@/modules/finance/pages/payWay/hooks/useMenuList';
  import { useGetPrinter } from '../hooks/usePrinter';
  import { usePrintInterface } from '../hooks/useInterface';
  import { saveReceiptXPrinter, openReportFile } from '@/api/common';
  import { generateUUID, cloneDeep } from '@sun-toolkit/shared';
  import { SaveReceiptXPrinterResParams } from '@/api/types';
  import { ref, nextTick, watch, computed } from 'vue';
  import { useReport } from '../hooks/useReport';
  import {
    useTemplateRuleTableConfig,
    useMenuXReceiptTableConfig,
  } from '../config/useTableColumnsConfig';
  import {
    addReceipt,
    updateReceiptById,
  } from '@/modules/baseConfig/api/receipt';
  import {
    useBaseInfoFormConfig,
    useTemplateRuleFormConfig,
    useMenuXReceiptFormConfig,
  } from '../config/useDialogFormConfig';
  import {
    FLAG,
    INTERFACE_TYPE_CODE,
    DATA_SOURCE_TYPE_CODE,
    RECEIPT_SPLIT_TYPE_CODE,
    RECEIPT_PRINT_RULE_CODE,
    RECEIPT_PRINT_CATEG_CODE,
    RECEIPT_TEMPLATE_RULE_CODE,
  } from '@/utils/constant';
  import {
    Title,
    ProForm,
    ProTable,
    ProDialog,
    TableRef,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';

  export type baseInfoFormType = {
    receiptName: string | undefined;
    receipt2ndName?: string | undefined;
    receiptExtName?: string | undefined;
    receiptSplitTypeCode: string | undefined;
    dataSourceTypeCode: string | undefined;
    spName: string | undefined;
    invoiceInterfaceId: string | undefined;
    printInterfaceId: string | undefined;
    printQty: number | undefined;
    printSetupEnabledFlag: FLAG;
    receiptTemplateRuleCode: string | undefined;
    receiptPrintRuleCode: string | undefined;
    redPrintCategCode: string | undefined;
    receiptPrintCategCode: string | undefined;
  };

  const props = defineProps<{
    dialogTitle: string;
    rowValue: Receipt.ReceiptReqItem;
  }>();

  const emit = defineEmits(['success']);
  const { isLocalPrint } = useAppConfigData([MAIN_APP_CONFIG.IS_LOCAL_PRINT]);

  const { queryInterfaceList } = usePrintInterface();
  const { reportList, getReportList } = useReport();
  const {
    printerList,
    receiptXPrinterList,
    getPrinter,
    getReceiptXPrinterList,
  } = useGetPrinter();
  const { getMenuList, menuFilterList } = useMenuList();

  const rowValueCon = computed(() => props.rowValue);
  const isLocalPath = computed(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return isLocalPrint && window?.Api?.PluginMain;
  });

  const loading = ref(false);
  const radioSelect = ref<string>();
  const baseInfoFormModel = ref<baseInfoFormType>(); //基本信息
  const templateRuleTableData = ref<Receipt.ChooseReceiptTemplateRuleItem[]>(
    [],
  ); // 单据模板规则
  const menuXReceiptTableData = ref<Receipt.MenuXReceiptItem[]>([]); // 打印列表
  const invoiceInterfaceList = ref<Interface.InterfaceInfo[]>([]); //票据接口
  const printInterfaceList = ref<Interface.InterfaceInfo[]>([]); //打印接口

  const dialogRef = ref(); // 弹窗Ref
  const baseInfoFormRef = ref(); //基本信息ref
  const templateRuleFormRef = ref(); //模板规则Ref
  const menuXReceiptFormRef = ref(); //菜单单据
  const templateRuleTableRef = ref<TableRef>(); //模板规则table
  const menuXReceiptTableRef = ref<TableRef>(); //菜单单据table

  // 打开弹窗
  const openDialog = async () => {
    nextTick(async () => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value?.open();
      baseInfoFormModel.value = {
        receiptName: rowValue?.receiptName ?? undefined,
        receipt2ndName: rowValue?.receipt2ndName ?? undefined,
        receiptExtName: rowValue?.receiptExtName ?? undefined,
        receiptSplitTypeCode:
          rowValue?.receiptSplitTypeCode ?? RECEIPT_SPLIT_TYPE_CODE.NO_SPLIT,
        dataSourceTypeCode:
          rowValue?.dataSourceTypeCode ?? DATA_SOURCE_TYPE_CODE.STORAGE,
        spName: rowValue?.spName ?? undefined,
        invoiceInterfaceId: rowValue?.invoiceInterfaceId ?? undefined,
        printInterfaceId: rowValue?.printInterfaceId ?? undefined,
        printQty: rowValue?.printQty ?? 1,
        printSetupEnabledFlag: rowValue?.printSetupEnabledFlag ?? FLAG.YES,
        receiptTemplateRuleCode:
          rowValue?.receiptTemplateRuleCode ??
          RECEIPT_TEMPLATE_RULE_CODE.FREE_SELECT,
        receiptPrintRuleCode:
          rowValue?.receiptPrintRuleCode ?? RECEIPT_PRINT_RULE_CODE.GENERAL,
        redPrintCategCode:
          rowValue?.redPrintCategCode ?? RECEIPT_PRINT_CATEG_CODE.PRINT,
        receiptPrintCategCode:
          rowValue?.receiptPrintCategCode ?? RECEIPT_PRINT_CATEG_CODE.PRINT,
      };
      templateRuleTableData.value =
        rowValue.chooseReceiptTemplateRuleList ?? [];
      (
        templateRuleTableData.value as unknown as (Receipt.ChooseReceiptTemplateRuleItem & {
          rowKey: string;
        })[]
      ).map((item) => {
        item.rowKey = item.receiptTemplateRuleId ?? item.rowKey;
        return item;
      });
      menuXReceiptTableData.value = rowValue.menuXReceiptList ?? [];
      const templateRuleIds = templateRuleTableData.value.map(
        (item) => item.receiptTemplateRuleId,
      );
      if (!isLocalPath.value) {
        return;
      }
      if (rowValue?.receiptId) {
        // 获取单据打印机列表
        await getReceiptXPrinterList({
          receiptTemplateRuleIds: templateRuleIds,
        });
      }
      templateRuleTableData.value.map((item) => {
        receiptXPrinterList.value.forEach((im) => {
          if (im.receiptTemplateRuleId === item.receiptTemplateRuleId) {
            (
              item as unknown as {
                printerName: string;
              }
            ).printerName = im.printerName;
          }
        });
        (
          item as unknown as {
            editable: boolean;
          }
        ).editable = true;
        return item;
      });
    });
    await getMenuList();
    await getInvoiceInterface();
    await getPrintInterface();

    if (isLocalPath.value) {
      await getPrinter();
    } else {
      await getReportList();
    }
  };

  // 票据接口
  const getInvoiceInterface = async (keyWord?: string) => {
    const invoiceInterface = await queryInterfaceList({
      keyWord: keyWord,
      interfaceTypeCode: INTERFACE_TYPE_CODE.RECEIPT,
    });
    invoiceInterfaceList.value = invoiceInterface as Interface.InterfaceInfo[];
  };

  // 打印接口
  const getPrintInterface = async (keyWord?: string) => {
    const printInterface = await queryInterfaceList({
      keyWord: keyWord,
      interfaceTypeCode: INTERFACE_TYPE_CODE.PRINT,
    });
    printInterfaceList.value = printInterface as Interface.InterfaceInfo[];
  };

  // 新增模板规则
  const addTemplateRule = async () => {
    await templateRuleAddItem({
      rowKey: generateUUID(),
      editable: true,
      enabledFlag: FLAG.YES,
    } as unknown as Receipt.ChooseReceiptTemplateRuleItem & {
      editable: boolean;
    });
    if (templateRuleTableData.value.length === 1) {
      radioSelect.value = (
        templateRuleTableData
          .value[0] as unknown as Receipt.ChooseReceiptTemplateRuleItem & {
          rowKey: string;
        }
      ).rowKey;
      templateRuleTableData.value[0].defaultFlag = FLAG.YES;
    }
  };

  // 新增打印规则
  const addMenuXReceipt = async () => {
    menuXReceiptAddItem({
      editable: true,
      enabledFlag: FLAG.YES,
      receiptPrintCategCode: RECEIPT_PRINT_CATEG_CODE.PRINT,
    } as unknown as Receipt.MenuXReceiptItem & {
      editable: boolean;
    });
  };

  // 选择文件
  const selectFile = async (row: Receipt.ChooseReceiptTemplateRuleItem) => {
    const [, res] = await openReportFile();
    if (res?.success) {
      row.receiptTemplateName = res?.data?.fileName;
    }
    // // 创建隐藏的文件输入框
    // const input = document.createElement('input');
    // input.type = 'file';
    // input.accept = '*'; // 允许选择任何文件类型，可根据需求调整

    // // 监听文件选择事件
    // input.onchange = (event: Event) => {
    //   const target = event.target as HTMLInputElement;
    //   const file = target.files?.[0]; // 获取选中的文件
    //   if (file) {
    //     row.receiptTemplateName = file.name; // 更新文件名
    //     // ElMessage.success(`文件 "${file.name}" 已选择`);
    //   }
    // };

    // // 触发文件选择
    // input.click();
  };

  // 提交
  const handleConfirmSubmit = async () => {
    let arr = [];
    if (
      baseInfoFormModel.value?.receiptPrintRuleCode ===
      RECEIPT_PRINT_RULE_CODE.MENU
    ) {
      arr = [
        baseInfoFormRef.value?.ref?.validate(),
        templateRuleFormRef.value?.ref?.validate(),
        templateRuleTableRef.value?.formRef?.validate(),
        menuXReceiptFormRef.value?.ref?.validate(),
        menuXReceiptTableRef.value?.formRef?.validate(),
      ];
    } else {
      arr = [
        baseInfoFormRef.value?.ref?.validate(),
        templateRuleFormRef.value?.ref?.validate(),
        templateRuleTableRef.value?.formRef?.validate(),
        menuXReceiptFormRef.value?.ref?.validate(),
      ];
    }
    await Promise.all(arr);
    templateRuleTableData.value.map((item) => {
      if (
        item.receiptTemplateRuleId === radioSelect.value ||
        (
          item as unknown as {
            rowKey: string;
          }
        ).rowKey === radioSelect.value
      ) {
        item.defaultFlag = FLAG.YES;
      } else {
        item.defaultFlag = FLAG.NO;
      }
      return item;
    });
    menuXReceiptTableData.value.map((item, index) => {
      item.sort = index + 1;
      return item;
    });

    const params = {
      receiptId: props.rowValue.receiptId ?? undefined,
      receiptDataSourceId: props.rowValue.receiptDataSourceId ?? undefined,
      receiptName: baseInfoFormModel.value?.receiptName as string,
      receipt2ndName: baseInfoFormModel.value?.receipt2ndName,
      receiptExtName: baseInfoFormModel.value?.receiptExtName,
      dataSourceTypeCode: baseInfoFormModel.value?.dataSourceTypeCode as string,
      receiptSplitTypeCode: baseInfoFormModel.value
        ?.receiptSplitTypeCode as string,
      printQty: baseInfoFormModel.value?.printQty as number,
      receiptPrintRuleCode: baseInfoFormModel.value
        ?.receiptPrintRuleCode as string,
      receiptPrintCategCode: baseInfoFormModel.value
        ?.receiptPrintCategCode as string,
      redPrintCategCode: baseInfoFormModel.value?.redPrintCategCode as string,
      receiptTemplateRuleCode: baseInfoFormModel.value
        ?.receiptTemplateRuleCode as string,
      printSetupEnabledFlag: baseInfoFormModel.value
        ?.printSetupEnabledFlag as FLAG,
      invoiceInterfaceId: baseInfoFormModel.value?.invoiceInterfaceId,
      printInterfaceId: baseInfoFormModel.value?.printInterfaceId,
      spName: baseInfoFormModel.value?.spName as string,
      chooseReceiptTemplateRuleList: templateRuleTableData.value,
      menuXReceiptList: menuXReceiptTableData.value,
    };
    if (props.rowValue?.receiptId) {
      return await updateReceiptById(params);
    } else {
      return await addReceipt(params);
    }
  };

  // dialog确认方法
  const handleConfirm = async () => {
    const [error, res] = await handleConfirmSubmit();
    if (res?.success && isLocalPath.value) {
      const result = res.data?.receiptTemplateRuleList ?? [];
      result.map((item) => {
        templateRuleTableData.value.forEach((im) => {
          if (im.receiptTemplateName === item.receiptTemplateName) {
            (
              item as unknown as {
                printerName: string;
              }
            ).printerName = (
              im as unknown as {
                printerName: string;
              }
            ).printerName;
            (
              item as unknown as {
                templateName: string;
              }
            ).templateName = im.receiptTemplateName;
          }
          return item;
        });
      });
      const [err, response] = await saveReceiptXPrinter({
        receiptTemplateList: result,
      } as unknown as SaveReceiptXPrinterResParams);
      if (response?.success) {
        dialogRef.value?.close();
      }
      return [err, response];
    }
    return [error, res];
  };

  // 清空数据
  const handleReset = async () => {
    baseInfoFormModel.value = {} as baseInfoFormType;
    templateRuleTableData.value = [];
    menuXReceiptTableData.value = [];
  };

  // 表单配置
  const baseInfoFormConfig = useBaseInfoFormConfig({
    getInvoiceInterface: getInvoiceInterface,
    getPrintInterface: getPrintInterface,
    invoiceInterfaceList: invoiceInterfaceList,
    printInterfaceList: printInterfaceList,
  });
  const templateRuleFormConfig = useTemplateRuleFormConfig(); //模板规则form
  const menuXReceiptFormConfig = useMenuXReceiptFormConfig({
    baseInfoFormModel: baseInfoFormModel,
    rowValue: rowValueCon,
  }); //打印规则form

  /** 模板规则table */
  const {
    tableColumns: templateRuleTableConfig,
    addItem: templateRuleAddItem,
  } = useTemplateRuleTableConfig({
    tableRef: templateRuleTableRef,
    data: templateRuleTableData,
    radioSelect: radioSelect,
    id: 'receiptTemplateRuleId',
    selectFile: selectFile,
    printerList: printerList,
    isLocalPrint: isLocalPath.value as boolean,
    reportList: reportList,
  });

  /** 打印规则table */
  const {
    tableColumns: menuXReceiptTableConfig,
    addItem: menuXReceiptAddItem,
  } = useMenuXReceiptTableConfig({
    tableRef: menuXReceiptTableRef,
    data: menuXReceiptTableData,
    id: 'menuXReceiptId',
    menuFilterData: menuFilterList,
    getMenuList: getMenuList,
  });

  watch(
    () => templateRuleTableData.value.length,
    async () => {
      const obj = templateRuleTableData.value.find(
        (item) => item.defaultFlag === FLAG.YES,
      );
      if (templateRuleTableData.value.length > 0 && !obj) {
        radioSelect.value = (
          templateRuleTableData
            .value[0] as unknown as Receipt.ChooseReceiptTemplateRuleItem & {
            rowKey: string;
          }
        ).rowKey;
      }
      if (obj) {
        radioSelect.value =
          obj.receiptTemplateRuleId ??
          (
            obj as unknown as {
              rowKey: string;
            }
          ).rowKey;
      }
    },
  );

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    class="w-4/5"
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirm() as unknown as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
          handleReset();
        });
      }
    "
  >
    <Title :title="$t('baseInfo', '基本信息')" class="mb-2" />
    <ProForm
      ref="baseInfoFormRef"
      :column="2"
      :data="baseInfoFormConfig"
      v-model="baseInfoFormModel"
    />
    <Title :title="$t('templateRules', '模板规则')" class="mb-2" />
    <div class="flex justify-between">
      <ProForm
        ref="templateRuleFormRef"
        layout-mode="inline"
        :data="templateRuleFormConfig"
        v-model="baseInfoFormModel"
      />
      <el-button type="primary" @click="addTemplateRule">{{
        $t('global:add')
      }}</el-button>
    </div>
    <ProTable
      :max-height="180"
      ref="templateRuleTableRef"
      :editable="true"
      :data="templateRuleTableData"
      :columns="templateRuleTableConfig"
      :loading="loading"
      row-key="rowKey"
    />
    <Title :title="$t('printRules', '打印规则')" class="mb-2 mt-3" />
    <div class="flex justify-between">
      <ProForm
        ref="menuXReceiptFormRef"
        layout-mode="inline"
        :data="menuXReceiptFormConfig"
        v-model="baseInfoFormModel"
      />
      <el-button
        v-if="
          baseInfoFormModel?.receiptPrintRuleCode ===
          RECEIPT_PRINT_RULE_CODE.MENU
        "
        type="primary"
        @click="addMenuXReceipt"
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      v-if="
        baseInfoFormModel?.receiptPrintRuleCode === RECEIPT_PRINT_RULE_CODE.MENU
      "
      :max-height="150"
      ref="menuXReceiptTableRef"
      :editable="true"
      :data="menuXReceiptTableData"
      :columns="menuXReceiptTableConfig"
      :loading="loading"
      row-key="menuXReceiptId"
    />
  </ProDialog>
</template>
