<script setup lang="ts" name="menuDialog">
  import { useMenuList } from '@/modules/finance/pages/payWay/hooks/useMenuList';
  import { ref, nextTick } from 'vue';
  import { useMenuReceiptFormConfig } from '../config/useDialogFormConfig';
  import { useMenuXReceiptTableConfigNotOperation } from '../config/useTableColumnsConfig';
  import {
    queryMenuXReceiptBySysMenuId,
    saveMenuXReceipt,
  } from '@/modules/baseConfig/api/receipt';
  import { Title, ProForm, ProTable, ProDialog } from 'sun-biz';

  const emit = defineEmits(['success']);

  const loading = ref(false);
  const menuXReceiptList = ref<Receipt.MenuXReceiptListReqItem[]>([]);
  const menuFormModel = ref<{
    menuId: string | undefined;
  }>({
    menuId: undefined,
  });

  const dialogRef = ref();
  const printRuleTableRef = ref();

  const { getMenuList, menuFilterList } = useMenuList();

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      dialogRef.value?.open();
    });
    await getMenuList();

    menuFormModel.value = {
      menuId: (menuFilterList.value ?? [])[0].menuId,
    };
    await getMenuXReceipt();
  };

  const getMenuXReceipt = async (
    params: Receipt.MenuXReceiptReqParams = {
      menuId: menuFormModel.value?.menuId as string,
    },
  ) => {
    menuFormModel.value = {
      ...menuFormModel.value,
      ...params,
    };
    loading.value = true;
    const [, res] = await queryMenuXReceiptBySysMenuId({
      ...menuFormModel.value,
    } as unknown as Receipt.MenuXReceiptReqParams);
    loading.value = false;
    if (res?.success) {
      menuXReceiptList.value = res.data?.menuXReceiptList ?? [];
      menuXReceiptList.value.map((item) => {
        (
          item as unknown as {
            editable: boolean;
          }
        ).editable = true;
        return item;
      });
    }
  };

  // 提交
  const handleConfirmSubmit = async () => {
    await printRuleTableRef.value?.formRef?.validate();
    return await saveMenuXReceipt({
      ...menuFormModel.value,
      menuXReceiptList: menuXReceiptList.value,
    } as unknown as Receipt.SaveMenuXReceiptReqParams);
  };

  const menuReceiptFormConfig = useMenuReceiptFormConfig({
    menuList: menuFilterList,
  });
  const { tableColumns } = useMenuXReceiptTableConfigNotOperation();

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    class="w-2/4"
    ref="dialogRef"
    :title="$t('receipt.menu', '菜单的单据')"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="
      () => handleConfirmSubmit() as unknown as Promise<[never, unknown]>
    "
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <div>
      <ProForm
        ref="menuFormRef"
        layout-mode="inline"
        :data="menuReceiptFormConfig"
        v-model="menuFormModel"
        @model-change="
          (data: Receipt.MenuXReceiptReqParams) =>
            getMenuXReceipt(data as Receipt.MenuXReceiptReqParams)
        "
      />
      <Title :title="$t('printRules', '打印规则')" class="mb-2" />
      <ProTable
        :max-height="300"
        ref="printRuleTableRef"
        :editable="true"
        :data="menuXReceiptList"
        :columns="tableColumns"
        :loading="loading"
        row-key="rowKey"
      />
    </div>
  </ProDialog>
</template>
