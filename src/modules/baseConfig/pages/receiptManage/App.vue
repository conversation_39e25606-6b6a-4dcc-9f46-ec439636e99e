<script setup lang="ts" name="receiptManage">
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from './config/useSearchFormConfig.ts';
  import { useTableColumnsConfig } from './config/useTableColumnsConfig.tsx';
  import { queryReceiptByExample } from '@/modules/baseConfig/api/receipt.ts';
  import { ref, computed, onMounted, nextTick } from 'vue';
  import {
    print,
    Title,
    ProForm,
    ProTable,
    DmlButton,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import {
    FLAG,
    PRINT_TYPE,
    BIZ_ID_TYPE_CODE,
    FORM_OPERATION_TYPE,
  } from '@/utils/constant.ts';
  import MenuDialog from './components/menuDialog.vue';
  import OperationDialog from './components/operationDialog.vue';

  type searchFormType = {
    keyWord?: string;
  };

  const { t } = useTranslation();
  const { isCloudEnv, menuId } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.MENU_ID,
  ]);

  const loading = ref<boolean>(false); // 加载状态
  const rowValue = ref<Receipt.ReceiptReqItem>(); //编辑行数据
  const selectTableData = ref<Receipt.ReceiptReqItem[]>([]);
  const searchFormModel = ref<searchFormType>({
    keyWord: undefined,
  }); // 检索条件数据
  const tableData = ref<Receipt.ReceiptReqItem[]>([]); // 表格数据

  const menuDialogRef = ref(); //菜单dialog
  const tableColumnsRef = ref(); //表格
  const operationDialogRef = ref(); //操作dialog

  const dialogTitle = computed(() => {
    return rowValue.value?.receiptId
      ? t('receipt.edit', '编辑单据')
      : t('receipt.add', '新增单据');
  });

  const bizData = computed(() => {
    const list = selectTableData.value.map((im) => {
      return im.receiptId;
    });
    return list ?? [];
  });

  // 查询单据列表
  const queryReceiptList = async (params: Receipt.ReceiptReqParams = {}) => {
    searchFormModel.value = {
      ...searchFormModel.value,
      ...params,
    };
    loading.value = true;
    const [, res] = await queryReceiptByExample(searchFormModel.value);
    loading.value = false;
    if (res?.success) {
      tableData.value = res?.data;
    }
  };

  // 菜单的单据弹窗
  const openMenuReceipt = () => {
    menuDialogRef.value.open();
  };

  // 新增编辑操作的弹窗
  const operationFn = async (row: Receipt.ReceiptReqItem) => {
    rowValue.value = row;
    nextTick(() => {
      operationDialogRef.value.open();
    });
  };

  // 选中行设置
  const selectionChange = (val: Receipt.ReceiptReqItem[]) => {
    selectTableData.value = val;
  };

  // 设计操作方法
  const operationDesignFn = async (row: Receipt.ReceiptReqItem) => {
    rowValue.value = row;
    nextTick(async () => {
      await print({
        bizIds: ['0'],
        menuId: menuId,
        designFlag: FLAG.YES,
        bizIdTypeCode: 'DICT_RECEIPT',
        printType: PRINT_TYPE.GENERATE_IMAGE,
        receiptId: rowValue.value?.receiptId as string,
        formOperationType: FORM_OPERATION_TYPE.DESIGN,
      });
    });
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryReceiptList: queryReceiptList,
  });

  // 表格配置数据
  const { tableColumns } = useTableColumnsConfig({
    operationFn: operationFn,
    operationDesignFn: operationDesignFn,
  });
  onMounted(async () => {
    await queryReceiptList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title class="mb-2" :title="$t('receiptManage.list', '单据列表')" />
    <div class="flex justify-between">
      <ProForm
        ref="searchFormRef"
        layout-mode="inline"
        v-model="searchFormModel"
        :data="searchConfig"
        :show-search-button="true"
        @model-change="queryReceiptList"
      />
      <div class="mb-3">
        <DmlButton
          :code="BIZ_ID_TYPE_CODE.DICT_RECEIPT"
          :biz-data="bizData"
          @success="
            () => {
              tableColumnsRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button class="ml-3" type="primary" @click="openMenuReceipt">{{
          $t('menu.receipt', '菜单的单据')
        }}</el-button>
        <el-button
          type="primary"
          @click="operationFn"
          :disabled="!isCloudEnv"
          >{{ $t('global:add') }}</el-button
        >
      </div>
    </div>
    <ProTable
      ref="tableColumnsRef"
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      row-key="receiptId"
      @selection-change="selectionChange"
    />
    <MenuDialog ref="menuDialogRef" />
    <OperationDialog
      ref="operationDialogRef"
      :dialog-title="dialogTitle"
      :row-value="rowValue as Receipt.ReceiptReqItem"
      @success="queryReceiptList"
    />
  </div>
</template>
