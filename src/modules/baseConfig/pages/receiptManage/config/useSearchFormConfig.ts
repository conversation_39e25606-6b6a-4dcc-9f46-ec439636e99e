import { useFormConfig } from 'sun-biz';
export function useSearchFormConfig(options: {
  queryReceiptList: (val: { keyWord?: string }) => Promise<void>;
}) {
  const { queryReceiptList } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-80',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              await queryReceiptList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}
