import { useFormConfig } from 'sun-biz';
import { useTranslation } from 'i18next-vue';
import { baseInfoFormType } from '../components/operationDialog.vue';
import { Ref, ComputedRef } from 'vue';
import {
  FLAG,
  RECEIPT_PRINT_RULE_CODE,
  DATA_SOURCE_TYPE_CODE_NAME,
  RECEIPT_PRINT_RULE_CODE_NAME,
  RECEIPT_SPLIT_TYPE_CODE_NAME,
  RECEIPT_PRINT_CATEG_CODE_NAME,
  RECEIPT_TEMPLATE_RULE_CODE_NAME,
} from '@/utils/constant';
export function useBaseInfoFormConfig(options: {
  printInterfaceList: Ref<Interface.InterfaceInfo[]>;
  invoiceInterfaceList: Ref<Interface.InterfaceInfo[]>;
  getPrintInterface: (keyWord: string) => Promise<void>;
  getInvoiceInterface: (keyWord: string) => Promise<void>;
}) {
  const {
    printInterfaceList,
    invoiceInterfaceList,
    getPrintInterface,
    getInvoiceInterface,
  } = options;
  const { t } = useTranslation();
  // 打印份数校验
  const printQtyValidator = (
    rule: unknown,
    value: string,
    callback: (error?: Error | undefined) => void,
  ) => {
    const regex = /^[1-9]\d*$/;
    if (!value || value === '') {
      callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('receipt.printQty', '打印份数'),
          }),
        ),
      );
    } else if (!regex.test(value.toString())) {
      callback(
        new Error(
          t('global:placeholder.input.template', {
            content: t('receipt.valid.positiveInteger', '正整数'),
          }),
        ),
      );
    }
    callback();
  };
  const data = useFormConfig({
    dataSetCodes: [RECEIPT_SPLIT_TYPE_CODE_NAME, DATA_SOURCE_TYPE_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'receiptName',
        label: t('receipt.receiptName', '单据名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('receipt.receiptName', '单据名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('receipt.receiptName', '单据名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'receipt2ndName',
        label: t('receipt.receipt2ndName', '单据辅助名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('receipt.receipt2ndName', '单据辅助名称'),
        }),
      },
      {
        name: 'receiptExtName',
        label: t('receipt.receiptExtName', '单据扩展名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('receipt.receiptExtName', '单据扩展名称'),
        }),
      },
      {
        name: 'receiptSplitTypeCode',
        label: t('receipt.receiptSplitTypeDesc', '分单方式'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.receiptSplitTypeDesc', '分单方式'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptSplitTypeDesc', '分单方式'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[RECEIPT_SPLIT_TYPE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'dataSourceTypeCode',
        label: t('receipt.dataSourceTypeDesc', '数据源类型'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.dataSourceTypeDesc', '数据源类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.dataSourceTypeDesc', '数据源类型'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[DATA_SOURCE_TYPE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'spName',
        label: t('receipt.spName', '存过名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('receipt.spName', '存过名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('receipt.spName', '存过名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'invoiceInterfaceId',
        label: t('receipt.invoiceInterfaceName', '票据接口'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.invoiceInterfaceName', '票据接口'),
        }),
        extraProps: {
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (val: string) => getInvoiceInterface(val),
          options: invoiceInterfaceList.value,
          props: {
            label: 'interfaceName',
            value: 'interfaceId',
          },
        },
      },
      {
        name: 'printInterfaceId',
        label: t('receipt.printInterfaceName', '打印接口'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.printInterfaceName', '打印接口'),
        }),
        extraProps: {
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (val: string) => getPrintInterface(val),
          options: printInterfaceList.value,
          props: {
            label: 'interfaceName',
            value: 'interfaceId',
          },
        },
      },
      {
        name: 'printQty',
        label: t('receipt.printQty', '打印份数'),
        type: 'number',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('receipt.printQty', '打印份数'),
        }),
        rules: [
          {
            required: true,
            validator: printQtyValidator,
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'printSetupEnabledFlag',
        label: t('receipt.printSetupEnabledFlag', '打印设置询问'),
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
        component: 'switch',
      },
    ],
  });
  return data;
}

export function useTemplateRuleFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [RECEIPT_TEMPLATE_RULE_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'receiptTemplateRuleCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.receiptTemplateRuleCode', '模板规则名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptTemplateRuleCode', '模板规则名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          class: 'w-40',
          clearable: false,
          filterable: true,
          options: data?.value
            ? data.value[RECEIPT_TEMPLATE_RULE_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
    ],
  });
  return data;
}

export function useMenuXReceiptFormConfig(options: {
  baseInfoFormModel: Ref<baseInfoFormType | undefined>;
  rowValue: ComputedRef<Receipt.ReceiptReqItem>;
}) {
  const { baseInfoFormModel, rowValue } = options;
  const data = useFormConfig({
    dataSetCodes: [RECEIPT_PRINT_RULE_CODE_NAME, RECEIPT_PRINT_CATEG_CODE_NAME],
    getData: (t, data) => [
      {
        name: 'receiptPrintRuleCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.receiptPrintRuleCode', '打印规则'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptPrintRuleCode', '打印规则'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          class: 'w-40',
          clearable: false,
          filterable: true,
          options: data?.value ? data.value[RECEIPT_PRINT_RULE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
          onChange: (val: string) => {
            if (val === RECEIPT_PRINT_RULE_CODE.GENERAL) {
              baseInfoFormModel.value = {
                ...baseInfoFormModel.value,
                receiptPrintCategCode:
                  baseInfoFormModel.value?.receiptPrintCategCode ??
                  rowValue.value.receiptPrintCategCode,
              } as baseInfoFormType;
            }
          },
        },
      },
      {
        name: 'receiptPrintCategCode',
        label: t('receipt.receiptPrintCategCode', '单据打印类别'),
        component: 'select',
        isHidden:
          baseInfoFormModel.value?.receiptPrintRuleCode !==
          RECEIPT_PRINT_RULE_CODE.GENERAL,
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.receiptPrintCategCode', '单据打印类别'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptPrintCategCode', '单据打印类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          class: 'w-56',
          clearable: false,
          filterable: true,
          options: data?.value ? data.value[RECEIPT_PRINT_CATEG_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'redPrintCategCode',
        label: t('receipt.redPrintCategCode', '红票打印类别'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('receipt.redPrintCategCode', '红票打印类别'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.redPrintCategCode', '红票打印类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          class: 'w-40',
          clearable: false,
          filterable: true,
          options: data?.value ? data.value[RECEIPT_PRINT_CATEG_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
    ],
  });
  return data;
}

export function useMenuReceiptFormConfig(options: {
  menuList: ComputedRef<Menu.MenuInfo[]>;
}) {
  const { menuList } = options;
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'menuId',
        label: t('system.menu', '选择菜单'),
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('system.menu', '菜单'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('system.menu', '菜单'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          class: 'w-52',
          clearable: false,
          filterable: true,
          options: menuList.value ?? [],
          props: {
            label: 'menuGroupName',
            value: 'menuId',
          },
        },
      },
    ],
  });
  return data;
}
