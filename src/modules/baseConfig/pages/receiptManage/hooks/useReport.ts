import { ref } from 'vue';
import { getReportTemp } from '@/modules/baseConfig/api/report';

/** 是web端打印的时候获取打印模板文件名称列表 */
export function useReport() {
  const reportList = ref<Report.ReportTempReqItem[]>([]);
  const loading = ref<boolean>(false);

  const getReportList = async () => {
    loading.value = true;
    const [, res] = await getReportTemp();
    loading.value = false;
    if (res?.success) {
      reportList.value = res?.data ?? [];
    }
  };
  return { loading, reportList, getReportList };
}
