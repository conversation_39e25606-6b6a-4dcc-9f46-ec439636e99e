import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryInterfaceListByExample } from '@/modules/baseConfig/api/interface';

// 打印接口相关hooks
export function usePrintInterface() {
  const loading = ref(false);
  const interfaceList = ref<Interface.InterfaceInfo[]>([]);
  const queryInterfaceList = async (params: Interface.QueryParams = {}) => {
    loading.value = true;
    const [, res] = await queryInterfaceListByExample({
      ...params,
      enabledFlag: FLAG.YES,
    });
    loading.value = false;
    if (res?.success) {
      interfaceList.value = res.data ?? [];
      return res.data ?? [];
    }
  };
  return {
    loading,
    interfaceList,
    queryInterfaceList,
  };
}
