import { ref } from 'vue';
import { getPrinterList, getReceiptXPrinter } from '@/api/common';
import {
  PrinterResItem,
  GetReceiptXPrinterResParams,
  GetReceiptXPrinterResItem,
} from '@/api/types';

export function useGetPrinter() {
  const loading = ref(false);
  const printerList = ref<PrinterResItem[]>([]);
  const receiptXPrinterList = ref<GetReceiptXPrinterResItem[]>([]);

  // 获取可用打印机列表
  const getPrinter = async () => {
    loading.value = true;
    const [, res] = await getPrinterList();
    loading.value = false;
    if (res?.success) {
      printerList.value = res.data ?? [];
    }
  };

  // 保存单据的打印机

  // 获取单据的打印机列表
  const getReceiptXPrinterList = async (
    params: GetReceiptXPrinterResParams = {
      receiptTemplateRuleIds: [],
    },
  ) => {
    loading.value = true;
    const [, res] = await getReceiptXPrinter(params);
    loading.value = false;
    if (res?.success) {
      receiptXPrinterList.value = res.data ?? [];
    }
  };
  return {
    loading,
    printerList,
    receiptXPrinterList,
    getPrinter,
    getReceiptXPrinterList,
  };
}
