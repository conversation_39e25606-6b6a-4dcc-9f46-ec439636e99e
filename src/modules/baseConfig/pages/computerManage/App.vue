<script lang="ts" name="computerManage" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { useComputerManageTableConfig } from './config/useTableConfig.tsx';
  import {
    addBizTag,
    deleteBizTagByExample,
    queryComputerManageListByExample,
  } from '@modules/baseConfig/api/computerManage';
  import { useComputerManageFormConfig } from './config/useFormConfig.tsx';
  import ComputerManageUpsertDialog from './components/ComputerManageUpsertDialog.vue';
  import ComputerIndexUpsertDialog from './components/ComputerIndexUpsertDialog.vue';
  import { TAG_GROUP_ID } from './constant.ts';
  import { commonSort } from '@/api/common.ts';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { ElMessage } from 'element-sun';
  import { queryTagListByExample } from '@/modules/cisOutp/api/code.ts';
  import { FLAG } from '@sun-toolkit/enums';

  const tagOptions = ref<DiagnosisSearch.SelectOptions[]>([]);
  const showAddButton = ref<boolean>(false); // 是否显示添加按钮

  const total = ref(0); // 数据总条数

  const ComputerInfo = ref<ComputerManage.ComputerInfo[]>([]);
  const { t } = useTranslation();
  const searchParams = ref<ComputerManage.QueryParams>({
    keyWord: '',
    computerTypeCode: '',
    hospitalId: '',
    monitorFlag: '',
    pageNumber: 1,
    pageSize: 20,
  });

  const loading = ref(false);
  const computerManageUpsertDialogRef = ref();
  const computerManageUpsertDialogMode = ref('');
  const computerIndexUpsertDialogRef = ref();
  const computerManageTableRef = ref();
  const rowValue = ref<ComputerManage.ComputerInfo>();
  // 标题
  const dialogTitle = computed(() => {
    return rowValue.value?.computerId
      ? t('computerManage.edit', '编辑计算机')
      : t('computerManage.add', '新增计算机');
  });

  async function queryComputerManageData(data?: ComputerManage.QueryParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === -1
          ? undefined
          : searchParams.value.enabledFlag,
    };
    try {
      const [, res] = await queryComputerManageListByExample(params);
      if (res?.success) {
        res.data.sort(
          (a: Address.AddressInfo, b: Address.AddressInfo) =>
            Number(a.sort) - Number(b.sort),
        );
        ComputerInfo.value = res.data || [];
        total.value = res.total;
      }
    } catch {
      ComputerInfo.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }

  // const deleteItem = async (row: ComputerManage.ComputerInfo) => {
  //   ElMessageBox.confirm(
  //     t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
  //       action: t('global:delete'),
  //       name: row.computerName || row.computerDesc,
  //     }),
  //     t('global:tip'),
  //     {
  //       confirmButtonText: t('global:confirm'),
  //       cancelButtonText: t('global:cancel'),
  //       type: 'warning',
  //     },
  //   )
  //     .then(async () => {
  //       const [, res] = await deleteComputerIndex({
  //         computerIndexId: row.computerId,
  //       });
  //       if (res?.success) {
  //         queryComputerManageData();
  //       }
  //     })
  //     .catch(() => {});
  // };
  const onOpenComputerManageDialog = (
    row: ComputerManage.addComputerParams,
  ) => {
    rowValue.value = row && row.computerId ? row : {};
    computerManageUpsertDialogRef.value.open();
  };
  const openIndexSettingDialog = (row: ComputerManage.addComputerParams) => {
    rowValue.value = row && row.computerId ? row : {};
    computerIndexUpsertDialogRef.value.open();
  };
  // 处理标签变更
  const handleTagsChange = async (
    tag: { tagId: string; tagName: string },
    computerId: string,
  ) => {
    try {
      const [, res] = await addBizTag({
        bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_COMPUTER,
        bizId: computerId,
        tagId: tag.tagId,
      });
      if (res?.success) {
        ElMessage.success(t('global:modify.success'));
        // 刷新列表数据
        await queryComputerManageData();
      }
    } catch (error) {
      console.error('更新标签失败:', error);
    }
  };

  // 处理标签删除
  const handleTagDelete = async (tag: {
    tagId: string;
    tagName: string;
    bizTagId: string;
  }) => {
    try {
      const [, res] = await deleteBizTagByExample({
        bizTagIds: [tag.bizTagId],
      });
      if (res?.success) {
        ElMessage.success(t('global:delete.success'));
        // 刷新列表数据
        await queryComputerManageData();
      }
    } catch (error) {
      console.error('删除标签失败:', error);
    }
  };

  // 表格配置数据
  const tableConfig = useComputerManageTableConfig(
    onOpenComputerManageDialog,
    openIndexSettingDialog,
    tagOptions,
    handleTagsChange,
    handleTagDelete,
    showAddButton,
  );

  // 搜索条件改变
  const changeSelect = async (
    data?: ComputerManage.QueryParams,
    key?: string,
  ) => {
    console.log(key, data);
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    await queryComputerManageData();
  };
  const searchConfig = useComputerManageFormConfig(queryComputerManageData);
  const handleSortEnd = async (list: ComputerManage.ComputerInfo[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.computerId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_COMPUTER,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryComputerManageData();
    }
  };

  // 获取标签选项
  async function fetchTags() {
    let [, result] = await queryTagListByExample({
      keyWord: '',
      tagGroupIds: [TAG_GROUP_ID],
    });
    if (result?.success) {
      let { data } = result;
      tagOptions.value = data
        .filter((item) => item.enabledFlag === FLAG.YES)
        .map((item) => ({
          value: item.tagId,
          label: item.tagNameDisplay,
        }));
      console.log(tagOptions.value, 'tagOptions');
    }
  }

  onMounted(() => {
    fetchTags();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('computerManage.list.title', '计算机管理')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
      </div>
      <div class="flex justify-start">
        <el-button class="mr-2" type="primary" @click="queryComputerManageData">
          {{ $t('global:search', '查询') }}
        </el-button>
        <el-button
          class="mr-2"
          type="primary"
          @click="onOpenComputerManageDialog"
        >
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="computerManageTableRef"
      :columns="tableConfig"
      :data="ComputerInfo"
      :draggable="true"
      :loading="loading"
      :page-info="{
        total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :page-sizes="[20, 50, 100, 200, 500]"
      :pagination="true"
      row-key="computerId"
      @cell-mouse-enter="
        (row: ComputerManage.ComputerInfo, column: { property: string }) => {
          if (column.property === 'bizTagList') {
            row.showAddButton = true;
          }
        }
      "
      @cell-mouse-leave="
        (row: ComputerManage.ComputerInfo, column: { property: string }) => {
          if (column.property === 'bizTagList') {
            row.showAddButton = false;
          }
        }
      "
      @drag-end="handleSortEnd"
      @current-page-change="
        (val: number) => {
          queryComputerManageData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryComputerManageData({
            pageSize: val,
          });
        }
      "
    />
    <ComputerManageUpsertDialog
      ref="computerManageUpsertDialogRef"
      :dialog-title="dialogTitle"
      :mode="computerManageUpsertDialogMode"
      :row-value="rowValue as ComputerManage.addComputerParams"
      @success="queryComputerManageData"
    />
    <ComputerIndexUpsertDialog
      ref="computerIndexUpsertDialogRef"
      :mode="computerManageUpsertDialogMode"
      :row-value="rowValue as ComputerManage.addComputerParams"
      @success="queryComputerManageData"
    />
  </div>
</template>
