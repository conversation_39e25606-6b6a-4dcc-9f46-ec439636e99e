import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';
import { CRITICAL_VALUE_TYPE_CODE, TIME_UNIT_CODE } from '@/utils/constant.ts';
import { Plus } from '@element-sun/icons-vue';
import TagSelect from '../components/TagSelect.vue';

interface ComputerInfo extends ComputerManage.addComputerParams {
  selectedTags?: string[];
  bizTagList?: Array<{
    tagId: string;
    tagName: string;
    bizTagId: string;
  }>;
  showAddButton?: boolean | undefined;
}

export const useComputerManageTableConfig = (
  onOpenComputerManageDialog: (row: ComputerManage.addComputerParams) => void,
  openIndexSettingDialog: (row: ComputerManage.addComputerParams) => void,
  tagOptions: Ref<DiagnosisSearch.SelectOptions[]>,
  onTagsChange: (
    tag: { tagId: string; tagName: string },
    computerId: string,
  ) => Promise<void>,
  onTagDelete: (
    tag: { tagId: string; tagName: string; bizTagId: string },
    computerId: string,
  ) => Promise<void>,
) => {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '序号'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'computerManage.computerManageTable.computerDesc',
          '计算机描述',
        ),
        prop: 'computerDesc',
        minWidth: 150,
      },
      {
        label: t(
          'computerManage.computerManageTable.computerName',
          '计算机名称',
        ),
        prop: 'computerName',
        minWidth: 150,
        render: (row: ComputerManage.ComputerInfo) => {
          return <>{row.computerName ? row.computerName : '--'}</>;
        },
      },
      {
        label: t('computerManage.computerManageTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        width: 90,
        render: (row: ComputerManage.addComputerParams) => {
          return (
            <span>
              {row.enabledFlag === ENABLED_FLAG.YES ? (
                <el-tag type={'success'}>启用</el-tag>
              ) : (
                <el-tag type={'danger'}>停用</el-tag>
              )}
            </span>
          );
        },
      },
      {
        label: t('computerManage.computerManageTable.monitorFlag', '标签'),
        prop: 'bizTagList',
        width: 300,
        minWidth: 300,
        showOverflowTooltip: false,
        render: (row: ComputerInfo) => {
          return (
            <>
              <TagSelect
                style={'min-width:200px;'}
                modelValue={
                  row.bizTagList?.map((tag) => ({
                    tagId: tag.tagId || '',
                    tagName: tag.tagName || '',
                    bizTagId: tag.bizTagId || '',
                  })) || []
                }
                tagOptions={tagOptions.value}
                showAddButton={row.showAddButton || false}
                onTagsChange={(tag: { tagId: string; tagName: string }) =>
                  onTagsChange(tag, row.computerId || '')
                }
                onTagDelete={(tag: {
                  tagId: string;
                  tagName: string;
                  bizTagId: string;
                }) => onTagDelete(tag, row.computerId || '')}
              />
            </>
          );
        },
      },
      {
        label: t(
          'computerManage.computerManageTable.computerTypeCodeDesc',
          '计算机类型',
        ),
        prop: 'computerTypeCodeDesc',
        minWidth: 100,
      },
      {
        label: t('computerManage.computerManageTable.ipAddrList', 'IP地址'),
        prop: 'ipAddrList',
        minWidth: 150,
        render: (row: ComputerManage.ComputerInfo) => {
          return (
            <>
              {row.ipAddrList?.map((item) => (
                <div class={'flex items-center justify-start'}>
                  {item.ipAddrTypeCode === '1' ? (
                    <el-tag
                      size={'small'}
                      class={'mr-2'}
                      type="success"
                      disabled={true}
                    >
                      {t('computerManage.form.ipAddrTypeCodeIn', '内网')}
                    </el-tag>
                  ) : (
                    <el-tag
                      size="small"
                      class={'mr-2'}
                      type="primary"
                      disabled={true}
                    >
                      {t('computerManage.form.ipAddrTypeCodeOut', '外网')}
                    </el-tag>
                  )}
                  <div>{item.ipAddr}</div>
                </div>
              ))}
            </>
          );
        },
      },
      {
        label: t('computerManage.computerManageTable.monitorFlag', '需要监控'),
        prop: 'monitorFlag',
        minWidth: 90,
        render: (row: ComputerManage.addComputerParams) => {
          return (
            <span>{row.monitorFlag === ENABLED_FLAG.YES ? '是' : '否'}</span>
          );
        },
      },
      {
        label: t(
          'computerManage.computerManageTable.operatingSystemCodeDesc',
          '操作系统',
        ),
        prop: 'operatingSystemCodeDesc',
        minWidth: 150,
      },
      {
        label: t('computerManage.computerManageTable.hospitalName', '所属医院'),
        prop: 'hospitalName',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 80,
        render: (row: ComputerManage.ComputerInfo) => {
          return (
            <>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  onOpenComputerManageDialog(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
              {/*<el-button*/}
              {/*  onClick={(e: { preventDefault: () => void }) => {*/}
              {/*    e.preventDefault();*/}
              {/*    openIndexSettingDialog(row);*/}
              {/*  }}*/}
              {/*  type="primary"*/}
              {/*  link*/}
              {/*>*/}
              {/*  {t('computerManage.computerManageTable.indexSetting', '指标')}*/}
              {/*</el-button>*/}
            </>
          );
        },
      },
    ],
  });
};

export function useIPEditColumnConfig(options: {
  id: string;
  tableRef: Ref<TableRef, TableRef>;
  data: Ref<(ComputerManage.ComputerIPItem & { editable: boolean })[]>;
  isAdd: string;
  deleteRow: (row: ComputerManage.ComputerIPItem, index: number) => void;
}) {
  const { tableRef, data, deleteRow } = options;
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data,
    id: 'ipAddr',
  });

  const columnConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('computerManage.computerIPEdit.ipAddr', 'IP地址'),
        prop: 'ipAddr',
        minWidth: 140,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerManage.computerIPEdit.ipAddrTips', 'IP地址'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            pattern:
              /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            message: t('global:placeholder.input.template', {
              content: t(
                'computerManage.computerIPEdit.ipAddrLimitTips',
                '正确的IP地址',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (row: ComputerManage.ComputerIPItem) => {
          return row.editable ? (
            <>
              <div class={'flex items-center justify-between'}>
                <el-button-group
                  className={'mr-2 flex items-center justify-between'}
                  size={'small'}
                >
                  <el-button
                    type={row.ipAddrTypeCode === '1' ? 'success' : 'default'}
                    onClick={() => {
                      row.ipAddrTypeCode = '1';
                    }}
                  >
                    {t('computerManage.form.ipAddrTypeCodeIn', '内网')}
                  </el-button>
                  <el-button
                    type={row.ipAddrTypeCode === '0' ? 'primary' : 'default'}
                    onClick={() => {
                      row.ipAddrTypeCode = '0';
                    }}
                  >
                    {t('computerManage.form.ipAddrTypeCodeOut', '外网')}
                  </el-button>
                </el-button-group>
                <el-input
                  v-model={row.ipAddr}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'computerManage.computerIPEdit.ipAddr',
                      'IP地址',
                    ),
                  })}
                ></el-input>
              </div>
            </>
          ) : (
            <>
              <div class={'flex items-center justify-start'}>
                {row.ipAddrTypeCode === '1' ? (
                  <el-tag size="small" class="mr-2" type="success">
                    {t('computerManage.form.ipAddrTypeCodeIn', '内网')}
                  </el-tag>
                ) : (
                  <el-tag size="small" class="mr-2" type="primary">
                    {t('computerManage.form.ipAddrTypeCodeOut', '外网')}
                  </el-tag>
                )}
                {row.ipAddr}
              </div>
            </>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (
          row: ComputerManage.ComputerIPItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <>
              <el-button
                onClick={() => cancelEdit(row, $index)}
                type="primary"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                onClick={() => {
                  toggleEdit(row);
                }}
                type="danger"
                link
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  console.log(row);
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => deleteRow(row, $index)}
                type="danger"
                link
              >
                {t('global:delete')}
              </el-button>
            </>
          );
        },
      },
    ],
  });

  return {
    columnConfig,
    addItem,
  };
}

export function useComputerIndexEditColumnConfig(options: {
  id: string;
  tableRef: Ref<TableRef, TableRef>;
  data: Ref<
    (ComputerManage.ComputerIndexSettingList & { editable: boolean })[]
  >;
  computerIndexList: Ref<SelectOptions[]>;
  insertRow: () => void;
  deleteRow: (
    row: ComputerManage.ComputerIndexSettingList,
    index: number,
  ) => void;
}) {
  const { id, tableRef, data, computerIndexList, insertRow, deleteRow } =
    options;
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data,
    id,
  });
  const columnConfig = useColumnConfig({
    dataSetCodes: [CRITICAL_VALUE_TYPE_CODE, TIME_UNIT_CODE],
    getData: (t, dataSet) => [
      {
        label: t(
          'computerIndexSetting.computerIndexEditTable.computerIndexId',
          '计算机指标',
        ),
        prop: 'computerIndexId',
        minWidth: 140,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'computerIndexSetting.computerIndexEditTable.computerIndexId',
                '计算机指标',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          options: computerIndexList.value,
          filterable: true,
        },
        render: (
          row: ComputerManage.ComputerIndexSettingList & {
            editable: boolean;
          },
        ) => {
          return row.editable ? (
            <>
              <el-select
                v-model={row.computerIndexId}
                filterable
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'computerIndexSetting.computerIndexEditTable.computerIndexId',
                    '计算机指标',
                  ),
                })}
                onChange={(val: string) => {
                  const obj = computerIndexList.value.find(
                    (item) => item.value === val,
                  );
                  row.computerIndexName = obj?.label;
                }}
              >
                {(computerIndexList.value ?? [])?.map((item) => (
                  <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  />
                ))}
              </el-select>
            </>
          ) : (
            <>{row.computerIndexName}</>
          );
        },
      },
      {
        label: t(
          'computerIndexSetting.computerIndexEditTable.criticalValueTypeCode',
          '阈值类型',
        ),
        prop: 'criticalValueTypeCode',
        minWidth: 140,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'computerIndexSetting.computerIndexEditTable.criticalValueTypeCode',
                '阈值类型',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],

        render: (
          row: ComputerManage.ComputerIndexSettingList & {
            editable: boolean;
          },
        ) => {
          return row.editable ? (
            <>
              <el-select
                v-model={row.criticalValueTypeCode}
                filterable
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'computerIndexSetting.computerIndexEditTable.criticalValueTypeCode',
                    '阈值类型',
                  ),
                })}
                onChange={(val: string) => {
                  const obj =
                    dataSet?.value &&
                    dataSet.value[CRITICAL_VALUE_TYPE_CODE].find(
                      (item) => item.dataValueNo === val,
                    );
                  row.criticalValueTypeCodeDesc = obj?.dataValueNameDisplay;
                }}
              >
                {(dataSet?.value
                  ? dataSet.value[CRITICAL_VALUE_TYPE_CODE]
                  : []
                )?.map((item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ))}
              </el-select>
            </>
          ) : (
            <>{row.criticalValueTypeCodeDesc}</>
          );
        },
      },
      {
        label: t(
          'computerIndexSetting.computerIndexEditTable.criticalValue',
          '阈值',
        ),
        prop: 'criticalValue',
        minWidth: 140,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'computerIndexSetting.computerIndexEditTable.criticalValue',
                '阈值',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ComputerManage.ComputerIndexSettingList & {
            editable: boolean;
          },
        ) => {
          return row.editable ? (
            <>
              <el-input-number
                min={0}
                max={999999999}
                step={1}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'computerIndexSetting.computerIndexEditTable.criticalValue',
                    '阈值',
                  ),
                })}
                precision={2}
                v-model={row.criticalValue}
                controls-position="right"
                style={{ width: '140px' }}
              />
            </>
          ) : (
            <>{row.criticalValue}</>
          );
        },
      },
      {
        label: t(
          'computerIndexSetting.computerIndexEditTable.logTimeValue',
          '日志时间设置',
        ),
        prop: 'logTimeValue',
        minWidth: 200,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'computerIndexSetting.computerIndexEditTable.logTimeValue',
                '日志时间设置',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],

        render: (
          row: ComputerManage.ComputerIndexSettingList & {
            editable: boolean;
          },
        ) => {
          return row.editable ? (
            <>
              <div class={'flex items-center justify-center'}>
                <el-input-number
                  min={0}
                  max={999999999}
                  step={1}
                  step-strictly={true}
                  precision={0}
                  v-model={row.logTimeValue}
                  controls-position="right"
                  style={{ width: '100px' }}
                />
                <el-select
                  v-model={row.timeUnitCode}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'computerIndexSetting.computerIndexEditTable.timeUnitCode',
                      '时间单位',
                    ),
                  })}
                  onChange={(val: string) => {
                    const obj =
                      dataSet?.value &&
                      dataSet.value[TIME_UNIT_CODE].find(
                        (item) => item.dataValueNo === val,
                      );
                    row.timeUnitCodeDesc = obj?.dataValueNameDisplay;
                  }}
                  style={{ width: '120px' }}
                >
                  {(dataSet?.value ? dataSet.value[TIME_UNIT_CODE] : [])?.map(
                    (item) => (
                      <el-option
                        key={item.dataValueNo}
                        label={item.dataValueNameDisplay}
                        value={item.dataValueNo}
                      />
                    ),
                  )}
                </el-select>
              </div>
            </>
          ) : (
            <>
              {row.logTimeValue}
              {row.timeUnitCodeDesc}
            </>
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        renderHeader: () => {
          return (
            <span class={'flex items-center justify-center'}>
              {t('appConfigManage.appConfigManageTable.appConfigName', '操作')}
              <el-icon
                class={'ml-2 cursor-pointer text-blue-500'}
                onClick={() => {
                  insertRow();
                }}
              >
                <Plus />
              </el-icon>
            </span>
          );
        },
        fixed: 'right',
        width: 120,
        render: (
          row: ComputerManage.ComputerIPItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <>
              <el-button
                onClick={() => {
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:confirm')}
              </el-button>
              <el-button
                onClick={() => cancelEdit(row, $index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
            </>
          ) : (
            <>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  console.log(row);
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => deleteRow(row, $index)}
                type="danger"
                link
              >
                {t('global:delete')}
              </el-button>
            </>
          );
        },
      },
    ],
  });

  return {
    columnConfig,
    addItem,
  };
}
