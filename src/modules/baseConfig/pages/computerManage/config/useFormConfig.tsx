import { useFormConfig } from 'sun-biz';
import {
  COMPUTER_TYPE_CODE,
  ENABLED_FLAG,
  IP_TYPE_CODE,
  MACHINE_TYPE_CODE,
  OPERATING_SYSTEM_CODE,
} from '@/utils/constant';
import HospitalSelect from '@/modules/project/components/HospitalSelect/index.vue';
import { Ref } from 'vue';

export function useComputerManageFormConfig(
  queryComputerManageData: (
    params: ComputerManage.QueryParams,
  ) => Promise<void>,
) {
  const dataSetCodes = [COMPUTER_TYPE_CODE];

  const monitorFlagList = [
    { value: '', label: '全部' },
    { value: ENABLED_FLAG.YES, label: '是' },
    { value: ENABLED_FLAG.NO, label: '否' },
  ];
  const data = useFormConfig({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        label: '关键字',
        name: 'keyWord',
        component: 'input',
        placeholder: t(
          'computerManage.keyword',
          '请输入计算机名称 / 描述 / IP',
        ),
        className: 'w-80',
        extraProps: {
          suffixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryComputerManageData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryComputerManageData({ keyWord: '' });
          },
        },
      },
      {
        label: t('computerManage.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        extraProps: {
          clearable: false,
          className: 'w-52',
        },
        render: () => <HospitalSelect automaticallySetValue={true} />,
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag', '启用标志'),
        component: 'flagSelect',
        placeholder: t('computerManage.enabledFlagTips', '请选择启用标志'),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
        },
      },
      {
        name: 'computerTypeCode',
        label: t('computerManage.computerTypeCode', '计算机类型'),
        component: 'select',
        placeholder: t(
          'computerManage.computerTypeCodeTips',
          '请选择计算机类型',
        ),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          clearable: true,
          options: dataSet?.value ? dataSet.value[COMPUTER_TYPE_CODE] : [],
        },
      },
      {
        name: 'monitorFlag',
        label: t('computerManage.monitorFlag', '需要监控'),
        component: 'select',
        placeholder: t('computerManage.monitorFlagTips', '请选择是否需要监控'),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          clearable: true,
          options: monitorFlagList || [],
        },
      },
    ],
  });
  return data;
}

export function useComputerManageUpsertFormConfig(
  isAdd: Ref<string>,
  computerManageForm: Ref<ComputerManage.addComputerParams | undefined>,
) {
  const dataSetCodes = [
    OPERATING_SYSTEM_CODE,
    MACHINE_TYPE_CODE,
    COMPUTER_TYPE_CODE,
    IP_TYPE_CODE,
  ];
  const data = useFormConfig({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        label: t('computerManage.form.computerName', '计算机名称'),

        name: 'computerName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('computerManage.form.computerName', '计算机名称'),
        }),
      },
      {
        label: t('computerManage.form.ipAddrTips', 'IP地址'),
        name: 'ipAddr',
        isHidden: isAdd?.value === 'edit',
        placeholder: t('global:placeholder.input.template', {
          content: t('computerManage.form.ipAddrTips', 'IP地址'),
        }),
        extraProps: {
          style: { width: '500px' },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerManage.form.ipAddrTips', 'IP地址'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            pattern:
              /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            message: t(
              'computerManage.form.ipAddrInvalid',
              '请输入有效的IP地址',
            ),
            trigger: ['change', 'blur'],
          },
        ],
        render: () => {
          return (
            <div class={'flex items-center justify-between'}>
              <el-button-group
                class={'mr-2 flex items-center justify-between'}
                size={'small'}
              >
                <el-button
                  type={
                    computerManageForm.value?.ipAddrTypeCode === '1'
                      ? 'success'
                      : 'default'
                  }
                  onClick={() => {
                    if (computerManageForm.value) {
                      computerManageForm.value.ipAddrTypeCode = '1';
                    }
                  }}
                >
                  {t('computerManage.form.ipAddrTypeCodeIn', '内网')}
                </el-button>
                <el-button
                  type={
                    computerManageForm.value?.ipAddrTypeCode === '0'
                      ? 'primary'
                      : 'default'
                  }
                  onClick={() => {
                    if (computerManageForm.value) {
                      computerManageForm.value.ipAddrTypeCode = '0';
                    }
                  }}
                >
                  {t('computerManage.form.ipAddrTypeCodeOut', '外网')}
                </el-button>
              </el-button-group>
              <el-input
                type="text"
                class={'w-60'}
                v-model={computerManageForm.value.ipAddr}
              />
            </div>
          );
        },
      },
      {
        name: 'computerDesc',
        span: 3,
        label: t('computerManage.form.computerDesc', '计算机描述'),
        component: 'input',
        type: 'textarea',
        placeholder: t('global:placeholder.input.template', {
          content: t('computerManage.form.computerDesc', '计算机描述'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerManage.form.computerDesc', '计算机描述'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('computerManage.search.computerTypeCode', '计算机类型'),
        name: 'computerTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataDict.search.computerTypeCode', '计算机类型'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: dataSet?.value ? dataSet.value[COMPUTER_TYPE_CODE] : [],
          className: 'w-60',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('computerManage.form.computerTypeCode', '计算机类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('computerManage.search.operatingSystemCode', '操作系统'),
        name: 'operatingSystemCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataDict.search.operatingSystemCode', '操作系统'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: dataSet?.value ? dataSet.value[OPERATING_SYSTEM_CODE] : [],
          className: 'w-60',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('computerManage.form.operatingSystemCode', '操作系统'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        span: 3,
        label: t('computerManage.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        extraProps: {
          clearable: false,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('computerManage.form.belongHospital', '所属医院'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: () => <HospitalSelect automaticallySetValue={true} />,
      },
    ],
  });
  return data;
}

export function useComputerIndexUpsertFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('computerManage.form.computerName', '计算机名称'),

        name: 'computerName',
        component: 'input',
        extraProps: {
          disabled: true,
        },
      },
      {
        label: t('computerManage.form.ipAddrTips', '内网IP地址'),
        name: 'ipAddr',
        component: 'input',
        extraProps: {
          disabled: true,
        },
      },
      {
        name: 'computerDesc',
        span: 3,
        label: t('computerManage.form.computerDesc', '计算机描述'),
        component: 'input',
        type: 'textarea',
        placeholder: t('global:placeholder.input.template', {
          content: t('computerManage.form.computerDesc', '计算机描述'),
        }),
        extraProps: {
          disabled: true,
        },
      },
    ],
  });
  return data;
}
