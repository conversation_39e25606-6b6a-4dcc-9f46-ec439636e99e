<script lang="ts" name="ComputerManageUpsertDialog" setup>
  import { computed, nextTick, ref } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import {
    queryComputerIndexByExample,
    queryComputerIndexSettingByExample,
    saveComputerIndexSetting,
  } from '@modules/baseConfig/api/computerManage.ts';
  import { useComputerIndexUpsertFormConfig } from '../config/useFormConfig.tsx';
  import { ProDialog, ProForm, ProTable, TableRef, Title } from 'sun-biz';
  import { useComputerIndexEditColumnConfig } from '../config/useTableConfig.tsx';
  import { ENABLED_FLAG } from '@/utils/constant.ts';

  const { t } = useTranslation();

  const props = defineProps<{
    mode: string;
    rowValue: ComputerManage.addComputerParams;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: ComputerManage.addComputerParams;
  }>();
  const dialogRef = ref();
  const computerIndexTableRef = ref<TableRef>();
  const computerIndexForm = ref<ComputerManage.SaveComputerIndexSetting>({
    computerId: '',
    ipAddr: '',
    ipAddrList: [],
    computerName: '',
    computerDesc: '',
  });

  const emits = defineEmits<{ success: [] }>();
  const computerIndexSettingList = ref<
    ComputerManage.ComputerIndexSettingList[]
  >([]); // IP列表

  const queryComputerIndexSettingList = async (
    data?: ComputerManage.SaveComputerIndexSetting,
  ) => {
    const params = {
      computerId: data.computerId,
    };
    const [, res] = await queryComputerIndexSettingByExample(params);
    if (res?.success) {
      computerIndexSettingList.value = (res.data.map((item) => {
        return {
          ...item,
          editable: false,
          key: Math.random().toString(),
        };
      }) ?? []) as unknown as ComputerManage.ComputerIndexSettingList[];

      console.log(computerIndexSettingList.value, 'computerIndexSettingList');
    }
  };
  const computerIndexList = ref<SelectOptions[]>([]);
  // 查询指标 list
  const queryComputerIndexList = async () => {
    const params = {
      enabledFlag: ENABLED_FLAG.YES,
    };
    const [, res] = await queryComputerIndexByExample(params);
    if (res?.success) {
      computerIndexList.value =
        res.data.map((item) => {
          return {
            label: item.computerIndexName,
            value: item.computerIndexId,
          };
        }) ?? [];
    }
  };

  // 是否为新增状态
  const isAdd = computed(() => {
    return computerIndexSettingList.value.length ? 'edit' : 'add';
  });

  const canUpsertTableRow = () => {
    const isEditing = computerIndexSettingList.value.some(
      (item) => !!item.editable,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      if (!canUpsertTableRow()) {
        return reject(['', new Error('参数错误')]);
      }
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          computerIndexTableRef.value?.formRef.validate(
            async (tableValid: boolean) => {
              if (tableValid) {
                const params = {
                  computerId: props.rowValue.computerId,
                  computerIndexSettingList: computerIndexSettingList.value,
                };
                const [, res] = await saveComputerIndexSetting(params);
                if (res?.success) {
                  ElMessage.success(t('global:edit.success'));
                  dialogRef.value.close();
                  resolve([] as unknown as [never, unknown]);
                } else {
                  reject(['', new Error('接口错误')]);
                }
              } else {
                reject(['', new Error('参数错误')]);
              }
            },
          );
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  /**添加指标  */
  const insertRow = () => {
    const newItem = {
      key: Math.random().toString(),
      computerIndexId: '',
      computerIndexName: '',
      criticalValueTypeCode: '',
      criticalValueTypeCodeDesc: '',
      criticalValue: '',
      logTimeValue: '',
      timeUnitCode: 'SECOND',
      timeUnitCodeDesc: '秒',
      computerId: computerIndexForm.value.computerId,
      editable: true,
    };
    computerIndexSettingList.value.splice(
      computerIndexList.value.length,
      0,
      newItem,
    );
  };

  const deleteRow = (row: ComputerManage.ComputerIPItem, index: number) => {
    const hasEditableRow = computerIndexSettingList.value.filter(
      (item) => item.editable === true,
    );
    if (hasEditableRow.length <= 0) {
      computerIndexSettingList.value.splice(index, 1);
    } else {
      ElMessage.warning(
        t('computerManage.onlyOne', '存在编辑中的数据，请保存后再操作'),
      );
    }
  };

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);

      let ipAddr = '';
      if (rowValue.ipAddrList && rowValue.ipAddrList.length) {
        ipAddr =
          rowValue.ipAddrList.find((item) => item.ipAddrTypeCode === '1')
            ?.ipAddr || '';
      }
      computerIndexForm.value = {
        ...rowValue,
        ipAddr,
      };
      queryComputerIndexSettingList({ computerId: rowValue.computerId });
      queryComputerIndexList();
      dialogRef.value.open();
    });
  };
  const cancelDialog = () => {
    dialogRef.value.close();
  };
  defineExpose({ open: openDialog });

  const formConfig = useComputerIndexUpsertFormConfig();

  const { columnConfig: useComputerIndexEditColumn } =
    useComputerIndexEditColumnConfig({
      id: 'key',
      tableRef: computerIndexTableRef,
      data: computerIndexSettingList,
      computerIndexList,
      insertRow,
      deleteRow,
    });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="false"
    :title="
      isAdd === 'add'
        ? $t('computerIndex.add.title', '新增计算机指标')
        : $t('computerIndex.edit.title', '编辑计算机指标')
    "
    :width="900"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="computerIndexForm"
      :column="2"
      :data="formConfig"
    />
    <div>
      <Title :title="$t('computerIndex.list.title', '计算机指标')" />
      <ProTable
        id="ipAddrId"
        ref="computerIndexTableRef"
        :columns="useComputerIndexEditColumn"
        :data="computerIndexSettingList"
        :editable="true"
        class="w-765 mt-1"
        style="height: 200px; max-height: 200px"
      >
      </ProTable>
      <div class="mt-2 flex items-start justify-center">
        <el-button type="primary" @click="onConfirm"
          >{{ $t('global:save') }}
        </el-button>
        <el-button plain type="primary" @click="cancelDialog"
          >{{ $t('global:cancel') }}
        </el-button>
      </div>
    </div>
  </ProDialog>
</template>
