<script lang="ts" name="ComputerManageUpsertDialog" setup>
  import { computed, nextTick, ref } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import {
    addComputer,
    editComputer,
  } from '@modules/baseConfig/api/computerManage';
  import { useComputerManageUpsertFormConfig } from '../config/useFormConfig.tsx';
  import { ProDialog, ProForm, ProTable, TableRef } from 'sun-biz';
  import { useIPEditColumnConfig } from '../config/useTableConfig.tsx';
  import { ENABLED_FLAG } from '@/utils/constant.ts';

  const props = defineProps<{
    mode: string;
    rowValue: ComputerManage.addComputerParams;
    dialogTitle: string;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: ComputerManage.addComputerParams;
  }>();
  const dialogRef = ref();
  const ipEditTableRef = ref<TableRef>();
  const { t } = useTranslation();
  const computerManageForm = ref<ComputerManage.addComputerParams>({
    computerDesc: '',
    computerId: '',
    computerName: '',
    computerTypeCode: '',
    enabledFlag: undefined,
    monitorFlag: undefined,
    heartbeatInterval: undefined,
    monitorTransferInterval: undefined,
    hospitalId: '',
    ipAddrList: [
      {
        ipAddrTypeCode: '1',
        ipAddr: '',
      },
    ],
    ipAddrTypeCode: '1',
    ipAddr: '',
    machineTypeCode: '',
    operatingSystemCode: '',
  });
  const emits = defineEmits<{ success: [] }>();
  const computerIPItemModel = ref<ComputerManage.ComputerIPItem[]>([]); // IP列表

  // 是否为新增状态
  const isAdd = computed(() => {
    return props.rowValue?.computerId ? 'edit' : 'add';
  });

  const onConfirm = async () => {
    if (isAdd.value === 'add') {
      return new Promise<[never, unknown]>((resolve, reject) => {
        formRef?.value?.ref.validate(async (valid) => {
          if (valid) {
            const params = {
              ...computerManageForm.value,
              ...formRef?.value?.model,
              machineTypeCode: 1, // 机器类型
              ipAddrList: [
                {
                  ipAddr: formRef?.value?.model?.ipAddr,
                  ipAddrTypeCode: computerManageForm.value.ipAddrTypeCode, // ip类型
                },
              ],
            };
            const [, res] = await addComputer(params);
            if (res?.success) {
              ElMessage.success(t('global:add.success'));
              resolve([] as unknown as [never, unknown]);
            } else {
              reject(['', new Error('接口错误')]);
            }
          } else {
            reject(['', new Error('参数错误')]);
          }
        });
      });
    } else {
      return new Promise<[never, unknown]>((resolve, reject) => {
        formRef?.value?.ref.validate(async (valid) => {
          if (valid) {
            ipEditTableRef.value?.formRef.validate(
              async (tableValid: boolean) => {
                if (tableValid) {
                  const obj = (computerIPItemModel.value ?? []).find(
                    (item) => item.editable === true,
                  );
                  if (obj) {
                    ElMessage.error(
                      t('notSave.refundPayWay', '存在未保存的配置'),
                    );
                    return reject(['', new Error('参数错误')]);
                  }
                  // computerIPItemModel.value.forEach((item) => {
                  //   item.ipAddrTypeCode = '1';
                  // });
                  const params = {
                    ...computerManageForm.value,
                    ...formRef?.value?.model,
                    machineTypeCode: 1, // 机器类型
                    ipAddrList: computerIPItemModel.value,
                  };
                  const [, res] = await editComputer(params);
                  if (res?.success) {
                    ElMessage.success(t('global:edit.success'));
                    dialogRef.value.close();
                    resolve([] as unknown as [never, unknown]);
                  } else {
                    reject(['', new Error('接口错误')]);
                  }
                } else {
                  reject(['', new Error('参数错误')]);
                }
              },
            );
          } else {
            reject(['', new Error('参数错误')]);
          }
        });
      });
    }
  };

  /**添加IP  */
  function insertRow() {
    const newItem = {
      key: new Date().getTime().toString(),
      ipAddr: '',
      ipAddrTypeCode: '1',
      editable: true,
      computerId: computerManageForm.value?.computerId,
    };
    computerIPItemModel.value.splice(
      computerIPItemModel.value.length,
      0,
      newItem,
    );
  }

  function deleteRow(row: ComputerManage.ComputerIPItem, index: number) {
    const currentId = row.computerId;
    const sameIdRows = computerIPItemModel.value.filter(
      (item) => item.computerId === currentId,
    );
    console.log(sameIdRows);
    if (sameIdRows.length > 1) {
      computerIPItemModel.value.splice(index, 1);
    } else {
      ElMessage.error(t('computerManage.onlyOne', '至少保留一行数据'));
    }
  }

  const formConfig = useComputerManageUpsertFormConfig(
    isAdd,
    computerManageForm,
  );

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      const rowValue = cloneDeep(props.rowValue);
      console.log('rowValue', rowValue);
      computerManageForm.value = {
        ...computerManageForm.value,
        computerDesc: rowValue.computerDesc ?? undefined,
        computerId: rowValue.computerId ?? undefined,
        computerName: rowValue.computerName ?? undefined,
        computerTypeCode: rowValue.computerTypeCode ?? undefined,
        enabledFlag: rowValue.enabledFlag ?? ENABLED_FLAG.YES,
        ipAddr: rowValue.ipAddr ?? undefined,
        ipAddrTypeCode: rowValue.ipAddrTypeCode ?? '1',
        monitorFlag: undefined,
        heartbeatInterval: undefined,
        monitorTransferInterval: undefined,
        hospitalId: rowValue.hospitalId ?? undefined,
        ipAddrList: [],
        machineTypeCode: rowValue.machineTypeCode ?? undefined,
        operatingSystemCode: rowValue.operatingSystemCode ?? undefined,
      };
      if (rowValue.ipAddrList) {
        rowValue.ipAddrList.forEach((item) => {
          item.computerId = rowValue.computerId;
          item.editable = false;
        });
      }
      computerIPItemModel.value = (rowValue?.ipAddrList ??
        []) as unknown as (ComputerManage.ComputerIPItem & {
        editable: boolean;
      })[];
      dialogRef.value.open();
    });
  };
  defineExpose({ open: openDialog });

  const { columnConfig: useIPEditColumn } = useIPEditColumnConfig({
    id: 'ipAddr',
    tableRef: ipEditTableRef,
    data: computerIPItemModel,
    isAdd: isAdd.value,
    deleteRow,
  });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :title="props.dialogTitle ?? ''"
    :width="900"
    destroy-on-close
    include-footer
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="computerManageForm"
      :column="3"
      :data="formConfig"
    />
    <template v-if="isAdd === 'edit'">
      <ProTable
        id="ipAddrId"
        ref="ipEditTableRef"
        :columns="useIPEditColumn"
        :data="computerIPItemModel"
        :editable="true"
        class="w-765"
        style="max-height: 200px"
      >
      </ProTable>
      <el-button
        class="rounded-0 w-full border-t-0"
        icon="Plus"
        style="border-top-width: 0; border-radius: 0"
        @click="insertRow"
        >{{ $t('global:add') }}
      </el-button>
    </template>
  </ProDialog>
</template>
