<script lang="ts" name="tagSelect" setup>
  import { computed, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();

  interface Props {
    modelValue: Array<{
      tagId: string;
      tagName: string;
      bizTagId: string;
    }>;
    tagOptions: Array<{
      value: string;
      label: string;
    }>;
    onTagsChange?: (tag: { tagId: string; tagName: string }) => Promise<void>;
    onTagDelete?: (tag: {
      tagId: string;
      tagName: string;
      bizTagId: string;
    }) => Promise<void>;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
    showAddButton?: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['update:modelValue']);

  const showSelect = ref(false);
  const selectedTag = ref<string>('');
  const isDropdownOpen = ref<boolean>(false);

  // 计算可选的标签选项（过滤掉已选择的标签）
  const availableTagOptions = computed(() => {
    if (!props.modelValue || !props.tagOptions) return [];
    const selectedTagIds = new Set(props.modelValue.map((tag) => tag.tagId));
    return props.tagOptions.filter(
      (option) => !selectedTagIds.has(option.value),
    );
  });

  // 计算是否显示添加按钮
  const canAddTag = computed(() => {
    if (!props.modelValue || !props.tagOptions) return false;
    // 获取当前已选标签的ID集合
    const selectedTagIds = new Set(props.modelValue.map((tag) => tag.tagId));
    // 检查是否所有可选标签都已被选中
    return !props.tagOptions.every((option) =>
      selectedTagIds.has(option.value),
    );
  });

  const handleClose = async (tag: {
    tagId: string;
    tagName: string;
    bizTagId: string;
  }) => {
    // 先调用删除接口
    if (props.onTagDelete) {
      await props.onTagDelete(tag);
    }
    // 更新本地状态
    const newTags = props.modelValue.filter((item) => item.tagId !== tag.tagId);
    emit('update:modelValue', newTags);
  };

  const handleChange = async (value: string) => {
    if (value) {
      const tag = props.tagOptions.find((t) => t.value === value);
      if (tag) {
        const newTag = {
          tagId: tag.value,
          tagName: tag.label,
          bizTagId: '', // 新增标签时，bizTagId为空
        };
        const newTags = [...props.modelValue, newTag];
        emit('update:modelValue', newTags);
        // 调用添加接口
        if (props.onTagsChange) {
          await props.onTagsChange(newTag);
        }
      }
      selectedTag.value = '';
    }
    showSelect.value = false;
  };

  // 监听showSelect变化，当显示下拉框时重置选中的标签
  watch(showSelect, (newVal) => {
    if (newVal) {
      selectedTag.value = '';
    }
  });
</script>

<template>
  <div
    class="flex flex-wrap items-center"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <template v-if="modelValue && modelValue.length > 0">
      <el-tag
        v-for="(item, idx) in modelValue"
        :key="idx"
        :closable="showAddButton"
        class="mb-1 mr-1"
        type="primary"
        @close="handleClose(item)"
      >
        {{ item.tagName }}
      </el-tag>
    </template>
    <el-select
      v-if="(showSelect && showAddButton) || isDropdownOpen"
      v-model="selectedTag"
      :placeholder="
        t('global:placeholder.select.template', {
          name: t('computerManage.computerManageTable.tags', '标签'),
        })
      "
      class="w-30"
      filterable
      @change="handleChange"
      @mouseleave="
        () => {
          showSelect = false;
        }
      "
      @visible-change="
        (val: boolean) => {
          console.log(val);
          if (val) {
            isDropdownOpen = true;
          } else {
            isDropdownOpen = false;
          }
        }
      "
    >
      <el-option
        v-for="item in availableTagOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-button
      v-else-if="canAddTag && showAddButton"
      class="ml-1"
      plain
      size="small"
      @click="showSelect = true"
    >
      + {{ t('global:add', '添加') }}
    </el-button>
  </div>
</template>

<style scoped>
  .w-30 {
    width: 120px;
  }
</style>
