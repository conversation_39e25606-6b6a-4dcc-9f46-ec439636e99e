<script lang="ts" name="address" setup>
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { Title } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant.ts';

  import {
    queryAddressListByExample,
    updateAddressById,
    updateAddressEnabledFlagById,
    updateAddressSortByIds,
  } from '@modules/baseConfig/api/address';
  import Sortable from 'sortablejs';

  // 扩展tbody类型，支持_sortable属性
  interface SortableTbody extends HTMLTableSectionElement {
    _sortable?: Sortable;
  }

  const addressList = ref<Address.AddressInfo[]>([]);
  const { t } = useTranslation();
  const searchParams = ref<Address.QueryParams>({
    keyWord: '',
    addressLevelCode: '1',
    addressId: '',
  });
  const loading = ref(false);
  const checkedKeys = ref<string[]>([]);
  const addressTableRef = ref();

  /** 查询地址列表 */
  async function queryAddressData(data?: Address.QueryParams) {
    loading.value = true;
    if (data?.keyWord) {
      searchParams.value = {
        ...searchParams.value,
        keyWord: data.keyWord,
        addressId: '',
        addressLevelCode: '',
      };
    } else {
      searchParams.value = {
        keyWord: '',
        addressLevelCode: '1',
        addressId: '',
      };
    }
    const params = {
      ...searchParams.value,
    };
    const [, res] = await queryAddressListByExample(params);
    loading.value = false;
    if (res?.success) {
      res.data.sort(
        (a: Address.AddressInfo, b: Address.AddressInfo) =>
          Number(a.sort) - Number(b.sort),
      );
      addressList.value = res.data.map((item: Address.AddressInfo) => ({
        ...item,
        hasChildren: Number(item.addressLevelCode) < 4, // 4层以下才有子节点
      }));
    }
  }

  /** 启用状态切换 */
  async function handleEnableSwitch(row: Address.AddressInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.addressName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        addressId: row.addressId,
        enabledFlag: (row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES) as number,
      };
      const [, res] = await updateAddressEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryAddressData();
      }
    });
  }

  const loadChildren = async (
    row: Address.AddressInfo,
    treeNode: unknown,
    resolve: (children: Address.AddressInfo[]) => void,
  ) => {
    // 只查当前节点的下一级
    const params = {
      addressId: row.addressId,
      addressLevelCode: String(Number(row.addressLevelCode) + 1),
    };
    const [, res] = await queryAddressListByExample(params);
    if (res?.success) {
      const children = res.data.map((item: Address.AddressInfo) => ({
        ...item,
        hasChildren: Number(item.addressLevelCode) < 4, // 4层以下才有子节点
      }));
      row.children = children;
      resolve(children);
    } else {
      resolve([]);
    }
  };

  const handleExpand = async (
    _row: Address.AddressInfo,
    _expanded: boolean,
  ) => {
    console.log('handleExpand', _row, _expanded);
    // 可根据需要扩展
  };

  /** 籍贯标识切换 */
  async function handleNativeFlagChange(row: Address.AddressInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要为 "{{name}}" {{action}}籍贯标识吗？', {
        action:
          row.nativeFlag === 1
            ? t('address.nativeFlagTrue', '设置')
            : t('address.nativeFlagFalse', '移除'),
        name: row.addressName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        nativeFlag: row.nativeFlag === 1 ? ENABLED_FLAG.YES : ENABLED_FLAG.NO,
      };

      const [, res] = await updateAddressById(params);
      if (res?.success) {
        row.nativeFlag = params.nativeFlag;
      } else {
        row.nativeFlag =
          row.nativeFlag === 1 ? ENABLED_FLAG.NO : ENABLED_FLAG.YES;
      }
    });
  }

  // 主表拖拽
  const initTableDrag = () => {
    const tableBody = addressTableRef.value?.$el.querySelector(
      '.el-table__body-wrapper tbody',
    ) as SortableTbody;
    if (!tableBody) return;

    if (tableBody._sortable) {
      tableBody._sortable.destroy();
    }

    tableBody._sortable = Sortable.create(tableBody, {
      animation: 150,
      handle: 'tr',
      onEnd: async (evt) => {
        const oldIndex = evt.oldIndex ?? -1;
        const newIndex = evt.newIndex ?? -1;
        if (oldIndex === -1 || newIndex === -1) return;
        const draggedRow = addressList.value[oldIndex];
        const targetRow = addressList.value[newIndex];
        if (
          !draggedRow ||
          !targetRow ||
          draggedRow.addressLevelCode !== targetRow.addressLevelCode ||
          draggedRow.parentAddressId !== targetRow.parentAddressId
        ) {
          ElMessage.warning('只允许同层级内排序');
          queryAddressData();
          return;
        }
        let siblings;
        if (draggedRow.parentAddressId) {
          const parent = addressList.value.find(
            (item) => item.addressId === draggedRow.parentAddressId,
          );
          siblings = parent?.children || [];
        } else {
          siblings = addressList.value;
        }
        const oldSiblingIndex = siblings.findIndex(
          (item) => item.addressId === draggedRow.addressId,
        );
        const newSiblingIndex = siblings.findIndex(
          (item) => item.addressId === targetRow.addressId,
        );
        if (oldSiblingIndex === -1 || newSiblingIndex === -1) {
          ElMessage.warning('排序失败，未找到对应数据');
          queryAddressData();
          return;
        }
        const moved = siblings.splice(oldSiblingIndex, 1)[0];
        siblings.splice(newSiblingIndex, 0, moved);
        const arr = siblings.map((item, idx) => ({
          addressId: item.addressId,
          sort: idx + 1,
        }));
        await updateAddressSortByIds({ addressSortList: arr });
        await queryAddressData();
      },
    });

    // 递归初始化子表
    const tbodys = addressTableRef.value?.$el.querySelectorAll(
      '.el-table__body-wrapper tbody',
    );
    const mainTbody = tbodys[0] as SortableTbody;
    tbodys.forEach((tbodyNode: Element) => {
      const tbody = tbodyNode as SortableTbody;
      if (tbody === mainTbody) return;
      const firstTr = tbody.querySelector('tr') as HTMLTableRowElement;
      if (!firstTr) return;

      // 通过innerText匹配children
      function findChildrenByTr(
        tr: HTMLTableRowElement,
      ): Address.AddressInfo[] | null {
        function findInList(
          list: Address.AddressInfo[],
        ): Address.AddressInfo[] | null {
          for (const item of list) {
            if (item.children && item.children.length > 0) {
              if (
                item.children.some((child: Address.AddressInfo) =>
                  tr.innerText.includes(child.addressName || ''),
                )
              ) {
                return item.children;
              }
              const found = findInList(item.children);
              if (found) return found;
            }
          }
          return null;
        }

        return findInList(addressList.value);
      }

      const children = findChildrenByTr(firstTr);
      if (children) {
        initTableDragRecursive(tbody, children);
      }
    });
  };

  // 子表拖拽
  const initTableDragRecursive = (
    tbody: SortableTbody,
    children: Address.AddressInfo[],
  ) => {
    if (tbody._sortable) {
      tbody._sortable.destroy();
    }

    tbody._sortable = Sortable.create(tbody, {
      animation: 150,
      handle: 'tr',
      onEnd: async (evt) => {
        const oldIndex = evt.oldIndex ?? -1;
        const newIndex = evt.newIndex ?? -1;
        if (oldIndex === -1 || newIndex === -1) return;
        const draggedRow = children[oldIndex];
        const targetRow = children[newIndex];
        if (
          !draggedRow ||
          !targetRow ||
          draggedRow.addressLevelCode !== targetRow.addressLevelCode ||
          draggedRow.parentAddressId !== targetRow.parentAddressId
        ) {
          ElMessage.warning('只允许同层级内排序');
          queryAddressData();
          return;
        }
        let siblings;
        if (draggedRow.parentAddressId) {
          // 这里children就是同层级，不需要再找parent
          siblings = children;
        } else {
          siblings = children;
        }
        const oldSiblingIndex = siblings.findIndex(
          (item) => item.addressId === draggedRow.addressId,
        );
        const newSiblingIndex = siblings.findIndex(
          (item) => item.addressId === targetRow.addressId,
        );
        if (oldSiblingIndex === -1 || newSiblingIndex === -1) {
          ElMessage.warning('排序失败，未找到对应数据');
          queryAddressData();
          return;
        }
        const moved = siblings.splice(oldSiblingIndex, 1)[0];
        siblings.splice(newSiblingIndex, 0, moved);
        const arr = siblings.map((item, idx) => ({
          addressId: item.addressId,
          sort: idx + 1,
        }));
        await updateAddressSortByIds({ addressSortList: arr });
        await queryAddressData();
      },
    });
  };

  watch(addressList, () => {
    nextTick(() => {
      initTableDrag();
    });
  });

  onMounted(() => {
    queryAddressData();
    nextTick(() => {
      initTableDrag();
    });
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('address.list.title', '地址列表')" class="mb-2" />
    <div class="mb-4 mr-2.5 flex w-80">
      <el-input
        v-model="searchParams.keyWord"
        :placeholder="$t('global:placeholder.keyword', '请输入关键字查询')"
        class="mr-2"
        clearable
        prefix-icon="Search"
        @clear="
          () => {
            searchParams.keyWord = '';
            queryAddressData();
          }
        "
        @keyup.enter="() => queryAddressData({ keyWord: searchParams.keyWord })"
      ></el-input>
      <el-button
        v-if="!checkedKeys?.length"
        class="mr-2"
        icon="Search"
        type="primary"
        @click="
          () => {
            queryAddressData({ keyWord: searchParams.keyWord });
          }
        "
      >
        {{ $t('global:search', '查询') }}
      </el-button>
    </div>
    <el-table
      ref="addressTableRef"
      :border="true"
      :data="addressList"
      :default-expand-all="false"
      :highlight-current-row="true"
      :lazy="true"
      :load="loadChildren"
      :loading="loading"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      row-key="addressId"
      style="width: 100%"
      @expand-change="handleExpand"
    >
      <!-- 序号 -->
      <el-table-column
        :label="$t('global.sequence', '序号')"
        min-width="120"
        prop="indexNo"
      >
        <template #default="{ row }">
          <span
            v-if="
              row.addressName.includes('北京') ||
              row.addressName.includes('朝阳')
            "
          >
            {{ row.sort }}
          </span>
          <span v-else>
            {{ Number(row.addressLevelCode) > 1 ? row.sort + 1 : row.sort }}
          </span>
        </template>
      </el-table-column>
      <!-- 地址级别 -->
      <el-table-column
        :label="$t('address.addressTable.addressLevelDesc', '地址级别')"
        min-width="200"
        prop="addressLevelDesc"
      />
      <!-- 地址编码 -->
      <el-table-column
        :label="$t('address.addressTable.addressNo', '地址编码')"
        min-width="120"
        prop="addressNo"
      >
        <template #default="{ row }">
          {{ row.addressNo || '--' }}
        </template>
      </el-table-column>
      <!-- 地址名称 -->
      <el-table-column
        :label="$t('address.addressTable.addressName', '地址名称')"
        min-width="180"
        prop="addressName"
      >
        <template #default="{ row }">
          {{ row.addressName || '--' }}
        </template>
      </el-table-column>
      <!-- 辅助名称 -->
      <el-table-column
        :label="$t('global:secondName', '辅助名称')"
        min-width="150"
        prop="address2ndName"
      >
        <template #default="{ row }">
          {{ row.address2ndName || '--' }}
        </template>
      </el-table-column>
      <!-- 扩展名称 -->
      <el-table-column
        :label="$t('global:thirdName', '扩展名称')"
        min-width="150"
        prop="addressExtName"
      >
        <template #default="{ row }">
          {{ row.addressExtName || '--' }}
        </template>
      </el-table-column>
      <!-- 邮政编码 -->
      <el-table-column
        :label="$t('address.addressTable.postalNo', '邮政编码')"
        min-width="150"
        prop="postalNo"
      >
        <template #default="{ row }">
          {{ row.postalNo || '--' }}
        </template>
      </el-table-column>
      <!-- 是否启用 -->
      <el-table-column
        :label="$t('global:enabledFlag')"
        min-width="100"
        prop="enabledFlag"
      >
        <template #default="{ row }">
          <el-switch
            v-model="row.enabledFlag"
            :active-text="$t('global:enabled')"
            :active-value="ENABLED_FLAG.YES"
            :before-change="() => handleEnableSwitch(row)"
            :inactive-text="$t('global:disabled')"
            :inactive-value="ENABLED_FLAG.NO"
            inline-prompt
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('address.addressTable.nativeFlag', '籍贯标识')"
        min-width="100"
      >
        <template #default="{ row }">
          <el-checkbox
            v-if="row.addressLevelCode === '2' || row.addressLevelCode === '3'"
            v-model="row.nativeFlag"
            :false-label="0"
            :true-label="1"
            @change="() => handleNativeFlagChange(row)"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
