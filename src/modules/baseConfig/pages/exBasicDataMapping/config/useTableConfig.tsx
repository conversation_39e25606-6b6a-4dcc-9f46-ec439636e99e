/* eslint-disable @typescript-eslint/no-explicit-any */
import { Ref } from 'vue';
import { ElMessage } from 'element-sun';
import { dayjs } from '@sun-toolkit/shared';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { ENABLED_FLAG, FLAG_STR } from '@/utils/constant';
import { HIS_BASIC_DATA_TYPE_CODE } from '@/modules/baseConfig/pages/exBasicDataDict/constant';
import {
  updateBasicDataMapping,
  deleteBasicDataMapping,
} from '@/modules/baseConfig/api/exBasicDataMapping';

export function useHisDataTableConfig(
  searchHisBasicDataTypeCode: Ref<string | undefined>,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:sequence'),
          prop: 'indexNo',
          minWidth: 80,
          render: (
            row: ExBasicDataMapping.HisDataAndMappingInfo,
            $index: number,
          ) => <>{$index + 1}</>,
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 100,
          render: (row: ExBasicDataMapping.HisDataAndMappingInfo) => {
            return (
              <div>
                {row.enabledFlag === ENABLED_FLAG.YES
                  ? t('global:enabled')
                  : t('global:disabled')}
              </div>
            );
          },
        },
        {
          label: t(
            'exBasicDataMapping.hisDataTable.mappingStatusDesc',
            '对照状态',
          ),
          prop: 'mappingStatusDesc',
          minWidth: 100,
          fixed: 'right',
        },
      ];
      let typeData = [] as any;
      switch (searchHisBasicDataTypeCode.value) {
        case HIS_BASIC_DATA_TYPE_CODE.DRUG:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.commodityNo', '编码'),
              prop: 'commodityNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityNameDisplay',
                '名称',
              ),
              prop: 'commodityNameDisplay',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.commoditySpec', '规格'),
              prop: 'commoditySpec',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.unitName', '单位'),
              prop: 'unitName',
              minWidth: 100,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityPurchasePrice',
                '进价',
              ),
              prop: 'commodityPurchasePrice',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.price', '价格'),
              prop: 'price',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.memo', '备注'),
              prop: 'memo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.producedByOrgName',
                '生产厂家',
              ),
              prop: 'producedByOrgName',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.approvalNo',
                '批准文号',
              ),
              prop: 'approvalNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.validPeriod',
                '有效期（月）',
              ),
              prop: 'validPeriod',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityCategoryName',
                '费用分类',
              ),
              prop: 'commodityCategoryName',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.PROJECT:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.commodityNo', '编码'),
              prop: 'commodityNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityNameDisplay',
                '名称',
              ),
              prop: 'commodityNameDisplay',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.commoditySpec', '规格'),
              prop: 'commoditySpec',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.unitName', '单位'),
              prop: 'unitName',
              minWidth: 100,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityCategoryName',
                '费用分类',
              ),
              prop: 'commodityCategoryName',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.price', '价格'),
              prop: 'price',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.memo', '备注'),
              prop: 'memo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.producedByOrgName',
                '生产厂家',
              ),
              prop: 'producedByOrgName',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.chargeItemConnotation',
                '项目内涵',
              ),
              prop: 'chargeItemConnotation',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.DIAGNOSIS:
          typeData = [];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.DOCTOR:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.userNo', '编码'),
              prop: 'userNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.userNameDisplay',
                '名称',
              ),
              prop: 'userNameDisplay',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.userTypeDesc',
                '用户类别',
              ),
              prop: 'userTypeDesc',
              minWidth: 100,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.userJobDesc',
                '用户岗位描述（语言环境）',
              ),
              prop: 'userJobDesc',
              minWidth: 150,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.genderDesc', '性别'),
              prop: 'genderDesc',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.DEPARTMENT:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.orgNo', '编码'),
              prop: 'orgNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.orgNameDisplay',
                '名称',
              ),
              prop: 'orgNameDisplay',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.deptTypeDesc',
                '科室类型',
              ),
              prop: 'deptTypeDesc',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.WARD:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.orgNo', '编码'),
              prop: 'orgNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.orgNameDisplay',
                '名称',
              ),
              prop: 'orgNameDisplay',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.HOSPITAL:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.orgNo', '编码'),
              prop: 'orgNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.orgNameDisplay',
                '名称',
              ),
              prop: 'orgNameDisplay',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.PAYMENT_METHOD:
          typeData = [
            {
              label: t('exBasicDataMapping.hisDataTable.payWayNo', '编码'),
              prop: 'payWayNo',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.payWayNameDisplay',
                '名称',
              ),
              prop: 'payWayNameDisplay',
              minWidth: 120,
            },
          ];
          break;
        case HIS_BASIC_DATA_TYPE_CODE.COST_TYPE:
          typeData = [
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityCategoryNameDisplay',
                '名称',
              ),
              prop: 'commodityCategoryNameDisplay',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityClassDesc',
                '大类描述',
              ),
              prop: 'commodityClassDesc',
              minWidth: 130,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityCategoryName',
                '费用分类名称',
              ),
              prop: 'commodityCategoryName',
              minWidth: 120,
            },
            {
              label: t(
                'exBasicDataMapping.hisDataTable.commodityCategoryWayDesc',
                '费用分类方式',
              ),
              prop: 'commodityCategoryWayDesc',
              minWidth: 100,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.spellNo', '拼音码'),
              prop: 'spellNo',
              minWidth: 120,
            },
            {
              label: t('exBasicDataMapping.hisDataTable.wbNo', '五笔码'),
              prop: 'wbNo',
              minWidth: 120,
            },
          ];
          break;
        default:
          break;
      }
      data.splice(0, 1, ...typeData);
      return data;
    },
  });
}

type MappingEditItem = ExBasicDataMapping.MappingItem & { editable: boolean };

export function useMappingTableColumnConfig(
  tableRef: Ref<TableRef>,
  data: Ref<MappingEditItem[]>,
  isAddMapping: Ref<boolean>,
  currentHisDataMappingStatusCode: Ref<string>,
  queryHisDataAndMappingList: () => Promise<void>,
  handleBindData: (data: ExBasicDataMapping.MappingItem) => void,
) {
  const { t } = useTranslation();

  const { toggleEdit, cancelEdit, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'basicDataMappingId',
  });

  const onConfirmEdit = async (data: MappingEditItem) => {
    const { basicDataMappingId, startAt, endAt } = data;
    const [, res] = await updateBasicDataMapping({
      basicDataMappingId,
      startAt,
      endAt,
    });
    if (res?.success) {
      ElMessage.success(t('global:edit.success'));
      toggleEdit(data);
    }
  };

  const onDeleteItem = async (row: MappingEditItem, index: number) => {
    const { basicDataMappingId } = row;
    const [, res] = await deleteBasicDataMapping({
      basicDataMappingId,
    });
    if (res?.success) {
      ElMessage.success(t('global:delete.success'));
      delItem(index);
      if (!data.value.length) {
        queryHisDataAndMappingList();
      }
    }
  };

  const mappingListTableColumnsConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('exBasicDataMapping.mappingTable.basicDataNo', '基础数据编码'),
        prop: 'basicDataNo',
        minWidth: 120,
      },
      {
        label: t(
          'exBasicDataMapping.mappingTable.basicDataName',
          '基础数据名称',
        ),
        prop: 'basicDataName',
        minWidth: 150,
      },
      {
        label: t(
          'exBasicDataMapping.mappingTable.basicDataDictTypeName',
          '目录分类',
        ),
        prop: 'basicDataDictTypeName',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: MappingEditItem) => {
          return (
            <div>
              {row.enabledFlag === ENABLED_FLAG.YES
                ? t('global:enabled')
                : t('global:disabled')}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataMapping.mappingTable.basicDataSourceDesc',
          '数据来源',
        ),
        prop: 'basicDataSourceDesc',
        minWidth: 100,
      },
      {
        label: t('exBasicDataMapping.mappingTable.startAt', '有效开始时间'),
        prop: 'startAt',
        minWidth: 170,
        required: true,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataMapping.mappingTable.startAt',
                '有效开始时间',
              ),
            }),
            trigger: 'change',
          },
        ],
        render: (row: MappingEditItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.startAt}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataMapping.mappingTable.startAt',
                      '有效开始时间',
                    ),
                  })}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.endAt &&
                      dayjs(time).isAfter(dayjs(row.endAt), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.startAt}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataMapping.mappingTable.endAt', '有效结束时间'),
        required: true,
        editable: true,
        prop: 'endAt',
        minWidth: 170,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('exBasicDataMapping.mappingTable.endAt', '有效结束时间'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: MappingEditItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.endAt}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataMapping.mappingTable.endAt',
                      '有效结束时间',
                    ),
                  })}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.startAt &&
                      dayjs(time).isBefore(dayjs(row.startAt), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.endAt}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'interfaceId',
        width: 130,
        render: (row: MappingEditItem, $index: number) => {
          return (
            <>
              {currentHisDataMappingStatusCode.value === FLAG_STR.YES &&
              !isAddMapping.value ? (
                row.editable ? (
                  <div class={'flex justify-around'}>
                    <el-button
                      type="danger"
                      link={true}
                      onClick={() => cancelEdit(row, $index, false)}
                    >
                      {t('global:cancel')}
                    </el-button>
                    <el-button
                      type="primary"
                      link={true}
                      onClick={() => onConfirmEdit(row)}
                    >
                      {t('global:confirm')}
                    </el-button>
                  </div>
                ) : (
                  <div class={'flex justify-around'}>
                    <el-button
                      type="danger"
                      link={true}
                      onClick={() => onDeleteItem(row, $index)}
                    >
                      {t('exBasicDataMapping.mappingTable.unbind', '解绑')}
                    </el-button>
                    <el-button
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:edit')}
                    </el-button>
                  </div>
                )
              ) : (
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleBindData(row)}
                >
                  {t('exBasicDataMapping.mappingTable.bind', '绑定')}
                </el-button>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    mappingListTableColumnsConfig,
  };
}
