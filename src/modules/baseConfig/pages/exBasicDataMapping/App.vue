<script setup lang="ts" name="exBasicDataMapping">
  import { ref, onBeforeMount, watch, Ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { SelectOptions } from '@/typings/common';
  import { dayjs } from '@sun-toolkit/shared';
  import { ElMessage } from 'element-sun';
  import { ENABLED_FLAG, FLAG_STR } from '@/utils/constant';
  import {
    MAPPING_DATA_SOURCE_CODE,
    HIS_BASIC_DATA_TYPE_CODE,
  } from '@/modules/baseConfig/pages/exBasicDataDict/constant';
  import {
    Title,
    ProForm,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import {
    useHisDataSearchFormConfig,
    useMappingSearchFormConfig,
  } from './config/useFormConfig.ts';
  import {
    useHisDataTableConfig,
    useMappingTableColumnConfig,
  } from './config/useTableConfig.tsx';
  import {
    queryExBasicDtaDictListByExample,
    queryExBasicDtaListByExample,
    queryExBasicDtaDictTypeListByExample,
  } from '@/modules/baseConfig/api/exBasicDataDict';
  import {
    addBasicDataMapping,
    getHisBasicDataTypeList,
    queryHisDataAndMappingByExample,
  } from '@/modules/baseConfig/api/exBasicDataMapping';

  type MappingItemEdit = ExBasicDataMapping.MappingItem & { editable: boolean };

  const loadingPage = ref(true);
  const { t } = useTranslation();
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const hisDataTableLoading = ref(false);
  const searchParams = ref<ExBasicDataMapping.HisDataAndMappingQueryParams>({
    basicDataDictId: '',
    hisBasicDataTypeCode: '',
    hisBasicDataSubTypeCode: '',
    enabledFlag: ENABLED_FLAG.ALL,
    mappingStatusCode: '-1',
    keyWord: '',
    pageNumber: 1,
    pageSize: 25,
  }); // HIS数据查询参数
  const mappingSearchParams = ref<ExBasicDataDict.ExBasicDtaQueryParams>({
    basicDataDictId: '',
    basicDataDictTypeId: '',
    hisBasicDataTypeCode: '',
    keyword: '',
    pageNumber: 1,
    pageSize: 25,
  }); // 数据对照查询参数
  const total = ref(0); // HIS数据总条数
  const exBasicDtaListTotal = ref(0); // 数据对照总条数
  const exBasicDataDictSelections = ref<
    (ExBasicDataDict.ExBasicDtaDictInfo & SelectOptions)[]
  >([]); // 目录选择项列表
  const hisBasicDataTypeSelections = ref<SelectOptions[]>([]); // 分类选择项列表
  const hisDataList = ref<ExBasicDataMapping.HisDataAndMappingInfo[]>([]); // HIS数据列表
  const currentHisData = ref<ExBasicDataMapping.HisDataAndMappingInfo>(); // 当前选中的HIS数据
  const currentHisDataMappingStatusCode = ref(''); // 当前选中的HIS数据对照状态
  const searchHisBasicDataTypeCode = ref<string>(); // 当前查询HIS数据的分类选择项
  const mappingList = ref<MappingItemEdit[]>([]); // 数据对照列表
  const isAddMapping = ref(false); // 继续添加对照
  const mappingListTableLoading = ref(false);
  const mappingListTableRef = ref();
  const exBasicDtaDictTypeSelections = ref<
    { label?: string; value?: string }[]
  >([]); // 上方查询“分类”对应外部目录分类列表

  const queryExBasicDtaDictTypeSelections = async () => {
    if (
      !searchParams.value?.basicDataDictId ||
      !searchHisBasicDataTypeCode?.value
    )
      return;
    let hisBasicDataTypeCode = searchHisBasicDataTypeCode.value;
    if (searchHisBasicDataTypeCode.value.includes('/')) {
      const ids = searchHisBasicDataTypeCode.value.split('/');
      hisBasicDataTypeCode = ids[0];
    }
    const [, res] = await queryExBasicDtaDictTypeListByExample({
      basicDataDictId: searchParams.value.basicDataDictId,
      hisBasicDataTypeCode,
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      exBasicDtaDictTypeSelections.value = (res.data || []).map((item) => ({
        label: item?.basicDataDictTypeName,
        value: item?.basicDataDictTypeId,
      }));
      mappingSearchParams.value.basicDataDictTypeId =
        exBasicDtaDictTypeSelections.value.length > 0
          ? exBasicDtaDictTypeSelections.value[0].value
          : '';
    }
  };

  watch(
    () => [
      searchHisBasicDataTypeCode?.value,
      searchParams.value.basicDataDictId,
    ],
    () => {
      mappingSearchParams.value.basicDataDictId =
        searchParams.value.basicDataDictId;
      // 上半部目录和分类查询条件变化后重新查询下半部目录分类列表
      queryExBasicDtaDictTypeSelections();
    },
    {
      immediate: true,
    },
  );

  // 获取目录选择项列表
  const queryExBasicDtaDictList = async () => {
    const [, res] = await queryExBasicDtaDictListByExample({
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      exBasicDataDictSelections.value = (res.data || []).map((item) => ({
        ...item,
        label: item?.basicDataDictName,
        value: item?.basicDataDictId,
      }));
      const standardFlagRow = exBasicDataDictSelections.value.find(
        (item) => !!item.standardFlag,
      );
      searchParams.value.basicDataDictId = (standardFlagRow?.value ||
        exBasicDataDictSelections.value[0].value) as string;
      mappingSearchParams.value.basicDataDictId =
        searchParams.value.basicDataDictId;
    }
  };

  // 获取分类选择项列表
  const queryHisBasicDataTypeList = async () => {
    const [, res] = await getHisBasicDataTypeList({});
    if (res?.success && res.data?.length) {
      hisBasicDataTypeSelections.value = res.data.map((item) => ({
        label: item.hisBasicDataTypeDesc,
        value: item.hisBasicDataTypeCode,
        children: item.hisBasicDataSubTypeList?.length
          ? [
              ...item.hisBasicDataSubTypeList.map((subItem) => ({
                label: subItem.hisBasicDataSubTypeDesc,
                value: `${item.hisBasicDataTypeCode}/${subItem.hisBasicDataSubTypeCode}`,
              })),
            ]
          : undefined,
      }));
      searchParams.value.hisBasicDataTypeCode = hisBasicDataTypeSelections
        .value?.[0]?.value as string;
      searchHisBasicDataTypeCode.value = hisBasicDataTypeSelections.value?.[0]
        ?.value as string;
    }
  };

  // 查询上部分his数据
  const queryHisDataAndMappingList = async (
    data?: Partial<ExBasicDataMapping.HisDataAndMappingQueryParams>,
  ) => {
    currentHisData.value = undefined;
    currentHisDataMappingStatusCode.value = '';
    hisDataTableLoading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
      mappingStatusCode:
        searchParams.value.mappingStatusCode === '-1'
          ? undefined
          : searchParams.value.mappingStatusCode,
      hospitalId: currentOrg?.orgId || '',
    };
    if (
      params.hisBasicDataTypeCode &&
      params.hisBasicDataTypeCode.includes('/')
    ) {
      const ids = params.hisBasicDataTypeCode.split('/');
      params.hisBasicDataTypeCode = ids[0];
      params.hisBasicDataSubTypeCode = ids[1];
    }
    searchHisBasicDataTypeCode.value = params.hisBasicDataTypeCode;
    mappingSearchParams.value.hisBasicDataTypeCode =
      params.hisBasicDataTypeCode;
    try {
      const [, res] = await queryHisDataAndMappingByExample(params);
      if (res?.success) {
        hisDataList.value = res.data || [];
        total.value = res.total;
      }
    } catch {
      hisDataList.value = [];
      total.value = 0;
    } finally {
      hisDataTableLoading.value = false;
    }
  };

  const queryExBasicDtaList = async (
    data?: Partial<ExBasicDataDict.ExBasicDtaQueryParams>,
  ) => {
    mappingListTableLoading.value = true;
    if (data) {
      mappingSearchParams.value = {
        ...mappingSearchParams.value,
        ...data,
      };
    }
    if (!mappingSearchParams.value.basicDataDictTypeId) {
      // 目录分类为空时不触发查询
      mappingList.value = [];
      mappingListTableLoading.value = false;
      return;
    }
    const [, res] = await queryExBasicDtaListByExample({
      ...mappingSearchParams.value,
      enabledFlag: ENABLED_FLAG.YES,
    });
    mappingListTableLoading.value = false;
    if (res?.success) {
      if (res?.data?.length > 0) {
        mappingList.value = res.data.map((item) => ({
          ...item,
          startAt: item.startAt || dayjs().format('YYYY-MM-DD HH:mm:ss'),
          endAt: item.endAt || '2099-12-31 23:59:59',
          editable: true,
        })) as MappingItemEdit[];
      } else {
        mappingList.value = [];
      }
      exBasicDtaListTotal.value = res.total;
    }
  };

  const handleSelectRow = async (
    rowData: ExBasicDataMapping.HisDataAndMappingInfo,
  ) => {
    if (currentHisData.value?.hisBasicDataId === rowData.hisBasicDataId) return;
    currentHisData.value = rowData;
    currentHisDataMappingStatusCode.value = rowData.mappingStatusCode;
    isAddMapping.value = false;
    switch (searchHisBasicDataTypeCode.value) {
      case HIS_BASIC_DATA_TYPE_CODE.DRUG:
      case HIS_BASIC_DATA_TYPE_CODE.PROJECT:
        mappingSearchParams.value.keyword = rowData.commodityNameDisplay as
          | string
          | undefined;
        break;
      case HIS_BASIC_DATA_TYPE_CODE.DOCTOR:
        mappingSearchParams.value.keyword = rowData.userNameDisplay as
          | string
          | undefined;
        break;
      case HIS_BASIC_DATA_TYPE_CODE.DEPARTMENT:
      case HIS_BASIC_DATA_TYPE_CODE.WARD:
      case HIS_BASIC_DATA_TYPE_CODE.HOSPITAL:
        mappingSearchParams.value.keyword = rowData.orgNameDisplay as
          | string
          | undefined;
        break;
      case HIS_BASIC_DATA_TYPE_CODE.PAYMENT_METHOD:
        mappingSearchParams.value.keyword = rowData.payWayNameDisplay as
          | string
          | undefined;
        break;
      case HIS_BASIC_DATA_TYPE_CODE.COST_TYPE:
        mappingSearchParams.value.keyword = rowData.commodityCategoryName as
          | string
          | undefined;
        break;
      default:
        mappingSearchParams.value.keyword = '';
        break;
    }
    if (rowData.mappingStatusCode === FLAG_STR.YES) {
      mappingList.value = (rowData.mappingList || []) as MappingItemEdit[];
    } else {
      mappingList.value = [];
      await queryExBasicDtaList();
    }
  };

  const handleBindData = async (data: ExBasicDataMapping.MappingItem) => {
    const {
      basicDataDictTypeId,
      basicDataId,
      startAt,
      endAt,
      basicDataNo,
      basicDataName,
    } = data;
    const params = {
      basicDataDictTypeId,
      basicDataId,
      startAt,
      endAt,
      hisBasicDataTypeCode: searchParams.value.hisBasicDataTypeCode as string,
      hisBasicDataId: currentHisData.value?.hisBasicDataId as string,
      mappingDataSourceCode: MAPPING_DATA_SOURCE_CODE.SEARCH_MAPPING, // 目前仅支持检索对照，写死
      basicDataNo,
      basicDataName,
    };
    const [, res] = await addBasicDataMapping(params);
    if (res?.success) {
      ElMessage.success(
        t('exBasicDataMapping.mappingTable.bindSuccess', '绑定成功'),
      );
      isAddMapping.value = false;
      const oldCurrentHisDataHisBasicDataId =
        currentHisData.value!.hisBasicDataId;
      await queryHisDataAndMappingList();
      currentHisData.value = (hisDataList.value?.find(
        (item) => item?.hisBasicDataId === oldCurrentHisDataHisBasicDataId,
      ) || {}) as ExBasicDataMapping.HisDataAndMappingInfo;
      currentHisDataMappingStatusCode.value =
        currentHisData.value?.mappingStatusCode || '';
      mappingList.value = (currentHisData.value?.mappingList ||
        []) as MappingItemEdit[];
    }
  };

  const onAddMappingClick = async () => {
    mappingList.value = [];
    await queryExBasicDtaList();
    isAddMapping.value = true;
  };

  const onCancelAddMappingClick = () => {
    isAddMapping.value = false;
    mappingList.value = (currentHisData.value!.mappingList ||
      []) as MappingItemEdit[];
  };

  const searchConfig = useHisDataSearchFormConfig(
    exBasicDataDictSelections,
    hisBasicDataTypeSelections,
    queryHisDataAndMappingList,
  );
  const basicDataDictTypeTableColumnsConfig = useHisDataTableConfig(
    searchHisBasicDataTypeCode,
  );
  const mappingSearchConfig = useMappingSearchFormConfig(
    exBasicDtaDictTypeSelections as Ref<SelectOptions[]>,
    queryExBasicDtaList,
  );
  const { mappingListTableColumnsConfig } = useMappingTableColumnConfig(
    mappingListTableRef,
    mappingList,
    isAddMapping,
    currentHisDataMappingStatusCode,
    queryHisDataAndMappingList,
    handleBindData,
  );

  onBeforeMount(async () => {
    loadingPage.value = true;
    await queryExBasicDtaDictList();
    await queryHisBasicDataTypeList();
    await queryHisDataAndMappingList();
    loadingPage.value = false;
  });
</script>
<template>
  <div class="p-box flex h-full flex-col" v-loading="loadingPage">
    <div class="flex flex-1 flex-col">
      <Title :title="$t('exBasicDataMapping.hisDataList.title', 'HIS数据')" />
      <div class="el-form-item my-2">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :column="5"
          :data="searchConfig"
          @model-change="queryHisDataAndMappingList"
        />
      </div>
      <ProTable
        ref="hisDataListTableRef"
        row-key="hisBasicDataId"
        :style="{ maxHeight: currentHisData ? '284px' : 'none' }"
        highlight-current-row
        row-class-name="cursor-pointer"
        :data="hisDataList"
        :loading="hisDataTableLoading"
        :columns="basicDataDictTypeTableColumnsConfig"
        :page-info="{
          total,
          pageNumber: searchParams.pageNumber,
          pageSize: searchParams.pageSize,
        }"
        :pagination="true"
        layout="prev, pager, next, sizes, jumper"
        @row-click="handleSelectRow"
        @current-page-change="
          (val: number) => {
            queryHisDataAndMappingList({
              pageNumber: val,
            });
          }
        "
        @size-page-change="
          (val: number) => {
            queryHisDataAndMappingList({
              pageSize: val,
            });
          }
        "
      />
    </div>
    <div
      v-if="currentHisData && currentHisData.hisBasicDataId"
      class="flex flex-1 flex-col"
    >
      <Title :title="$t('exBasicDataMapping.mappingList.title', '数据对照')" />
      <div
        v-if="isAddMapping || currentHisDataMappingStatusCode === FLAG_STR.NO"
        class="el-form-item my-2 flex justify-between"
      >
        <ProForm
          v-model="mappingSearchParams"
          layout-mode="inline"
          :column="2"
          :data="mappingSearchConfig"
          @model-change="queryExBasicDtaList"
        />
        <el-button v-if="isAddMapping" @click="onCancelAddMappingClick">
          {{ $t('global:cancel') }}
        </el-button>
      </div>
      <div
        v-else-if="currentHisDataMappingStatusCode === FLAG_STR.YES"
        class="mb-2 flex justify-end"
      >
        <el-button type="primary" @click="onAddMappingClick">
          {{ $t('exBasicDataMapping.mappingList.add', '继续添加') }}
        </el-button>
      </div>
      <ProTable
        ref="mappingListTableRef"
        row-key="basicDataMappingId"
        :editable="true"
        :data="mappingList"
        :loading="mappingListTableLoading"
        :columns="mappingListTableColumnsConfig"
        max-height="234"
        :pagination="
          !!(isAddMapping || currentHisDataMappingStatusCode === FLAG_STR.NO)
        "
        :page-info="{
          total: exBasicDtaListTotal,
          pageNumber: mappingSearchParams.pageNumber,
          pageSize: mappingSearchParams.pageSize,
        }"
        layout="prev, pager, next, sizes, jumper"
        @current-page-change="
          (val: number) => {
            queryExBasicDtaList({
              pageNumber: val,
            });
          }
        "
        @size-page-change="
          (val: number) => {
            queryExBasicDtaList({
              pageSize: val,
            });
          }
        "
      />
    </div>
  </div>
</template>
