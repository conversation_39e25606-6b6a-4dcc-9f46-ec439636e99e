<script lang="ts" name="appEnvDefine" setup>
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { computed, onMounted, ref } from 'vue';
  import { useAppEnvFormConfig } from './config/useFormConfig.tsx';
  import { useAppEnvTableConfig } from './config/useTableConfig.tsx';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    addAppEnv,
    deleteAppEnv,
    editAppEnv,
    queryAppEnvByExample,
  } from '@/modules/baseConfig/api/appEnvDefine.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    AppEnvDefineAppEnvList,
    AppEnvDefineQueryParams,
    AppEnvDefineUpsertAppEnv,
  } from '@/modules/baseConfig/typings/appEnvDefine.ts';
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const appEnvDefineTableRef = ref();
  const selectTableData = ref<AppEnvDefineAppEnvList[]>([]);

  let queryAppEnvListParams = ref<AppEnvDefineQueryParams>({
    appEnvIds: [],
    keyWord: '',
  }); // 查询参数
  const appEnvDefineList = ref<AppEnvDefineAppEnvList[]>([]); // 表格数据
  const loading = ref(false);

  // 查询联系方式列表
  const queryAppEnvListData = async (params: AppEnvDefineQueryParams = {}) => {
    loading.value = true;
    queryAppEnvListParams.value = {
      ...queryAppEnvListParams.value,
      ...params,
    };
    let [, res] = await queryAppEnvByExample({
      ...queryAppEnvListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      appEnvDefineList.value = res.data || [];
    }
  };

  // 启用状态改变
  const changeSelect = async (data?: AppEnvDefineQueryParams) => {
    queryAppEnvListParams.value = {
      ...queryAppEnvListParams.value,
      ...data,
    };
    await queryAppEnvListData();
  };

  const addNewAppEnv = async () => {
    if (!canUpsertTableRow()) return;
    appEnvDefineList.value.push({
      appEnvId: '',
      appEnvName: '',
      enabledFlag: ENABLED_FLAG.YES,
      editableFlag: isCloudEnv ? ENABLED_FLAG.NO : ENABLED_FLAG.YES,
      editable: true,
    });
  };
  const saveRow = async (row: AppEnvDefineUpsertAppEnv, index: number) => {
    const isValid = await appEnvDefineTableRef?.value?.validateRow(index);
    if (!isValid) return;
    let result: object | undefined;
    if (row.appEnvId === '') {
      const [, res] = await addAppEnv({ ...row });
      result = res;
    } else {
      const [, res] = await editAppEnv({ ...row });
      result = res;
    }
    if (result?.success) {
      await queryAppEnvListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };
  const handleEnableSwitch = async (
    row: AppEnvDefineAppEnvList,
    action: string,
  ) => {
    if (!row.appEnvId) {
      ElMessage({
        type: 'warning',
        message: t(
          'appEnvDefine.enableSwitch.appEnvIdEmpty',
          '请先保存后再操作',
        ),
      });
      return;
    }
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
        action:
          action === 'enable'
            ? row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled')
            : row.editableFlag === ENABLED_FLAG.YES
              ? t('appEnvDefine.enabledEditableFlag', '停用编辑')
              : t('appEnvDefine.disabledEditableFlag', '启用编辑'),
        name: row.appEnvName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      let params: AppEnvDefineUpsertAppEnv = {};
      if (action === 'enable') {
        params = {
          ...row,
          enabledFlag:
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        };
      } else {
        params = {
          ...row,
          editableFlag:
            row.editableFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        };
      }
      const [, res] = await editAppEnv(params);
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:modify.success'),
        });
        queryAppEnvListData();
      }
    });
  };
  const deleteAppEnvByID = async (row: AppEnvDefineAppEnvList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.appEnvName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      console.log(row);
      const [, res] = await deleteAppEnv({
        appEnvId: row.appEnvId,
      });
      if (res?.success) {
        appEnvDefineTableRef?.value.proTableRef.clearSelection();
        selectTableData.value = [];
        queryAppEnvListData();
      }
    });
  };

  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.appEnvId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: AppEnvDefineUpsertAppEnv[]) => {
    selectTableData.value = val;
  };
  const canUpsertTableRow = () => {
    const isEditing = appEnvDefineList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  // 编辑
  const handleEdit = (data: DataTypeManage.UpsertDataTypeManageParams) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(data);
  };
  const searchConfig = useAppEnvFormConfig(queryAppEnvListData);
  const { tableColumns, toggleEdit } = useAppEnvTableConfig({
    id: 'appEnvId',
    tableRef: appEnvDefineTableRef,
    data: appEnvDefineList,
    saveRow,
    handleEnableSwitch,
    deleteAppEnvByID,
    isCloudEnv,
    handleEdit,
  });
  onMounted(async () => {
    await queryAppEnvListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('appEnvDefine.list.title', '应用环境定义')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryAppEnvListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button class="mr-2" type="primary" @click="queryAppEnvListData">
          {{ $t('appEnvDefine.search', '搜索') }}
        </el-button>
        <el-button class="mr-2" type="primary" @click="addNewAppEnv">
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_APP_ENV"
        @success="
          () => {
            appEnvDefineTableRef?.proTableRef.clearSelection();
          }
        "
      />
    </div>
    <ProTable
      ref="appEnvDefineTableRef"
      :columns="tableColumns"
      :data="appEnvDefineList"
      :editable="true"
      :loading="loading"
      row-key="appEnvId"
      @selection-change="selectionChange"
    />
  </div>
</template>
