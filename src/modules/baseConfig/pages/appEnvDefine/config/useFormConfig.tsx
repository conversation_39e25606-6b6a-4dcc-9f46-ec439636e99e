import { useFormConfig } from 'sun-biz';
import { AppEnvDefineQueryParams } from '@/modules/baseConfig/typings/appEnvDefine.ts';

export function useAppEnvFormConfig(
  queryAppEnvListData: (params?: AppEnvDefineQueryParams) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '环境名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appEnvDefine.form.keyWord', '环境名称'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              await queryAppEnvListData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}
