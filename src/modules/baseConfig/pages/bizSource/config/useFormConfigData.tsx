import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { filterSelectData } from '@sun-toolkit/shared';
import { BIZ_SOURCE_CODE_NAME, FLAG } from '@/utils/constant';

export function useBizSourceFormConfig(options: {
  rowValue: Origin.BizSourceInfo | undefined;
  isCloudEnv: boolean;
  hospitalId: string;
  filterList: Ref<Origin.BizSourceInfo[]>;
  dictEncResAccesser: Ref<Channel.DictEncResAccesser[]>;
  queryChannelManageList: (keyWord: string) => Promise<void>;
}) {
  const {
    rowValue,
    isCloudEnv,
    filterList,
    dictEncResAccesser,
    queryChannelManageList,
  } = options;
  // 用户端禁用，云端不禁用
  const disableFlag =
    isCloudEnv === false && rowValue?.editableFlag === FLAG.NO;

  //弹窗form
  return useFormConfig({
    dataSetCodes: [BIZ_SOURCE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'bizSourceNo',
        label: t('global:code'),
        component: 'input',
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:code'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'bizSourceName',
        label: t('global:name'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'bizSourceAuxName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:secondName'),
            }),
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'bizSourceExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: disableFlag
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:thirdName'),
            }),
        extraProps: {
          disabled: disableFlag,
        },
      },
      {
        name: 'dictEncResAccesserId',
        label: t('bizSource.table.EncResAccesser', '号源使用渠道'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('bizSource.table.EncResAccesser', '号源使用渠道'),
        }),
        extraProps: {
          options: dictEncResAccesser.value,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: queryChannelManageList,
          props: {
            label: 'dictEncResAccesserNameDisplay',
            value: 'dictEncResAccesserId',
          },
        },
      },
      {
        name: 'bizSourceCode',
        label: t('bizSource.table.codeNo', '业务来源代码'),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizSource.table.codeNo', '业务来源代码'),
            }),
            trigger: 'change',
          },
        ],

        render: () => {
          return (
            <el-select
              modelValue={rowValue?.bizSourceCode}
              filterable
              clearable
              placeholder={
                disableFlag
                  ? ''
                  : t('global:placeholder.select.template', {
                      name: t('bizSource.table.codeNo', '业务来源代码'),
                    })
              }
            >
              {{
                default: () =>
                  (dataSet?.value
                    ? filterSelectData(
                        dataSet.value[BIZ_SOURCE_CODE_NAME],
                        filterList.value,
                        'bizSourceCode',
                        'dataValueNo',
                      )
                    : []
                  ).map(
                    (item: {
                      dataValueNameDisplay: string;
                      dataValueNo: string;
                    }) => (
                      <el-option
                        label={`${item.dataValueNameDisplay}（${item.dataValueNo}）`} // 修改为 aa（01）形式
                        value={item.dataValueNo}
                        key={item.dataValueNo}
                      ></el-option>
                    ),
                  ),
                // 自定义选中项的显示内容
                label: (option: { label: string }) => {
                  if (option && option.label) {
                    const displayText = option.label.split('（')[0]; // 只取 aa 部分
                    return [<span>{displayText}</span>]; // 返回只包含 aa 的 VNode
                  }
                  return [<span></span>]; // 返回一个空的 VNode 数组
                },
              }}
            </el-select>
          );
        },
      },
      {
        name: 'editableFlag',
        label: t('bizSource.table.allowEdit', '允许编辑'),
        component: 'checkbox',
        isHidden: !isCloudEnv,
        extraProps: {
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          disabled: disableFlag,
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
}
