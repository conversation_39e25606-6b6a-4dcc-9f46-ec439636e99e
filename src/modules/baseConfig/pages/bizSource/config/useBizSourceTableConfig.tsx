import { FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

// 来源table的tsx
export function useBizSourceConfig(
  hospitalId: string | undefined,
  isCloudEnv: boolean | undefined,
  operationFn: (row: Origin.BizSourceInfo) => Promise<void>,
  handleEnableSwitch: (row: Origin.BizSourceInfo) => Promise<void>,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        type: 'selection',
        prop: 'selection',
        minWidth: 60,
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('bizSource.table.code', '业务来源编码'),
        prop: 'bizSourceNo',
        minWidth: 150,
      },
      {
        label: t('bizSource.table.codeNo', '业务来源代码'),
        prop: 'bizSourceCode',
        minWidth: 150,
      },
      {
        label: t('bizSource.table.name', '业务来源名称'),
        prop: 'bizSourceName',
        minWidth: 150,
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global:secondName'),
        prop: 'bizSourceAuxName',
        minWidth: 150,
      },
      {
        label: t('global:thirdName'),
        prop: 'bizSourceExtName',
        minWidth: 150,
      },
      {
        label: t('bizSource.table.EncResAccesserName', '号源使用渠道名称'),
        prop: 'dictEncResAccesserName',
        minWidth: 200,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('bizSource.table.allowEdit', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 150,
        render: (row: Origin.BizSourceInfo) => {
          return (
            <el-checkbox
              modelValue={row.editableFlag === FLAG.YES}
              disabled={true}
            />
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: Origin.BizSourceInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: Origin.BizSourceInfo) => {
          return (
            <el-button
              type="primary"
              link={true}
              onClick={() => operationFn(row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
