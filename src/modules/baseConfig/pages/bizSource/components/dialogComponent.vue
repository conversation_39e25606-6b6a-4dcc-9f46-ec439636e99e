<script setup lang="ts" name="dialogComponent">
  import { FLAG } from '@/utils/constant';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { saveBizSource } from '@/modules/baseConfig/api/bizSource';
  import { type FormInstance } from 'element-sun';
  import { ref, nextTick, Ref } from 'vue';
  import { useChannelManageList } from '../hooks/useChannelManageList';
  import { useBizSourceFormConfig } from '../config/useFormConfigData';
  import { ProForm, ProDialog } from 'sun-biz';
  type FormModelType = {
    bizSourceNo: string | undefined;
    bizSourceName: string | undefined;
    spellNo: string | undefined;
    wbNo: string | undefined;
    bizSourceAuxName: string | undefined;
    bizSourceExtName: string | undefined;
    dictEncResAccesserId: string | undefined;
    bizSourceCode: string | undefined;
    enabledFlag: FLAG;
    editableFlag: FLAG;
  };

  const emit = defineEmits(['success']);
  const props = defineProps<{
    rowValue: Origin.BizSourceInfo | undefined;
    isCloudEnv: boolean;
    dialogTitle: string;
    hospitalId: string;
    tableData: Origin.BizSourceInfo[];
  }>();

  const dialogRef = ref(); //弹窗dialogRef
  const formModelRef = ref<{
    ref: FormInstance;
  }>(); //formRef
  const formModel = ref<FormModelType>({
    bizSourceNo: undefined,
    bizSourceName: undefined,
    spellNo: undefined,
    wbNo: undefined,
    bizSourceAuxName: undefined,
    bizSourceExtName: undefined,
    dictEncResAccesserId: undefined,
    bizSourceCode: undefined,
    enabledFlag: FLAG.YES,
    editableFlag: props.isCloudEnv ? FLAG.NO : FLAG.YES,
  });
  const filterList = ref<Origin.BizSourceInfo[]>([]);

  const { dictEncResAccesser, queryChannelManageList } = useChannelManageList();
  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      dialogRef.value.open();
      const rowValue = cloneDeep(props.rowValue);
      filterList.value = props.tableData.filter(
        (item) => item.bizSourceCode !== rowValue?.bizSourceCode,
      );
      formModel.value = {
        bizSourceNo: rowValue?.bizSourceNo ?? undefined,
        bizSourceName: rowValue?.bizSourceName ?? undefined,
        spellNo: rowValue?.spellNo ?? undefined,
        wbNo: rowValue?.wbNo ?? undefined,
        bizSourceAuxName: rowValue?.bizSourceAuxName ?? undefined,
        bizSourceExtName: rowValue?.bizSourceExtName ?? undefined,
        dictEncResAccesserId: rowValue?.dictEncResAccesserId ?? undefined,
        bizSourceCode: rowValue?.bizSourceCode ?? undefined,
        enabledFlag: rowValue?.enabledFlag ?? FLAG.YES,
        editableFlag:
          rowValue?.editableFlag ?? (props.isCloudEnv ? FLAG.NO : FLAG.YES),
      };
    });
    await queryChannelManageList();
  };

  // 提交
  const handleConfirmSubmit = async () => {
    await formModelRef.value?.ref.validate();
    const params = {
      ...props.rowValue,
      hospitalId: props.hospitalId,
      ...formModel?.value,
    };
    return await saveBizSource(params);
  };

  // form配置
  const bizSourceConfig = useBizSourceFormConfig({
    rowValue: props.rowValue,
    isCloudEnv: props.isCloudEnv,
    hospitalId: props.hospitalId,
    filterList: filterList as Ref<Origin.BizSourceInfo[]>,
    dictEncResAccesser,
    queryChannelManageList,
  });

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <ProForm
      ref="formModelRef"
      layout-mode="column"
      :column="3"
      v-model="formModel"
      :data="bizSourceConfig"
    />
  </ProDialog>
</template>
