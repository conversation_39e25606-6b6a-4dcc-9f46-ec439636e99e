<script setup lang="ts" name="bizSourceManage">
  import { ENABLED_FLAG, BIZ_SOURCE } from '@/utils/constant';
  import { saveBizSource } from '@/modules/baseConfig/api/bizSource';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from './config/useSearchConfigData.ts';
  import { useBizSourceConfig } from './config/useBizSourceTableConfig.tsx';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ref, onBeforeMount, computed } from 'vue';
  import {
    updateBizSourceSortById,
    queryBizSourceByExample,
  } from '@/modules/baseConfig/api/bizSource';
  import {
    Title,
    ProForm,
    ProTable,
    MAIN_APP_CONFIG,
    useAppConfigData,
    DmlButton,
  } from 'sun-biz';
  import dialogComponent from './components/dialogComponent.vue';
  export type searchFormType = {
    keyWord: string | undefined;
    enabledFlag: ENABLED_FLAG;
    hospitalId?: string;
  };

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv, currentOrg } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.CURRENT_ORG,
  ]);
  //  hospitalId
  const hospitalId = currentOrg?.orgId;
  const { t } = useTranslation();

  // 查询业务来源条件
  const searchModel = ref<searchFormType>({
    keyWord: undefined,
    enabledFlag: ENABLED_FLAG.ALL,
    hospitalId: hospitalId,
  });
  const loading = ref(false); //加载状态
  const draggableFlag = ref(true); //是否拖拽
  const tableData = ref<Origin.BizSourceInfo[]>([]); //table数据
  const rowValue = ref<Origin.BizSourceInfo>(); //当前行数据

  const dialogRef = ref(); //dialog弹窗
  const bizData = ref<string[]>([]);

  // 标题
  const dialogTitle = computed(() => {
    return rowValue.value?.bizSourceId
      ? t('origin.manage.title.edit', '编辑业务来源')
      : t('origin.manage.title.add', '新增业务来源');
  });

  /** 获取业务来源列表 */
  const queryBizSourceData = async (
    val: {
      keyWord?: string;
      enabledFlag?: ENABLED_FLAG;
    } = {},
  ) => {
    searchModel.value = {
      ...searchModel.value,
      ...val,
      hospitalId: hospitalId,
    };
    loading.value = true;
    const params = {
      hospitalId: hospitalId,
      enabledFlag:
        searchModel.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchModel.value.enabledFlag,
      keyWord: searchModel.value.keyWord ?? undefined,
    };
    const [, res] = await queryBizSourceByExample(params);
    loading.value = false;
    if (res?.data) {
      tableData.value = res.data ?? [];
      draggableFlag.value =
        searchModel.value.keyWord ||
        searchModel.value.enabledFlag !== ENABLED_FLAG.ALL
          ? false
          : true;
    }
  };

  /** 拖拽排序 */
  const handleSortEnd = async (list: Origin.BizSourceInfo[]) => {
    const arr: Origin.UpdateSortParams[] = [];
    list?.forEach(async (item, index) => {
      arr.push({
        bizSourceId: item.bizSourceId,
        sort: index + 1,
      });
    });
    await updateBizSourceSortById({ bizSourceSortList: arr });
    await queryBizSourceData();
  };

  /** 启用状态切换 */
  const handleEnableSwitch = async (row: Origin.BizSourceInfo) => {
    const params = {
      ...row,
      enabledFlag: (row.enabledFlag === ENABLED_FLAG.YES
        ? ENABLED_FLAG.NO
        : ENABLED_FLAG.YES) as number,
      hospitalId: hospitalId,
    };
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.bizSourceName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await saveBizSource(params);
      if (res?.success) {
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
        await queryBizSourceData();
      }
    });
  };

  /** 新增 */
  const operationFn = async (row: Origin.BizSourceInfo) => {
    rowValue.value = row;
    dialogRef.value.open();
  };

  const searchConfig = useSearchFormConfig({
    queryBizSourceData: queryBizSourceData,
  }); //搜索form配置
  /** 业务来源table配置 */
  const bizSourceColumns = useBizSourceConfig(
    hospitalId,
    isCloudEnv,
    operationFn,
    handleEnableSwitch,
  );

  // 初始获取
  onBeforeMount(async () => {
    await queryBizSourceData();
  });
  //选中列表
  const selectionChange = (val: Origin.BizSourceInfo[]) => {
    bizData.value = val.map((item) => {
      return item.bizSourceId || '';
    });
    console.log('查看拿到的代码', bizData.value);
  };
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('origin.manage.title.list', '业务来源列表')"
      class="mb-2"
    />
    <div class="flex justify-between">
      <!-- 检索项 -->
      <ProForm
        :class="draggableFlag ? 'mb-2' : 'mb-4'"
        :data="searchConfig"
        v-model="searchModel"
        layout-mode="inline"
        :show-search-button="true"
        @model-change="queryBizSourceData"
      />
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_SOURCE"
          class="mr-3"
        ></DmlButton>
        <el-button type="primary" @click="operationFn">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <!-- 业务来源列表 -->
    <pro-table
      :data="tableData"
      :columns="bizSourceColumns"
      :loading="loading"
      :draggable="draggableFlag"
      row-key="bizSourceId"
      @drag-end="handleSortEnd"
      @selection-change="selectionChange"
    />

    <dialogComponent
      ref="dialogRef"
      :row-value="rowValue"
      :dialog-title="dialogTitle"
      :hospital-id="hospitalId as string"
      :is-cloud-env="isCloudEnv as boolean"
      :table-data="tableData"
      @success="queryBizSourceData"
    />
  </div>
</template>
