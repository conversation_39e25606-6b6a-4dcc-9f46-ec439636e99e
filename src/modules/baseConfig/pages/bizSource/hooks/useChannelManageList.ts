import { ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { queryDictEncResAccesserByExample } from '@/modules/schedule/api/resourceAccesser';

// 获取号源渠道
export function useChannelManageList() {
  const loading = ref(false);
  const dictEncResAccesser = ref<Channel.DictEncResAccesser[]>([]);

  const queryChannelManageList = async (keyWord: string = '') => {
    loading.value = true;
    const [, res] = await queryDictEncResAccesserByExample({
      keyWord,
      enabledFlag: ENABLED_FLAG.YES,
    });
    loading.value = false;
    if (res?.success) {
      dictEncResAccesser.value = res.data ?? [];
    }
  };
  return {
    loading,
    dictEncResAccesser,
    queryChannelManageList,
  };
}
