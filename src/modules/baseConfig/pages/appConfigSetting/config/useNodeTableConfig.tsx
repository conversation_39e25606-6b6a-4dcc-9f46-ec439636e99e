import { Ref } from 'vue';
import { useColumnConfig } from 'sun-biz';

export function useNodeTableConfig(options: {
  nodeList?: Ref<AppConfigSetting.AppConfigKeyItem[]>;
  insertRow?: (row: AppConfigSetting.AppConfigKeyItem) => void;
  deleteRow?: (row: AppConfigSetting.AppConfigKeyItem) => void;
}) {
  const { insertRow, deleteRow } = options;
  console.log(insertRow);
  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('appConfigSetting.nodeTable.sort', '序号'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'appConfigSetting.nodeTable.appConfigKeyId',
          '软件配置节点标识',
        ),
        prop: 'appConfigKeyId',
        minWidth: 150,
      },
      {
        label: t(
          'appConfigSetting.nodeTable.appConfigKeyName',
          '软件配置节点名称',
        ),
        prop: 'appConfigKeyName',
        minWidth: 150,
      },
      {
        label: t('appConfigSetting.nodeTable.dataTypeName', '数据类型代码描述'),
        prop: 'dataTypeName',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 50,
        render: (row: AppConfigSetting.AppConfigKeyItem) => {
          return (
            <div class="flex justify-around">
              {/*<el-button*/}
              {/*  type="primary"*/}
              {/*  link={true}*/}
              {/*  onClick={() => insertRow(row)}*/}
              {/*>*/}
              {/*  {t('global.edit', '插入')}*/}
              {/*</el-button>*/}
              <el-button
                type={'danger'}
                link={true}
                onClick={() => deleteRow(row)}
              >
                {t('global:remove')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
  };
}
