import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';

export function useAppConfigListSearchFormConfig(
  queryAppConfigByExampleList: (
    data?: AppConfigSetting.QueryAppConfigParams,
  ) => Promise<void>,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: '关键字',
        name: 'keyWord',
        component: 'input',
        placeholder: t('appConfigSetting.search.keyword', '请输入关键字'),
        className: 'w-80',
        extraProps: {
          suffixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              queryAppConfigByExampleList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryAppConfigByExampleList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
}

export function useListDialogFormConfig() {
  return useFormConfig({
    getData: (t) => [
      {
        label: '配置项名称',
        name: 'appConfigItemName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t(
            'appConfigSetting.addNode.appConfigItemName',
            '配置项名称',
          ),
        }),
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'appConfigSetting.addNode.appConfigItemName',
                '配置项名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
      },
    ],
  });
}

export function useAppConfigNodeSearchFormConfig(
  queryAppConfigKeyList: (data?: AppConfigSetting.QueryAppConfigParams) => void,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: '关键字',
        name: 'keyWord',
        component: 'input',
        placeholder: t('appConfigSetting.search.keyword', '请输入关键字'),
        className: 'w-80',
        extraProps: {
          suffixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              queryAppConfigKeyList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryAppConfigKeyList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
}

export function useNodeDialogFormConfig(
  dataTypeManageList: Ref<SelectOptions[]>,
  codeSystemList: Ref<SelectOptions[]>,
  changeDataType: (val: string) => void,
  disabledCodeSystem: Ref<boolean>,
) {
  return useFormConfig({
    getData: (t) => [
      {
        label: '节点名称',
        name: 'appConfigKeyName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('appConfigSetting.addNode.appConfigKeyName', '节点名称'),
        }),
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'appConfigSetting.addNode.appConfigKeyName',
                '节点名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
      },
      {
        label: '配置节点描述',
        name: 'appConfigKeyDesc',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t(
            'appConfigSetting.addNode.appConfigKeyDesc',
            '配置节点描述',
          ),
        }),
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'appConfigSetting.addNode.appConfigKeyDesc',
                '配置节点描述',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        className: 'w-80',
      },
      {
        name: 'dataTypeId',
        label: t('appConfigSetting.addNode.dataTypeId', '数据类型代码'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('appConfigSetting.addNode.dataTypeId', '数据类型代码'),
        }),
        extraProps: {
          options: dataTypeManageList.value,
          onChange: (val: string) => {
            changeDataType(val);
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('appConfigSetting.addNode.dataTypeId', '数据类型代码'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'codeSystemId',
        label: t('appConfigSetting.addNode.codeSystemId', '编码体系'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('appConfigSetting.addNode.codeSystemId', '编码体系'),
        }),
        extraProps: {
          disabled: disabledCodeSystem.value,
          options: codeSystemList.value,
        },
        rules: [
          {
            required: !disabledCodeSystem.value,
            message: t('global:placeholder.select.template', {
              name: t('appConfigSetting.addNode.codeSystemId', '编码体系'),
            }),
            trigger: 'change',
          },
        ],
      },
    ],
  });
}
