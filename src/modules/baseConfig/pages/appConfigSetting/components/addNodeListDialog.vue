<script lang="ts" name="addNodeDialog" setup>
  import { computed, ref, watch } from 'vue';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ProDialog } from 'sun-biz';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    appendAppConfigKey,
    queryAppConfigKey,
  } from '@/modules/baseConfig/api/appConfigSetting';

  const { t } = useTranslation();

  const props = defineProps<{
    data: AppConfigSetting.AddOrUpdateAppConfigKey | undefined;
  }>();
  const dialogRef = ref();
  const disabled = ref(false);
  const rowData = ref<AppConfigSetting.AddOrUpdateAppConfigKey>();
  const emits = defineEmits<{ success: [] }>();
  const searchParams = ref<AppConfigSetting.QueryAppConfigParams>({
    keyWord: '',
  });
  //搜索关键字
  const addQueryKeyWord = ref<string>('');
  // 选中的
  const checked = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  // 源数据
  const appConfigNodeList = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  // 未使用源数据
  const unusedConfigNodeList = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  // 已存在数据
  const usedConfigNodeList = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  // 过滤后数据
  const filterConfigNodeList = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  // watch(
  //   () => addQueryKeyWord.value,
  //   () => {
  //     rowData.value = cloneDeep(props.data);
  //     usedConfigNodeList.value = rowData?.value?.appConfigKeyList
  //       ? rowData?.value?.appConfigKeyList
  //       : [];
  //   },
  //   {
  //     deep: true,
  //     immediate: true,
  //   },
  // );
  const onDialogOpen = () => {
    queryAppConfigKeyList();

    rowData.value = cloneDeep(props.data);
    usedConfigNodeList.value = rowData?.value?.appConfigKeyList
      ? rowData?.value?.appConfigKeyList
      : [];
    addQueryKeyWord.value = '';
    checked.value = [];
  };
  // 查询软件配置节点
  const queryAppConfigKeyList = async (
    data?: AppConfigSetting.QueryAppConfigParams,
  ) => {
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryAppConfigKey({
      ...searchParams.value,
    });
    if (res?.success) {
      appConfigNodeList.value = res?.data ?? [];
      console.log(usedConfigNodeList.value, 'usedConfigNodeList');
      console.log(appConfigNodeList.value, '222');

      // 过滤掉已存在的节点
      unusedConfigNodeList.value = appConfigNodeList.value.filter(
        (item) =>
          !usedConfigNodeList.value.some(
            (usedItem) => usedItem.appConfigKeyId === item.appConfigKeyId,
          ),
      );
      console.log(unusedConfigNodeList.value, 'unusedConfigNodeList');
    }
  };

  const onConfirm = async () => {
    const params = {
      appConfigItemId: rowData?.value?.appConfigItemId,
      appConfigKeyIds: checked.value,
    };
    if (checked.value.length > 0) {
      const [, res] = await appendAppConfigKey(params);
      if (res?.success) {
        ElMessage.success(t('addNodeListDialog.add.success', '添加成功'));
        return new Promise<[never, unknown]>((resolve) => {
          resolve([] as unknown as [never, unknown]);
        });
      } else {
        ElMessage.success(t('addNodeListDialog.add.error', '接口错误'));
      }
    } else {
      return new Promise<[never, unknown]>((resolve) => {
        resolve([] as unknown as [never, unknown]);
      });
    }
  };

  const handleClose = () => {
    dialogRef.value.close();
  };
  // 计算属性，根据关键字过滤数据
  const addQuery = computed(() => {
    if (addQueryKeyWord.value) {
      return unusedConfigNodeList.value.filter(
        (item) =>
          item.appConfigKeyName &&
          item.appConfigKeyName.includes(addQueryKeyWord.value),
      );
    } else {
      return unusedConfigNodeList.value;
    }
  });

  // 使用 watch 监听计算属性的变化，并更新 filterConfigNodeList
  watch(addQuery, (newValue) => {
    filterConfigNodeList.value = newValue;
  });

  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :title="`${$t('appConfigSetting.AppNodeList', '添加配置节点')}`"
    :width="600"
    destroy-on-close
    @open="onDialogOpen"
    @success="emits('success')"
  >
    <div class="mb-4">
      <el-input
        v-model="addQueryKeyWord"
        class="w-80"
        clearable
        height="200px"
        placeholder="输入关键字搜索"
        @input="addQuery"
      />
      <div v-if="filterConfigNodeList.length">
        <el-checkbox-group v-model="checked" class="p4 grid grid-cols-3">
          <el-checkbox
            v-for="node in filterConfigNodeList"
            :key="node"
            :label="node.appConfigKeyId"
          >
            <el-tooltip
              :content="node.appConfigKeyName"
              class="box-item"
              placement="bottom"
            >
              <span class="overflow-hidden text-ellipsis whitespace-nowrap">{{
                node.appConfigKeyName.length > 10
                  ? node.appConfigKeyName.slice(0, 10) + '...'
                  : node.appConfigKeyName
              }}</span>
            </el-tooltip>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <p v-else class="mt-4 text-center text-[#666]">
        未搜索到结果或已全部添加
      </p>
    </div>
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
