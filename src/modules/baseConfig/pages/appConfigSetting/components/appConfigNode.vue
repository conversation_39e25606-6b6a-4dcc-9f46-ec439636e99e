<script lang="ts" name="appConfigNode" setup>
  import { computed, onMounted, ref } from 'vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
  } from 'sun-biz';
  import { ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useAppConfigNodeSearchFormConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useFormConfigData';
  import { useAppConfigNodeTableColumnConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useTableColumnConfig.tsx';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import {
    deleteAppConfigKey,
    queryAppConfigKey,
  } from '@/modules/baseConfig/api/appConfigSetting';

  import addNodeDialog from '@/modules/baseConfig/pages/appConfigSetting/components/addNodeDialog.vue';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const { t } = useTranslation();
  const appConfigNodeTableRef = ref(); // 表格Ref
  const addNodeDialogRef = ref(); // dialogRef
  const searchParams = ref<AppConfigSetting.QueryAppConfigParams>({
    keyWord: '',
  });
  const appConfigNodeList = ref<AppConfigSetting.AppConfigKeyItem[]>([]);
  const loading = ref(false);
  const dialogMode = ref<string>('add');
  const nodeVal = ref<AppConfigSetting.AddOrUpdateAppConfigKey>({});
  const selectTableData = ref<AppConfigSetting.AppConfigKeyItem[]>([]);

  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.appConfigKeyId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: AppConfigSetting.AppConfigKeyItem[]) => {
    selectTableData.value = val;
  };

  // 查询软件配置节点
  const queryAppConfigKeyList = async (
    data?: AppConfigSetting.QueryAppConfigParams,
  ) => {
    loading.value = true;
    if (data) {
      console.log(data, '查询软件配置节点');
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryAppConfigKey({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.success) {
      appConfigNodeList.value = res?.data ?? [];
    }
  };

  const openAddNodeDialog = (
    mode: string,
    data?: AppConfigSetting.AppConfigKeyItem,
  ) => {
    dialogMode.value = mode;
    if (mode === 'add') {
      nodeVal.value = {
        appConfigKeyName: '',
        dataTypeId: undefined,
      } as AppConfigSetting.AppConfigKeyItem;
    } else if (mode === 'edit' && data) {
      nodeVal.value = {
        ...data,
      };
    }
    addNodeDialogRef.value.dialogRef.open();
  };

  const appConfigSettingSearchConfig = useAppConfigNodeSearchFormConfig(
    queryAppConfigKeyList,
  );
  const deleteRow = async (row: AppConfigSetting.AppConfigKeyItem) => {
    if (row) {
      ElMessageBox.confirm(
        t('appConfigSetting.delete.ask.title', '您确定要删除“{{name}}”吗', {
          name: `${row.appConfigKeyName}`,
        }),
        t('global:tip', '提示'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          appConfigKeyId: row.appConfigKeyId,
        };
        const [, res] = await deleteAppConfigKey(params);
        if (res?.success) {
          appConfigNodeTableRef?.value.proTableRef.clearSelection();
          selectTableData.value = [];
          queryAppConfigKeyList();
        }
      });
    }
  };

  const appConfigNodeTableColumnConfig = useAppConfigNodeTableColumnConfig({
    openAddNodeDialog,
    deleteRow,
    isCloudEnv,
  });

  onMounted(async () => {
    await queryAppConfigKeyList();
  });
</script>
<template>
  <div>
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <div class="el-form-item">
          <ProForm
            v-model="searchParams"
            :data="appConfigSettingSearchConfig"
            :show-search-button="false"
            layout-mode="inline"
            @model-change="queryAppConfigKeyList"
          />
          <el-button class="mr-2" type="primary" @click="queryAppConfigKeyList">
            {{ $t('global:search') }}
          </el-button>
        </div>
        <div class="el-form-item">
          <el-button
            :disabled="!isCloudEnv"
            class="mr-2"
            type="primary"
            @click="openAddNodeDialog('add')"
          >
            {{ $t('global:add') }}
          </el-button>
        </div>
      </div>

      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_APP_CONFIG_KEY"
        @success="
          () => {
            appConfigNodeTableRef?.proTableRef.clearSelection();
            selectTableData.value = [];
          }
        "
      />
    </div>

    <ProTable
      ref="appConfigNodeTableRef"
      :columns="appConfigNodeTableColumnConfig"
      :data="appConfigNodeList"
      :loading="loading"
      row-key="appConfigKeyId"
      @selection-change="selectionChange"
    />
    <addNodeDialog
      ref="addNodeDialogRef"
      :data="nodeVal"
      :mode="dialogMode"
      @success="queryAppConfigKeyList"
    />
  </div>
</template>
