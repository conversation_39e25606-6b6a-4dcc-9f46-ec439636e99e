<script lang="ts" name="addNodeDialog" setup>
  import { ref, watch } from 'vue';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ProDialog, ProForm } from 'sun-biz';
  import type { FormInstance } from 'element-sun';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { CODE_SYSTEM_OPTION } from '@/utils/constant.ts';
  import {
    addAppConfigKey,
    editAppConfigKey,
  } from '@/modules/baseConfig/api/appConfigSetting';

  import { useNodeDialogFormConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useFormConfigData.tsx';
  import { queryDataTypeManageList } from '@/modules/baseConfig/api/dataTypeManage.ts';
  import { SelectOptions } from '@/typings/common.ts';
  import { queryCodeSystemListByExample } from '@/modules/baseConfig/api/code';

  const dataTypeManageList = ref<SelectOptions[]>([]);

  const { t } = useTranslation();

  const props = defineProps<{
    mode: string;
    data: AppConfigSetting.AppConfigKeyItem | undefined;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: AppConfigSetting.AppConfigKeyItem;
  }>();
  const dialogRef = ref();
  const disabled = ref(false);
  const dialogForm = ref<AppConfigSetting.AppConfigKeyItem>();
  const emits = defineEmits<{ success: [] }>();
  const disabledCodeSystem = ref(true);

  const codeSystemList = ref<SelectOptions[]>([]);

  const queryCodeSystemList = async () => {
    const [, result] = await queryCodeSystemListByExample({
      pageNumber: -1,
      pageSize: 1000,
      keyWord: '',
      enabledFlag: 1,
    });
    if (result?.success) {
      codeSystemList.value = (result?.data || []).map((item) => ({
        value: item.codeSystemId,
        label: item.codeSystemName,
      }));
    }
  };

  // 查询数据类型
  const queryDataTypeManageListData = async () => {
    let [, res] = await queryDataTypeManageList({
      dtUsageScopeCode: 2,
    });
    if (res?.success) {
      res.data.sort(
        (
          a: DataTypeManage.DataTypeManageList,
          b: DataTypeManage.DataTypeManageList,
        ) => Number(a.sort) - Number(b.sort),
      );
      dataTypeManageList.value = (res.data || []).map((item) => {
        return {
          label: `${item.dataTypeName}(${item.dataTypeDesc})`,
          value: item.dataTypeId,
        };
      });
    }
  };
  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addAppConfigKey(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editAppConfigKey(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };
  watch(
    () => dialogRef.value?.visible,
    (val) => {
      if (val) {
        disabledCodeSystem.value = true;
      }
    },
  );

  const handleClose = () => {
    dialogRef.value.close();
  };
  const changeDataType = (val: string) => {
    const tmp = CODE_SYSTEM_OPTION; // 写死，编码体系
    formRef.value.ref.clearValidate();
    formRef.value.model.codeSystemId = '';
    disabledCodeSystem.value = val !== tmp;
  };
  const formConfig = useNodeDialogFormConfig(
    dataTypeManageList,
    codeSystemList,
    changeDataType,
    disabledCodeSystem,
  );

  watch(
    () => props,
    () => {
      queryDataTypeManageListData();
      queryCodeSystemList();
      disabled.value = props.mode === 'view';
      dialogForm.value = cloneDeep(props.data);
      const tmp = CODE_SYSTEM_OPTION; // 写死，编码体系
      disabledCodeSystem.value = dialogForm.value?.dataTypeId !== tmp;
    },
    {
      deep: true,
      immediate: true,
    },
  );
  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :title="`${$t(`global:${props.mode}`)}${$t('appConfigSetting.AppConfigNode', '软件配置节点')}`"
    :width="400"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="1"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
