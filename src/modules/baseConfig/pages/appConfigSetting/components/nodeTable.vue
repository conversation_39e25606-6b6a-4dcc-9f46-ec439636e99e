<script lang="ts" name="nodeTable" setup>
  import { ref, toRef } from 'vue';
  import { useNodeTableConfig } from '../config/useNodeTableConfig.tsx';
  import { ProTable } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { removeAppConfigKey } from '@/modules/baseConfig/api/appConfigSetting.ts';
  import { commonSort } from '@/api/common.ts';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';

  const { t } = useTranslation();

  const { rowValue, queryAppConfigByExampleList } = defineProps<{
    rowValue: AppConfigSetting.AppConfigList;
    queryAppConfigByExampleList: () => void;
  }>();
  // 如果需要在 setup 中使用这个函数，可以这样做

  const tableData = toRef(() => rowValue.appConfigKeyList || []);

  /** 元素ref */
  const nodeTableRef = ref();

  const deleteRow = async (row: AppConfigSetting.removeAppConfigKey) => {
    if (row) {
      ElMessageBox.confirm(
        t('appConfigSetting.delete.ask.title', '您确定要移除“{{name}}”吗', {
          name: `${row.appConfigKeyName}`,
        }),
        t('global:tip', '提示'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          appConfigSettingId: row.appConfigSettingId,
        };
        const [, res] = await removeAppConfigKey(params);
        if (res?.success) {
          queryAppConfigByExampleList();
          ElMessage.success(t('global:delete.success'));
        } else {
          ElMessage.error(t('global:delete.fail'));
        }
      });
    }
  };

  const { tableConfig } = useNodeTableConfig({
    deleteRow,
  });
  /** 拖拽排序 */
  const handleSortEnd = async (list: AppConfigSetting.AppConfigList[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.appConfigSettingId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_APP_CONFIG_SETTING,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryAppConfigByExampleList();
    }
  };
</script>
<template>
  <ProTable
    ref="nodeTableRef"
    :columns="tableConfig"
    :data="tableData"
    :draggable="true"
    class="nodeTable"
    max-height="300"
    row-key="appConfigKeyId"
    @drag-end="handleSortEnd"
  />
</template>
