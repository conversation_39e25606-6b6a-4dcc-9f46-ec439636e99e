<script lang="ts" name="appConfigNode" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
  } from 'sun-biz';
  import { useAppConfigListSearchFormConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useFormConfigData';
  import { useAppConfigSettingTableColumnConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useTableColumnConfig.tsx';
  import { commonSort } from '@/api/common';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import {
    deleteAppConfig,
    queryAppConfigByExample,
  } from '@/modules/baseConfig/api/appConfigSetting';
  import addListDialog from './addListDialog.vue';
  import addNodeListDialog from './addNodeListDialog.vue';
  import { ElMessage, ElMessageBox } from 'element-sun';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const { t } = useTranslation();
  const appConfigSettingTableRef = ref(); // 表格Ref
  const addListDialogRef = ref(); // dialogRef
  const addNodeListDialogRef = ref(); // dialogRef
  const searchParams = ref<AppConfigSetting.QueryAppConfigParams>({
    keyWord: '',
  });
  const appConfigSettingList = ref<AppConfigSetting.AppConfigList[]>([]);
  const loading = ref(false);
  const dialogMode = ref<string>('add');
  const nodeVal = ref<AppConfigSetting.AddOrUpdateAppConfigKey>({});

  const selections = ref<AppConfigSetting.AppConfigList[]>([]);

  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.appConfigItemId;
    });
    return list ?? [];
  });

  // 查询软件配置项
  const queryAppConfigByExampleList = async (
    data?: AppConfigSetting.QueryAppConfigParams,
  ) => {
    loading.value = true;
    if (data) {
      console.log(data, '查询软件配置项');
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryAppConfigByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res) {
      appConfigSettingList.value = res?.data ?? [];
    }
  };
  const appConfigSettingSearchConfig = useAppConfigListSearchFormConfig(
    queryAppConfigByExampleList,
  );
  const deleteRow = async (row: AppConfigSetting.AppConfigList) => {
    if (row) {
      ElMessageBox.confirm(
        t('appConfigSetting.delete.ask.title', '您确定要删除“{{name}}”吗', {
          name: `${row.appConfigItemName}`,
        }),
        t('global:tip', '提示'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          appConfigItemId: row.appConfigItemId,
        };
        const [, res] = await deleteAppConfig(params);
        if (res?.success) {
          appConfigSettingTableRef?.value.proTableRef.clearSelection();
          selections.value = [];
          queryAppConfigByExampleList();
        } else {
          ElMessage.error(t('global:delete.fail'));
        }
      });
    }
  };
  const openAddNodeDialog = (
    mode: string,
    data?: AppConfigSetting.AppConfigList,
  ) => {
    dialogMode.value = mode;
    if (mode === 'add') {
      nodeVal.value = {
        appConfigItemName: '',
      } as AppConfigSetting.AppConfigList;
    } else if (mode === 'edit' && data) {
      nodeVal.value = {
        ...data,
      };
    }
    addListDialogRef.value.dialogRef.open();
  };
  const openAddNodeListDialog = (data?: AppConfigSetting.AppConfigList) => {
    nodeVal.value = data;
    addNodeListDialogRef.value.dialogRef.open();
  };
  const appConfigSettingTableColumnConfig =
    useAppConfigSettingTableColumnConfig({
      openAddNodeDialog,
      deleteRow,
      openAddNodeListDialog,
      queryAppConfigByExampleList,
      isCloudEnv,
    });

  const expandChange = (expanded: boolean) => {
    console.log(expanded);
  };

  /** 拖拽排序 */
  const handleSortEnd = async (list: AppConfigSetting.AppConfigList[]) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.appConfigItemId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_APP_CONFIG_ITEM,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryAppConfigByExampleList();
    }
  };
  const handleSelectChange = (value: AppConfigSetting.AppConfigList[]) => {
    selections.value = value;
  };
  onMounted(async () => {
    queryAppConfigByExampleList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <div class="el-form-item">
          <ProForm
            v-model="searchParams"
            :data="appConfigSettingSearchConfig"
            layout-mode="inline"
            @model-change="queryAppConfigByExampleList"
          />
          <el-button
            class="mr-2"
            type="primary"
            @click="queryAppConfigByExampleList"
          >
            {{ $t('global:search') }}
          </el-button>
        </div>
        <div class="el-form-item">
          <el-button
            :disabled="!isCloudEnv"
            class="mr-2"
            type="primary"
            @click="openAddNodeDialog('add')"
          >
            {{ $t('global:add') }}
          </el-button>
        </div>
      </div>
      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_APP_CONFIG_ITEM"
        @success="
          () => {
            appConfigSettingTableRef?.proTableRef.clearSelection();
            selections.value = [];
          }
        "
      />
    </div>

    <ProTable
      ref="appConfigSettingTableRef"
      :columns="appConfigSettingTableColumnConfig"
      :data="appConfigSettingList"
      :draggable="true"
      :loading="loading"
      class="appConfigSettingTable"
      row-key="appConfigItemId"
      style="max-height: calc(100vh - 200px)"
      @expand-change="expandChange"
      @selection-change="handleSelectChange"
      @drag-end="handleSortEnd"
    />

    <addListDialog
      ref="addListDialogRef"
      :data="nodeVal"
      :mode="dialogMode"
      @success="queryAppConfigByExampleList"
    />
    <addNodeListDialog
      ref="addNodeListDialogRef"
      :data="nodeVal"
      @success="queryAppConfigByExampleList"
    />
  </div>
</template>
