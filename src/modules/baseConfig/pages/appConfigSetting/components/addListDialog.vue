<script lang="ts" name="addNodeDialog" setup>
  import { ref, watch } from 'vue';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { ProDialog, ProForm } from 'sun-biz';
  import type { FormInstance } from 'element-sun';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    addAppConfig,
    editAppConfig,
  } from '@/modules/baseConfig/api/appConfigSetting';
  import { useListDialogFormConfig } from '@/modules/baseConfig/pages/appConfigSetting/config/useFormConfigData.tsx';

  const { t } = useTranslation();

  const props = defineProps<{
    mode: string;
    data: AppConfigSetting.AddOrUpdateAppConfigKey | undefined;
  }>();
  const formRef = ref<{
    ref: FormInstance;
    model: AppConfigSetting.AddOrUpdateAppConfigKey;
  }>();
  const dialogRef = ref();
  const disabled = ref(false);
  const dialogForm = ref<AppConfigSetting.AddOrUpdateAppConfigKey>();
  const emits = defineEmits<{ success: [] }>();

  watch(
    () => props,
    () => {
      disabled.value = props.mode === 'view';
      dialogForm.value = cloneDeep(props.data);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let isSuccess = false;
          if (props.mode === 'add') {
            const [, res] = await addAppConfig(params);
            isSuccess = !!res?.success;
          } else if (props.mode === 'edit') {
            const [, res] = await editAppConfig(params);
            isSuccess = !!res?.success;
          }
          if (isSuccess) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };
  const formConfig = useListDialogFormConfig();

  defineExpose({ dialogRef });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="!disabled"
    :title="`${$t(`global:${props.mode}`)}${$t('appConfigSetting.AppConfigNode', '软件配置项')}`"
    :width="400"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="1"
      :data="formConfig"
    />
    <div v-if="disabled" class="mt-4 text-right">
      <el-button @click="handleClose">{{ $t('global:close') }}</el-button>
    </div>
  </ProDialog>
</template>
