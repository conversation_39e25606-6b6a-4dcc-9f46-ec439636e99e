<script lang="ts" name="appConfigSetting" setup>
  import { ref } from 'vue';
  import appConfigList from './components/appConfigList.vue';
  import appConfigNode from './components/appConfigNode.vue';

  const currentTab = ref('AppConfigList');
  const onTabClick = (tab: string) => {
    currentTab.value = tab;
  };
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div
      class="flex w-max space-x-1 overflow-x-auto rounded-md bg-gray-100 p-1"
    >
      <div
        :class="[
          'relative flex shrink-0 cursor-pointer items-center rounded-md px-3 py-1 text-sm',
          currentTab === 'AppConfigList'
            ? 'bg-blue-500 text-white'
            : 'hover:bg-gray-200',
        ]"
        @click="onTabClick('AppConfigList')"
      >
        {{ $t('appConfigSetting.AppConfigList', '软件配置项') }}
      </div>
      <div
        :class="[
          'relative flex shrink-0 cursor-pointer items-center rounded-md px-3 py-1 text-sm',
          currentTab === 'AppConfigNode'
            ? 'bg-blue-500 text-white'
            : 'hover:bg-gray-200',
        ]"
        @click="onTabClick('AppConfigNode')"
      >
        {{ $t('appConfigSetting.AppConfigNode', '软件配置节点') }}
      </div>
    </div>
    <appConfigList
      v-show="currentTab === 'AppConfigList'"
      :current-tab="currentTab"
    />
    <appConfigNode
      v-show="currentTab === 'AppConfigNode'"
      :current-tab="currentTab"
    />
  </div>
</template>
