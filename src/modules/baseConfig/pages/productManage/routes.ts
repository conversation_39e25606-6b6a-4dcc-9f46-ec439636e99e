export const routes = [
  {
    path: '/',
    name: 'productManage',
    component: () => import('./views/App.vue'),
  },
  {
    path: '/hospital-list/:productId',
    name: 'hospitalList',
    component: () => import('./views/detail/hospitalList.vue'),
  },
  {
    path: '/product-define/:productId/:orgId',
    name: 'productDefine',
    component: () => import('./views/detail/productDefine.vue'),
  },
  {
    path: '/product-add-code-repository/:productId/:orgId',
    name: 'productAddCodeRepository',
    component: () => import('./views/detail/productAddCodeRepository.vue'),
  },
];

export default routes;
