import { useFormConfig } from 'sun-biz';

export function useProductManageFormConfig(
  queryProductByExampleByApi: (
    params: ProductManage.QueryProductManageParams,
  ) => Promise<void>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: '关键字',
        name: 'keyWord',
        component: 'input',
        placeholder: t('computerManage.keyword', '请输入关键字进行搜索'),
        className: 'w-80',
        extraProps: {
          suffixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              queryProductByExampleByApi({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryProductByExampleByApi({ keyWord: '' });
          },
        },
      },
    ],
  });
  return data;
}
