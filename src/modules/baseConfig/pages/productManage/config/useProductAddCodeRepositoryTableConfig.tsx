import { useColumnConfig } from 'sun-biz';
import { CodeSystemType } from '@/typings/codeManage';

export function useProductAddCodeRepositoryTableConfig() {
  const tableColumns = useColumnConfig({
    dataSetCodes: [CodeSystemType.CODE_REPOSITORY_TYPE_CODE],
    getData: (t) => [
      {
        prop: 'selection',
        editable: true,
        type: 'selection',
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryName',
          '代码仓库名称',
        ),
        component: 'input',
        prop: 'codeRepositoryName',
        minWidth: 150,
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryDesc',
          '代码仓库描述',
        ),
        prop: 'codeRepositoryDesc',
        minWidth: 150,
        editable: false,
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryTypeCodeDesc',
          '代码仓库类型',
        ),
        prop: 'codeRepositoryTypeCodeDesc',
        minWidth: 150,
        editable: false,
      },
    ],
  });
  return { tableColumns };
}
