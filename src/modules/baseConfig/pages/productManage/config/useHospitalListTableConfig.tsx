import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref, ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';

export function useHospitalListTableConfig(options: {
  id: string;
  data: Ref<ProductManage.OrgList[]>;
  tableRef: Ref<TableRef>;
  hospitalSelections: Ref<SelectOptions>;
  onOrgChange: (val: string, row: ProductManage.OrgList) => void;
  branchList: Ref<ProductManage.OrgList[]>;
  systemList: Ref<SelectOptions>;
  handleSave: (row: ProductManage.OrgList) => Promise<void>;
  deleteItem: (row: ProductManage.OrgList) => Promise<void>;
  handleEdit: (row: ProductManage.OrgList) => void;
  go2ProductDefine: (row: ProductManage.OrgList) => void;
  isCloudEnv: boolean | undefined;
}) {
  const {
    id,
    data,
    tableRef,
    hospitalSelections,
    onOrgChange,
    branchList,
    systemList,
    handleSave,
    deleteItem,
    handleEdit,
    go2ProductDefine,
    isCloudEnv,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data,
    id,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('hospitalList.hospitalListTable.orgId', '医院名称'),
        prop: 'orgId',
        minWidth: 150,
        render: (
          row: ProductManage.OrgList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.orgId}
                filterable={true}
                onChange={(val: string) => {
                  onOrgChange(val, row);
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t('hospitalList.hospitalListTable.orgId', '医院名称'),
                })}
              >
                {hospitalSelections.value?.map(
                  (
                    item: SelectOptions & { orgId?: string; orgName?: string },
                  ) => (
                    <el-option
                      key={item.orgId}
                      label={item.orgName}
                      value={item.orgId}
                    />
                  ),
                )}
              </el-select>
            );
          } else {
            return <>{row.orgName}</>;
          }
        },
      },
      {
        label: t('hospitalList.hospitalListTable.codeBranchId', '代码分支'),
        prop: 'codeBranchId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'hospitalList.hospitalListTable.codeBranchId',
                '代码分支',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: ProductManage.OrgList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.codeBranchId}
                filterable={true}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'hospitalList.hospitalListTable.codeBranchId',
                    '代码分支',
                  ),
                })}
              >
                {branchList.value?.map((item) => (
                  <el-option
                    key={item.codeBranchId}
                    label={item.codeBranchName}
                    value={item.codeBranchId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.codeBranchName}</>;
          }
        },
      },
      {
        label: t('hospitalList.hospitalListTable.sysList', '包含系统'),
        prop: 'sysList',
        minWidth: 150,
        editable: true,
        render: (
          row: ProductManage.OrgList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            if (!Array.isArray(row.sysList)) {
              row.sysList = [];
            }
            const selectedIds = ref(row.sysList.map((item) => item.sysId));
            return (
              <el-select
                v-model={selectedIds.value}
                filterable={true}
                multiple={true}
                placeholder={t('global:placeholder.select.template', {
                  name: t('hospitalList.hospitalListTable.sysList', '包含系统'),
                })}
                onChange={(val: string[]) => {
                  row.sysList = val.map((sysId) => {
                    const existingItem = row.sysList?.find(
                      (item) => item.sysId === sysId,
                    );
                    const systemInfo = systemList.value?.find(
                      (sys) => sys.value === sysId,
                    );

                    return {
                      prodXSysId: existingItem?.prodXSysId || '',
                      sysId,
                      sysName: systemInfo?.label || '',
                    };
                  });
                }}
              >
                {systemList.value?.map((item: SelectOptions) => (
                  <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.sysList?.map((item) => item.sysName).join(', ')}</>;
          }
        },
      },

      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleSave(row)}
                  >
                    {t('global:save')}
                  </el-button>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link
                    disabled={!isCloudEnv}
                    onClick={() => handleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    disabled={!isCloudEnv}
                    onClick={() => deleteItem(row)}
                  >
                    {t('global:delete')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => go2ProductDefine(row)}
                  >
                    {t('hospitalList.hospitalListTable.hospital', '产品定义')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
