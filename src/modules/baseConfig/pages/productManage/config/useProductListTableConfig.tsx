import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import {
  CODE_REPOSITORY_TYPE_CODE_NAME,
  ENABLED_FLAG,
} from '@/utils/constant.ts';
import { Ref } from 'vue';

export function useProductManageTableConfig(options: {
  id: string;
  data: Ref<ProductManage.ProductList[]>;
  tableRef: Ref<TableRef>;
  handleSave: (row: ProductManage.ProductList) => Promise<void>;
  deleteItem: (row: ProductManage.ProductList) => Promise<void>;
  handleEdit: (row: ProductManage.ProductList) => void;
  handleEnableSwitch: (data: ProductManage.ProductList) => void;
  go2hospitalList: (row: ProductManage.ProductList) => void;
  isCloudEnv: boolean;
}) {
  const dataSetCodes = [CODE_REPOSITORY_TYPE_CODE_NAME];
  const {
    id,
    data,
    tableRef,
    handleSave,
    deleteItem,
    handleEdit,
    handleEnableSwitch,
    go2hospitalList,
    isCloudEnv,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef: tableRef,
    data: data,
    id: id as string,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes,
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('productManage.productManageTable.productId', '产品标识'),
        prop: 'productId',
        minWidth: 150,
      },
      {
        label: t('productManage.productManageTable.productName', '产品名称'),
        prop: 'productName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'productManage.productManageTable.productName',
                '产品名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.productName}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'productManage.productManageTable.productName',
                    '产品名称',
                  ),
                })}
              />
            );
          } else {
            return <>{row.productName}</>;
          }
        },
      },
      {
        label: t(
          'productManage.productManageTable.product2ndName',
          '产品辅助名称',
        ),
        prop: 'product2ndName',
        minWidth: 150,
        editable: true,
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.product2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'productManage.productManageTable.product2ndName',
                    '产品辅助名称',
                  ),
                })}
              />
            );
          } else {
            return <>{row.product2ndName}</>;
          }
        },
      },
      {
        label: t(
          'productManage.productManageTable.productExtName',
          '产品扩展名称',
        ),
        prop: 'productExtName',
        minWidth: 150,
        editable: true,
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.productExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'productManage.productManageTable.productExtName',
                    '产品扩展名称',
                  ),
                })}
              />
            );
          } else {
            return <>{row.productExtName}</>;
          }
        },
      },
      {
        label: t('global:enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        fixed: 'right',
        render: (row: ProductManage.ProductList) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('productManage.productManageTable.productDesc', '产品描述'),
        prop: 'productDesc',
        minWidth: 150,
        editable: true,
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.productDesc}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'productManage.productManageTable.productDesc',
                    '产品描述',
                  ),
                })}
              />
            );
          } else {
            return <>{row.productDesc}</>;
          }
        },
      },
      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 130,
        render: (
          row: ProductManage.ProductList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => {
                      toggleEdit(row);
                      handleSave(row);
                    }}
                  >
                    {t('global:save')}
                  </el-button>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    disabled={!isCloudEnv}
                    link
                    onClick={() => handleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    type="danger"
                    disabled={!isCloudEnv}
                    link
                    onClick={() => deleteItem(row)}
                  >
                    {t('global:delete')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => go2hospitalList(row)}
                  >
                    {t('productManage.productManageTable.hospital', '医院')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
