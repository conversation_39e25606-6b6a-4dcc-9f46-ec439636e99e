<script lang="ts" name="AppManageUpsertDialog" setup>
  import { nextTick, ref } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useAppManageAddFormConfig } from '../config/useFormConfig.tsx';
  import { createAppModelColumnConfig } from '../config/useTableConfig.tsx';
  import { ProDialog, ProForm, ProTable, Title } from 'sun-biz';
  import { SelectOptions } from '~/typings/common.ts';
  import { querySystemListByExample } from '@/api/menu';
  import { queryCodeRepositoryByExample } from '@/api/codeRepositoryManage';
  import {
    createAppApi,
    queryComputerManageListByExample,
  } from '~/modules/releaseDeployment/api/appManage.ts';
  import SystemInfo = Menu.SystemInfo;

  const isRequired = ref(true);

  const formRef = ref<{
    ref: FormInstance;
    model: AppManage.createAppModel;
  }>();
  const tableRef = ref();
  const createAppDialogRef = ref();
  const { t } = useTranslation();
  const emits = defineEmits<{ success: [] }>();

  // 当前表单数据
  const createAppParams = ref<AppManage.createAppModel>({
    hospitalId: '',
    appEnvId: '',
    copies: 1,
    appList: [],
  });

  // 当前表格数据
  const AppListItemForTable = ref<AppManage.AppListItemModel[]>([
    {
      appName: '',
      appDesc: '',
      machineId: '',
      port: '',
      sysId: '',
      codeRepositoryId: '',
      codeRepositoryTypeCode: '1',
      editable: true,
      key: Math.random().toString(36),
      appPath: '',
      backupPath: '',
    },
  ]);

  const props = defineProps<{
    appEnvId: string;
    hospitalId: string;
    mode?: string;
  }>();

  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      resetFormData();
      queryCodeRepositoryList();
      querySystemList();
      queryComputerManageData();
      createAppDialogRef.value.open();
    });
  };

  const computerList = ref<SelectOptions[]>([]);
  const tmpCodeRepositoryName = ref('');
  const tmpPort = ref();
  const tmpAppPath = ref();
  const tmpBackupPath = ref();

  /** 查询计算机列表 */
  async function queryComputerManageData() {
    const params = {
      hospitalId: '',
      pageNumber: 1,
      pageSize: 200,
    };
    try {
      const [, res] = await queryComputerManageListByExample(params);
      if (res?.success) {
        computerList.value = (res.data || []).map((item) => ({
          label: item?.computerName || item?.computerDesc,
          value: item?.computerId,
        }));
      }
    } catch {
      computerList.value = [];
    }
  }

  const codeRepositoryListSource = ref<SelectOptions[]>([]);
  const codeRepositoryList = ref<SelectOptions[]>([]);
  const codeRepositoryListSearchParams =
    ref<CodeRepositoryManage.CodeRepositoryQueryParams>({
      keyWord: '',
      enabledFlag: '1',
    });
  const queryCodeRepositoryList = async () => {
    const [, res] = await queryCodeRepositoryByExample(
      codeRepositoryListSearchParams.value,
    );
    if (res?.success) {
      codeRepositoryListSource.value = (res.data || []).map((item) => ({
        label: `${item?.codeRepositoryDesc}（${item?.codeRepositoryName}）`,
        value: item?.codeRepositoryId,
        codeRepositoryTypeCode: item?.codeRepositoryTypeCode,
        codeRepositoryDesc: item?.codeRepositoryDesc,
        codeRepositoryName: item?.codeRepositoryName,
        port: item?.port,
        appPath: item?.appPath,
        backupPath: item?.backupPath,
      }));
      codeRepositoryList.value = codeRepositoryListSource.value;
      createAppParams.value.copies = 1; // 默认1个副本
    }
  };
  const systemList = ref<Menu.SystemInfo[]>([]);
  const systemSelections = ref<SelectOptions[]>([]);
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        systemList.value = res.data || [];
        systemSelections.value = res.data
          .sort((a, b) => {
            return Number(a?.sort) - Number(b?.sort);
          })
          .map((item) => ({
            value: item.sysId,
            label: item.sysName,
          }));
      } else {
        systemSelections.value = [];
      }
    }
  };
  const changeRequire = (flag: string) => {
    isRequired.value = flag === '2';
    createAppParams.value.codeRepositoryId = undefined;
    createAppParams.value.appDesc = undefined;
    for (let i = 1; i <= createAppParams.value.copies; i++) {
      AppListItemForTable.value.map((item) => {
        item.appName = '';
        item.port = '';
        item.appPath = '';
        item.backupPath = '';
      });
    }
    changeSystem(formRef?.value?.model?.sysId);
  };

  const isCanSelectCodeRepository = ref(true);
  const changeSystem = (sysId: string) => {
    if (sysId) {
      if (formRef.value) formRef.value.model.codeRepositoryId = undefined;
      codeRepositoryList.value = [];
      isCanSelectCodeRepository.value = false;
      // 选择系统后，过滤出对应的代码库
      systemList?.value.filter((item: SystemInfo) => {
        if (item.sysId === sysId) {
          codeRepositoryList.value = item?.codeRepositoryList
            ? item?.codeRepositoryList.map(
                (child: CodeRepositoryManage.CodeRepositoryInfo) => {
                  return {
                    value: child?.codeRepositoryId,
                    label: `${child?.codeRepositoryDesc}（${child?.codeRepositoryName}）`,
                    codeRepositoryTypeCode: child?.codeRepositoryTypeCode,
                    codeRepositoryDesc: child?.codeRepositoryDesc,
                    codeRepositoryName: child?.codeRepositoryName,
                    port: child?.port,
                    appPath: child?.appPath,
                    backupPath: child?.backupPath,
                  };
                },
              )
            : [];
        }
      });
      nextTick(() => {
        codeRepositoryList.value = codeRepositoryList?.value?.filter(
          (item: CodeRepositoryManage.CodeRepositoryInfo) =>
            item.codeRepositoryTypeCode ===
            formRef?.value?.model?.codeRepositoryTypeCode,
        );
        // 合并相同codeRepositoryId的项
        if (codeRepositoryListSource?.value?.length) {
          codeRepositoryList.value = codeRepositoryList.value.map(
            (item: CodeRepositoryManage.CodeRepositoryInfo) => {
              const matchItem = codeRepositoryListSource.value.filter(
                (source: SelectOptions) => source.value === item.value,
              );
              if (matchItem) {
                return {
                  ...item,
                  port: matchItem[0].port || item.port,
                  appPath: matchItem[0].appPath || item.appPath,
                  backupPath: matchItem[0].backupPath || item.backupPath,
                };
              }
              return item;
            },
          );
        }
      });
    } else {
      nextTick(() => {
        codeRepositoryList.value = [];
        createAppParams.value.codeRepositoryId = undefined;
        isCanSelectCodeRepository.value = true;
      });
    }
  };
  const changeCodeRepository = (val?: string) => {
    if (val) {
      if (formRef.value) formRef.value.model.appDesc = '';
      codeRepositoryList?.value.map(
        (item: CodeRepositoryManage.CodeRepositoryInfo) => {
          if (item.value === val) {
            formRef.value.model.appDesc = item?.codeRepositoryDesc;
            AppListItemForTable.value.map(
              (tb: AppManage.AppListItemModel, index: number) => {
                tb.appName = item.codeRepositoryName + `-${index + 1}`;
                tb.port = item.port;
                tb.appPath = item.appPath;
                tb.backupPath = item.backupPath;
              },
            );
            tmpCodeRepositoryName.value = item.codeRepositoryName;
            tmpPort.value = item.port;
            tmpAppPath.value = item.appPath;
            tmpBackupPath.value = item.backupPath;
          }
        },
      );
    } else {
      formRef.value.model.appDesc = '';
    }
  };
  // 新增配置form配置数据
  const useAppManageAddForm = useAppManageAddFormConfig(
    isCanSelectCodeRepository,
    codeRepositoryList,
    systemSelections,
    changeRequire,
    changeSystem,
    changeCodeRepository,
    addNewRow,
  );

  function deleteRow(row: AppManage.AppListBatchModel, index: number) {
    const currentId = row.codeRepositoryId;
    const sameIdRows = AppListItemForTable.value.filter(
      (item) => item.codeRepositoryId === currentId,
    );
    if (sameIdRows.length > 1) {
      AppListItemForTable.value.splice(index, 1);
    } else {
      ElMessage.error(t('appManage.onlyone', '至少保留一行数据'));
    }
  }

  // 表格配置数据
  const { columnConfig } = createAppModelColumnConfig({
    id: 'machineId',
    tableRef,
    data: AppListItemForTable,
    isRequired,
    computerList,
    deleteRow,
  });

  /**
   * 新增配置信息列表
   */
  function addNewRow(number?: number | string) {
    console.log(number, 'num');
    const newRow = {
      appName: '',
      appDesc: '',
      machineId: '',
      port: '',
      sysId: '',
      codeRepositoryId: '',
      codeRepositoryTypeCode: '',
      editable: true,
      appPath: '',
      backupPath: '',
    };
    AppListItemForTable.value = [];
    for (let i = 1; i <= number; i++) {
      AppListItemForTable.value.push({
        ...newRow,
        appName: tmpCodeRepositoryName.value + `-${i}`,
        port: tmpPort.value,
        appPath: tmpAppPath.value,
        backupPath: tmpBackupPath.value,
        key: Math.random().toString(36),
      });
    }
  }

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          tableRef.value?.formRef.validate(async (tableValid: boolean) => {
            if (tableValid) {
              const obj = (AppListItemForTable.value ?? []).find(
                (item) => item.editable === true,
              );
              if (obj) {
                ElMessage.error(t('notSave.refundPayWay', '存在未保存的配置'));
                return reject(['', new Error('参数错误')]);
              }
              AppListItemForTable.value.forEach((item) => {
                item.appDesc = formRef?.value?.model?.appDesc;
                item.codeRepositoryId = formRef?.value?.model?.codeRepositoryId;
                item.codeRepositoryTypeCode =
                  formRef?.value?.model?.codeRepositoryTypeCode;
                item.sysId = formRef?.value?.model?.sysId;
              });

              const params = {
                ...createAppParams.value,
                ...formRef?.value?.model,
                appEnvId: props.appEnvId,
                hospitalId: props.hospitalId,
                appList: AppListItemForTable.value,
              };
              const [, res] = await createAppApi(params);
              if (res?.success) {
                ElMessage.success(t('global:save.success'));
                createAppDialogRef.value.close();

                // 重置表单数据
                resetFormData();

                resolve([] as unknown as [never, unknown]);
              } else {
                reject(['', new Error('接口错误')]);
              }
            } else {
              reject(['', new Error('参数错误')]);
            }
          });
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  // 修改后的取消逻辑 - 只重置表单，保留表格数据
  function onCancelDialog() {
    // 重置表单数据但保留表格数据
    createAppParams.value = {
      hospitalId: '',
      appEnvId: '',
      copies: 1,
      appList: [],
    };
    AppListItemForTable.value = [
      {
        appName: '',
        appDesc: '',
        machineId: '',
        port: '',
        sysId: '',
        codeRepositoryId: '',
        codeRepositoryTypeCode: '1',
        editable: true,
        key: Math.random().toString(36),
        appPath: '',
        backupPath: '',
      },
    ];
    formRef.value?.ref.resetFields();
  }

  // 统一的表单重置方法
  function resetFormData() {
    createAppParams.value = {
      hospitalId: '',
      appEnvId: '',
      copies: 1,
      appList: [],
    };
    AppListItemForTable.value = [
      {
        appName: '',
        appDesc: '',
        machineId: '',
        port: '',
        sysId: '',
        codeRepositoryId: '',
        codeRepositoryTypeCode: '1',
        editable: true,
        key: Math.random().toString(36),
        appPath: '',
        backupPath: '',
      },
    ];
    formRef.value?.ref.resetFields();
  }

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="createAppDialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :on-close="onCancelDialog"
    :title="`${t('appManage.createdApp', '新建应用')}`"
    :width="1000"
    destroy-on-close
    include-footer
    @success="emits('success')"
  >
    <div>
      <Title :title="$t('paramSetting.baseInfo', '应用信息')" class="my-3" />
      <ProForm
        ref="formRef"
        v-model="createAppParams"
        :column="3"
        :data="useAppManageAddForm"
      />
    </div>
    <div>
      <Title :title="$t('paramSetting.settingInfo', '部署情况')" class="my-3" />
      <ProTable
        ref="tableRef"
        :columns="columnConfig"
        :data="AppListItemForTable"
        :editable="true"
        height="180"
        row-key="key"
        style=" height: 180px;max-height: 180px"
      />
    </div>
  </ProDialog>
</template>
