<script lang="ts" setup>
  import { nextTick, ref, watch } from 'vue';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProForm, Title } from 'sun-biz';
  import { SelectOptions } from '~/typings/common.ts';
  import { querySystemListByExample } from '@/api/menu';
  import { queryCodeRepositoryByExample } from '@/api/codeRepositoryManage';
  import { useAppManageEditFormConfig } from '../config/useFormConfig.tsx';
  import {
    editAppApi,
    queryComputerManageListByExample,
  } from '~/modules/releaseDeployment/api/appManage.ts';

  const editFromRef = ref<{
    ref: FormInstance;
    model: AppManage.AppListModel;
  }>();
  const editAppDialogRef = ref();
  const { t } = useTranslation();
  const emits = defineEmits<{ success: [] }>();
  const props = defineProps<{ data: AppManage.AppListModel; mode?: string }>();
  let editAppParams = ref<AppManage.AppListModel>({});

  // 中间变量：避免表单直接依赖响应式数组
  const computerListOptions = ref<SelectOptions[]>([]);
  const systemList = ref<SelectOptions[]>([]);
  const systemSelectionsOptions = ref<SelectOptions[]>([]);
  const codeRepositoryListOptions = ref<SelectOptions[]>([]);
  const isRequired = ref(true);

  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        editAppParams.value = newVal;
        isRequired.value = newVal.codeRepositoryTypeCode !== '1';
      }
    },
    { immediate: true },
  );
  // 打开弹窗时加载数据（使用 Promise.all 优化）
  const openDialog = async () => {
    try {
      await Promise.all([
        queryComputerManageData(),
        querySystemList(),
        queryCodeRepositoryList(),
      ]);
      editAppDialogRef.value.open();
    } catch {
      ElMessage.error(t('global:error.loadingData'));
    }

    nextTick(() => {
      isCanSelectCodeRepository.value = true;
    });
  };

  // 查询计算机列表（简化错误处理）
  const queryComputerManageData = async () => {
    const params = { hospitalId: '', pageNumber: 1, pageSize: 200 };
    const [, res] = await queryComputerManageListByExample(params);
    computerListOptions.value = (res?.data || []).map((item) => ({
      label: item.computerName || item.computerDesc,
      value: item.computerId,
    }));
  };

  // 查询代码仓库列表
  const queryCodeRepositoryList = async () => {
    const [, res] = await queryCodeRepositoryByExample({ enabledFlag: '1' });
    codeRepositoryListOptions.value = (res?.data || []).map((item) => ({
      label: `${item.codeRepositoryDesc}（${item.codeRepositoryName}）`,
      value: item.codeRepositoryId,
      codeRepositoryTypeCode: item.codeRepositoryTypeCode,
      codeRepositoryDesc: item.codeRepositoryDesc,
      codeRepositoryName: item.codeRepositoryName,
    }));
  };

  // 查询系统列表
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    systemList.value = res?.data || [];
    systemSelectionsOptions.value = (res?.data || [])
      .sort((a, b) => Number(a.sort) - Number(b.sort))
      .map((item) => ({ value: item.sysId, label: item.sysName }));
  };

  const isCanSelectCodeRepository = ref(true);

  const changeSystem = (sysId: string | undefined) => {
    if (sysId) {
      // 找到对应系统的代码仓库
      const targetSystem = systemList.value.find(
        (item) => item.sysId === sysId,
      );
      codeRepositoryListOptions.value =
        targetSystem?.codeRepositoryList?.map((child) => ({
          value: child.codeRepositoryId,
          label: `${child.codeRepositoryDesc}（${child.codeRepositoryName}）`,
          codeRepositoryTypeCode: child.codeRepositoryTypeCode,
          codeRepositoryDesc: child.codeRepositoryDesc,
          codeRepositoryName: child.codeRepositoryName,
        })) || [];
      // 延迟修改表单模型，避免即时循环
      nextTick(() => {
        if (editFromRef.value) {
          editFromRef.value.model.codeRepositoryId = undefined;
        }
      });
      isCanSelectCodeRepository.value = false;
    } else {
      nextTick(() => {
        codeRepositoryListOptions.value = [];
        editAppParams.value.codeRepositoryId = undefined;
        isCanSelectCodeRepository.value = true;
      });
    }
  };

  const changeCodeRepository = (val: string | undefined) => {
    nextTick(() => {
      if (val && editFromRef.value) {
        const targetRepo = codeRepositoryListOptions.value.find(
          (item) => item.value === val,
        );
        editFromRef.value.model.appDesc = targetRepo?.codeRepositoryDesc || '';
      } else {
        editFromRef.value.model.appDesc = '';
      }
    });
  };

  // 将中间变量传递给表单配置
  const useAppManageEditForm = useAppManageEditFormConfig(
    isCanSelectCodeRepository,
    codeRepositoryListOptions, // 传递中间变量
    systemSelectionsOptions, // 传递中间变量
    changeSystem,
    changeCodeRepository,
    computerListOptions, // 传递中间变量
    isRequired,
  );

  // 表单提交逻辑（简化参数合并）
  const onConfirm = async () => {
    return new Promise((resolve, reject) => {
      editFromRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = { ...editAppParams.value, ...editFromRef.value.model };
          try {
            const [, res] = await editAppApi(params);
            if (res?.success) {
              ElMessage.success(t('global:save.success'));
              isRequired.value = true;
              editAppDialogRef.value.close();
              resolve([] as never[]);
            } else {
              reject(new Error('保存失败'));
            }
          } catch {
            reject(new Error('网络错误'));
          }
        } else {
          reject(new Error('表单验证失败'));
        }
      });
    });
  };

  const onCancelDialog = () => {
    editAppParams.value = {};
  };

  defineExpose({ editAppDialogRef });
</script>

<template>
  <ProDialog
    ref="editAppDialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :on-close="onCancelDialog"
    :title="`${t('appManage.editApp', '编辑应用')}`"
    :width="800"
    destroy-on-close
    include-footer
    @open="openDialog"
    @success="emits('success')"
  >
    <div>
      <Title :title="$t('paramSetting.baseInfo', '应用信息')" class="my-3" />
      <ProForm
        ref="editFromRef"
        v-model="editAppParams"
        :column="3"
        :data="useAppManageEditForm"
      />
    </div>
  </ProDialog>
</template>
