<script lang="ts" name="AppManageUpsertDialog" setup>
  import { nextTick, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useAppManageBatchAddFormConfig } from '../config/useFormConfig.tsx';
  import { ProDialog, ProForm } from 'sun-biz';
  import { querySystemListByExample } from '@/api/menu';
  import { SelectOptions } from '~/typings/common.ts';

  const router = useRouter();

  const formRef = ref<{
    ref: FormInstance;
    model: AppManage.createAppBatchFormModel;
  }>();
  const props = defineProps<{
    appEnvId: string;
    hospitalId: string;
  }>();
  const createBatchAppDialogRef = ref();

  const { t } = useTranslation();
  const appManageForm = ref<AppManage.createAppBatchFormModel>({
    sysId: '',
    sysName: '',
    codeRepositoryId: '',
    codeRepositoryName: '',
    replicas: '',
    appPath: '',
    backupPath: '',
  });
  const emits = defineEmits<{ success: [] }>();

  const systemSelections = ref<SelectOptions[]>([]);
  const codeRepositoryName = ref({});
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        systemSelections.value = res.data
          .sort((a, b) => {
            return Number(a?.sort) - Number(b?.sort);
          })
          .map((item) => ({
            value: item.sysId,
            label: item.sysName,
          }));
      } else {
        systemSelections.value = [];
      }
    }
  };

  const getCodeRepositoryName = async (
    val: CodeRepositoryManage.CodeRepositoryInfo,
  ) => {
    codeRepositoryName.value = val ?? {};
  };
  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const query = {
            sysId: formRef?.value?.model.sysId,
            sysName:
              systemSelections?.value?.find(
                (item) => item.value === formRef?.value?.model.sysId,
              )?.label || '未知',
            codeRepositoryId: formRef?.value?.model.codeRepositoryId,
            codeRepositoryName: codeRepositoryName?.value?.dataValueNameDisplay,
            codeRepositoryTypeCode: codeRepositoryName?.value?.dataValueNo,
            replicas: formRef?.value?.model.replicas,
            appEnvId: props.appEnvId,
            hospitalId: props.hospitalId,
            appPath: formRef?.value?.model.appPath,
            backupPath: formRef?.value?.model.backupPath,
          };
          await router.push({
            path: '/detail/add',
            query,
          });
        } else {
          reject(['', new Error('校验失败')]);
        }
      });
    });
  };

  const formConfig = useAppManageBatchAddFormConfig(
    getCodeRepositoryName,
    systemSelections,
  );
  // 打开弹窗
  const openDialog = async () => {
    nextTick(() => {
      querySystemList();
      createBatchAppDialogRef.value.open();
    });
  };
  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="createBatchAppDialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :title="`${t('appManage.createdApp', '批量创建应用')}`"
    :width="500"
    destroy-on-close
    include-footer
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="appManageForm"
      :column="1"
      :data="formConfig"
    />
  </ProDialog>
</template>
