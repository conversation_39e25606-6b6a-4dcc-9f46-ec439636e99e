<script lang="ts" name="productManage" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    TableRef,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import {
    addProduct,
    deleteProduct,
    editProduct,
    queryProductByExample,
  } from '@/modules/baseConfig/api/productManage.ts';
  import { useProductManageFormConfig } from '../config/useProductListFormConfig.tsx';
  import { useProductManageTableConfig } from '../config/useProductListTableConfig.tsx';
  import { ElMessage, ElMessageBox, type FormInstance } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';

  const router = useRouter();
  const selections = ref<Menu.MixSystemMenuElement[]>([]);
  const productManageTableRef = ref<TableRef>();
  const productListData = ref<ProductManage.ProductList[]>([]);
  const proForm = ref<{
    ref: FormInstance;
  }>();
  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<ProductManage.QueryProductManageParams>({
    keyWord: '',
  });
  const loading = ref(false);

  /** 查询产品列表 */
  async function queryProductByExampleByApi(
    data?: ProductManage.QueryProductManageParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
    };
    const [, res] = await queryProductByExample(params);
    loading.value = false;
    if (res?.success) {
      productListData.value = res.data || [];
    }
  }

  const canUpsertTableRow = () => {
    const isEditing = productListData.value.some((item) => !!item.editable);
    console.log('isEditing', isEditing);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  const addNewProductInfo = () => {
    if (!canUpsertTableRow()) return;
    productListData.value.push({
      productName: '',
      enabledFlag: ENABLED_FLAG.YES,
      product2ndName: '',
      productExtName: '',
      productDesc: '',
      editable: true,
    });
  };
  const handleSave = async (row: ProductManage.ProductList) => {
    productManageTableRef.value?.formRef.validate(
      async (tableValid: boolean) => {
        if (tableValid) {
          let result;
          if (row.productId) {
            const [, res] = await editProduct({
              ...row,
            });
            result = { ...res };
          } else {
            const [, res] = await addProduct({
              ...row,
            });
            result = { ...res };
          }

          if (result?.success) {
            queryProductByExampleByApi();
          }
        } else {
          // ElMessage.warning(
          //   t('productManage.errorTip.formValidateError', '请填写必填项'),
          // );
        }
      },
    );
  };
  // 删除
  const deleteItem = async (row: ProductManage.ProductList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.productName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        loading.value = true;
        const [, res] = await deleteProduct({
          ...row,
        });
        loading.value = false;
        if (res?.success) {
          productManageTableRef?.value.proTableRef.clearSelection();
          selections.value = [];

          queryProductByExampleByApi();
        }
      })
      .catch(() => {});
  };

  // 编辑
  const handleEdit = async (row: ProductManage.ProductList) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(row);
  };
  const handleEnableSwitch = async (row: ProductManage.ProductList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.productName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        loading.value = true;
        const [, res] = await editProduct({
          ...row,
          enabledFlag:
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        });
        loading.value = false;
        if (res?.success) {
          row.enabledFlag =
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES;
          await queryProductByExampleByApi();
        }
      })
      .catch(() => {});
  };
  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.productId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: ProductManage.ProductList[]) => {
    selections.value = val;
  };

  const go2hospitalList = (row?: ProductManage.ProductList) => {
    router.push({
      name: 'hospitalList',
      params: {
        productId: row?.productId,
      },
    });
  };
  // 表格配置数据
  const { tableColumns, toggleEdit } = useProductManageTableConfig({
    id: 'productId',
    data: productListData,
    tableRef: productManageTableRef,
    handleSave,
    deleteItem,
    handleEdit,
    handleEnableSwitch,
    go2hospitalList,
    isCloudEnv,
  });

  const searchConfig = useProductManageFormConfig(queryProductByExampleByApi);

  onMounted(() => {
    queryProductByExampleByApi();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="t('productManage.list.title', '产品管理')" />

    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
        />
      </div>
      <div class="flex justify-between">
        <el-button
          class="mr-2"
          type="primary"
          @click="queryProductByExampleByApi"
        >
          {{ t('productManage.searchForm.search', '查询') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-4"
          type="primary"
          @click="addNewProductInfo"
        >
          {{ t('productManage.searchForm.add', '新增') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_PRODUCT"
          @success="
            () => {
              productManageTableRef?.proTableRef.clearSelection();
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="productManageTableRef"
      :columns="tableColumns"
      :data="productListData"
      :editable="true"
      :loading="loading"
      row-key="productId"
      @selection-change="selectionChange"
    />
  </div>
</template>
