<script lang="ts" name="hospitalList" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable } from 'sun-biz';
  import { SelectOptions } from '@/typings/common.ts';
  import { addOrgXProdCodeRepository } from '@/modules/baseConfig/api/productManage.ts';
  import {
    queryCodeRepositoryByExample,
    querySystemListByExample,
  } from '@/modules/system/api/menu';
  import { useProductAddCodeRepositoryTableConfig } from '../../config/useProductAddCodeRepositoryTableConfig.tsx';
  import { useProductAddCodeRepositoryFormConfig } from '../../config/useProductAddCodeRepositoryFromConfig.tsx';
  import { ElMessage } from 'element-sun';

  const route = useRoute();
  const router = useRouter();

  // 扩展查询参数类型，添加可能的过滤字段
  interface ExtendedQueryParams extends ProductManage.QueryProductManageParams {
    codeRepositoryTypeCode?: string;
    systemId: string;
  }

  const searchParams = ref<ExtendedQueryParams>({
    keyWord: '',
    codeRepositoryTypeCode: '',
    systemId: '',
  });

  // 扩展产品信息类型，确保包含所需属性
  interface ProductInfo extends ProductManage.UpsertOrgXProdCodeRepository {
    productId?: string;
    productName?: string;
    orgId?: string;
    orgName?: string;
  }

  const info = ref<ProductInfo>({});

  const productAddCodeRepositoryRef =
    ref<ProductManage.UpsertOrgXProdCodeRepository>(); // 使用any类型避免类型检查错误

  const selections = ref<ProductManage.UpsertOrgXProdCodeRepository[]>([]);
  const codeRepositorySource = ref<
    ProductManage.UpsertOrgXProdCodeRepository[]
  >([]);
  const codeRepositoryUse = ref<ProductManage.UpsertOrgXProdCodeRepository[]>(
    [],
  );

  const { t } = useTranslation();

  const loading = ref(false);

  const queryCodeRepositoryList = async () => {
    const [, res] = await queryCodeRepositoryByExample({
      keyWord: '',
      enabledFlag: 1,
    });
    if (res?.success) {
      codeRepositorySource.value = res.data || [];
      codeRepositoryUse.value = codeRepositorySource.value;
    }
  };

  const systemList = ref<Menu.SystemInfo[]>([]);
  const systemSelections = ref<SelectOptions[]>([]);
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        systemList.value = res.data || [];
        systemSelections.value = res.data
          .sort((a, b) => {
            return Number(a?.sort) - Number(b?.sort);
          })
          .map((item) => ({
            value: item.sysId,
            label: item.sysName,
          }));
      } else {
        systemSelections.value = [];
      }
    }
  };

  const saveProductDefine = async () => {
    const ids = selections.value.map((item) => item.codeRepositoryId);
    const params = {
      orgXProdId: info.value.orgXProdId,
      codeRepositoryIds: ids,
    };
    const [, res] = await addOrgXProdCodeRepository(params);

    if (res?.success) {
      ElMessage.success(t('productManage.successTip.addSuccess', '添加成功！'));
      history.back();
    }
  };
  // 选中行设置
  const selectionChange = (
    val: ProductManage.UpsertOrgXProdCodeRepository[],
  ) => {
    selections.value = val;
  };

  // 表格配置数据
  const { tableColumns } = useProductAddCodeRepositoryTableConfig();

  const go2ProductList = () => {
    router.push({
      name: 'productManage',
    });
  };
  const go2HospitalList = () => {
    router.push({
      name: 'hospitalList',
      params: {
        productId: info.value.productId,
      },
    });
  };
  const go2ProductDefine = (row) => {
    router.push({
      name: 'productDefine',
      params: {
        productId: info.value.productId,
        orgId: row.orgId,
      },
      query: {
        productName: info.value.productName,
        orgName: info.value.orgName,
        orgXProdId: row.orgXProdId,
      },
    });
  };
  const filterData = (data?: ExtendedQueryParams) => {
    if (data) {
      // 重置为原始数据
      codeRepositoryUse.value = codeRepositorySource.value;
      // 应用所有筛选条件
      codeRepositoryUse.value = codeRepositoryUse.value.filter((item) => {
        // 关键字筛选
        if (data.keyWord) {
          const name = item.codeRepositoryName || '';
          const desc = item.codeRepositoryDesc || '';
          const keyword = data.keyWord.toLowerCase();
          if (
            !name.toLowerCase().includes(keyword) &&
            !desc.toLowerCase().includes(keyword)
          ) {
            return false;
          }
        }
        // 仓库类型筛选
        if (data.codeRepositoryTypeCode) {
          if (item.codeRepositoryTypeCode !== data.codeRepositoryTypeCode) {
            return false;
          }
        }
        // 系统筛选
        if (data.systemId) {
          const system = systemList.value.find(
            (sys) => sys.sysId === data.systemId,
          );
          if (system) {
            const hasCodeRepository = system.codeRepositoryList?.some(
              (repo) => repo.codeRepositoryId === item.codeRepositoryId,
            );
            if (!hasCodeRepository) {
              return false;
            }
          }
        }
        return true;
      });
    }
  };
  const searchConfig = useProductAddCodeRepositoryFormConfig(
    filterData,
    systemSelections,
  );

  onMounted(() => {
    // 解析路由参数
    const { productId, orgId } = route.params;
    const { productName, orgName, orgXProdId } = route.query;

    if (productId && orgId) {
      info.value = {
        productId,
        orgId,
        orgXProdId,
        productName: typeof productName === 'string' ? productName : '',
        orgName: typeof orgName === 'string' ? orgName : '',
      } as ProductInfo;
      console.log('解析产品信息:', info.value);
    }
    queryCodeRepositoryList();
    querySystemList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="el-form-item text-base">
      <el-button link @click="go2ProductList">产品列表</el-button>
      /
      <el-button link @click="go2HospitalList">医院列表</el-button>
      /
      <el-button link type="primary" @click="go2ProductDefine"
        >代码仓库列表
      </el-button>
    </div>
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="filterData"
        />
      </div>
      <div class="flex justify-between">
        <el-button type="primary" @click="saveProductDefine">
          {{ t('hospitalList.searchForm.save', '保存') }}
        </el-button>

        <el-button plain type="primary" @click="go2ProductDefine">
          {{ t('global:cancel', '取消') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="productAddCodeRepositoryRef"
      :columns="tableColumns"
      :data="codeRepositoryUse"
      :loading="loading"
      row-key="codeRepositoryId"
      @selection-change="selectionChange"
    />
  </div>
</template>
