<script lang="ts" name="appManageDetail" setup>
  import { onMounted, ref } from 'vue';
  import type { LocationQueryValue } from 'vue-router';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage, TableColumnCtx } from 'element-sun';

  import { ProTable } from 'sun-biz';

  import { useBatchTableColumnConfig } from '../../config/useTableConfig.tsx';
  import { querySystemListByExample } from '~/api/menu.ts';
  import { SelectOptions } from '~/typings/common.ts';
  import {
    createAppApi,
    queryComputerManageListByExample,
  } from '~/modules/releaseDeployment/api/appManage.ts';
  import { useTranslation } from 'i18next-vue';

  const router = useRouter();
  const route = useRoute();
  const submitLoading = ref(false);
  const { t } = useTranslation();
  const {
    sysId,
    sysName,
    codeRepositoryName,
    codeRepositoryTypeCode,
    replicas,
    appEnvId,
    hospitalId,
    appPath,
    backupPath,
  } = route.query;
  const isRequired = ref(true);

  let codeRepositoryList = ref<SelectOptions[]>([]);
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        res.data.find((item) => {
          if (item.sysId === sysId) {
            codeRepositoryList.value = item?.codeRepositoryList
              ? item?.codeRepositoryList.map(
                  (child: CodeRepositoryManage.CodeRepositoryInfo) => {
                    return {
                      value: child?.codeRepositoryId,
                      label: child?.codeRepositoryName,
                      codeRepositoryTypeCode: child?.codeRepositoryTypeCode,
                      codeRepositoryDesc: child?.codeRepositoryDesc,
                    };
                  },
                )
              : [];
          }
        });
        codeRepositoryList.value = codeRepositoryList?.value.filter(
          (item: CodeRepositoryManage.CodeRepositoryInfo) =>
            item.codeRepositoryTypeCode === codeRepositoryTypeCode,
        );
        initTableData(codeRepositoryList.value);
      }
    }
  };

  const computerList = ref<SelectOptions[]>([]);

  /** 查询计算机列表 */
  async function queryComputerManageData() {
    const params = {
      hospitalId: '',
      pageNumber: 1,
      pageSize: 200,
    };
    try {
      const [, res] = await queryComputerManageListByExample(params);
      if (res?.success) {
        computerList.value = (res.data || []).map((item) => ({
          label: item?.computerName || item?.computerDesc || '',
          value: item?.computerId || '',
        }));
      }
    } catch {
      computerList.value = [];
    }
  }

  // 配置信息table列表
  const AppListBatchItemForTable = ref<AppManage.AppListBatchModel[]>([]);

  // 添加类型转换函数
  const toString = (
    value: LocationQueryValue | LocationQueryValue[] | null | undefined,
  ): string => {
    if (!value) return '';
    if (Array.isArray(value)) {
      return value[0] || '';
    }
    return value;
  };

  // 转换查询参数为字符串
  const sysIdStr = toString(sysId as LocationQueryValue);
  const sysNameStr = toString(sysName as LocationQueryValue);
  const codeRepositoryTypeCodeStr = toString(
    codeRepositoryTypeCode as LocationQueryValue,
  );
  const appPathStr = toString(appPath as LocationQueryValue);
  const backupPathStr = toString(backupPath as LocationQueryValue);
  const hospitalIdStr = toString(hospitalId as LocationQueryValue);
  const appEnvIdStr = toString(appEnvId as LocationQueryValue);

  function initTableData(data: SelectOptions[]) {
    if (Array.isArray(data)) {
      for (const repositoryItem of data) {
        for (let i = 0; i < Number(replicas); i++) {
          AppListBatchItemForTable.value.push({
            codeRepositoryId: repositoryItem.value,
            codeRepositoryName: repositoryItem.label,
            sysId: sysIdStr,
            sysName: sysNameStr,
            codeRepositoryTypeCode: codeRepositoryTypeCodeStr,
            appDesc: repositoryItem.codeRepositoryDesc,
            appName: repositoryItem.label + '-' + (i + 1),
            machineId: '',
            port: '',
            editable: true,
            appPath: appPathStr,
            backupPath: backupPathStr,
          });
        }
      }
      isRequired.value = codeRepositoryTypeCodeStr === '2';
    }
    getSpanArr(AppListBatchItemForTable.value);
  }

  const spanArr = ref<number[]>([]);
  let position = 0;
  const getSpanArr = (data: AppManage.AppListBatchModel[]) => {
    spanArr.value = [];
    position = 0;
    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        spanArr.value.push(1);
        position = 0;
      } else {
        if (data[i].codeRepositoryId === data[i - 1].codeRepositoryId) {
          spanArr.value[position]++;
          spanArr.value.push(0);
        } else {
          spanArr.value.push(1);
          position = i;
        }
      }
    }
  };

  const indexMethod = (index: number) => {
    let realIndex = 0;
    for (let i = 0; i <= index; i++) {
      if (spanArr.value[i] > 0) {
        realIndex++;
      }
    }
    return realIndex;
  };

  /**
   * 新增配置信息列表
   */
  function insertRow(index: number) {
    const currentItem = AppListBatchItemForTable.value[index];
    const newItem = {
      codeRepositoryId: currentItem.codeRepositoryId,
      codeRepositoryName: currentItem.codeRepositoryName,
      sysId: sysIdStr,
      sysName: sysNameStr,
      codeRepositoryTypeCode: codeRepositoryTypeCodeStr,
      appDesc: currentItem.codeRepositoryDesc,
      appName: currentItem.codeRepositoryName,
      machineId: '',
      port: '',
      editable: true,
      appPath: appPathStr,
      backupPath: backupPathStr,
    };
    const targetIndex = findLastIndexOfSameId(index);
    AppListBatchItemForTable.value.splice(targetIndex + 1, 0, newItem);
    getSpanArr(AppListBatchItemForTable.value);
  }

  function cancelRow(row: AppManage.AppListBatchModel, index: number) {
    const codeRepositoryId = row.codeRepositoryId;
    const sameList = AppListBatchItemForTable.value.filter(
      (item) => item.codeRepositoryId === codeRepositoryId,
    );
    if (sameList.length > 1) {
      const hasValues =
        (codeRepositoryTypeCodeStr === '1' &&
          row.appName &&
          row.appDesc &&
          row.machineId &&
          row.appPath &&
          row.backupPath) ||
        (codeRepositoryTypeCodeStr === '2' &&
          row.appName &&
          row.appDesc &&
          row.machineId &&
          row.port &&
          row.appPath &&
          row.backupPath);
      if (hasValues) {
        row.editable = false;
      } else {
        AppListBatchItemForTable.value.splice(index, 1);
      }
      getSpanArr(AppListBatchItemForTable.value);
    } else if (sameList.length === 1) {
      const hasValues =
        (codeRepositoryTypeCodeStr === '1' &&
          row.appName &&
          row.appDesc &&
          row.machineId &&
          row.appPath &&
          row.backupPath) ||
        (codeRepositoryTypeCodeStr === '2' &&
          row.appName &&
          row.appDesc &&
          row.machineId &&
          row.port &&
          row.appPath &&
          row.backupPath);
      if (hasValues) {
        row.editable = false;
      } else {
        ElMessage.error(t('appManage.onlyone', '至少保留一行数据'));
      }
      getSpanArr(AppListBatchItemForTable.value);
    } else {
      ElMessage.error(t('appManage.onlyone', '至少保留一行数据'));
      return;
    }
  }

  function deleteRow(row: AppManage.AppListBatchModel, index: number) {
    const currentId = row.codeRepositoryId;
    const sameIdRows = AppListBatchItemForTable.value.filter(
      (item) => item.codeRepositoryId === currentId,
    );
    if (sameIdRows.length > 1) {
      AppListBatchItemForTable.value.splice(index, 1);
      getSpanArr(AppListBatchItemForTable.value);
    } else {
      ElMessage.error(t('appManage.onlyone', '至少保留一行数据'));
    }
  }

  // 查找相同 codeRepositoryId 的最后一行索引
  const findLastIndexOfSameId = (index: number) => {
    const currentId = AppListBatchItemForTable.value[index].codeRepositoryId;
    let lastIndex = index;
    for (let i = index + 1; i < AppListBatchItemForTable.value.length; i++) {
      if (AppListBatchItemForTable.value[i].codeRepositoryId === currentId) {
        lastIndex = i;
      } else {
        break;
      }
    }
    return lastIndex;
  };

  const tableRef = ref();
  const { columnConfig } = useBatchTableColumnConfig({
    id: 'sysId',
    tableRef,
    data: AppListBatchItemForTable,
    insertRow: insertRow,
    computerList,
    deleteRow,
    cancelRow,
    isRequired,
    indexMethod,
  });

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  // 保存
  const handleSubmit = async () => {
    submitLoading.value = false;
    return new Promise<[never, unknown]>((resolve, reject) => {
      tableRef.value?.formRef.validate(async (tableValid: boolean) => {
        if (tableValid) {
          // 检查是否有未保存的配置
          const hasUnsaved = (AppListBatchItemForTable.value ?? []).find(
            (item) => item.editable === true,
          );
          if (hasUnsaved) {
            ElMessage.error(t('notSave.refundPayWay', '存在未保存的配置'));
            return reject(['', new Error('参数错误')]);
          }
          let hasEmptyRequired = false;
          // 检查必填字段
          hasEmptyRequired = (AppListBatchItemForTable.value ?? []).find(
            (item) => {
              if (codeRepositoryTypeCodeStr === '1') {
                return (
                  !item.appPath ||
                  !item.backupPath ||
                  !item.appName ||
                  !item.appDesc ||
                  !item.machineId
                );
              }
              if (codeRepositoryTypeCodeStr === '2') {
                return (
                  !item.appPath ||
                  !item.backupPath ||
                  !item.appName ||
                  !item.appDesc ||
                  !item.machineId ||
                  !item.port
                );
              }
            },
          );
          if (hasEmptyRequired) {
            ElMessage.error(t('global:required.field', '请填写必填字段'));
            return reject(['', new Error('必填字段未填写')]);
          }

          const params = {
            hospitalId: hospitalIdStr,
            sysId: sysIdStr,
            appEnvId: appEnvIdStr,
            appList: AppListBatchItemForTable.value,
          };

          const [, res] = await createAppApi(params);
          if (res?.success) {
            ElMessage.success(t('global:save.success'));
            goBack();
          }
          resolve([] as unknown as [never, unknown]);
        } else {
          reject(['', new Error('接口错误')]);
        }
      });
    });
  };

  interface SpanMethodProps {
    row: AppManage.AppListBatchModel;
    column: TableColumnCtx<AppManage.AppListBatchModel>;
    rowIndex: number;
    columnIndex: number;
  }

  const spanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
  }: SpanMethodProps) => {
    console.log(column);
    if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
      const idRowspanMap = new Map<string, number>();
      const idFirstRowMap = new Map<string, number>();

      AppListBatchItemForTable.value.forEach((item, index) => {
        const id: string = item?.codeRepositoryId ?? '';
        if (!idRowspanMap.has(id)) {
          idRowspanMap.set(id, 1);
          idFirstRowMap.set(id, index);
        } else {
          idRowspanMap.set(id, idRowspanMap.get(id)! + 1);
        }
      });

      const currentId = row.codeRepositoryId ?? '';
      const firstRow = idFirstRowMap.get(currentId)!;
      const rowspan = idRowspanMap.get(currentId)!;

      if (rowIndex === firstRow) {
        return {
          rowspan,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
    return {
      rowspan: 1,
      colspan: 1,
    };
  };

  onMounted(() => {
    querySystemList();
    queryComputerManageData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <div class="flex h-full flex-1 flex-col overflow-hidden">
      <el-page-header @back="goBack">
        <template #content>
          <span class="text-base">
            <span class="mr-2">系统：{{ sysName }}</span>
            <span> 代码仓库类型：{{ codeRepositoryName }} </span>
          </span>
        </template>
      </el-page-header>

      <div class="mb-2 mt-5 text-right">
        <el-button
          :disabled="submitLoading"
          :loading="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          {{ $t('global:save') }}
        </el-button>
        <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
      </div>
      <ProTable
        ref="tableRef"
        :columns="columnConfig"
        :data="AppListBatchItemForTable"
        :editable="true"
        :span-method="spanMethod"
      />
    </div>
  </div>
</template>
