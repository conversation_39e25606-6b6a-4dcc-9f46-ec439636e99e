<script lang="ts" name="hospitalList" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { useTranslation } from 'i18next-vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProTable,
    TableRef,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import {
    deleteOrgXProd,
    queryProductByExample,
    saveOrgXProd,
  } from '@/modules/baseConfig/api/productManage.ts';
  import { useHospitalListTableConfig } from '../../config/useHospitalListTableConfig.tsx';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { getHospitalSelections } from '@/utils/common';
  import { queryCodeBranchByExample } from '@/modules/project/api/codeBranchManage';
  import { querySystemListByExample } from '@modules/system/api/menu';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const route = useRoute();
  const router = useRouter();

  const productId = ref<ProductManage.ProductList>({});
  const productInfo = ref<ProductManage.ProductList>({});
  const orgList = ref<ProductManage.OrgList[]>([]);
  const hospitalSelections = ref<ProductManage.OrgList[]>([]);
  const branchListSource = ref<ProductManage.OrgList[]>([]);
  const branchList = ref<ProductManage.OrgList[]>([]);
  const systemList = ref<Menu.SystemInfo[]>([]);

  const selections = ref<Menu.MixSystemMenuElement[]>([]);
  const hospitalListTableRef = ref<TableRef>();

  const { t } = useTranslation();

  const loading = ref(false);

  /** 查询产品列表 */
  async function queryProductByExampleByApi() {
    const params = {
      productId: productId.value,
    };
    const [, res] = await queryProductByExample(params);
    if (res?.success) {
      if (res.data.length > 0) {
        productInfo.value = res.data[0];
        orgList.value = productInfo.value.orgList || [];
      }
    }
  }

  const getOrgList = async () => {
    hospitalSelections.value = await getHospitalSelections();
  };
  const canUpsertTableRow = () => {
    const isEditing = orgList.value.some((item) => !!item.editable);
    console.log('isEditing', isEditing);

    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  const addNewHospitalInfo = () => {
    if (!canUpsertTableRow()) return;
    orgList.value.push({
      productId: '',
      orgXProdId: '',
      orgId: '',
      codeBranchId: '',
      sysList: [],
      editable: true,
    });
  };
  const queryCodeBranchData = async (): Promise<void> => {
    let [, res] = await queryCodeBranchByExample({});
    if (res?.success) {
      branchListSource.value = (res.data || []).filter(
        (item) => item.enabledFlag === ENABLED_FLAG.YES,
      );
      branchList.value = branchListSource.value;
    }
  };
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        systemList.value = res.data
          .sort((a, b) => {
            return Number(a?.sort) - Number(b?.sort);
          })
          .map((item) => ({
            value: item.sysId,
            label: item.sysName,
          }));
      } else {
        systemList.value = [];
      }
    }
  };
  const onOrgChange = (
    orgId: string,
    row: ProductManage.OrgList,
    clear: boolean = true,
  ) => {
    row.codeBranchId = clear ? '' : row.codeBranchId;
    if (orgId) {
      branchList.value = branchListSource.value.filter(
        (item) =>
          item.hospitalList?.some((item) => item.hospitalId === orgId) &&
          item.enabledFlag === ENABLED_FLAG.YES,
      );
    } else {
      branchList.value = branchListSource.value;
    }
  };
  const handleSave = async (row: ProductManage.ProductList) => {
    hospitalListTableRef.value?.formRef.validate(
      async (tableValid: boolean) => {
        if (tableValid) {
          const [, res] = await saveOrgXProd({
            ...row,
            productId: productId.value,
          });
          if (res?.success) {
            row.editable = false;
            queryProductByExampleByApi();
          }
        }
      },
    );
  };
  // 删除
  const deleteItem = async (row: ProductManage.OrgList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.orgName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, res] = await deleteOrgXProd({
          orgXProdId: row.orgXProdId,
        });
        if (res?.success) {
          hospitalListTableRef?.value.proTableRef.clearSelection();
          selections.value = [];
          queryProductByExampleByApi();
        }
      })
      .catch(() => {});
  };

  // 编辑
  const handleEdit = async (row: ProductManage.ProductList) => {
    if (!canUpsertTableRow()) return;
    onOrgChange(row.orgId, row, false);
    toggleEdit(row);
  };

  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.orgXProdId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: ProductManage.ProductList[]) => {
    selections.value = val;
  };

  const go2ProductDefine = (row) => {
    router.push({
      name: 'productDefine',
      params: {
        productId: productInfo.value.productId,
        orgId: row.orgId,
      },
      query: {
        productName: productInfo.value.productName,
        orgName: row.orgName,
        orgXProdId: row.orgXProdId,
      },
    });
  };
  // 表格配置数据
  const { tableColumns, toggleEdit } = useHospitalListTableConfig({
    id: 'orgId',
    data: orgList,
    tableRef: hospitalListTableRef,
    hospitalSelections,
    onOrgChange,
    branchList,
    systemList,
    handleSave,
    deleteItem,
    handleEdit,
    go2ProductDefine,
    isCloudEnv,
  });

  const go2productPage = () => {
    router.push({
      name: 'productManage',
    });
  };
  onMounted(() => {
    productId.value = route.params.productId;
    console.log('解析产品信息:', productId.value);
    queryProductByExampleByApi();
    getOrgList();
    queryCodeBranchData();
    querySystemList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="t('hospitalList.list.title', '医院产品配置')" />

    <div class="mt-3 flex justify-between">
      <div class="el-form-item text-base">
        {{ t('hospitalList.searchForm.productName', '产品名称') }}：
        <el-button link>{{ productInfo.productName }}</el-button>
      </div>
      <div class="flex justify-between">
        <el-button
          :disabled="!isCloudEnv"
          class="mr-4"
          type="primary"
          @click="addNewHospitalInfo"
        >
          {{ t('hospitalList.searchForm.add', '新增') }}
        </el-button>

        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_ORG_X_PROD"
          class="mr-4"
          @success="
            () => {
              hospitalListTableRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button plain type="primary" @click="go2productPage()">
          {{ t('global:cancel', '取消') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="hospitalListTableRef"
      :columns="tableColumns"
      :data="orgList"
      :editable="true"
      :loading="loading"
      row-key="orgXProdId"
      @selection-change="selectionChange"
    />
  </div>
</template>
