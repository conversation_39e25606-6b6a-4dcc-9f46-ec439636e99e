<script setup lang="tsx" name="CadnSelect">
  import { WarningFilled, CirclePlus } from '@element-sun/icons-vue';
  import { useAttrs } from 'vue';
  import { useRouter } from 'vue-router';

  const props = defineProps<{
    alreadyUseFlag?: boolean;
  }>();

  const attrs: {
    modelValue?: string;
    disabled?: boolean | undefined;
    options?: Cadn.CadnMedicineSpecItem[];
    'onUpdate:modelValue'?: (value: string) => void;
  } = useAttrs();

  const router = useRouter();

  const emit = defineEmits(['change']);

  function change(value: string) {
    if (attrs['onUpdate:modelValue']) {
      attrs['onUpdate:modelValue'](value);
    }
    emit('change', value);
  }

  const onClick = () => {
    if (attrs.disabled) {
      const page = {
        name: 'cadnDetail',
        params: { id: attrs.modelValue },
        query: {},
      };
      if (props.alreadyUseFlag) {
        page.query = { alreadyUseFlag: true };
      }
      router.push(page);
      return;
    }
    router.push('/cadnDetail/add');
  };
</script>
<template>
  <div class="flex w-full items-center">
    <el-select @change="change" v-bind="{ ...attrs }">
      <el-option
        v-for="item in attrs.options ?? []"
        :key="item.cadnId"
        :label="item.cadn"
        :value="item.cadnId"
      >
        <span>{{
          `${item.cadn} | ${item.medicineSpec || '-'} | ${item.medicineTypeDesc || '-'} | ${item.dosageFormDesc || '-'}`
        }}</span>
      </el-option>
    </el-select>
    <el-icon
      v-if="attrs.disabled"
      class="ml-2 cursor-pointer text-blue-500"
      size="20"
      @click="onClick"
    >
      <WarningFilled />
    </el-icon>
    <el-icon
      v-else
      class="ml-2 cursor-pointer text-blue-500"
      size="20"
      @click="onClick"
    >
      <CirclePlus />
    </el-icon>
  </div>
</template>
