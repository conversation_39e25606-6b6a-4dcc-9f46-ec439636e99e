<script setup lang="ts" name="cadnUnitTab">
  import { ref, nextTick, watch } from 'vue';
  import { ProTable, ProForm } from 'sun-biz';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { SelectOptions } from '@/typings/common';
  import { useUnit } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import { useCadnMedicineSpecFormConfig } from '@/modules/baseConfig/pages/medicineManage/config/useFormConfig';
  import { useCadnMedicineSpecDosageUnitTableConfig } from '@/modules/baseConfig/pages/medicineManage/config/useTableConfig';

  type MedicineSpecItem = Partial<
    Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }
  >;
  type CadnMedicineSpecDosageUnitTableRow =
    Partial<Cadn.MedicineSpecDosageUnitItem> & {
      editable: boolean;
      key?: string;
      isDefault?: boolean;
    };
  type Props = {
    alreadyUseFlag?: boolean;
    data: MedicineSpecItem;
    selectOptionsMap: {
      [key: string]: SelectOptions[];
    };
  };

  const props = withDefaults(defineProps<Props>(), {
    data: () => ({}),
  });
  const emit = defineEmits(['change']);
  const { unitOptions, getUnitList } = useUnit(); // 计价单位 options
  const medicineSpecItem = ref<MedicineSpecItem>({});
  const medicineSpecDosageUnitList = ref<CadnMedicineSpecDosageUnitTableRow[]>(
    [],
  );

  const optionsMap = ref<{
    [key: string]: SelectOptions[];
  }>({});

  const formRef = ref();
  const medicineSpecDosageUnitListTableRef = ref();

  watch(
    () => props.data,
    () => {
      medicineSpecItem.value = props.data ? { ...props.data } : {};
      medicineSpecDosageUnitList.value =
        props.data.medicineSpecDosageUnitList?.map((item) => ({
          ...item,
          editable: !!item?.editable,
        })) || [];
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.selectOptionsMap,
    () => {
      if (props.selectOptionsMap) {
        optionsMap.value = {
          ...optionsMap.value,
          ...props.selectOptionsMap,
        };
      }
    },
    { deep: true, immediate: true },
  );

  const handleChange = () => {
    emit('change', {
      ...medicineSpecItem.value,
      medicineSpecDosageUnitList: medicineSpecDosageUnitList.value,
    });
  };

  const onAddUnitItem = () => {
    addItem({
      editable: true,
      enabledFlag: ENABLED_FLAG.YES,
    });
  };

  const formConfig = useCadnMedicineSpecFormConfig(
    props.alreadyUseFlag,
    medicineSpecItem,
    unitOptions,
    optionsMap,
    getUnitList,
    handleChange,
  );

  const { cadnMedicineSpecDosageUnitTableConfig, addItem } =
    useCadnMedicineSpecDosageUnitTableConfig(
      medicineSpecDosageUnitListTableRef,
      medicineSpecDosageUnitList,
      medicineSpecItem,
      optionsMap,
    );

  const handleFormChange = (formData: MedicineSpecItem, name?: string) => {
    switch (name) {
      case 'miniUnitId': {
        let unitItem =
          unitOptions.value?.find(
            (item) => item.value === formData.miniUnitId,
          ) ||
          optionsMap.value?.unitOptions?.find(
            (item) => item.value === formData.miniUnitId,
          );
        if (unitItem) {
          nextTick(() => {
            medicineSpecItem.value.miniUnitName = unitItem.label;
            medicineSpecItem.value.medicineSpec = `${medicineSpecItem.value.doseFactor || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
            handleChange();
          });
        }
        break;
      }
      case 'doseFactor':
        nextTick(() => {
          medicineSpecItem.value.medicineSpec = `${medicineSpecItem.value.doseFactor || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
          handleChange();
        });
        break;
      default:
        break;
    }
  };

  defineExpose({
    formRef,
    medicineSpecDosageUnitListTableRef,
    medicineSpecItem,
    medicineSpecDosageUnitList,
  });
</script>
<template>
  <div class="flex flex-col">
    <div class="flex items-center justify-between">
      <ProForm
        ref="formRef"
        class="mb-4"
        v-model="medicineSpecItem"
        :data="formConfig"
        @model-change="handleFormChange"
      />
      <el-button type="primary" @click="onAddUnitItem">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="medicineSpecDosageUnitListTableRef"
      row-key="medicineSpecDosageUnitId"
      :max-height="400"
      :editable="true"
      :data="medicineSpecDosageUnitList"
      :columns="cadnMedicineSpecDosageUnitTableConfig"
    />
  </div>
</template>
