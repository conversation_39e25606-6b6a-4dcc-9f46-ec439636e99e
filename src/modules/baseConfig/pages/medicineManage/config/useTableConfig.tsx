import { Ref } from 'vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';

type MedicineTableItem = Medicine.MedicineListItem & {
  [key: string]: {
    hospitalId?: string;
    hospitalName?: string;
    commodityCategoryName?: string;
    commodityPurchasePrice?: number;
    price?: number;
    enabledFlag?: number;
  };
};

export function useMedicineTableColumnConfig(
  medicineListHospitalList: Ref<Medicine.HospitalMedicineItem[]>,
  handleEdit: (data: MedicineTableItem) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:sequenceNumber'),
          prop: 'indexNo',
          editable: false,
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('medicine.medicineTable.commodityNo', '药品编码'),
          minWidth: 120,
          prop: 'commodityNo',
        },
        {
          label: t('medicine.medicineTable.commodityName', '药品名称'),
          minWidth: 140,
          prop: 'commodityName',
        },
        {
          label: t('medicine.medicineTable.commodity2ndName', '药品辅助名称'),
          minWidth: 140,
          prop: 'commodity2ndName',
        },
        {
          label: t('medicine.medicineTable.commodityExtName', '药品扩展名称'),
          minWidth: 140,
          prop: 'commodityExtName',
        },
        {
          label: t('medicine.medicineTable.commoditySpec', '药品扩展名称'),
          minWidth: 110,
          prop: 'commoditySpec',
        },
        {
          label: t('medicine.medicineTable.unitName', '单位'),
          minWidth: 100,
          prop: 'unitName',
        },
        {
          label: t('medicine.medicineTable.producedByOrgName', '生产厂家'),
          minWidth: 150,
          prop: 'producedByOrgName',
        },
        {
          label: t('medicine.medicineTable.validPeriod', '有效期(月)'),
          minWidth: 130,
          prop: 'validPeriod',
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          width: 100,
          render: (row: MedicineTableItem) => {
            return (
              <div class={'flex justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleEdit(row)}
                >
                  {t('global:edit')}
                </el-button>
              </div>
            );
          },
        },
      ];
      if (medicineListHospitalList.value?.length === 1) {
        const hospitalItemColumnList = [
          {
            label: t(
              'medicine.medicineTable.commodityCategoryName',
              '费用分类',
            ),
            minWidth: 130,
            prop: 'hospital0.commodityCategoryName',
          },
          {
            label: t(
              'medicine.medicineTable.commodityPurchasePrice',
              '参考进价',
            ),
            minWidth: 120,
            prop: 'hospital0.commodityPurchasePrice',
          },
          {
            label: t('medicine.medicineTable.price', '销售单价'),
            minWidth: 120,
            prop: 'hospital0.price',
          },
          {
            label: t('global:enabledFlag'),
            prop: 'enabledFlag',
            minWidth: 100,
            render: (row: MedicineTableItem) => {
              return (
                <el-switch
                  modelValue={row.hospital0.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  disabled={true}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              );
            },
          },
        ];
        data.splice(8, 0, ...hospitalItemColumnList);
      } else if (medicineListHospitalList.value?.length > 1) {
        const hospitalItemColumnList = [];
        for (let i = 0; i < medicineListHospitalList.value.length; i++) {
          hospitalItemColumnList.push({
            label: medicineListHospitalList.value[i].hospitalName,
            prop: `hospital${i}`,
            _children: [
              {
                label: t(
                  'medicine.medicineTable.commodityCategoryName',
                  '费用分类',
                ),
                minWidth: 130,
                prop: `hospital${i}.commodityCategoryName`,
              },
              {
                label: t(
                  'medicine.medicineTable.commodityPurchasePrice',
                  '参考进价',
                ),
                minWidth: 120,
                prop: `hospital${i}.commodityPurchasePrice`,
              },
              {
                label: t('medicine.medicineTable.price', '销售单价'),
                minWidth: 120,
                prop: `hospital${i}.price`,
              },
              {
                label: t('global:enabledFlag'),
                prop: 'enabledFlag',
                minWidth: 100,
                render: (row: MedicineTableItem) => {
                  return (
                    <el-switch
                      modelValue={row[`hospital${i}`]?.enabledFlag}
                      inline-prompt
                      active-value={ENABLED_FLAG.YES}
                      inactive-value={ENABLED_FLAG.NO}
                      disabled={true}
                      active-text={t('global:enabled')}
                      inactive-text={t('global:disabled')}
                    />
                  );
                },
              },
            ],
          });
        }
        data.splice(8, 0, ...hospitalItemColumnList);
      }
      return data;
    },
  });
}

type MedicinePackUnitTableRow = Partial<Medicine.MedicinePackUnitItem> & {
  isUnit?: boolean;
  editable: boolean;
};

export function useMedicinePackUnitTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<MedicinePackUnitTableRow[]>,
  alreadyUseFlag: Ref<boolean>,
  unitOptions: Ref<SelectOptions[]>,
  getUnitList: (params?: Unit.QueryParams) => void,
  handleDelMedicinePackUnit: (
    data: MedicinePackUnitTableRow,
    index: number,
  ) => void,
  handleIsUnitChange: (index: number) => void,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicinePackUnitId',
  });

  const medicinePackUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        width: 80,
        render: (row: MedicinePackUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('medicine.medicinePackUnitTable.packUnitName', '包装单位'),
        prop: 'packUnitId',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicinePackUnitTable.packUnitName',
                '包装单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicinePackUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.packUnitId}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicinePackUnitTable.packUnitName',
                      '包装单位',
                    ),
                  })}
                  clearable={false}
                  filterable={true}
                  remote={true}
                  remoteMethod={(keyWord: string) => {
                    getUnitList({ keyWord, pageNumber: 1, pageSize: 100 });
                  }}
                  onChange={(val: string) => {
                    const item = unitOptions.value?.find(
                      (item) => item.value === val,
                    );
                    row.packUnitName = item?.label as string;
                  }}
                >
                  {unitOptions.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.packUnitName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('medicine.medicinePackUnitTable.convertFactor', '换算系数'),
        prop: 'convertFactor',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.medicinePackUnitTable.convertFactor',
                '换算系数',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicinePackUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  type="number"
                  v-model={row.convertFactor}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'medicine.medicinePackUnitTable.convertFactor',
                      '换算系数',
                    ),
                  })}
                  v-slots={{
                    append: () => (
                      <span>
                        {row.packUnitName
                          ? `${row.packUnitName}/${row.packUnitName}`
                          : ''}
                      </span>
                    ),
                  }}
                ></el-input>
              ) : (
                <>{row.convertFactor}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('medicine.medicinePackUnitTable.isUnit', '计价单位'),
        prop: 'isUnit',
        width: 120,
        render: (row: MedicinePackUnitTableRow, index: number) => {
          return (
            <el-radio
              v-model={row.isUnit}
              value={true}
              onChange={() => handleIsUnitChange(index)}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: MedicinePackUnitTableRow, $index: number) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    disabled={!!alreadyUseFlag.value}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    disabled={!!row.isUnit || !!alreadyUseFlag.value}
                    onClick={() => handleDelMedicinePackUnit(row, $index)}
                  >
                    {t('global:remove')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    disabled={!!alreadyUseFlag.value}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    medicinePackUnitTableConfig,
    addItem,
    delItem,
  };
}

type MedicineUseSceneXUnitTableRow = {
  medicineUseSceneXUnitId?: string;
  medicineUseSceneCode: string;
  medicineUseSceneDesc?: string;
  packUnitId: string;
  packUnitName?: string;
  editable: boolean;
};

export function useMedicineUseSceneXUnitTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<MedicineUseSceneXUnitTableRow[]>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  selectedUnitOptions: Ref<SelectOptions[]>,
  handleConfirm: (row: MedicineUseSceneXUnitTableRow) => void,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicineUseSceneXUnitId',
  });

  const medicineUseSceneXUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        width: 80,
        render: (row: MedicineUseSceneXUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
          '应用场景',
        ),
        width: 380,
        prop: 'medicineUseSceneCode',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
                '应用场景',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicineUseSceneXUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {/* {row.editable ? (
                <el-select
                  v-model={row.medicineUseSceneCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
                      '应用场景',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = (
                      optionsMap.value.medicineUseSceneOptions || []
                    )?.find((item) => item.value === val);
                    row.medicineUseSceneDesc = item?.label as string;
                  }}
                >
                  {(optionsMap.value.medicineUseSceneOptions || []).map(
                    (item) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ),
                  )}
                </el-select>
              ) : ( */}
              <>{row.medicineUseSceneDesc}</>
              {/* )} */}
            </div>
          );
        },
      },
      {
        label: t(
          'medicine.medicineUseSceneXUnitTable.packUnitName',
          '包装单位',
        ),
        prop: 'packUnitId',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineUseSceneXUnitTable.packUnitName',
                '包装单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicineUseSceneXUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.packUnitId}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicineUseSceneXUnitTable.packUnitName',
                      '包装单位',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = selectedUnitOptions.value?.find(
                      (item) => item.value === val,
                    );
                    row.packUnitName = item?.label as string;
                  }}
                >
                  {selectedUnitOptions.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.packUnitName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: MedicineUseSceneXUnitTableRow, $index: number) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleConfirm(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  {/* <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:remove')}
                  </el-button> */}
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    medicineUseSceneXUnitTableConfig,
    toggleEdit,
  };
}

type CadnMedicineSpecDosageUnitTableRow =
  Partial<Cadn.MedicineSpecDosageUnitItem> & {
    editable: boolean;
  };

export function useCadnMedicineSpecDosageUnitTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<CadnMedicineSpecDosageUnitTableRow[]>,
  medicineSpecItem: Ref<
    Partial<Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }>
  >,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
) {
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicineSpecDosageUnitId',
  });

  const cadnMedicineSpecDosageUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        width: 80,
        render: (row: CadnMedicineSpecDosageUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
          '可用剂量单位',
        ),
        width: 450,
        prop: 'doseUnitCode',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
                '可用剂量单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.doseUnitCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
                      '可用剂量单位',
                    ),
                  })}
                  filterable={true}
                  onChange={(val: string) => {
                    const item = optionsMap.value.doseUnitOptions?.find(
                      (item) => item.value === val,
                    );
                    row.doseUnitDesc = item?.label as string;
                  }}
                >
                  {optionsMap.value.doseUnitOptions?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.doseUnitDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'medicine.cadnMedicineSpecDosageUnitTable.convertFactor',
          '换算系数',
        ),
        prop: 'convertFactor',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnMedicineSpecDosageUnitTable.convertFactor',
                '换算系数',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input-number
                  v-model={row.convertFactor}
                  min={0}
                  precision={2}
                  controls={false}
                />
              ) : (
                <>
                  {/* {row.convertFactor &&
                  row.doseUnitDesc &&
                  medicineSpecItem.value.doseUnitDesc
                    ? `${row.convertFactor}${row.doseUnitDesc}/${row.convertFactor}${medicineSpecItem.value.miniUnitName}`
                    : ''} */}
                  {row.convertFactor}
                </>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        width: 100,
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: CadnMedicineSpecDosageUnitTableRow, $index: number) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  {/* <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:remove')}
                  </el-button> */}
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    cadnMedicineSpecDosageUnitTableConfig,
    addItem,
    toggleEdit,
  };
}
