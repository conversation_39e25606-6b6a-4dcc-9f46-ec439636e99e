import { Ref, ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import CadnSelect from '@modules/baseConfig/pages/medicineManage/components/CadnSelect.vue';

export function useMedicineSearchFormConfig(
  queryMedicineList: (data?: Partial<Medicine.MedicineListQueryParams>) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        extraProps: {
          clearable: true,
          className: 'w-52',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryMedicineList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}

export function useMedicineUpsertFormConfig(
  isAdd: Ref<boolean>,
  alreadyUseFlag: Ref<boolean>,
  cadnFormData: Ref<Partial<Cadn.CadnMedicineSpecItem>>,
  cadnMedicineSpecList: Ref<Cadn.CadnMedicineSpecItem[]>,
  producedOrgList: Ref<Org.Item[]>,
  getMedicineServiceList: (
    params?: Cadn.MedicineServiceListQueryParams,
  ) => void,
  getOrgList: (params?: Org.queryReqFlatPageParams) => void,
) {
  const data = useFormConfig<string[], ['cadnForm', 'medicineForm']>({
    getData: (t) => {
      return {
        cadnForm: [
          {
            label: t('medicine.cadnForm.cadnId', '通用名'),
            name: 'cadnId',
            // component: 'select',
            render: () => <CadnSelect alreadyUseFlag={alreadyUseFlag.value} />,
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.cadnId', '通用名'),
            }),
            triggerModelChange: true,
            extraProps: {
              options: cadnMedicineSpecList.value || [],
              clearable: false,
              disabled: !isAdd.value,
              remote: true,
              filterable: true,
              remoteMethod: (keyWord: string) => {
                getMedicineServiceList({
                  keyWord,
                });
              },
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.cadnId', '通用名'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
            name: 'medicineTypeCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.medicineTypeCode || '',
                  label: cadnFormData.value?.medicineTypeDesc || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.dosageFormCode', '剂型'),
            name: 'dosageFormCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.dosageFormCode', '剂型'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.dosageFormCode || '',
                  label: cadnFormData.value?.dosageFormDesc || '',
                },
              ],
            },
          },
          {
            label: t('medicine.cadnForm.pharmacologyClassCode', '药理分类'),
            name: 'pharmacologyClassCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.pharmacologyClassCode', '药理分类'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.pharmacologyClassCode || '',
                  label: cadnFormData.value?.pharmacologyClassDesc || '',
                },
              ],
            },
          },
          {
            label: t(
              'medicine.cadnForm.specialManageMedicineCode',
              '精麻毒分类',
            ),
            name: 'specialManageMedicineCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnForm.specialManageMedicineCode',
                '精麻毒分类',
              ),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.specialManageMedicineCode || '',
                  label: cadnFormData.value?.specialManageMedicineDesc || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t(
                    'medicine.cadnForm.specialManageMedicineCode',
                    '精麻毒分类',
                  ),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.medicineSpec', '基本规格'),
            name: 'medicineSpec',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.cadnForm.medicineSpec', '基本规格'),
            }),
            extraProps: {
              disabled: true,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.cadnForm.medicineSpec', '基本规格'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.medicineForm.doseFactor', '规格剂量'),
            name: 'doseFactor',
            // component: 'input',
            extraProps: {
              type: 'number',
              disabled: true,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.cadnForm.doseFactor', '规格剂量'),
                }),
                trigger: 'change',
              },
            ],
            render: () => (
              <el-input
                v-slots={{
                  append: () => {
                    return (
                      <el-select
                        v-model={cadnFormData.value.doseUnitCode}
                        style={{ width: '70px' }}
                        placeholder={''}
                        disabled={true}
                      >
                        <el-option
                          label={cadnFormData.value.doseUnitDesc || ''}
                          value={cadnFormData.value.doseUnitCode || ''}
                        />
                      </el-select>
                    );
                  },
                }}
              ></el-input>
            ),
          },
          {
            label: t('medicine.cadnForm.miniUnitId', '最小单位'),
            name: 'miniUnitId',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.miniUnitId', '最小单位'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  label: cadnFormData.value?.miniUnitName || '',
                  value: cadnFormData.value?.miniUnitId || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.miniUnitId', '最小单位'),
                }),
                trigger: 'change',
              },
            ],
          },
        ],
        medicineForm: [
          {
            label: t('medicine.medicineForm.commodityNo', '药品编码'),
            name: 'commodityNo',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commodityNo', '药品编码'),
            }),
            extraProps: {
              disabled: !!alreadyUseFlag.value,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commodityNo', '药品编码'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.commodityName', '药品名称'),
            name: 'commodityName',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commodityName', '药品名称'),
            }),
            autoConvertSpellNoAndWbNo: true,
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commodityName', '药品名称'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            name: 'commodity2ndName',
            label: t('global:secondName'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:secondName'),
            }),
          },
          {
            name: 'commodityExtName',
            label: t('global:thirdName'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:thirdName'),
            }),
          },
          {
            name: 'spellNo',
            label: t('global:spellNo'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
          },
          {
            name: 'wbNo',
            label: t('global:wbNo'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
          },
          {
            label: t('medicine.medicineForm.commoditySpec', '药品规格'),
            name: 'commoditySpec',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commoditySpec', '药品规格'),
            }),
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commoditySpec', '药品规格'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
            name: 'producedByOrgId',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
            }),
            extraProps: {
              options: producedOrgList.value.map((item) => ({
                label: item.orgName,
                value: item.orgId,
              })),
              disabled: !!alreadyUseFlag.value,
              clearable: false,
              remote: true,
              filterable: true,
              remoteMethod: (keyWord: string) => {
                getOrgList({
                  keyWord,
                });
              },
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.approvalNo', '批准文号'),
            name: 'approvalNo',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.approvalNo', '批准文号'),
            }),
          },
          {
            label: t('medicine.medicineForm.validPeriod', '有效期'),
            name: 'validPeriod',
            // component: 'input',
            extraProps: {
              type: 'number',
              min: 1,
              placeholder: t('global:placeholder.input.template', {
                content: t('medicine.medicineForm.validPeriod', '有效期'),
              }),
            },
            render: () => (
              <el-input
                v-slots={{
                  append: () => (
                    <span>{t('medicine.medicineForm.month', '月')}</span>
                  ),
                }}
              ></el-input>
            ),
          },
          {
            label: t('medicine.medicineForm.memo', '备注'),
            name: 'memo',
            span: 2,
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.memo', '备注'),
            }),
          },
        ],
      };
    },
  });
  return data;
}

export function useHospitalMedicineFormConfig(
  alreadyUseFlag: Ref<boolean>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t(
          'medicine.hospitalMedicineForm.commodityCategoryId',
          '费用分类',
        ),
        name: 'commodityCategoryId',
        component: 'select',
        extraProps: {
          clearable: false,
          filterable: true,
          options: optionsMap.value.commodityCategory || [],
        },
        triggerModelChange: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.commodityCategoryId',
                '费用分类',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.outCommodityCategoryId',
          '门诊发票分类',
        ),
        name: 'outCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.outCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.inCommodityCategoryId',
          '住院发票分类',
        ),
        name: 'inCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.inCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.accCommodityCategoryId',
          '会计分类',
        ),
        name: 'accCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.accCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.fncCommodityCategoryId',
          '财务分类',
        ),
        name: 'fncCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.fncCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.mrCommodityCategoryId',
          '病案分类',
        ),
        name: 'mrCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.mrCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.encounterTypeCodes',
          '使用范围',
        ),
        name: 'encounterTypeCodes',
        component: 'select',
        extraProps: {
          multiple: true,
          options: optionsMap.value.encounterTypeOptions || [],
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.encounterTypeCodes',
                '使用范围',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.commodityPurchasePrice',
          '参考进价',
        ),
        name: 'commodityPurchasePrice',
        render: () => {
          return (
            <el-input-number
              precision={4}
              controls={false}
              v-slots={{
                suffix: () => (
                  <span>
                    {t('medicine.hospitalMedicineForm.priceUnit', '元')}
                  </span>
                ),
              }}
            ></el-input-number>
          );
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.hospitalMedicineForm.commodityPurchasePrice',
                '参考进价',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.hospitalMedicineForm.price', '销售单价'),
        name: 'price',
        render: () => {
          return (
            <el-input-number
              precision={4}
              controls={false}
              disabled={!!alreadyUseFlag.value}
              v-slots={{
                suffix: () => (
                  <span>
                    {t('medicine.hospitalMedicineForm.priceUnit', '元')}
                  </span>
                ),
              }}
            ></el-input-number>
          );
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.hospitalMedicineForm.price', '销售单价'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}

export function useCadnFormConfig(
  alreadyUseFlag: Ref<boolean>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  getPharmacologyClassList: (data: { keyWord?: string }) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('medicine.cadn.cadn', '通用名称'),
        name: 'cadn',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('medicine.cadn.cadn', '通用名称'),
        }),
        autoConvertSpellNoAndWbNo: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.cadn.cadn', '通用名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.cadn.cadnEng', '英文名称'),
        name: 'cadnEng',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('medicine.cadn.cadnEng', '英文名称'),
        }),
      },
      {
        name: 'commodity2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'commodityExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        label: t('medicine.cadn.medicineTypeCode', '药品类型'),
        name: 'medicineTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.medicineTypeCode', '药品类型'),
        }),
        extraProps: {
          options: optionsMap.value.medicineTypeOptions || [],
          clearable: false,
          disabled: !!alreadyUseFlag.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadn.medicineTypeCode', '药品类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.cadn.dosageFormCode', '剂型'),
        name: 'dosageFormCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.dosageFormCode', '剂型'),
        }),
        extraProps: {
          options: optionsMap.value.cvOptions || [],
        },
      },
      {
        label: t('medicine.cadn.pharmacologyClassCode', '药理分类'),
        name: 'pharmacologyClassCode',
        component: 'tree-select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.pharmacologyClassCode', '药理分类'),
        }),
        extraProps: {
          props: {
            children: 'children',
            label: 'label',
            value: 'value',
          },
          'check-strictly': true,
          'default-expand-all': true,
          data: optionsMap.value.pharmacologyClassOptions || [],
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            getPharmacologyClassList({
              keyWord,
            });
          },
        },
      },
      {
        label: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
        name: 'specialManageMedicineCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
        }),
        extraProps: {
          options: optionsMap.value.specialManageMedicineOptions || [],
          clearable: false,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}

export function useCadnMedicineSpecFormConfig(
  alreadyUseFlag: boolean | undefined,
  medicineSpecItem: Ref<
    Partial<Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }>
  >,
  unitOptions: Ref<SelectOptions[]>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  getUnitList: (data?: Unit.QueryParams) => void,
  handleChange: () => void,
) {
  const filterKeyWord = ref('');
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
        name: 'miniUnitId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: filterKeyWord.value
            ? unitOptions.value
            : optionsMap.value?.unitOptions || [],
          disabled: !!alreadyUseFlag,
          clearable: false,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            filterKeyWord.value = keyWord;
            getUnitList({
              keyWord,
              pageNumber: 1,
              pageSize: 100,
            });
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('medicine.cadnMedicineSpecForm.doseFactor', '剂量'),
        name: 'doseFactor',
        // component: 'input',
        extraProps: {
          type: 'number',
          min: 0,
          disabled: !!alreadyUseFlag,
        },
        triggerModelChange: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.cadnForm.doseFactor', '剂量'),
            }),
            trigger: 'change',
          },
        ],
        render: () => (
          <el-input
            v-slots={{
              append: () => {
                return (
                  <el-select
                    v-model={medicineSpecItem.value.doseUnitCode}
                    style={{ width: '70px' }}
                    disabled={!!alreadyUseFlag}
                    placeholder={''}
                    onChange={(val: string) => {
                      const item = optionsMap.value.doseUnitOptions?.find(
                        (item) => item.value === val,
                      );
                      medicineSpecItem.value.doseUnitDesc =
                        item?.label as string;
                      medicineSpecItem.value.medicineSpec = `${medicineSpecItem.value.doseFactor || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
                      handleChange();
                    }}
                  >
                    {(optionsMap.value.doseUnitOptions || []).map((item) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ))}
                  </el-select>
                );
              },
            }}
          ></el-input>
        ),
      },
      {
        label: t('medicine.cadnMedicineSpecForm.medicineSpec', '基本规格'),
        name: 'medicineSpec',
        component: 'input',
        extraProps: {
          disabled: true,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.cadnMedicineSpecForm.medicineSpec',
                '基本规格',
              ),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}
