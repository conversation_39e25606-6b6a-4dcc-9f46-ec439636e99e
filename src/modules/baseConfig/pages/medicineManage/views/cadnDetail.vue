<script setup lang="ts" name="cadnDetail">
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { Plus } from '@element-sun/icons-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ref, watch, computed, nextTick, onMounted } from 'vue';
  import {
    ENABLED_FLAG,
    CV08_50_002_NAME,
    DOSE_UNIT_CODE_NAME,
    MEDICINE_TYPE_CODE_NAME,
    SPECIAL_MANAGE_MEDICINE_CODE_NAME,
  } from '@/utils/constant';
  import { Title, ProForm, useFetchDataset } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { generateUUID } from '@sun-toolkit/shared';
  import { useUnit } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import {
    queryCadnListByExample,
    addCadn,
    updateCadnById,
  } from '@/modules/baseConfig/api/cadn';
  import { queryPharmacologyClassListByExample } from '@/modules/baseConfig/api/code';
  import { useCadnFormConfig } from '@/modules/baseConfig/pages/medicineManage/config/useFormConfig';
  import CadnUnitTab from '@modules/baseConfig/pages/medicineManage/components/CadnUnitTab.vue';

  type CadnUpsertInfo = Partial<Cadn.CadnListItem>;
  type MedicineSpecItem = {
    medicineSpecId?: string;
    miniUnitId?: string;
    miniUnitName?: string;
    doseFactor?: number;
    doseUnitCode?: string;
    doseUnitDesc?: string;
    enabledFlag?: number;
    key?: string;
    medicineSpec?: string;
    medicineSpecDosageUnitList?: {
      medicineSpecDosageUnitId?: string;
      doseUnitCode?: string;
      doseUnitDesc?: string;
      convertFactor?: number;
      enabledFlag?: number;
      editable?: boolean;
      key?: string;
      isDefault?: boolean;
    }[];
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const submitLoading = ref(false);
  const cadnId = computed(() => route?.params?.id as string);
  const isAdd = computed(() => cadnId.value === 'add'); // 是否是新增
  const alreadyUseFlag = computed(() => !!route.query.alreadyUseFlag); // 是否是新增

  const { unitOptions, getUnitList } = useUnit(); // 计价单位 options
  const editCadnInfo = ref<Cadn.CadnListItem>(); // 编辑-通用名对象信息

  const cadnFormRef = ref();
  const cadnForm = ref<CadnUpsertInfo>();
  const medicineSpecList = ref<MedicineSpecItem[]>([]);
  const cadnUnitTabRef = ref();
  const currentMedicineSpecTab = ref('');

  const optionsMap = ref<{
    [key: string]: SelectOptions[];
  }>({}); // 选择项数据源

  const dataSetList = useFetchDataset([
    MEDICINE_TYPE_CODE_NAME,
    CV08_50_002_NAME,
    SPECIAL_MANAGE_MEDICINE_CODE_NAME,
    DOSE_UNIT_CODE_NAME,
  ]);

  watch(
    () => dataSetList.value,
    () => {
      // 药品类型
      optionsMap.value.medicineTypeOptions =
        dataSetList.value?.[MEDICINE_TYPE_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 药物剂型
      optionsMap.value.cvOptions =
        dataSetList.value?.[CV08_50_002_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 特殊管理药物(精麻毒放)
      optionsMap.value.specialManageMedicineOptions =
        dataSetList.value?.[SPECIAL_MANAGE_MEDICINE_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 剂量单位
      optionsMap.value.doseUnitOptions =
        dataSetList.value?.[DOSE_UNIT_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      nextTick(() => {
        initData();
      });
    },
  );

  const getPharmacologyClassList = async (params?: { keyWord?: string }) => {
    const [, res] = await queryPharmacologyClassListByExample(params || {});
    if (res?.data) {
      optionsMap.value.pharmacologyClassOptions = res.data.map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
        children: item?.dataValueSubList
          ? item?.dataValueSubList.map((child) => ({
              value: child?.dataValueNo,
              label: child?.dataValueCnName,
            }))
          : undefined,
      }));
    }
  };

  const goBack = () => {
    router.back();
  };

  const checkTabBeforeLeave = async (
    activeName?: string,
    oldActiveName?: string,
  ) => {
    if (!oldActiveName) {
      return true;
    }
    let formRef;
    if (cadnUnitTabRef.value?.length > 1) {
      const oldIndex = medicineSpecList.value.findIndex(
        (item) => item.key === oldActiveName,
      );
      if (oldIndex >= 0) {
        formRef = cadnUnitTabRef.value[oldIndex]?.formRef;
      }
    } else {
      formRef = cadnUnitTabRef.value[0]?.formRef;
    }
    if (formRef) {
      const isValid = await formRef?.ref?.validate();
      if (!isValid) {
        ElMessage.warning(
          t(
            'medicineManage.cadnDetail.errorTip.formValidateWaring',
            '请正确录入规格表单信息',
          ),
        );
        return false;
      }
    }
    let isEditing = false;
    const medicineSpecListItem = medicineSpecList.value.find(
      (item) => item.key === oldActiveName,
    );
    isEditing = !!medicineSpecListItem?.medicineSpecDosageUnitList?.some(
      (item) => item.editable === true,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'medicineManage.cadnDetail.errorTip.listIsEditing',
          '列表存在编辑状态中的信息，请先确定！',
        ),
      );
      return false;
    }
    return true;
  };

  const checkAllTab = async () => {
    if (!cadnUnitTabRef.value?.length) {
      return true;
    }
    for (const itemRef of cadnUnitTabRef.value) {
      const { formRef } = itemRef;
      const isValid = await formRef?.ref?.validate();
      if (!isValid) {
        ElMessage.warning(
          t(
            'medicineManage.cadnDetail.errorTip.formValidateWaring',
            '请正确录入规格表单信息',
          ),
        );
        return false;
      }
    }
    const isEditing = !!medicineSpecList.value.some((h) =>
      h.medicineSpecDosageUnitList?.some((m) => m.editable === true),
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'medicineManage.cadnDetail.errorTip.listIsEditing',
          '列表存在编辑状态中的信息，请先确定！',
        ),
      );
      return false;
    }
    return true;
  };

  const handleAddMedicineSpec = (isDefault?: boolean) => {
    const key = generateUUID();
    const item: MedicineSpecItem = {
      key,
      enabledFlag: ENABLED_FLAG.YES,
      miniUnitId: unitOptions.value[0]?.value as string,
      miniUnitName: unitOptions.value[0]?.label,
      doseUnitCode: optionsMap.value.doseUnitOptions[0]?.value as string,
      doseUnitDesc: optionsMap.value.doseUnitOptions[0]?.label,
      medicineSpecDosageUnitList: [
        {
          key,
          isDefault,
          enabledFlag: ENABLED_FLAG.YES,
          doseUnitCode: optionsMap.value.doseUnitOptions[0]?.value as string,
          doseUnitDesc: optionsMap.value.doseUnitOptions[0]?.label,
        },
      ],
    };
    item.medicineSpec = `${item.doseFactor || ''}${item.doseUnitDesc || ''}/${item.miniUnitName || ''}`;
    medicineSpecList.value.push(item);
    currentMedicineSpecTab.value = key;
  };

  const initData = async () => {
    await getUnitList({ pageSize: 200, pageNumber: 1 });
    optionsMap.value = { ...optionsMap.value, unitOptions: unitOptions.value };
    if (isAdd.value) {
      cadnForm.value = {};
      medicineSpecList.value = [];
      handleAddMedicineSpec(true);
    } else {
      const [, res] = await queryCadnListByExample({
        cadnIds: [cadnId.value],
      });
      if (res?.success && res.data.length) {
        editCadnInfo.value = res.data[0];
        cadnForm.value = res.data[0];
        medicineSpecList.value = (res.data[0].medicineSpecList || []).map(
          (item) => ({
            ...item,
            key: item.medicineSpecId,
            medicineSpec: `${item.doseFactor || ''}${item.doseUnitDesc || ''}/${item.miniUnitName || ''}`,
          }),
        );
      }
    }
    currentMedicineSpecTab.value = medicineSpecList.value[0]?.key || '';
  };

  const onMedicineSpecChange = (data: MedicineSpecItem) => {
    if (data.medicineSpec && data.medicineSpecDosageUnitList?.[0]?.isDefault) {
      data.medicineSpecDosageUnitList[0] = {
        ...data.medicineSpecDosageUnitList[0],
        doseUnitCode: data.doseUnitCode,
        doseUnitDesc: data.doseUnitDesc,
        convertFactor: data.doseFactor,
      };
    }
    const index = medicineSpecList.value.findIndex(
      (item) => item.key === data.key,
    );
    medicineSpecList.value.splice(index, 1, data);
  };

  const handleSubmit = async () => {
    if (!cadnForm.value || !currentMedicineSpecTab.value) return;
    const cadnFormValid = await cadnFormRef.value.ref.validate();
    if (!cadnFormValid) return;

    const canSave = await checkAllTab();
    if (!canSave) {
      return;
    }
    const medicineSpecListFormat = medicineSpecList.value.map((item, index) => {
      const { medicineSpecItem, medicineSpecDosageUnitList } =
        cadnUnitTabRef.value[index];
      return {
        ...item,
        ...medicineSpecItem,
        medicineSpecDosageUnitList,
      };
    });

    const params = {
      cadnId: isAdd.value ? undefined : cadnId.value,
      ...cadnForm.value,
      medicineSpecList: medicineSpecListFormat,
    };

    const [, res] = isAdd.value
      ? await addCadn(params)
      : await updateCadnById(params);
    if (res?.success) {
      goBack();
    }
  };

  const cadnFormConfig = useCadnFormConfig(
    alreadyUseFlag,
    optionsMap,
    getPharmacologyClassList,
  );

  onMounted(async () => {
    getPharmacologyClassList();
  });
</script>

<template>
  <div class="p-box size-full pr-0">
    <el-page-header @back="goBack">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('medicineManage.addCadn', '新增通用名')
              : `${$t('global:edit', '编辑')}-${editCadnInfo?.cadn}`
          }}
        </span>
      </template>
    </el-page-header>
    <div class="my-2 h-full overflow-auto" style="height: calc(100% - 5rem)">
      <el-scrollbar view-class="mr-2.5 mb-4">
        <Title
          :title="$t('medicineManage.cadnDetail.cadnInfo', '通用名信息')"
          class="my-3"
        />
        <ProForm ref="cadnFormRef" v-model="cadnForm" :data="cadnFormConfig" />
        <Title
          :title="$t('medicineManage.cadnDetail.medicineSpecList', '可用规格')"
          class="my-3"
        />
        <el-tabs
          v-model="currentMedicineSpecTab"
          class="h-full"
          :before-leave="checkTabBeforeLeave"
        >
          <el-tab-pane
            v-for="item in medicineSpecList"
            :key="item.key"
            :name="item.key"
            :label="item.medicineSpec"
            class="h-full"
          >
            <template #default>
              <CadnUnitTab
                class="h-full"
                ref="cadnUnitTabRef"
                :already-use-flag="alreadyUseFlag"
                :data="item"
                :select-options-map="optionsMap"
                @change="onMedicineSpecChange"
              />
            </template>
          </el-tab-pane>
          <el-tab-pane>
            <template #label>
              <el-icon
                @click.stop="() => handleAddMedicineSpec()"
                class="h-full"
                size="20"
              >
                <Plus />
              </el-icon>
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
    <div class="mr-2.5 text-right">
      <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
