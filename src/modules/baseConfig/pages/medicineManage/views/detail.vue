<script setup lang="ts" name="medicineDetail">
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { Plus } from '@element-sun/icons-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ref, watch, computed, nextTick, onMounted } from 'vue';
  import {
    FLAG,
    ENABLED_FLAG,
    ORG_TYPE_CODE,
    ENCOUNTER_TYPE_CODE_NAME,
    MEDICINE_USE_SCENE_CODE_NAME,
  } from '@/utils/constant';
  import {
    Title,
    ProForm,
    ProTable,
    MAIN_APP_CONFIG,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import {
    addMedicine,
    updateMedicineById,
    queryMedicineListByExample,
  } from '@/modules/baseConfig/api/medicine';
  import { SelectOptions } from '@/typings/common';
  import { generateUUID } from '@sun-toolkit/shared';
  import { queryOrgListByExampleFlat } from '@modules/system/api/org';
  import { useUnit } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import { queryCadnAndMedicineSpecListByExample } from '@/modules/baseConfig/api/cadn';
  import { useMedicineUpsertFormConfig } from '@/modules/baseConfig/pages/medicineManage/config/useFormConfig';
  import { useMedicinePackUnitTableConfig } from '@/modules/baseConfig/pages/medicineManage/config/useTableConfig';
  import HospitalMedicineTab from '@modules/baseConfig/pages/medicineManage/components/HospitalMedicineTab.vue';

  type MedicineUpsertInfo = Partial<Medicine.MedicineListItem>;
  type MedicinePackUnitTableRow = Partial<Medicine.MedicinePackUnitItem> & {
    isUnit?: boolean;
    editable: boolean;
  };
  type HospitalMedicineItem = Partial<Medicine.HospitalMedicineItem>;

  const { currentOrg, hospitalList } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.HOSPITAL_LIST,
  ]);

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const submitLoading = ref(false);
  const commodityId = computed(() => route?.params?.id as string);
  const isAdd = computed(() => commodityId.value === 'add'); // 是否是新增

  const cadnMedicineSpecList = ref<Cadn.CadnMedicineSpecItem[]>([]); // 通用名，药品服务列表
  const { unitOptions, getUnitList } = useUnit(); // 计价单位 options
  const producedOrgList = ref<Org.Item[]>([]); // 生产厂家
  const editMedicineInfo = ref<Medicine.MedicineListItem>(); // 编辑-药品对象信息

  const currentHospitalId = ref('');
  const originalHospitalSelectList = ref<Org.Item[]>([]);
  const hospitalSelectList = ref<Org.Item[]>([]);

  const cadnForm = ref<Partial<Cadn.CadnMedicineSpecItem>>({}); // 所选通用名对象信息
  const medicineInfoForm = ref<MedicineUpsertInfo>({}); // 药品信息表单
  const medicinePackUnitList = ref<MedicinePackUnitTableRow[]>([]);
  const hospitalMedicineList = ref<HospitalMedicineItem[]>([]);
  const cadnFormRef = ref();
  const medicineInfoFormRef = ref();
  const medicinePackUnitTableRef = ref();
  const hospitalMedicineTabRef = ref();

  const hospitalMedicineOptionsMap = ref<{
    [key: string]: SelectOptions[];
  }>({}); // 医院药品表单和表格所需要的选择项数据源

  // 药品已使用
  const alreadyUseFlag = computed(
    () => editMedicineInfo.value?.alreadyUseFlag === FLAG.YES,
  );

  const dataSetList = useFetchDataset([
    ENCOUNTER_TYPE_CODE_NAME,
    MEDICINE_USE_SCENE_CODE_NAME,
  ]);

  watch(
    () => dataSetList.value,
    () => {
      // 使用范围 === 就诊类型
      hospitalMedicineOptionsMap.value.encounterTypeOptions =
        dataSetList.value?.[ENCOUNTER_TYPE_CODE_NAME]?.map((item) => ({
          value: item?.dataValueNo,
          label: item?.dataValueCnName,
        })) || [];
      // 应用场景 === 药品应用场景
      hospitalMedicineOptionsMap.value.medicineUseSceneOptions =
        dataSetList.value?.[MEDICINE_USE_SCENE_CODE_NAME]?.map((item) => ({
          value: item?.dataValueNo,
          label: item?.dataValueCnName,
        })) || [];
      nextTick(() => {
        initData();
      });
    },
  );

  const getMedicineServiceList = async (
    params?: Cadn.MedicineServiceListQueryParams,
  ) => {
    const [, res] = await queryCadnAndMedicineSpecListByExample({
      pageSize: 100,
      pageNumber: 1,
      enabledFlag: ENABLED_FLAG.YES,
      ...params,
    });
    if (res?.data) {
      cadnMedicineSpecList.value = res.data;
    }
  };

  // 获取生产厂家列表
  const getOrgList = async (params?: Org.queryReqFlatPageParams) => {
    const [, res] = await queryOrgListByExampleFlat({
      pageSize: 100,
      pageNumber: 1,
      enabledFlag: ENABLED_FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.MANUFACTURER],
      ...params,
    });
    if (res?.data) {
      producedOrgList.value = res.data;
    }
  };

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  const handleCadnFormChange = (
    formData: Partial<Cadn.CadnMedicineSpecItem>,
    name?: string,
  ) => {
    switch (name) {
      case 'cadnId': {
        const cadn = formData.cadnId
          ? cadnMedicineSpecList.value.find(
              (item) => item.cadnId === formData.cadnId,
            )
          : null;
        nextTick(() => {
          cadnForm.value = cadn || {};
          medicineInfoForm.value.commodityName = cadn?.cadn;
        });
        break;
      }
      default:
        break;
    }
  };
  const checkTabBeforeLeave = async (
    activeName?: string,
    oldActiveName?: string,
  ) => {
    if (!oldActiveName) {
      return true;
    }
    let formRef;
    if (
      currentOrg?.orgTypeCode === ORG_TYPE_CODE.GROUP &&
      hospitalMedicineTabRef.value?.length > 1
    ) {
      const oldIndex = hospitalMedicineList.value.findIndex(
        (item) => item.hospitalId === oldActiveName,
      );
      if (oldIndex >= 0) {
        formRef =
          hospitalMedicineTabRef.value[oldIndex]?.hospitalMedicineFormRef;
      }
    } else {
      formRef = hospitalMedicineTabRef.value[0]?.hospitalMedicineFormRef;
    }
    if (formRef) {
      const isValid = await formRef?.ref?.validate();
      if (!isValid) {
        ElMessage.warning(
          t(
            'medicineManage.detail.errorTip.hospitalMedicineFormWaring',
            '请正确录入医院的药品表单信息',
          ),
        );
        return false;
      }
    }
    let isEditing = false;
    let hasEmptyPackUnit = false;
    const hospitalItem = hospitalMedicineList.value.find(
      (item) => item.hospitalId === oldActiveName,
    );
    isEditing = !!hospitalItem?.medicineUseSceneXUnitList?.some(
      (item) => item.editable === true,
    );
    hasEmptyPackUnit = !!hospitalItem?.medicineUseSceneXUnitList?.some(
      (item) => !item.packUnitId,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'medicineManage.detail.errorTip.medicineUseSceneXUnitListIsEditing',
          '应用场景对应的包装单位列表存在编辑状态中的信息，请先确定！',
        ),
      );
      return false;
    }
    if (hasEmptyPackUnit) {
      ElMessage.warning(
        t(
          'medicineManage.detail.errorTip.medicineUseSceneXUnitListHasEmptyPackUnit',
          '应用场景对应的包装单位不可为空！',
        ),
      );
      return false;
    }
    return true;
  };

  const checkAllTab = async () => {
    if (!hospitalMedicineTabRef.value?.length) {
      return true;
    }
    for (const itemRef of hospitalMedicineTabRef.value) {
      const { hospitalMedicineFormRef } = itemRef;
      const isValid = await hospitalMedicineFormRef?.ref?.validate();
      if (!isValid) {
        ElMessage.warning(
          t(
            'medicineManage.detail.errorTip.hospitalMedicineFormWaring',
            '请正确录入医院的药品表单信息',
          ),
        );
        return false;
      }
    }
    const isEditing = !!hospitalMedicineList.value.some((h) =>
      h.medicineUseSceneXUnitList?.some((m) => m.editable === true),
    );
    const hasEmptyPackUnit = !!hospitalMedicineList.value.some((h) =>
      h.medicineUseSceneXUnitList?.some((m) => !m.packUnitId),
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'medicineManage.detail.errorTip.medicineUseSceneXUnitListIsEditing',
          '应用场景对应的包装单位列表存在编辑状态中的信息，请先确定！',
        ),
      );
      return false;
    }
    if (hasEmptyPackUnit) {
      ElMessage.warning(
        t(
          'medicineManage.detail.errorTip.medicineUseSceneXUnitListHasEmptyPackUnit',
          '应用场景对应的包装单位不可为空！',
        ),
      );
      return false;
    }
    return true;
  };

  const onHospitalMedicineChange = (data: HospitalMedicineItem) => {
    const hospitalIndex = hospitalMedicineList.value.findIndex(
      (item) => item.hospitalId === data.hospitalId,
    );
    hospitalMedicineList.value.splice(hospitalIndex, 1, data);
  };

  const handleSelectHospital = async (item: Org.Item) => {
    const medicineUseSceneXUnitList = (
      hospitalMedicineOptionsMap.value.medicineUseSceneOptions || []
    ).map((item) => ({
      medicineUseSceneCode: item.value as string,
      medicineUseSceneDesc: item.label as string,
      medicineUseSceneXUnitId: '',
      key: generateUUID(),
      packUnitId: '',
      packUnitName: '',
    }));
    hospitalMedicineList.value.push({
      hospitalId: item.orgId,
      hospitalName: item.orgName,
      enabledFlag: ENABLED_FLAG.YES,
      medicineUseSceneXUnitList,
    });
    currentHospitalId.value = item.orgId || '';
  };

  const handleAddHospital = async () => {
    // 过滤掉已选择过的
    hospitalSelectList.value = originalHospitalSelectList.value?.filter(
      (itemA) =>
        !hospitalMedicineList.value?.some(
          (itemB) => itemB.hospitalId === itemA.orgId,
        ),
    ) as Org.Item[];
  };

  const initData = async () => {
    if (isAdd.value) {
      const medicineUseSceneXUnitList = (
        hospitalMedicineOptionsMap.value.medicineUseSceneOptions || []
      ).map((item) => ({
        medicineUseSceneCode: item.value as string,
        medicineUseSceneDesc: item.label as string,
        medicineUseSceneXUnitId: '',
        key: generateUUID(),
        packUnitId: '',
        packUnitName: '',
        editable: true,
      }));
      if (
        currentOrg?.orgTypeCode === ORG_TYPE_CODE.GROUP &&
        originalHospitalSelectList.value.length
      ) {
        hospitalMedicineList.value = [
          {
            hospitalId: originalHospitalSelectList.value[0].orgId,
            hospitalName: originalHospitalSelectList.value[0].orgName,
            enabledFlag: ENABLED_FLAG.YES,
            medicineUseSceneXUnitList,
          },
        ];
      } else {
        hospitalMedicineList.value = [
          {
            hospitalId: currentOrg?.orgId,
            hospitalName: currentOrg?.orgName,
            enabledFlag: ENABLED_FLAG.YES,
            medicineUseSceneXUnitList,
          },
        ];
      }
    } else {
      const [, res] = await queryMedicineListByExample({
        commodityId: commodityId.value as string,
        pageNumber: 1,
        pageSize: 1,
      });
      if (res?.success && res.data.length) {
        editMedicineInfo.value = res.data[0];
        const {
          cadnId,
          cadn,
          cadnExt,
          cadn2nd,
          cadnEng,
          medicineTypeCode,
          medicineTypeDesc,
          dosageFormCode,
          dosageFormDesc,
          pharmacologyClassCode,
          pharmacologyClassDesc,
          specialManageMedicineCode,
          specialManageMedicineDesc,
          medicineSpec,
          doseFactor,
          miniUnitId,
          miniUnitName,
          commodityNo,
          commodityName,
          commodity2ndName,
          commodityExtName,
          commoditySpec,
          spellNo,
          wbNo,
          producedByOrgId,
          approvalNo,
          validPeriod,
          memo,
          unitId,
        } = editMedicineInfo.value;
        cadnForm.value = {
          cadnId,
          cadn,
          cadnExt,
          cadn2nd,
          cadnEng,
          medicineTypeCode,
          medicineTypeDesc,
          dosageFormCode,
          dosageFormDesc,
          pharmacologyClassCode,
          pharmacologyClassDesc,
          specialManageMedicineCode,
          specialManageMedicineDesc,
          medicineSpec,
          doseFactor,
          miniUnitId,
          miniUnitName,
        };
        medicineInfoForm.value = {
          commodityNo,
          commodityName,
          commodity2ndName,
          commodityExtName,
          commoditySpec,
          spellNo,
          wbNo,
          producedByOrgId,
          approvalNo,
          validPeriod,
          memo,
        };
        medicinePackUnitList.value = (
          editMedicineInfo.value.medicinePackUnitList || []
        ).map((item) => ({
          ...item,
          editable: false,
          isUnit: unitId === item.packUnitId,
        }));

        if (editMedicineInfo.value.hospitalMedicineList?.length) {
          const list = editMedicineInfo.value.hospitalMedicineList;
          list.forEach((item) => {
            const unitList =
              hospitalMedicineOptionsMap.value.medicineUseSceneOptions.map(
                (optionItem) => {
                  const findItem = item.medicineUseSceneXUnitList.find(
                    (unitItem) =>
                      unitItem.medicineUseSceneCode === optionItem.value,
                  );
                  return {
                    medicineUseSceneCode: optionItem.value as string,
                    medicineUseSceneDesc: optionItem.label as string,
                    medicineUseSceneXUnitId: '',
                    key: generateUUID(),
                    packUnitId: '',
                    packUnitName: '',
                    ...(findItem || {}),
                  };
                },
              );
            item.medicineUseSceneXUnitList = unitList;
          });
          hospitalMedicineList.value = list;
        }
      }
    }
    currentHospitalId.value = hospitalMedicineList.value[0]?.hospitalId || '';
  };

  const handleSubmit = async () => {
    if (!cadnForm.value || !medicineInfoFormRef.value) return;
    const cadnFormValid = await cadnFormRef.value.ref.validate();
    const medicineInfoFormValid =
      await medicineInfoFormRef.value.ref.validate();
    if (!cadnFormValid || !medicineInfoFormValid) return;

    const canSave = await checkAllTab();
    if (!canSave) {
      return;
    }
    const hospitalMedicineListFormat = hospitalMedicineList.value.map(
      (item, index) => {
        const { hospitalMedicineItem, medicineUseSceneXUnitList } =
          hospitalMedicineTabRef.value[index];
        return {
          ...item,
          ...hospitalMedicineItem,
          medicineUseSceneXUnitList,
        };
      },
    );
    const unitId = medicinePackUnitList.value.find(
      (item) => !!item.isUnit,
    )?.packUnitId;
    const params: Medicine.MedicineUpsertParams = {
      commodityId: isAdd.value ? undefined : commodityId.value,
      medicineSpecId: cadnForm.value.medicineSpecId,
      ...medicineInfoForm.value,
      unitId,
      medicinePackUnitList: medicinePackUnitList.value.map((item) => ({
        ...item,
        editable: undefined,
        isUnit: undefined,
      })),
      hospitalMedicineList: hospitalMedicineListFormat,
    };

    const [, res] = isAdd.value
      ? await addMedicine(params)
      : await updateMedicineById(params);
    if (res?.success) {
      goBack();
    }
  };

  const handleDelMedicinePackUnit = (
    data: MedicinePackUnitTableRow,
    index: number,
  ) => {
    if (!data.packUnitId) {
      delItem(index);
      return;
    }
    const isUsed = hospitalMedicineList.value.some((h) =>
      h.medicineUseSceneXUnitList?.some(
        (m) => m.packUnitId === data.packUnitId,
      ),
    );
    if (isUsed) {
      ElMessage.warning(
        t(
          'medicineManage.detail.errorTip.delMedicinePackUnitWarning',
          '应用场景的单位必须在可用单位中',
        ),
      );
    } else {
      delItem(index);
    }
  };

  const handleIsUnitChange = (index: number) => {
    medicinePackUnitList.value = medicinePackUnitList.value.map((item, i) => ({
      ...item,
      isUnit: i === index,
    }));
  };

  const { medicinePackUnitTableConfig, addItem, delItem } =
    useMedicinePackUnitTableConfig(
      medicinePackUnitTableRef,
      medicinePackUnitList,
      alreadyUseFlag,
      unitOptions,
      getUnitList,
      handleDelMedicinePackUnit,
      handleIsUnitChange,
    );

  const addMedicinePackUnit = () => {
    addItem({
      editable: true,
    });
  };

  const config = useMedicineUpsertFormConfig(
    isAdd,
    alreadyUseFlag,
    cadnForm,
    cadnMedicineSpecList,
    producedOrgList,
    getMedicineServiceList,
    getOrgList,
  );

  onMounted(async () => {
    hospitalSelectList.value =
      currentOrg?.orgTypeCode === ORG_TYPE_CODE.GROUP
        ? ((hospitalList ?? []) as unknown as Org.Item[])
        : [currentOrg as unknown as Org.Item];
    originalHospitalSelectList.value = [...hospitalSelectList.value];
    getOrgList();
    getMedicineServiceList();
    getUnitList({ pageSize: 200, pageNumber: 1 });
  });
</script>

<template>
  <div class="p-box size-full pr-0">
    <el-page-header @back="goBack">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('medicineManage.addMedicine', '新增药品')
              : `${$t('global:edit', '编辑')}-${editMedicineInfo?.commodityName}`
          }}
        </span>
      </template>
    </el-page-header>
    <div class="my-2 h-full overflow-auto" style="height: calc(100% - 5rem)">
      <el-scrollbar view-class="mr-2.5 mb-4">
        <Title
          :title="$t('medicineManage.detail.cadn', '通用名')"
          class="my-3"
        />
        <ProForm
          ref="cadnFormRef"
          v-model="cadnForm"
          :data="config.cadnForm"
          @model-change="handleCadnFormChange"
        />

        <Title
          :title="$t('medicineManage.detail.medicineInfo', '药品信息')"
          class="my-3"
        />
        <ProForm
          ref="medicineInfoFormRef"
          v-model="medicineInfoForm"
          :data="config.medicineForm"
        />

        <Title
          :title="
            $t('medicineManage.detail.medicinePackUnitList', '可用的单位')
          "
          class="my-3"
        >
          <el-button type="primary" @click="addMedicinePackUnit">
            {{ $t('global:add') }}
          </el-button>
        </Title>
        <ProTable
          ref="medicinePackUnitTableRef"
          :columns="medicinePackUnitTableConfig"
          :editable="true"
          :data="medicinePackUnitList"
        />

        <Title
          :title="
            $t('medicineManage.detail.hospitalMedicineList', '医院的药品')
          "
          class="my-3"
        />
        <el-tabs
          v-if="hospitalMedicineList?.length"
          v-model="currentHospitalId"
          class="hospital-tabs h-full"
          :before-leave="checkTabBeforeLeave"
        >
          <el-tab-pane
            v-for="item in hospitalMedicineList"
            :key="item.hospitalId"
            :name="item.hospitalId"
            :label="item.hospitalName"
            class="h-full"
          >
            <template #default>
              <HospitalMedicineTab
                v-if="item.hospitalId"
                class="h-full"
                ref="hospitalMedicineTabRef"
                :edit-medicine-info="editMedicineInfo"
                :hospital-item="item"
                :hospital-medicine-options-map="hospitalMedicineOptionsMap"
                :medicine-pack-unit-list="medicinePackUnitList"
                @change="onHospitalMedicineChange"
              />
            </template>
          </el-tab-pane>
          <el-tab-pane v-if="currentOrg?.orgTypeCode === ORG_TYPE_CODE.GROUP">
            <template #label>
              <el-dropdown
                class="h-full"
                ref="dropdownRef"
                trigger="click"
                @command="handleSelectHospital"
              >
                <el-icon
                  @click.stop="handleAddHospital"
                  class="h-full"
                  size="20"
                >
                  <Plus />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="item in hospitalSelectList"
                      :key="item.orgId"
                      :command="item"
                    >
                      {{ item.orgName }}
                    </el-dropdown-item>
                    <div
                      v-if="hospitalSelectList.length === 0"
                      class="mx-2 py-1 text-base"
                      style="font-size: 0.8rem; color: #d5d7de"
                    >
                      {{ $t('notFound.hospital', '暂无可选医院') }}
                    </div>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
    <div class="mr-2.5 text-right">
      <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
