<script setup lang="ts" name="medicineManage">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { queryMedicineListByExample } from '@/modules/baseConfig/api/medicine';
  import { useMedicineSearchFormConfig } from '@/modules/baseConfig/pages/medicineManage/config/useFormConfig';
  import { useMedicineTableColumnConfig } from '@/modules/baseConfig/pages/medicineManage/config/useTableConfig';

  type MedicineTableItem = Medicine.MedicineListItem & {
    [key: string]: {
      hospitalId?: string;
      hospitalName?: string;
      commodityCategoryName?: string;
      commodityPurchasePrice?: number;
      price?: number;
      enabledFlag?: number;
    };
  };

  const router = useRouter();
  const searchParams = ref({
    hospitalId: '',
    enabledFlag: ENABLED_FLAG.ALL,
    keyWord: '',
    pageSize: 25,
    pageNumber: 1,
  });
  const loading = ref(false);
  const total = ref(0);
  const medicineList = ref<MedicineTableItem[]>([]);
  const medicineListHospitalList = ref<Medicine.HospitalMedicineItem[]>([]);

  const getFormatMedicineListData = (list: Medicine.MedicineListItem[]) => {
    if (!list?.length) {
      return [];
    }
    const hospitalMap = new Map();
    // hospitalMap.set('test123', {
    //   hospitalName: '测试医院123',
    //   hospitalId: 'test123',
    // });
    list.forEach((item) => {
      item.hospitalMedicineList.forEach((hospital) => {
        if (!hospitalMap.has(hospital.hospitalId)) {
          hospitalMap.set(hospital.hospitalId, {
            hospitalName: hospital.hospitalName,
            hospitalId: hospital.hospitalId,
          });
        }
      });
    });
    const allHospitals = Array.from(hospitalMap.values());
    medicineListHospitalList.value = allHospitals;
    if (!allHospitals.length) {
      return list as MedicineTableItem[];
    }
    const formattedList: MedicineTableItem[] = list.map((item) => {
      const currentMap = new Map(
        item.hospitalMedicineList.map((h) => [h.hospitalId, h]),
      );
      const fullHospitalList = allHospitals.map((hospital) => {
        return currentMap.has(hospital.hospitalId)
          ? (currentMap.get(
              hospital.hospitalId,
            ) as Medicine.HospitalMedicineItem)
          : ({
              hospitalId: hospital.hospitalId,
              hospitalName: hospital.hospitalName,
            } as Medicine.HospitalMedicineItem);
      });
      const tableItem = {
        ...item,
        hospitalMedicineList: fullHospitalList,
      } as MedicineTableItem;
      tableItem.hospitalMedicineList.forEach(
        (hospitalMedicineListItem, index) => {
          tableItem[`hospital${index}`] = {
            hospitalId: hospitalMedicineListItem.hospitalId,
            hospitalName: hospitalMedicineListItem.hospitalName,
            commodityCategoryName:
              hospitalMedicineListItem.commodityCategoryName,
            commodityPurchasePrice:
              hospitalMedicineListItem.commodityPurchasePrice,
            price: hospitalMedicineListItem.price,
            enabledFlag: hospitalMedicineListItem.enabledFlag,
          };
        },
      );
      return tableItem;
    });
    return formattedList;
  };

  const queryMedicineList = async (
    data?: Partial<Medicine.MedicineListQueryParams>,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryMedicineListByExample(params);
    if (res?.success) {
      medicineList.value = getFormatMedicineListData(res.data);
      total.value = res.total;
    }
    loading.value = false;
  };

  const handleEdit = (data: MedicineTableItem) => {
    router.push({
      name: 'detail',
      params: {
        id: data.commodityId,
      },
    });
  };

  const searchConfig = useMedicineSearchFormConfig(queryMedicineList);
  const medicineTableColumns = useMedicineTableColumnConfig(
    medicineListHospitalList,
    handleEdit,
  );
</script>

<template>
  <div class="flex h-full flex-col">
    <Title :title="$t('medicineManage.list.title', '药品列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item flex-1">
        <ProForm
          :data="searchConfig"
          layout-mode="inline"
          @model-change="queryMedicineList"
        />
      </div>
      <el-button
        type="primary"
        @click="
          () => {
            router.push('/detail/add');
          }
        "
      >
        {{ $t('global:add') }}
      </el-button>
    </div>
    <pro-table
      class="medicine-list-table"
      row-key="commodityId"
      :data="medicineList"
      :page-info="{
        total: total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      :loading="loading"
      :columns="medicineTableColumns"
      @current-page-change="
        (val: number) => {
          queryMedicineList({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          queryMedicineList({ pageSize: val, pageNumber: 1 });
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
  .medicine-list-table {
    :deep(.el-table__header-wrapper) {
      .is-group tr:nth-of-type(2) {
        display: none;
      }
    }
  }
</style>
