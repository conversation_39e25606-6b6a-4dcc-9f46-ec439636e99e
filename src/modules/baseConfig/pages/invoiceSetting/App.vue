<script lang="ts" name="invoiceSetting" setup>
  import {
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { onMounted, ref } from 'vue';
  import { useInvoiceFormConfig } from './config/useFormConfig.tsx';
  import { useInvoiceTableConfig } from './config/useTableConfig.tsx';
  import { queryDataSetListByExample } from '@/modules/baseConfig/api/code';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import {
    queryInvoiceSettingList,
    upsertInvoiceSettingData,
  } from '@/modules/baseConfig/api/invoiceSetting.ts';

  const invoiceSettingTableRef = ref();

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const invoiceMediaList = ref<{ value: string; label: string }[]>([]);
  const invoiceUsageList = ref<{ value: string; label: string }[]>([]);
  let queryInvoiceSettingListParams =
    ref<InvoiceSetting.QueryInvoiceSettingListParams>(); // 查询参数
  const invoiceList = ref<InvoiceSetting.InvoiceSettingInfoItem[]>([]); // 表格数据
  const loading = ref(false);

  // 查询票据配置列表
  const queryInvoiceSettingListData = async (
    params: InvoiceSetting.QueryInvoiceSettingListParams = {},
  ) => {
    loading.value = true;
    queryInvoiceSettingListParams.value = {
      ...queryInvoiceSettingListParams.value,
      ...params,
    };
    let [, res] = await queryInvoiceSettingList({
      ...queryInvoiceSettingListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      invoiceList.value = res.data || [];
    }
  };

  // 获取场景列表
  async function fetchDataSetList(codeSystemNo: string) {
    let [, result] = await queryDataSetListByExample({
      codeSystemNos: [codeSystemNo],
      hospitalId: currentOrg?.orgId || '',
      pageNumber: 0,
    });
    if (result?.success) {
      let { data } = result;
      if (codeSystemNo === 'INVOICE_MEDIA_TYPE_CODE') {
        invoiceMediaList.value = data
          .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
          .map((item) => ({
            value: item.dataValueNo,
            label: item.dataValueCnName,
          }));
      } else {
        invoiceUsageList.value = data
          .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
          .map((item) => ({
            value: item.dataValueNo,
            label: item.dataValueCnName,
          }));
      }
    }
  }

  // 业务场景改变
  const changeSelect = async (data?: InvoiceSetting.ProFormParams) => {
    queryInvoiceSettingListParams.value = {
      ...queryInvoiceSettingListParams.value,
      ...data,
    };
    await queryInvoiceSettingListData();
  };

  const upsertTable = async (
    row: InvoiceSetting.UpsertInvoiceSettingParams,
    index: number,
  ) => {
    const isValid = await invoiceSettingTableRef?.value?.validateRow(index);
    if (!isValid) return;
    const [, res] = await upsertInvoiceSettingData({ ...row });
    if (res?.success) {
      await queryInvoiceSettingListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };

  const searchConfig = useInvoiceFormConfig(invoiceMediaList, invoiceUsageList);
  const { tableColumns } = useInvoiceTableConfig({
    id: 'invoiceSettingId',
    tableRef: invoiceSettingTableRef,
    data: invoiceList,
    upsertTable: upsertTable,
  });
  onMounted(async () => {
    await fetchDataSetList('INVOICE_MEDIA_TYPE_CODE'); // 根据条件查询值域列表
    await fetchDataSetList('INVOICE_USAGE_CODE'); // 根据条件查询值域列表
    await queryInvoiceSettingListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('invoiceSetting.list.title', '票据配置')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryInvoiceSettingListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
      </div>
      <el-button
        class="mr-2"
        icon="search"
        type="primary"
        @click="queryInvoiceSettingListData"
      >
        {{ $t('invoice.search', '查询') }}
      </el-button>
    </div>
    <ProTable
      ref="invoiceSettingTableRef"
      :columns="tableColumns"
      :data="invoiceList"
      :editable="true"
      :loading="loading"
      row-key="invoiceSettingId"
    />
  </div>
</template>
