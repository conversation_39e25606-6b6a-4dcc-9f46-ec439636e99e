import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { SelectOptions } from '@/typings/common.ts';

export function useInvoiceFormConfig(
  invoiceMediaList: Ref<SelectOptions[]>,
  invoiceUsageList: Ref<SelectOptions[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'invoiceMediaTypeCode',
        label: t('invoiceSetting.invoiceType', '票据类型'),
        component: 'select',
        placeholder: t('invoiceSetting.invoiceType', '请选择票据类型'),
        triggerModelChange: true,
        extraProps: {
          filterable: true,
          options: invoiceMediaList.value,
          className: 'w-80',
        },
      },
      {
        name: 'invoiceUsageCodes',
        label: t('invoiceSetting.invoiceUsageCodes', '票据用途'),
        component: 'select',
        placeholder: t(
          'invoiceSetting.invoiceUsageCodesTips',
          '请选择票据用途',
        ),
        triggerModelChange: true,
        extraProps: {
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          multiple: true,
          filterable: true,
          options: invoiceUsageList.value,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}
