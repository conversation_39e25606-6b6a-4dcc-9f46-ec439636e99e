import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';

export function useInvoiceTableConfig(options: {
  id: string;
  data: Ref<InvoiceSetting.InvoiceSettingInfoItem[]>;
  tableRef: Ref<TableRef>;
  upsertTable: (
    row: InvoiceSetting.InvoiceSettingInfoItem,
    index: number,
  ) => Promise<void>;
}) {
  const { id, data, tableRef, upsertTable } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (InvoiceSetting.InvoiceSettingInfoItem & { editable: boolean })[]
    >,
    id,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t(
          'invoiceSetting.invoiceSettingTable.invoiceSettingId',
          '配置标识',
        ),
        prop: 'invoiceSettingId',
        minWidth: 150,
      },
      {
        label: t(
          'invoiceSetting.invoiceSettingTable.invoiceMediaTypeCode',
          '类型',
        ),
        prop: 'invoiceMediaTypeCode',
        minWidth: 180,
        render: (row: InvoiceSetting.InvoiceSettingInfoItem) => {
          return (
            <span>
              {row.invoiceMediaTypeCode === '1'
                ? t(
                    'invoiceSetting.invoiceSettingTable.invoiceMediaTypeCode01',
                    '电子',
                  )
                : t(
                    'invoiceSetting.invoiceSettingTable.invoiceMediaTypeCode02',
                    '纸质',
                  )}
            </span>
          );
        },
      },
      {
        label: t(
          'invoiceSetting.invoiceSettingTable.invoiceUsageDescDisplay',
          '用途',
        ),
        prop: 'invoiceUsageDescDisplay',
        minWidth: 150,
      },
      {
        label: t(
          'invoiceSetting.invoiceSettingTable.invoiceNoLength',
          '票据号长度',
        ),
        prop: 'invoiceNoLength',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('invoiceSetting.invoiceNoLength', '票据号长度'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            validator(rule, value, callback) {
              if (!value) {
                callback(); // 如果值为空，这里因为前面已经有必填校验，所以这里可以直接通过
              } else if (/^[0-9]+$/.test(value)) {
                callback(); // 匹配成功，是0或者正整数，通过校验
              } else {
                callback(new Error(t('请输入0或者正整数'))); // 匹配失败，提示错误信息
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: InvoiceSetting.InvoiceSettingInfoItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.invoiceNoLength}
                placeholder={t('global:placeholder.input.template', {
                  content: t('invoiceSetting.invoiceNoLength', '票据号长度'),
                })}
              />
            );
          } else {
            return <>{row.invoiceNoLength}</>;
          }
        },
      },
      {
        label: t(
          'invoiceSetting.invoiceSettingTable.warningThreshold',
          '报警阈值',
        ),
        prop: 'warningThreshold',
        minWidth: 180,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('invoiceSetting.warningThreshold', '报警阈值'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            validator(rule, value, callback) {
              if (!value) {
                callback(); // 如果值为空，这里因为前面已经有必填校验，所以这里可以直接通过
              } else if (/^[0-9]+$/.test(value)) {
                callback(); // 匹配成功，是0或者正整数，通过校验
              } else {
                callback(new Error(t('请输入0或者正整数'))); // 匹配失败，提示错误信息
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: InvoiceSetting.InvoiceSettingInfoItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.warningThreshold}
                placeholder={t('global:placeholder.input.template', {
                  content: t('invoiceSetting.warningThreshold', '报警阈值'),
                })}
              />
            );
          } else {
            return <>{row.warningThreshold}</>;
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (
          row: InvoiceSetting.InvoiceSettingInfoItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => upsertTable(row, $index)}
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}
