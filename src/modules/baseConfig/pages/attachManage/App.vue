<script lang="ts" name="attachManage" setup>
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { onMounted, ref } from 'vue';
  import { useAttachManageFormConfig } from './config/useFormConfig.tsx';
  import { useAttachManageTableConfig } from './config/useTableConfig.tsx';
  import AddAttachDialog from './components/addAttachDialog.vue';

  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    addAttachManage,
    deleteAttach,
    editAttach,
    queryAttachByExample,
  } from '@/modules/baseConfig/api/attachManage.ts';
  import { ElMessageBox } from 'element-sun';

  const { t } = useTranslation();
  const addAttachDialogRef = ref(); // dialogRef

  const attachManageTableRef = ref();

  let total = ref(0); // 总条数
  let queryAttachByExampleParams = ref<AttachManage.QueryAttachByExample>({
    keyWord: '',
    attachScopeCode: '',
    deleteFlag: ENABLED_FLAG.NO,
    pageSize: 25,
    pageNumber: 1,
  }); // 查询参数
  const attachManageList = ref<AttachManage.AttachList[]>([]); // 表格数据
  const loading = ref(false);

  // 查询联系方式列表
  const queryAttachByExampleData = async (
    params: AttachManage.QueryAttachByExample = {},
  ) => {
    loading.value = true;
    queryAttachByExampleParams.value = {
      ...queryAttachByExampleParams.value,
      ...params,
    };
    let [, res] = await queryAttachByExample({
      ...queryAttachByExampleParams.value,
    });
    loading.value = false;
    if (res?.success) {
      attachManageList.value = res.data || [];
      total.value = res?.total;
    }
  };

  const addNewAttachManage = () => {
    addAttachDialogRef.value.dialogRef.open();
  };
  const saveRow = async (row: AttachManage.AttachList, index: number) => {
    const isValid = await attachManageTableRef?.value?.validateRow(index);
    if (!isValid) return;
    let result: object | undefined;
    if (row.attachId === '') {
      const [, res] = await addAttachManage({ ...row });
      result = res;
    } else {
      const [, res] = await editAttach({ ...row });
      result = res;
    }
    if (result?.success) {
      await queryAttachByExampleData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };
  const deleteAttachManage = async (
    row: AttachManage.AttachList,
    flag: number,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          flag === 1
            ? t('global:delete')
            : t('attachManage.attachManageTable.yichu', '移除'),
        name: row.attachName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteAttach({
        attachId: row.attachId,
        deleteFlag: flag,
      });
      if (res?.success) {
        queryAttachByExampleData();
      }
    });
  };
  // 业务场景改变
  const changeSelect = async (data?: AttachManage.QueryAttachByExample) => {
    queryAttachByExampleParams.value = {
      ...queryAttachByExampleParams.value,
      ...data,
    };
    await queryAttachByExampleData();
  };

  const searchConfig = useAttachManageFormConfig(queryAttachByExampleData);
  const { tableColumns } = useAttachManageTableConfig({
    id: 'attachId',
    tableRef: attachManageTableRef,
    data: attachManageList,
    saveRow,
    deleteAttachManage,
  });
  onMounted(async () => {
    await queryAttachByExampleData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('attachManage.list.title', '附件管理')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryAttachByExampleParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button
          class="mr-2"
          type="primary"
          @click="queryAttachByExampleData"
        >
          {{ $t('attachManage.search', '搜索') }}
        </el-button>
        <el-button class="mr-2" type="primary" @click="addNewAttachManage">
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="attachManageTableRef"
      :columns="tableColumns"
      :data="attachManageList"
      :editable="true"
      :loading="loading"
      :page-info="{
        total,
        pageNumber: queryAttachByExampleParams.pageNumber,
        pageSize: queryAttachByExampleParams.pageSize,
      }"
      :pagination="true"
      row-key="attachId"
      @current-page-change="
        (val: number) => {
          queryAttachByExampleData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryAttachByExampleData({
            pageSize: val,
          });
        }
      "
    />
    <AddAttachDialog
      ref="addAttachDialogRef"
      @success="queryAttachByExampleData"
    ></AddAttachDialog>
  </div>
</template>
