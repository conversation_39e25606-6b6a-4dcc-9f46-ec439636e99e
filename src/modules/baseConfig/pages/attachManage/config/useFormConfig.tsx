import { useFormConfig } from 'sun-biz';
import { ATTACH_SCOPE_CODE } from '@/utils/constant.ts';

export function useAttachManageFormConfig(
  queryAttachByExampleData: (
    params?: AttachManage.QueryAttachByExample,
  ) => Promise<void>,
) {
  const data = useFormConfig({
    dataSetCodes: [ATTACH_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('attachManage.form.keyWord', '关键字'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryAttachByExampleData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
      {
        label: t('attachManage.attachManageTable.attachScopeCode', '应用范围'),
        name: 'attachScopeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('attachManage.attachManageTable.attachScopeCode', '应用范围'),
        }),
        extraProps: {
          clearable: true,
          className: 'w-80',
          options: dataSet?.value ? dataSet.value?.[ATTACH_SCOPE_CODE] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
    ],
  });
  return data;
}

export function useAddAttachDialogFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [ATTACH_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('attachManage.attachManageTable.attachScopeCodes', '应用范围'),
        name: 'attachScopeCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'attachManage.attachManageTable.attachScopeCodes',
            '应用范围',
          ),
        }),
        rules: [
          {
            required: true,
            message: t(
              'attachManage.attachManageTable.attachScopeCodes.required',
              '应用范围不能为空',
            ),
          },
        ],
        extraProps: {
          multiple: true,
          clearable: true,
          className: 'w-60',
          options: dataSet?.value ? dataSet.value?.[ATTACH_SCOPE_CODE] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
    ],
  });
  return data;
}
