import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { ATTACH_CATEGORY_CODE, ATTACH_SCOPE_CODE } from '@/utils/constant.ts';
import { filterSelectData } from '@sun-toolkit/shared';

export function useAttachManageTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<AttachManage.AttachList[]>;
  saveRow: (row: AttachManage.AttachList, index: number) => Promise<void>;
  deleteAttachManage: (
    row: AttachManage.AttachList,
    flag: number,
  ) => Promise<void>;
}) {
  const { id, tableRef, data, saveRow, deleteAttachManage } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    id,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes: [ATTACH_CATEGORY_CODE, ATTACH_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('attachManage.attachManageTable.attachName', '附件名称'),
        prop: 'attachName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'attachManage.attachManageTable.attachName',
                '附件名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.attachName}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'attachManage.attachManageTable.attachName',
                    '附件名称',
                  ),
                })}
              />
            );
          } else {
            return <>{row.attachName}</>;
          }
        },
      },
      {
        label: t(
          'attachManage.attachManageTable.attachCategoryCode',
          '附件分类',
        ),
        prop: 'attachCategoryCode',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'attachManage.attachManageTable.attachCategoryCode',
                '附件分类',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
        ) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[ATTACH_CATEGORY_CODE]
            : [];
          (
            data.value as (AttachManage.AttachList & {
              dataValueNo: string;
            })[]
          ).map(
            (
              item: AttachManage.AttachList & {
                dataValueNo: string;
              },
            ) => {
              item.dataValueNo = item.attachCategoryCode;
            },
          );

          const attachCategoryCodeList = filterSelectData(
            dataSetList,
            data.value.filter(
              (item) => item?.attachCategoryCode !== row?.attachCategoryCode,
            ),
            'dataValueNo',
          );
          if (row?.editable) {
            return (
              <>
                <el-select
                  v-model={row.attachCategoryCode}
                  onChange={(val: string) => {
                    const attachCategoryObj = attachCategoryCodeList.find(
                      (obj: { dataValueNo: string }) =>
                        obj?.dataValueNo === val,
                    );
                    row.attachCategoryCode =
                      attachCategoryObj?.dataValueNo as string;
                    row.attachCategoryCodeDesc =
                      attachCategoryObj?.dataValueNameDisplay as string;
                  }}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'attachManage.attachManageTable.attachCategoryCode',
                      '附件分类',
                    ),
                  })}
                >
                  {attachCategoryCodeList?.map(
                    (item: {
                      dataValueNo: string;
                      dataValueNameDisplay: string;
                    }) => (
                      <el-option
                        key={item.dataValueNo}
                        label={
                          item.dataValueNo + '-' + item.dataValueNameDisplay
                        }
                        value={item.dataValueNo}
                      />
                    ),
                  )}
                </el-select>
              </>
            );
          } else {
            return <>{row.attachCategoryCodeDesc}</>;
          }
        },
      },
      {
        label: t(
          'attachManage.attachManageTable.attachScopeList',
          '附件应用范围',
        ),
        prop: 'attachScopeList',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'attachManage.attachManageTable.attachScopeList',
                '附件应用范围',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
        ) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[ATTACH_SCOPE_CODE].map((item) => ({
                dataValueNo: item.dataValueNo,
                dataValueNameDisplay: item.dataValueNameDisplay,
                dtUsageScopeId: '',
              }))
            : [];
          if (row.editable) {
            // 编辑时提取 dtUsageScopeCodeList 中的 dtUsageScopeCode 用于回显
            const selectedCodes =
              row.attachScopeList?.map((item) => item.attachScopeCode || '') ||
              [];
            return (
              <el-select
                v-model={selectedCodes}
                clearable
                multiple
                filterable
                onChange={(val: string[]) => {
                  console.log(val);
                  const newAttachScopeList =
                    val.length > 0
                      ? dataSetList
                          .filter((obj) => val.includes(obj.dataValueNo))
                          .map((item) => {
                            const existingItem =
                              row.attachScopeList &&
                              row.attachScopeList.find(
                                (codeObj) =>
                                  codeObj.attachScopeCode === item.dataValueNo,
                              );
                            console.log(item);
                            return {
                              attachScopeCode: item.dataValueNo,
                              attachScopeId:
                                existingItem?.attachScopeId || null,
                              attachScopeCodeDesc: item.dataValueNameDisplay,
                            };
                          })
                      : [];

                  row.attachScopeList =
                    newAttachScopeList as AttachManage.AttachScopeList;
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'dataTypeManage.dataTypeManageTable.dtUsageScopeCodeList',
                    '应用范围',
                  ),
                })}
              >
                {dataSetList.map((item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ))}
              </el-select>
            );
          } else {
            return (
              <>
                {row.attachScopeList?.map((item) => (
                  <div key={item.attachScopeId}>{item.attachScopeCodeDesc}</div>
                ))}
              </>
            );
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 160,
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row, $index)}
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type={'primary'}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
              <el-button
                link={true}
                type={'danger'}
                onClick={() => deleteAttachManage(row, 1)}
              >
                {t('global:delete', '删除')}
              </el-button>
              <el-button
                link={true}
                type={'danger'}
                onClick={() => deleteAttachManage(row, 0)}
              >
                {t('attachManage.attachManageTable.yichu', '移除')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}

export function useFileUploadTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  attachList: Ref<AttachManage.AttachList[]>;
  deleteAttachFile: (row: AttachManage.AttachList, index: number) => void;
}) {
  const { id, tableRef, deleteAttachFile } = options;

  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    id,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes: [ATTACH_CATEGORY_CODE],
    getData: (t, dataSet) => [
      {
        label: t('attachManage.attachManageTable.attachName', '附件名称'),
        prop: 'attachName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'attachManage.attachManageTable.attachName',
                '附件名称',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.attachName}
                placeholder={t('global:placeholder.input.template', {
                  content: t(
                    'attachManage.attachManageTable.attachName',
                    '附件名称',
                  ),
                })}
              />
            );
          } else {
            return <>{row.attachName}</>;
          }
        },
      },
      {
        label: t(
          'attachManage.attachFileUploadTable.attachCategoryCode',
          '附件分类',
        ),
        prop: 'attachCategoryCode',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'attachManage.attachFileUploadTable.attachCategoryCode',
                '附件分类',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
        ) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[ATTACH_CATEGORY_CODE]
            : [];
          if (row?.editable) {
            return (
              <>
                <el-select
                  v-model={row.attachCategoryCode}
                  onChange={(val: string) => {
                    const attachCategoryObj = dataSetList.find(
                      (obj: { dataValueNo: string }) =>
                        obj?.dataValueNo === val,
                    );
                    if (attachCategoryObj) {
                      row.attachCategoryCode = attachCategoryObj.dataValueNo;
                      row.attachCategoryCodeDesc =
                        attachCategoryObj.dataValueNameDisplay;
                    }
                  }}
                  filterable
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'attachManage.attachFileUploadTable.attachCategoryCode',
                      '附件分类',
                    ),
                  })}
                >
                  {dataSetList?.map(
                    (item: {
                      dataValueNo: string;
                      dataValueNameDisplay: string;
                    }) => (
                      <el-option
                        key={item.dataValueNo}
                        label={
                          item.dataValueNo + '-' + item.dataValueNameDisplay
                        }
                        value={item.dataValueNo}
                      />
                    ),
                  )}
                </el-select>
              </>
            );
          } else {
            return <>{row.attachCategoryCodeDesc}</>;
          }
        },
      },
      {
        label: t('attachManage.attachManageTable.attachPosition', '附件路径'),
        prop: 'attachPosition',
        minWidth: 200,
      },
      {
        label: t('attachManage.attachManageTable.uploadStatus', '上传状态'),
        prop: 'uploadStatusDesc',
        minWidth: 100,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 160,
        render: (
          row: AttachManage.AttachList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={async () => {
                  try {
                    // 验证当前行
                    const valid = await tableRef.value?.validateRow($index);
                    if (valid) {
                      toggleEdit(row);
                    }
                  } catch (error) {
                    console.error('验证失败:', error);
                  }
                }}
              >
                {t('global:confrim', '确定')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type={'primary'}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
              <el-button
                link={true}
                type={'danger'}
                onClick={() => deleteAttachFile(row, $index)}
              >
                {t('global:delete', '删除')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}
