<script lang="ts" name="AttachmentUpload" setup>
  import { onBeforeUnmount, ref, useAttrs } from 'vue';
  import { dayjs } from 'element-sun';
  import { uploadFile } from '@/modules/baseConfig/api/attachManage.ts';

  const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB
  const loading = ref(false);
  const attrs: {
    modelValue?: {
      attachName: string;
      attachPosition: string;
      storyXAttachId?: string;
      uploadStatus?: string;
      uploadStatusDesc?: string;
      editable?: boolean;
    }[];
    hiddenUpload?: boolean;
    hiddenDeleteIcon?: boolean;
    disabled?: boolean;
    uploadBtnIcon?: Comment;
    uploadText?: string;
    useBeforeUploadIcon?: boolean;
    'onUpdate:modelValue'?: (
      value: {
        attachName: string;
        attachPosition: string;
        editable?: boolean;
      }[],
    ) => void;
  } = useAttrs();
  const uploadFileList = ref<File[]>([]); // 待上传文件列表
  const intervalId = ref<ReturnType<typeof setInterval> | null>(null); // 定时任务

  // 停止定时任务
  const stopInterval = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
    }
  };

  const uploadFileFn = async () => {
    if (!uploadFileList.value.length) return;
    stopInterval();
    for (const file of uploadFileList.value) {
      const totalSize = file.size;
      const formData = new FormData();
      formData.append('fileName', file.name);
      if (totalSize > CHUNK_SIZE) {
        const totalChunks = Math.ceil(totalSize / CHUNK_SIZE);
        let attachPosition = null;
        const filePath = String(dayjs().valueOf());
        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
          const start = chunkIndex * CHUNK_SIZE;
          const end = Math.min(totalSize, start + CHUNK_SIZE);
          const fileChunk = file.slice(start, end);
          const chunkFormData = new FormData();
          chunkFormData.append('fileName', file.name);
          chunkFormData.append('fileContent', fileChunk);
          chunkFormData.append('filePageSize', String(totalChunks));
          chunkFormData.append('filePageNo', String(chunkIndex + 1));
          chunkFormData.append('filePath', filePath);
          const [, res] = await uploadFile(chunkFormData);
          if (res?.success && chunkIndex === totalChunks - 1) {
            attachPosition = res.data.filePosition;
          }
        }
        if (attachPosition) {
          const list = attrs.modelValue || [];
          list.push({
            attachName: file.name,
            attachPosition: attachPosition,
            uploadStatus: 'success',
            uploadStatusDesc: '上传成功',
            editable: true,
          });
          attrs['onUpdate:modelValue']?.(list);
        }
      } else {
        formData.append('fileContent', file);
        formData.append('filePath', String(dayjs().valueOf()));
        const [, res] = await uploadFile(formData);
        if (res?.success && res.data?.filePosition) {
          const list = attrs.modelValue || [];
          list.push({
            attachName: file.name,
            attachPosition: res.data.filePosition,
            uploadStatus: 'success',
            uploadStatusDesc: '上传成功',
            editable: true,
          });
          attrs['onUpdate:modelValue']?.(list);
          console.log(attrs.modelValue, 'attrs.modelValue');
        }
      }
    }
    loading.value = false;
    uploadFileList.value = [];
  };

  const startInterval = () => {
    stopInterval();
    intervalId.value = setInterval(uploadFileFn, 1000);
  };

  const handleUploadFileRequest = (data: { file: File }) => {
    loading.value = true;
    const { file } = data;
    uploadFileList.value.push(file);
    startInterval();
  };

  const onDeleteClick = (index: number) => {
    const list = attrs.modelValue || [];
    list.splice(index, 1);
    attrs['onUpdate:modelValue']?.(list);
  };

  const onLinkClick = (item: AttachManage.FileParams) => {
    window.location.href = item.filePosition;
  };

  onBeforeUnmount(() => {
    stopInterval();
  });
</script>

<template>
  <div class="flex flex-col">
    <el-upload
      :class="`${attrs.hiddenUpload ? 'hidden' : ''}`"
      :disabled="attrs.disabled || loading"
      :http-request="handleUploadFileRequest"
      :show-file-list="false"
      action="#"
      multiple
      v-bind="{ ...attrs }"
    >
      <template #trigger>
        <el-button
          v-if="attrs.useBeforeUploadIcon && attrs.modelValue?.length"
          :icon="attrs.uploadBtnIcon"
          :loading="loading"
          type="primary"
        >
          {{
            attrs.uploadText ||
            $t('attachmentUpload.useBeforeUploadIconText', '添加附件')
          }}
        </el-button>

        <el-button
          v-else
          :disabled="attrs.disabled || loading"
          :icon="attrs.uploadBtnIcon"
          :loading="loading"
          type="primary"
          >{{
            loading
              ? $t('attachmentUpload.uploadingText', '正在上传...')
              : attrs.uploadText ||
                $t('attachmentUpload.uploadText', '添加附件')
          }}
        </el-button>
      </template>
    </el-upload>
    <div v-if="false" class="mt-2 flex flex-wrap">
      <div
        v-for="(item, index) in attrs.modelValue"
        :key="index"
        class="mb-2 mr-2 flex items-center"
      >
        <span
          class="mr-2 cursor-pointer text-blue-500"
          @click.stop="onLinkClick(item)"
        >
          {{ item.attachName }}
        </span>
        <el-icon
          v-if="attrs.hiddenDeleteIcon !== true && attrs.disabled !== true"
          class="cursor-pointer"
          color="#ff4500"
          @click.stop="onDeleteClick(index)"
        >
          <DeleteFilled />
        </el-icon>
      </div>
    </div>
  </div>
</template>
