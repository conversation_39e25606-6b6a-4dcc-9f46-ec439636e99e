<script lang="ts" name="contactInfo" setup>
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { computed, onMounted, ref } from 'vue';
  import { useContactInfoFormConfig } from './config/useFormConfig.tsx';
  import { useContactInfoTableConfig } from './config/useTableConfig.tsx';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    addContactInfo,
    deleteContactInfoById,
    editContactInfo,
    queryContactInfoList,
  } from '@/modules/baseConfig/api/contactInfo.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { queryMsgSendWayList } from '@/modules/baseConfig/api/msgSendWay.ts';
  import { SelectOptions } from '@/typings/common.ts';
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const contactInfoTableRef = ref();
  const selectTableData = ref<ContactInfo.ContactInfoList[]>([]);

  let total = ref(0); // 总条数
  let queryContactInfoListParams = ref<ContactInfo.QueryContactInfoList>({
    msgSendWayId: '',
    enabledFlag: undefined,
    contactInfoIds: [],
    keyWord: '',
    pageNumber: 1,
    pageSize: 25,
  }); // 查询参数
  const contactInfoList = ref<ContactInfo.ContactInfoList[]>([]); // 表格数据
  const loading = ref(false);

  // 查询联系方式列表
  const queryContactInfoListData = async (
    params: ContactInfo.QueryContactInfoList = {},
  ) => {
    loading.value = true;
    queryContactInfoListParams.value = {
      ...queryContactInfoListParams.value,
      ...params,
    };
    let [, res] = await queryContactInfoList({
      ...queryContactInfoListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      contactInfoList.value = res.data || [];
      total.value = res?.total;
    }
  };

  const msgSendWayList = ref<SelectOptions[]>([]);
  const queryMsgSendWayListData = async () => {
    const params = {
      pageNumber: 0,
    };
    const [, res] = await queryMsgSendWayList(params);
    if (res) {
      let { data } = res;
      msgSendWayList.value = data
        .filter((item) => item.enabledFlag === ENABLED_FLAG.YES)
        .map((item) => ({
          value: item.msgSendWayId,
          label: item.msgSendWayName,
        }));
    }
  };

  // 启用状态改变
  const changeSelect = async (data?: ContactInfo.QueryContactInfoList) => {
    queryContactInfoListParams.value = {
      ...queryContactInfoListParams.value,
      ...data,
    };
    await queryContactInfoListData();
  };
  const editRow = (row: ContactInfo.ContactInfoList) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(row);
  };
  const addNewContactInfo = async () => {
    if (!canUpsertTableRow()) return;
    contactInfoList.value.push({
      contactInfoId: '',
      contactNo: '',
      msgSendWayId: '',
      msgSendWayName: '',
      enabledFlag: ENABLED_FLAG.YES,
      createdAt: '',
      createdUserId: '',
      createdUserName: '',
      modifiedAt: '',
      modifiedUserId: '',
      modifiedUserName: '',
      editable: true,
    });
  };
  const saveRow = async (
    row: ContactInfo.UpsertContactInfoParams,
    index: number,
  ) => {
    const isValid = await contactInfoTableRef?.value?.validateRow(index);
    if (!isValid) return;
    let result: object | undefined;
    if (row.contactInfoId === '') {
      const [, res] = await addContactInfo({ ...row });
      result = res;
    } else {
      const [, res] = await editContactInfo({ ...row });
      result = res;
    }
    if (result?.success) {
      await queryContactInfoListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };
  const handleEnableSwitch = async (row: ContactInfo.ContactInfoList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.contactNo,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params: ContactInfo.ContactInfoList = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editContactInfo(params);
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:modify.success'),
        });
        queryContactInfoListData();
      }
    });
  };
  const deleteContactInfo = async (row: ContactInfo.ContactInfoList) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.contactNo,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      console.log(row);
      const [, res] = await deleteContactInfoById({
        contactInfoId: row.contactInfoId,
      });
      if (res?.success) {
        ElMessage({
          type: 'success',
          message: t('global:success'),
        });
        queryContactInfoListData();
      }
    });
  };

  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.contactInfoId;
    });
    return list ?? [];
  });
  // 选中行设置
  const selectionChange = (val: ContactInfo.UpsertContactInfoParams[]) => {
    selectTableData.value = val;
  };

  const canUpsertTableRow = () => {
    const isEditing = contactInfoList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  const searchConfig = useContactInfoFormConfig(
    msgSendWayList,
    queryContactInfoListData,
  );
  const { tableColumns, toggleEdit } = useContactInfoTableConfig({
    id: 'contactInfoId',
    tableRef: contactInfoTableRef,
    data: contactInfoList,
    saveRow,
    msgSendWayList,
    handleEnableSwitch,
    deleteContactInfo,
    isCloudEnv,
    editRow,
  });
  onMounted(async () => {
    await queryMsgSendWayListData();
    await queryContactInfoListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('contactInfo.list.title', '联系方式信息')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryContactInfoListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button
          class="mr-2"
          type="primary"
          @click="queryContactInfoListData"
        >
          {{ $t('contactInfo.search', '搜索') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-2"
          type="primary"
          @click="addNewContactInfo"
        >
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
      <DmlButton
        :biz-data="bizData"
        :code="BIZ_ID_TYPE_CODE.DICT_CONTACT_INFO"
        @success="
          () => {
            contactInfoTableRef?.proTableRef.clearSelection();
          }
        "
      />
    </div>
    <ProTable
      ref="contactInfoTableRef"
      :columns="tableColumns"
      :data="contactInfoList"
      :editable="true"
      :loading="loading"
      :page-info="{
        total,
        pageNumber: queryContactInfoListParams.pageNumber,
        pageSize: queryContactInfoListParams.pageSize,
      }"
      :pagination="true"
      row-key="contactInfoId"
      @selection-change="selectionChange"
      @current-page-change="
        (val: number) => {
          queryContactInfoListData({
            pageNumber: val,
          });
        }
      "
      @size-page-change="
        (val: number) => {
          queryContactInfoListData({
            pageSize: val,
          });
        }
      "
    />
  </div>
</template>
