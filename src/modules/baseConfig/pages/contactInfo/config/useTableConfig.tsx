import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { SelectOptions } from '@/typings/common.ts';

export function useContactInfoTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<ContactInfo.ContactInfoList[]>;
  saveRow: (
    row: ContactInfo.UpsertContactInfoParams,
    index: number,
  ) => Promise<void>;
  msgSendWayList: Ref<SelectOptions[]>;
  handleEnableSwitch: (
    row: ContactInfo.UpsertContactInfoParams,
  ) => Promise<void>;
  deleteContactInfo: (
    row: ContactInfo.UpsertContactInfoParams,
  ) => Promise<void>;
  isCloudEnv: boolean | undefined;
  editRow: (row: ContactInfo.ContactInfoList) => void;
}) {
  const {
    id,
    tableRef,
    data,
    saveRow,
    handleEnableSwitch,
    deleteContactInfo,
    isCloudEnv,
    editRow,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (ContactInfo.ContactInfoList & { editable: boolean })[]
    >,
    id,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('contactInfo.contactInfoTable.contactNo', '联系方式号码'),
        prop: 'contactNo',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('contactInfo.contactNo', '联系方式号码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ContactInfo.ContactInfoList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.contactNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('contactInfo.contactNo', '联系方式号码'),
                })}
              />
            );
          } else {
            return <>{row.contactNo}</>;
          }
        },
      },
      {
        label: t('contactInfo.contactInfoTable.msgSendWayId', '消息发送渠道'),
        prop: 'msgSendWayId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('contactInfo.msgSendWayId', '消息发送渠道'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ContactInfo.ContactInfoList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.msgSendWayId}
                clearable
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'contactInfo.contactInfoTable.msgSendWayId',
                    '消息发送渠道',
                  ),
                })}
              >
                {options.msgSendWayList.value.map((item) => (
                  <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.msgSendWayName}</>;
          }
        },
      },
      {
        label: t('contactInfo.contactInfoTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ContactInfo.ContactInfoList) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(row as ContactInfo.UpsertContactInfoParams)
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('contactInfo.contactInfoTable.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('contactInfo.contactInfoTable.createdAt', '创建时间'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t('contactInfo.contactInfoTable.modifiedUserName', '最后修改人'),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('contactInfo.contactInfoTable.modifiedAt', '最后修改时间'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 160,
        render: (
          row: ContactInfo.UpsertContactInfoParams & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row, $index)}
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type={!isCloudEnv ? '' : 'primary'}
                disabled={!isCloudEnv}
                onClick={() => editRow(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
              <el-button
                link={true}
                type={!isCloudEnv ? '' : 'danger'}
                disabled={!isCloudEnv}
                onClick={() => deleteContactInfo(row)}
              >
                {t('global:delete', '删除')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
