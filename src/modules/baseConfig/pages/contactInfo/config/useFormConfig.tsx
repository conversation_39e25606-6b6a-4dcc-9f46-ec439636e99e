import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant.ts';
import { SelectOptions } from '@/typings/common.ts';

export function useContactInfoFormConfig(
  msgSendWayList: Ref<SelectOptions[]>,
  queryContactInfoListData: (
    params?: ContactInfo.QueryContactInfoList,
  ) => Promise<void>,
) {
  const enabledFlagList = [
    { value: ENABLED_FLAG.YES, label: '启用' },
    { value: ENABLED_FLAG.NO, label: '停用' },
  ];
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('contactInfo.form.keyWord', '联系方式号码'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              await queryContactInfoListData({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
      {
        label: t('global:enabledFlag', '启用标志'),
        name: 'enabledFlag',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag', '启用标志'),
        }),
        extraProps: {
          options: enabledFlagList,
          clearable: true,
          className: 'w-80',
        },
      },
      {
        label: t('contactInfo.form.msgSendWayId', '发送渠道'),
        name: 'msgSendWayId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('contactInfo.form.msgSendWayId', '发送渠道'),
        }),
        extraProps: {
          options: msgSendWayList.value,
          clearable: true,
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}
