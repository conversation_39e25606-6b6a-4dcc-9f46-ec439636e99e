import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { DEV_GROUP_CODE_NAME } from '@/utils/constant.ts';
import {
  DevGroupList,
  UpsertDevGroup,
} from '@/modules/baseConfig/typings/devGroup.ts';

export function useDevGroupTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<DevGroupList[]>;
  saveRow: (row: UpsertDevGroup, index: number) => Promise<void>;
  handleEnableSwitch: (row: UpsertDevGroup) => Promise<void>;
  deleteDevGroup: (row: UpsertDevGroup) => Promise<void>;
  handleEdit: (row: UpsertDevGroup) => Promise<void>;
}) {
  const {
    id,
    tableRef,
    data,
    saveRow,
    handleEnableSwitch,
    deleteDevGroup,
    handleEdit,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<(DevGroupList & { editable: boolean })[]>,
    id,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes: [DEV_GROUP_CODE_NAME],

    getData: (t, dataSet) => [
      {
        label: t('devGroupTable.devGroupId', '开发组别标识'),
        prop: 'devGroupId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('devGroupId', '开发组别标识'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('devGroupTable.devGroupCode', '开发组别'),
        prop: 'devGroupCode',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('devGroupCode', '开发组别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: DevGroupList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.devGroupCode}
                clearable
                placeholder={t('global:placeholder.select.template', {
                  name: t('devGroupTable.devGroupCode', '开发组别'),
                })}
              >
                {dataSet?.value
                  ? dataSet.value[DEV_GROUP_CODE_NAME].map((item) => (
                      <el-option
                        key={item.dataValueNo}
                        label={item.dataValueNameDisplay}
                        value={item.dataValueNo}
                      />
                    ))
                  : null}
              </el-select>
            );
          } else {
            return <>{row.devGroupCodeDesc}</>;
          }
        },
      },
      {
        label: t('devGroupTable.devPlanMandatoryFlag', '开发计划必填标志'),
        prop: 'devPlanMandatoryFlag',
        minWidth: 100,
        render: (row: DevGroupList) => {
          return (
            <el-switch
              modelValue={row.devPlanMandatoryFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row as UpsertDevGroup)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 160,
        render: (
          row: UpsertDevGroup & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row, $index)}
              >
                {t('global:save', '保存')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type="primary"
                onClick={() => handleEdit(row)}
              >
                {t('global:edit', '编辑')}
              </el-button>
              <el-button
                link={true}
                type="danger"
                onClick={() => deleteDevGroup(row)}
              >
                {t('global:delete', '删除')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
