import { useFormConfig } from 'sun-biz';
import { DEV_GROUP_CODE_NAME } from '@/utils/constant.ts';

export function useDevGroupFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [DEV_GROUP_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'devGroupCode',
        label: t('devGroup.form.devGroupCode', '开发组别'),
        component: 'select',
        placeholder: t('global:placeholder.input.template', {
          content: t('devGroup.form.devGroupCode', '开发组别'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: dataSet?.value ? dataSet.value[DEV_GROUP_CODE_NAME] : [],
          className: 'w-80',
        },
      },
    ],
  });
  return data;
}
