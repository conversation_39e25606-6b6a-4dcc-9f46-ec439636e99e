<script lang="ts" name="devGroup" setup>
  import { ProForm, ProTable, Title } from 'sun-biz';
  import { onMounted, ref } from 'vue';
  import { useDevGroupFormConfig } from './config/useFormConfig.tsx';
  import { useDevGroupTableConfig } from './config/useTableConfig.tsx';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import {
    addDevGroup,
    deleteDevGroupById,
    editDevGroup,
    queryDevGroupByExample,
  } from '@/modules/baseConfig/api/devGroup.ts';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    DevGroupList,
    UpsertDevGroup,
  } from '@/modules/baseConfig/typings/devGroup.ts';

  const { t } = useTranslation();
  const devGroupTableRef = ref();
  let queryDevGroupListParams = ref<UpsertDevGroup>({
    devGroupCode: '',
  }); // 查询参数
  const devGroupList = ref<DevGroupList[]>([]); // 表格数据
  const loading = ref(false);

  // 查询联系方式列表
  const queryDevGroupListData = async (params: UpsertDevGroup = {}) => {
    loading.value = true;
    queryDevGroupListParams.value = {
      ...queryDevGroupListParams.value,
      ...params,
    };
    let [, res] = await queryDevGroupByExample({
      ...queryDevGroupListParams.value,
    });
    loading.value = false;
    if (res?.success) {
      devGroupList.value = res.data || [];
    }
  };

  // 查询条件改变
  const changeSelect = async (data?: UpsertDevGroup) => {
    queryDevGroupListParams.value = {
      ...queryDevGroupListParams.value,
      ...data,
    };
    await queryDevGroupListData();
  };

  const addNewDevGroup = async () => {
    if (!canUpsertTableRow()) return;
    devGroupList.value.push({
      devGroupCode: '',
      devPlanMandatoryFlag: ENABLED_FLAG.YES,
      editable: true,
    });
  };
  const saveRow = async (row: UpsertDevGroup, index: number) => {
    const isValid = await devGroupTableRef?.value?.validateRow(index);
    if (!isValid) return;
    let result: object | undefined;
    if (!row.devGroupId) {
      const [, res] = await addDevGroup({ ...row });
      result = res;
    } else {
      const [, res] = await editDevGroup({ ...row });
      result = res;
    }
    console.log(result);
    if (result?.success) {
      await queryDevGroupListData();
      (
        row as unknown as {
          editable: boolean;
        }
      ).editable = false;
    }
  };
  const handleEnableSwitch = async (row: DevGroupList) => {
    if (!canUpsertTableRow()) return;

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.devPlanMandatoryFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: '开发计划必填标志',
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params: UpsertDevGroup = {
        ...row,
        devPlanMandatoryFlag:
          row.devPlanMandatoryFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editDevGroup(params);
      if (res?.success) {
        queryDevGroupListData();
      }
    });
  };
  const deleteDevGroup = async (row: UpsertDevGroup) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} {{name}}吗？', {
        action: t('global:delete'),
        name: '该开发组别',
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteDevGroupById(row);
      if (res?.success) {
        queryDevGroupListData();
      }
    });
  };
  const canUpsertTableRow = () => {
    const isEditing = devGroupList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  // 编辑
  const handleEdit = (data: UpsertDevGroup) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(data);
  };
  const searchConfig = useDevGroupFormConfig();
  const { tableColumns, toggleEdit } = useDevGroupTableConfig({
    id: 'devGroupId',
    tableRef: devGroupTableRef,
    data: devGroupList,
    saveRow,
    handleEnableSwitch,
    deleteDevGroup,
    handleEdit,
  });
  onMounted(async () => {
    await queryDevGroupListData();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('devGroup.list.title', '开发组别管理')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="queryDevGroupListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="changeSelect"
        />
        <el-button class="mr-2" type="primary" @click="queryDevGroupListData">
          {{ $t('devGroup.search', '搜索') }}
        </el-button>
        <el-button class="mr-2" type="primary" @click="addNewDevGroup">
          {{ $t('global:add', '新增') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="devGroupTableRef"
      :columns="tableColumns"
      :data="devGroupList"
      :editable="true"
      :loading="loading"
      row-key="devGroupId"
    />
  </div>
</template>
