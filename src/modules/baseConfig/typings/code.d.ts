declare namespace Code {
  export interface PageInfo {
    pageNumber: number;
    pageSize: number;
    total?: number;
  }

  export interface ReqQueryCodeSystemListParams
    extends Omit<PageInfo, 'total'> {
    keyWord?: string;
    enabledFlag?: number;
  }
  export interface CodeSystemInfo {
    dataValueId: stinrg;
    dataValueNo: string;
    dataValueCnName: string;
    dataValue2ndName: string;
    dataValueExtName: string;
    dataValueDescription: string;
    dataValueNameDisplay: string;
    spellNo: string;
    wbNo: string;
    conceptId: string;
    hisExistsFlag: number;
    conceptDescription: string;
    enabledFlag: number;
    parentId?: string;
    codeSystemNo: string;
    codeSystemId: string;
    codeSystemName: string;
    codeSystem2ndName: string;
    description: string;
    version: string;
    sourceName: string;
    publishDate: string;
    version: string;
    beginDate: string;
    endDate: string;
  }
  export interface ResQueryCodeSystemList extends CodeSystemInfo {
    dataSetList: ResQueryCodeSystemList[];
    codeSystemList: Omit<ResQueryCodeSystemList, 'dataSetList', 'childen'>[];
  }

  export interface ReqSaveCodeSystemDataSetsParams {
    codeSystemList: Omit<ResQueryCodeSystemList, 'dataSetList', 'childen'>[];
  }
  export interface ResSaveCodeSystemDataSets {
    success: boolean;
  }

  export interface ReqQueryDataSetListByExampleParams {
    hospitalId: string;
    keyWord?: string;
    codeSystemIds?: string[];
    codeSystemNos?: string[];
    pageNumber?: number;
  }

  export interface ReqExportDmlScriptByExampleParams {
    bizIdTypeCode: BIZ_ID_TYPE_CODE;
    bisIds: string[];
    dataBaseTypeCode: string;
  }

  //下面是旧的 后面删除

  export interface ReqQueryCodeSystemDataParams
    extends Omit<PageInfo, 'total'> {
    codeSystemNo?: string;
    keyWord?: string;
    enabledFlag?: string;
  }
}
