declare namespace ProductManage {
  interface QueryProductManageParams {
    keyWord?: string;
    productId?: string;
    enabledFlag?: number;
    orgId?: string;
  }

  interface QueryCodeRepository {
    keyWord?: string;
    codeRepositoryTypeCode?: string;
    systemId?: string;
  }

  interface CodeRepositoryList {
    codeRepositoryId?: string;
    codeRepositoryName?: string;
    codeRepositoryDesc?: string;
    codeRepositoryAddr?: string;
    codeRepositoryTypeCode?: string;
    codeRepositoryTypeCodeDesc?: string;
    sort?: number;
  }

  interface ProductList {
    productId?: string;
    productName?: string;
    product2ndName?: string;
    productExtName?: string;
    productDesc?: string;
    enabledFlag?: number;
    editable?: boolean;
    orgList?: OrgList[];
  }

  interface OrgList {
    productId?: string;
    orgXProdId?: string;
    orgId?: string;
    orgName?: string;
    codeBranchId?: string;
    codeBranchName?: string;
    orgTypeCode?: string;
    orgTypeCodeDesc?: string;
    codeRepositoryIds?: string[];
    productList?: string[];
    editable?: boolean;
    sysList?: SysList[];
  }

  interface SysList {
    prodXSysId?: string;
    sysId?: string;
    sysName?: string;
    editable?: boolean;
  }

  interface UpsertOrgXProdCodeRepository {
    orgXProdId?: string;
    codeRepositoryIds?: string[];
    orgXProdDefId?: string;
    codeRepositoryId?: string;
    codeRepositoryName?: string;
    servicePrefix?: string;
    codeRepositoryDesc?: string;
    codeRepositoryAddr?: string;
    codeRepositoryTypeCode?: string;
    codeRepositoryTypeCodeDesc?: string;
    sort?: number;
    editable?: boolean;
  }
}
