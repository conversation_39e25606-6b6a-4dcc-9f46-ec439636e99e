declare namespace TimedTask {
  interface QueryParams {
    hospitalId?: string;
    keyWord?: string;
    timedTaskId?: string;
    pageNumber?: number;
    pageSize?: number;
  }

  interface TimedTaskInfo {
    timedTaskId: string;
    timedTaskName: string;
    allowMultiInstanceFlag: number;
    apiId: string;
    apiName: string;
    classNamePostMapping: string;
    timedTaskParamList: TimedTaskParamListItem[];
    timedTaskInstanceList: TimedTaskInstanceListItem[];
  }

  interface TimedTaskParamListItem {
    timedTaskParamId?: string;
    defaultValue?: string;
    mustInputFlag: number;
    paramDesc: string;
    paramKey: string;
    paramValue?: string;
  }

  interface TimedTaskInstanceListItem {
    cron: string;
    enabledFlag: number;
    execAt: string;
    firstExceAt: string;
    hospitalId: string;
    hospitalName: string;
    nextExecAt: string;
    timedTaskInstanceId: string;
    timedTaskParam: string;
    timedTaskParamList: TimedTaskParamListItem[];
    triggerMainTime: string;
    triggerPeriodCode: string;
    triggerPeriodDesc: string;
    triggerSubTime: string;
    timedTaskInstanceDesc: string;
    bindingUserId: string;
    bindingUserName: string;
  }

  interface UpsertTimedTaskParams {
    timedTaskId?: string;
    timedTaskName: string;
    allowMultiInstanceFlag?: number;
    apiId: string;
    timedTaskParamList: TimedTaskParamListItem[];
  }

  interface ExecLogQueryParams {
    timedTaskId?: string;
    hospitalId?: string;
    timedTaskInstanceId?: string;
    execBeginAt?: string;
    execEndAt?: string;
    pageNumber?: number;
    pageSize?: number;
    execAt?: string[];
  }

  interface TimedTaskExecLogInfo {
    bizInterfaceInParam: string;
    bizInterfaceOutParam: string;
    completeAt: string;
    execAt: string;
    hospitalId: string;
    hospitalName: string;
    successFlag: number;
    timedTaskExecLogId: string;
    timedTaskInstanceId: string;
    timedTaskParam: string;
  }

  interface UpsertTimedTaskInstanceParams {
    timedTaskId?: string;
    hospitalId?: string;
    timedTaskInstanceId?: string;
    timedTaskParamList?: {
      paramValue?: string;
      timedTaskParamId: string;
    }[];
    triggerMainTime?: string;
    triggerPeriodCode: string;
    triggerSubTime?: string;
    timedTaskInstanceDesc?: string;
    bindingUserId?: string;
  }
}
