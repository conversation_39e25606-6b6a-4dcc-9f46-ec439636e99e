declare namespace Address {
  // 查询地址请求参数
  interface QueryParams {
    keyWord?: string;
    addressLevelCode?: string;
    addressId?: string;
  }

  // 返回值
  interface AddressInfo {
    addressId?: string;
    addressLevelCode?: string | number;
    addressLevelDesc?: string;
    addressNo?: string;
    addressName?: string;
    address2ndName?: string;
    addressExtName?: string;
    addressNameDisplay?: string;
    postalNo?: string;
    enabledFlag?: number;
    sort?: number;
    nativeFlag?: number;
    spellNo?: string;
    wbNo?: string;
    hasChildren?: boolean;
    subAddressList?: string[] | AddressInfo[];
    parentAddressId?: string | null;
    parentLevelCode?: null;
    children?: AddressInfo[];
  }

  // 更新
  interface UpdateAddressByIdParams {
    addressId?: string;
    addressName?: string;
    address2ndName?: string;
    addressExtName?: string;
    addressDesc?: string;
    spellNo?: string;
    wbNo?: string;
    enabledFlag: number;
  }

  // 启停用
  interface UpdateAddressEnabledFlagByIdParams {
    addressId?: number;
    enabledFlag?: number;
  }

  // 排序更新参数
  export interface UpdateSortParams {
    addressId?: string;
    sort?: number;
  }

  // 排序
  interface AddressSortList {
    updateAddressSortByIds: UpdateSortParams[];
  }

  // 修改籍贯标识
  interface UpdateAddressNativeFlagById {
    addressId?: number;
    parentAddressId?: number;
    addressLevelCode?: string;
    addressNo?: string;
    addressName?: string;
    address2ndName?: string;
    addressExtName?: string;
    postalNo?: string;
    enabledFlag?: number;
    sort?: number;
    nativeFlag?: number;
    spellNo?: string;
    wbNo?: string;
  }
}
