declare namespace ParamSetting {
  export interface ReqParams {
    paramNos?: string[];
    keyWord?: string;
    paramCategoryCode?: string;
    pageSize: number;
    pageNumber: number;
  }

  export interface ParamSettingInfo {
    paramSettingId: string;
    paramValue: string;
  }

  export interface ParamUseScopeInfo {
    paramUseScopeId: string;
    paramInfluenceScopeCode: string;
    paramInfluenceScopeDesc: string;
    paramInfluenceScopeValueId: string;
    paramInfluenceScopeValueDesc: string;
    paramSettingList: ParamSettingInfo[];
  }

  /**
   * 根据条件查询参数配置列表（定义态）
   */
  export interface ParamInfo {
    paramId: string;
    paramNo: string;
    paramDesc: string;
    detailDesc: string;
    valueTypeCode: string;
    valueTypeDesc: string;
    paramMultiValueFlag: string;
    paramCategoryCode: string;
    paramCategoryDesc: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId: string;
    createdUserName: string;
    createdAt: string;
    modifiedOrgLocationId: string;
    modifiedOrgLocationName: string;
    modifiedUserId: string;
    modifiedUserName: string;
    modifiedAt: string;
    codeSystemNo?: string;
    paramUseScopeList: ParamUseScopeInfo[];
  }

  export interface AddParamUseScopeInfo {
    paramInfluenceScopeCode: string;
    paramUseScopeId?: string;
    paramInfluenceScopeValueId?: string;
    paramValues:
      | string[]
      | {
          paramValue: string;
          paramSettingId?: string;
        }[];
    paramSettingList?: ParamSettingInfo[];
  }

  /**
   * 新增参数
   */
  export interface ReqAddParam {
    paramId?: string;
    paramNo: string;
    paramDesc: string;
    detailDesc?: string;
    valueTypeCode: string;
    paramMultiValueFlag: string;
    paramCategoryCode: string;
    paramUseScopeList?: AddParamUseScopeInfo[];
  }

  /**
   * 更新参数
   */
  export interface ReqUpdateParam extends ReqAddParam {
    paramId: string;
    paramUseScopeList: (AddParamUseScopeInfo & {
      paramUseScopeId?: string;
    })[];
  }

  /**
   * 根据参数编码查询参数配置（业务态）
   */
  export interface ResQueryParamListInfo {
    paramId: string;
    paramNo;
    string;
    paramDesc: string;
    detailDesc: string;
    valueTypeCode: string;
    paramMultiValueFlag: string;
    paramCategoryCode: string;
    paramSettingList: ParamSettingInfo[];
  }
}
