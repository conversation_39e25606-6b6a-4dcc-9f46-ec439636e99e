declare namespace BizLock {
  interface QueryParams {
    bizIdTypeCode?: string;
    bizId?: string;
    lockResourceId?: string;
    bizLockSceneCode?: string;
    validFlag?: number;
    lockUserId?: string;
    lockDate?: string[];
    lockBeginAt?: string;
    lockEndAt?: string;
  }

  interface BizLockInfo {
    bizLockId?: string;
    bizLockSceneCode?: string;
    bizLockSceneDesc?: string;
    bizIdTypeCode?: string;
    bizIdTypeDesc?: string;
    bizId?: string;
    lockResourceId?: string;
    lockAt?: string;
    lockUserId?: string;
    lockUserNo?: string;
    lockUserName?: string;
    validFlag?: number;
  }

  interface BizLockInfoItem {
    bizLockId?: string;
    bizLockSceneCode?: string;
    bizLockSceneDesc?: string;
    bizIdTypeCode?: string;
    bizIdTypeDesc?: string;
    bizId?: string;
    lockResourceId?: string;
    lockAt?: string;
    lockUserId?: string;
    lockUserNo?: string;
    lockUserName?: string;
    validFlag?: number;
  }

  interface UpsertBizLockParams {
    bizLockIds?: string[];
  }

  interface ProFormParams {
    bizLockSceneCode?: string;
    key?: string;
    lockDate?: string[];
    lockUserId?: string;
  }

  interface QueryUserListParams {
    type?: string;
    personId?: string;
    enabledFlag?: number;
    keyWord?: string;
    userNo?: string;
    hospitalId?: string;
    userJobCodes?: string[];
    pageNumber?: number;
    pageSize?: number;
  }
}
