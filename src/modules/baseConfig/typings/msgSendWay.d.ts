declare namespace MsgSendWay {
  interface QueryMsgSendWayList {
    keyWord?: string;
    msgSendWayId?: string;
    enabledFlag?: number | undefined;
  }

  interface UpsertMsgSendWayParams {
    msgSendWayId?: string | undefined;
    msgSendWayName?: string | undefined;
    interfaceId?: string | undefined;
    enabledFlag?: string | undefined;
    editable?: boolean;
    msgSendLimitCode?: string;
    timePeriod?: string;
    timeUnitCode?: string;
    msgCountLimit?: number;
  }

  interface AddMsgSendWayParams {
    msgSendWayName?: string;
    interfaceId?: string;
    enabledFlag?: string;
  }

  interface MsgSendWayList {
    msgSendWayId?: string;
    msgSendWayName?: string;
    interfaceId?: string;
    interfaceName?: string;
    enabledFlag?: string | number | undefined;
    createdAt?: string;
    createdUserId?: string;
    createdUserName?: string;
    modifiedAt?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    editable?: boolean;
    pageNumber?: number;
    pageSize?: number;
    msgSendLimitCode?: string;
    msgSendLimitCodeDesc?: string;
    timePeriod?: string;
    timeUnitCode?: string;
    timeUnitCodeDesc?: string;
    msgCountLimit?: number;
  }
}
