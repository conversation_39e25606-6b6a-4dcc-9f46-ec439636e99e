declare namespace AppConfigSetting {
  interface QueryAppConfigParams {
    keyWord?: string;
  }

  /// [1-10297-1]根据条件查询软件配置节点
  interface AppConfigKeyItem {
    appConfigKeyId?: string;
    appConfigKeyName?: string;
    dataTypeId?: string;
    dataTypeName?: string;
    createdUserName: string;
    createdAt: string;
    modifiedUserId: string;
    modifiedUserName: string;
    modifiedAt: string;
    editable?: boolean;
    codeSystemId?: string;
    codeSystemName?: string;
  }

  /// [1-10298-1/1-10299-1/1-10300-1]新增/编辑/删除软件配置节点
  interface AddOrUpdateAppConfigKey {
    appConfigKeyName?: string;
    dataTypeId?: string;
    codeSystemId?: string;
    appConfigKeyId?: string;
  }

  /// [1-10301-1] 根据条件查询软件配置项
  interface AppConfigList {
    appConfigItemId?: string;
    appConfigItemName?: string;
    sort?: number;
    createdUserName?: string;
    createdAt?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    editable?: boolean;
    appConfigKeyList?: AppConfigItem[];
  }

  interface AppConfigItem {
    appConfigSettingId?: string;
    sort?: number;
    appConfigKeyId?: string;
    appConfigKeyName?: string;
    dataTypeId?: string;
    dataTypeName?: string;
    editable?: boolean;
  }

  /// [1-10302-1]新增/编辑/删除软件配置项
  interface AddOrUpdateAppConfig {
    appConfigItemName?: string;
    appConfigItemId?: string;
  }

  /// [1-10305-1] 添加软件配置节点
  interface AppendAppConfigKey {
    appConfigItemId?: string;
    appConfigKeyIds?: string[];
  }

  /// [1-10306-1] 移除软件配置节点
  interface removeAppConfigKey {
    appConfigKeyName?: string;
    appConfigSettingId?: string;
  }
}
