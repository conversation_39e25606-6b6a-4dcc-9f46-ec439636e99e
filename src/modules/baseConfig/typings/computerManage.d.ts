declare namespace ComputerManage {
  // 查询地址请求参数
  interface QueryParams {
    computerTypeCode?: string;
    enabledFlag?: number;
    hospitalId?: string;
    keyWord?: string;
    monitorFlag?: number | undefined | string;
    pageNumber?: number;
    pageSize?: number;
  }

  // computerList
  interface ComputerInfo {
    computerId?: string;
    computerName?: string;
    computerDesc?: string;
    computerTypeCode?: string;
    computerTypeCodeDesc?: string;
    operatingSystemCode?: string;
    operatingSystemCodeDesc?: string;
    enabledFlag?: number;
    hospitalId?: string;
    hospitalName?: string;
    ipAddrList?: ComputerIPItem[];
    editable?: boolean;
  }

  // computer IP List
  interface ComputerIPItem {
    ipAddrId?: string;
    ipAddr?: string;
    ipAddrTypeCode?: string | number;
    ipAddrTypeCodeDesc?: string;
    ipAddrType?: string;
    editable?: boolean;
    computerId?: string;
  }

  // 新增计算机
  interface addComputerParams {
    computerId?: string;
    computerName?: string;
    computerDesc?: string;
    computerTypeCode?: string;
    machineTypeCode?: string;
    operatingSystemCode?: string;
    enabledFlag?: number;
    hospitalId?: string;
    ipAddr?: string;
    ipAddrTypeCode?: string | number;
    monitorFlag?: number | undefined;
    heartbeatInterval?: number | undefined;
    monitorTransferInterval?: number | undefined;
    ipAddrList?: ComputerIPItem[];
    bizTagList?: BizTagList[];
  }

  interface BizTagList {
    bizTagId?: string;
    tagId?: string;
    tagName?: string;
  }

  interface ComputerIndexQueryParams {
    keyWord?: string;
    enabledFlag?: number;
    computerResTypeCode?: string;
    computerIndexIds?: string[];
  }

  interface ComputerIndexInfo {
    computerIndexId: string;
    computerIndexName: string;
    computerResTypeCode: string;
    computerResTypeCodeDesc: string;
    criticalValueTypeCode: string;
    criticalValueTypeCodeDesc: string;
    criticalValue: number;
    enabledFlag: number;
    logTimeValue: number;
    logTimeValueDisplay: string;
    timeUnitCodeDesc: string;
    timeUnitCode: string;
  }

  interface UpsertComputerIndexParams {
    computerIndexId?: string;
    computerIndexName?: string;
    computerResTypeCode?: string;
    criticalValueTypeCode?: string;
    criticalValue?: number;
    enabledFlag?: number;
    timeUnitCode?: string;
    logTimeValue?: number;
  }

  interface SaveComputerIndexSetting {
    computerId?: string;
    ipAddrList?: ComputerIPItem[];
    computerName?: string;
    ipAddr?: string;
    computerDesc?: string;
    computerIndexSettingList?: ComputerIndexSettingList[];
  }

  interface ComputerIndexSettingList {
    computerIndexSettingId?: string;
    computerIndexId?: string;
    criticalValueTypeCode?: string;
    criticalValue?: number;
    logTimeValue?: number;
    timeUnitCode?: string;
    computerIndexName?: string;
    criticalValueTypeCodeDesc?: string;
    timeUnitCodeDesc?: string;
    computerId?: string;
    key?: string;
    editable?: boolean;
  }
}
