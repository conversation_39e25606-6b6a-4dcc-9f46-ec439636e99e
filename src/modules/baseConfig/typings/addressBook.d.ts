declare namespace AddressBook {
  interface QueryParams {
    contactsCategoryCode?: string;
    keyWord?: string;
    pageNumber?: number;
    pageSize?: number;
  }

  interface QueryAddressBookParams {
    keyWord?: string;
    contactsCategoryCode?: string;
    userId?: string;
    isFavorit?: number;
    pageNumber?: number;
    pageSize?: number;
  }

  interface ContactsList {
    contactsId?: string;
    contactsName?: string;
    sort?: number;
    userFavoritId?: string;
    contactList?: ContactList[];
  }

  interface ContactList {
    contactTypeCode?: string;
    contactTypeCodeDesc?: string;
    contactNo?: string;
  }

  interface UpsertUserFavorit {
    userId?: string;
    bizIdTypeCode?: string;
    bizId?: string;
    userFavoritId?: string;
  }
}
