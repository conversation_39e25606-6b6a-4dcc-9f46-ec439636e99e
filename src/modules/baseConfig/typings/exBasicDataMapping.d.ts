declare namespace ExBasicDataMapping {
  interface BasicDataMappingByHisIdsQueryParams {
    basicDataDictId: string;
    basicDataDictTypeId?: string;
    hisBasicDataTypeCode?: string;
    hisBasicDataIds: string[];
  }
  interface HisDataAndMappingQueryParams {
    basicDataDictId?: string;
    hisBasicDataTypeCode?: string;
    hisBasicDataSubTypeCode?: string;
    hisBasicDataIds?: string[];
    keyWord?: string;
    mappingStatusCode?: string;
    enabledFlag?: number;
    hospitalId?: string;
    pageNumber: number;
    pageSize: number;
  }
  interface MappingItem {
    approvalNo: string;
    basicDataDictTypeId: string;
    basicDataDictTypeName: string;
    basicDataExtInfo: string;
    basicDataId: string;
    basicDataMappingId: string;
    basicDataName: string;
    basicDataNo: string;
    basicDataSourceCode: string;
    basicDataSourceDesc: string;
    billingUnit: string;
    ceilingPrice: number;
    chargeItemLevelCode: string;
    chargeItemLevelDesc: string;
    connotation: string;
    createdAt: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId: string;
    createdUserName: string;
    dedicineDosageForm: string;
    description: string;
    eachDosage: string;
    enabledFlag: number;
    endAt: string;
    exceptContent: string;
    hisBasicDataId: string;
    hisBasicDataTypeCode: string;
    itemType: string;
    limitUseScope: string;
    mappingDataSourceCode: string;
    mappingDataSourceDesc: string;
    medicineCommonName: string;
    memo: string;
    modifiedAt: string;
    modifiedOrgLocationId: string;
    modifiedOrgLocationName: string;
    modifiedUserId: string;
    modifiedUserName: string;
    provider: string;
    selfPayProportion: string;
    spec: string;
    spellNo: string;
    startAt: string;
    unitPrice: number;
    usedFrequency: string;
    usedMethod: string;
    usingFlag: number;
    versionNo: string;
    wbNo: string;
  }
  interface HisDataAndMappingInfo {
    hisBasicDataId: string;
    mappingStatusCode: string;
    mappingStatusDesc: string;
    [key: string]: unknown;
    mappingList: MappingItem[];
  }

  interface BasicDataMappingAddParams {
    basicDataDictTypeId: string;
    basicDataId?: string;
    startAt: string;
    endAt: string;
    hisBasicDataTypeCode: string;
    hisBasicDataId: string;
    mappingDataSourceCode: string;
    basicDataNo?: string;
    basicDataName?: string;
  }
  interface HisBasicDataType {
    hisBasicDataTypeCode: string;
    hisBasicDataTypeDesc: string;
    hisBasicDataSubTypeList: {
      hisBasicDataSubTypeCode: string;
      hisBasicDataSubTypeDesc: string;
    }[];
    basicDataDictTypeList: {
      basicDataDictTypeId: string;
      basicDataDictTypeName: string;
    }[];
  }
}
