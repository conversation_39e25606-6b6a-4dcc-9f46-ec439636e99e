declare namespace ReasonManage {
  interface QueryParams {
    enabledFlag?: number;
    reasonUseScopeCode?: string;
    keyWord?: string;
  }
  interface ReasonInfo {
    displayFlag: number;
    editableFlag: number;
    enabledFlag: number;
    reason2ndName: string;
    reasonExtName: string;
    reasonId: string;
    reasonName: string;
    sort: number;
    reasonUseScopeList: ReasonUseScope[];
    isEdit: boolean;
    form: UpsertReasonParams;
  }
  interface ReasonUseScope {
    reasonUseScopeCode: string;
    reasonUseScopeDesc: string;
    reasonUseScopeId: string;
  }
  interface UpsertReasonParams {
    reasonId?: string;
    reasonName: string;
    reason2ndName: string;
    reasonExtName: string;
    displayFlag: number;
    editableFlag: number;
    enabledFlag: number;
    reasonUseScopeCodes: string[];
    sort?: number;
  }
}
