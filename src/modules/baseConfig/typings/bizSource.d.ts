declare namespace Origin {
  // 获取列表参数
  export interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    bizSourceIds?: string[];
    hospitalId?: string;
  }
  //列表返回参数
  export interface BizSourceInfo {
    hospitalId?: string;
    bizSourceId?: string;
    bizSourceCode?: string;
    bizSourceNo?: string;
    bizSourceName?: string;
    bizSourceAuxName?: string;
    bizSourceExtName?: string;
    bizSourceNameDisplay?: string;
    enabledFlag?: number;
    editableFlag?: number;
    sort?: number;
    spellNo?: string;
    wbNo?: string;
    dictEncResAccesserId?: string;
    dictEncResAccesserName?: string;
    createdOrgLocationId?: string;
    createdOrgLocationName?: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt?: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    options?: Options[];
    isCloudenv?: boolean;
  }
  // 排序更新参数
  export interface UpdateSortParams {
    bizSourceId?: string;
    sort?: number;
  }
  export interface BizSourceSortList {
    bizSourceSortList: UpdateSortParams[];
  }
  // 号源渠道列表参数
  export interface EncResAccesserParams
    extends Omit<QueryParams, 'bizSourceIds', 'hospitalId'> {
    dictEncResAccesserIds?: string[];
  }
  // 号源渠道列表返回参数
  export interface Options extends BizSourceInfo {
    dictEncResAccesser2ndName?: string;
    dictEncResAccesserExtName?: string;
    dictEncResAccesserNameDisplay?: string;
    label: string;
    value: string;
  }
}
