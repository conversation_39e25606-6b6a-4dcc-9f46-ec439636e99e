declare namespace InvoiceSetting {
  interface InvoiceSettingInfoItem {
    invoiceSettingId?: string;
    invoiceMediaTypeCode?: string;
    invoiceUsageCode?: string;
    invoiceUsageDescDisplay?: string;
    invoiceNoLength?: number;
    warningThreshold?: number;
    enabledFlag?: boolean;
  }

  interface UpsertInvoiceSettingParams {
    invoiceSettingId?: string;
    invoiceNoLength?: number;
    warningThreshold?: number;
  }

  interface ProFormParams {
    invoiceType?: string;
    invoiceUsage?: string;
  }

  interface QueryInvoiceSettingListParams {
    invoiceMediaTypeCode?: string;
    invoiceUsageCodes?: string[];
  }

  interface UpsertInvoiceSettingListParams {
    invoiceSettingId?: string;
    invoiceNoLength?: number;
    warningThreshold?: number;
  }
}
