export interface AppEnvDefineQueryParams {
  appEnvIds?: string[];
  keyWord?: string;
}

// 返回值
export interface AppEnvDefineAppEnvList {
  appEnvId?: string;
  appEnvName?: string;
  enabledFlag?: number;
  editableFlag?: number;
  createdUserId?: string;
  createdAt?: string;
  modifiedUserId?: string;
  modifiedAt?: string;
  editable?: boolean;
}

// UPSERT
export interface AppEnvDefineUpsertAppEnv {
  createdUserId?: string;
  createdAt?: string;
  modifiedUserId?: string;
  modifiedAt?: string;
  appEnvId?: string;
  appEnvName?: string;
  enabledFlag?: number;
  editableFlag?: number;
  editable?: boolean;
}
