declare namespace TagManage {
  /**
   * 查询分组列表入参数、
   */
  interface ReqQueryTagGroupList {
    tagGroupIds?: string[];
    enabledFlag?: number;
    keyWord?: string;
  }

  /**
   * 标签分组
   */
  interface TagGroup {
    tagGroupId: string;
    tagGroupName: string;
    tagGroup2ndName: string;
    tagGroupExtName: string;
    tagGroupNameDisplay: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
  }

  /**
   * 标签详情
   */
  interface TagInfo {
    tagId: string;
    tagName: string;
    tag2ndName: string;
    tagExtName: string;
    tagNameDisplay: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    sort: number;
  }

  /**
   * 查询标签入参
   */
  interface ReqQueryTagList {
    tagGroupIds?: string[];
    tagIds?: string[];
    enabledFlag?: number;
    keyWord?: string;
  }

  /**
   *新增标签分组入参
   */
  interface ReqAddTagGroup {
    tagGroupName: string;
    tagGroup2ndName: string;
    tagGroupExtName: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
  }

  /**
   * 新增标签入参
   */
  interface ReqAddTag {
    tagGroupId: string;
    tagName: string;
    tag2ndName?: string;
    tagExtName?: string;
    spellNo?: string;
    wbNo?: string;
    enabledFlag: number;
    sort: number;
  }

  /**
   * 根据标识修改标签分组
   */
  interface ReqUpdateTagGroup {
    tagGroupId: string;
    tagGroupName: string;
    tagGroup2ndName?: string;
    tagGroupExtName?: string;
    spellNo?: string;
    wbNo?: string;
    enabledFlag: number;
  }

  /**
   * 根据标识修改标签
   */
  interface ReqUpdateTag {
    tagId: string;
    tagName: string;
    tag2ndName?: string;
    tagExtName?: string;
    spellNo?: string;
    wbNo?: string;
    enabledFlag: number;
  }

  /**
   * 根据标识修改标签
   */
  interface ReqUpdateTagSort {
    tagList: {
      tagId: string;
      sort: number;
    }[];
  }

  /**
   *根据标识停启用标签分组
   */
  interface ReqUpdateTagGroupEnabledFlag {
    tagGroupId: string;
    enabledFlag: number;
  }

  /**
   *根据标识停启用标签
   */
  interface ReqUpdateTagEnabledFlag {
    tagId: string;
    enabledFlag: number;
  }
}
