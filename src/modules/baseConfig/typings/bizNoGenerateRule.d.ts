declare namespace BizNoGenerateRule {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    bizNoObjectCode?: string;
  }

  interface BizNoGenerateRuleInfo {
    bizNoGenerateRuleId: string;
    bizNoObjectCode: string;
    bizNoObjectDesc: string;
    createdAt: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId: string;
    createdUserName: string;
    enabledFlag: number;
    hospitalList: BizNoGenerateRuleHospital[];
    maxCharSize: string;
    modifiedAt: string;
    modifiedOrgLocationId: string;
    modifiedOrgLocationName: string;
    modifiedUserId: string;
    modifiedUserName: string;
    ruleDesc: string;
    ruleName: string;
  }

  interface BizNoGenerateRuleHospital {
    bizNoFragmentList: BizNoFragment[];
    hospitalId: string;
    hospitalName: string;
  }

  interface BizNoFragment {
    bizNoFragmentId?: string;
    currentValue?: string;
    fullChar?: string;
    fullTypeCode?: FULL_TYPE_CODE;
    fullTypeCodeDesc?: string;
    incStepValue?: number;
    maxValue?: string;
    resetTypeCode?: string;
    resetTypeCodeDesc?: string;
    sort: number;
    startValue?: string;
    valueTypeCode?: VALUE_TYPE_CODE;
    valueTypeCodeDesc?: string;
  }

  interface BizNoGenerateRuleUpsertParams {
    bizNoGenerateRuleId?: string;
    bizNoObjectCode?: string;
    enabledFlag: string;
    maxCharSize?: number;
    ruleName: string;
    ruleDesc?: string;
    recycleFlag?: number;
    bizNoFragmentList?: {
      bizNoFragmentId?: string;
      valueTypeCode: string;
      currentValue: string;
      startValue?: string;
      maxValue?: string;
      incStepValue?: number;
      fullTypeCode?: string;
      fullChar?: string;
      resetTypeCode?: string;
      sort: number;
      hospitalId?: string;
    }[];
  }
  interface BusinessRecycling {
    usedFlag: number;
  }
  interface RecyclingParameters {
    bizNoObjectCodes: string[];
    usedFlag: number;
    bizNoValue?: string;
  }
}
