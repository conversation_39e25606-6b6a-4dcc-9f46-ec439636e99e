declare namespace HolidaySetting {
  interface QueryHolidayParams {
    holidayYear?: number | null;
    deleteFlag?: number | null;
    dayDate?: string;
    queryOption?: number;
  }

  interface HolidayInfo {
    holidayId?: string;
    holidayCode?: string;
    holidayCodeDesc?: string;
    holidayYear?: string;
    holidayMonth?: string;
    deleteFlag?: boolean;
    editable?: boolean;
    holidaySettingList?: HolidaySettingList[];
  }

  interface HolidaySettingList {
    holidaySettingId?: string;
    dayDate?: string;
    holidayTypeCode?: string;
    holidayTypeCodeDesc?: string;
    deleteFlag?: boolean;
    editable?: boolean;
  }
}
