declare namespace RegisterType {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    regTypeIds?: string[];
    checkRegValidityPeriodFlag?: number;
  }
  interface RegTypeInfo {
    regTypeId: string;
    registrationTypeName?: string;
    registrationType2ndName?: string;
    registrationTypeExtName?: string;
    registrationTypeNameDisplay?: string;
    wbNo: string;
    spellNo: string;
    enabledFlag: number;
    defaultFlag: number;
    sort: number;
    validityPeriodCalcRuleCode: string;
    validityPeriodCalcRuleDesc: string;
    regValidityPeriod: number;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
  }
}
