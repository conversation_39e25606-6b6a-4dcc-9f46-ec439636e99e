declare namespace ExBasicDataDict {
  interface ExBasicDtaDictQueryParams {
    keyWord?: string;
    interfaceId?: string;
    enabledFlag?: number;
    standardFlag?: string;
  }
  interface ExBasicDtaDictInfo {
    basicDataDictId: string;
    basicDataDictName: string;
    enabledFlag: number;
    interfaceId: string;
    interfaceName: string;
    standardFlag: number;
  }
  interface ExBasicDtaDictUpsertParams {
    basicDataDictId?: string;
    basicDataDictName?: string;
    enabledFlag?: number;
    interfaceId?: string;
    standardFlag?: number;
    basicDataDictTypeList?: {
      basicDataDictTypeId?: string;
      basicDataDictTypeName?: string;
      basicDataDictTypeNo?: string;
      basicDataSourceCode?: string;
      dataDownloadStatusCode?: string;
      dataDownloadStatusMemo?: string;
      enabledFlag?: number;
      hisBasicDataTypeCode?: string;
      mappingFlag?: number;
      sort?: number;
      editable?: boolean;
    }[];
  }
  interface ExBasicDtaDictTypeQueryParams {
    basicDataDictId: string;
    basicDataDictTypeId?: string;
    enabledFlag?: number;
    keyWord?: string;
    hisBasicDataTypeCode?: string;
  }
  interface ExBasicDtaDictTypeInfo {
    basicDataDictTypeId?: string;
    basicDataDictTypeName?: string;
    basicDataDictTypeNo?: string;
    basicDataSourceCode?: BASIC_DATA_SOURCE_CODE;
    basicDataSourceDesc?: string;
    dataDownloadStatusCode?: string;
    dataDownloadStatusDesc?: string;
    dataDownloadStatusMemo?: string;
    enabledFlag?: number;
    hisBasicDataTypeCode?: string;
    hisBasicDataTypeDesc?: string;
    mappingFlag?: number;
    sort?: number;
  }
  interface ExBasicDtaQueryParams {
    basicDataDictId?: string;
    basicDataDictTypeId?: string;
    basicDataIds?: string[];
    enabledFlag?: number;
    hisBasicDataTypeCode?: string;
    keyword?: string;
    pageNumber: number;
    pageSize: number;
  }
  interface ExBasicDtaInfo {
    basicDataDictTypeId: string;
    basicDataDictTypeName: string;
    basicDataExtInfo: string;
    basicDataId: string;
    basicDataName: string;
    basicDataNo: string;
    basicDataSourceCode: BASIC_DATA_SOURCE_CODE;
    basicDataSourceDesc: string;
    memo: string;
    spellNo: string;
    versionNo: string;
    wbNo: string;
    /* 药品 & 项目 */
    ceilingPrice: number;
    chargeItemLevelCode: string;
    chargeItemLevelDesc: string;
    selfPayProportion: string;
    startAt: string;
    endAt: string;
    limitUseScope: string;
    /***/
    enabledFlag: number;
    /* 药品 */
    eachDosage: string;
    dedicineDosageForm: string;
    medicineCommonName: string;
    spec: string;
    usedMethod: string;
    usedFrequency: string;
    provider: string;
    unitPrice: number;
    approvalNo: string;
    /***/
    /* 项目 */
    billingUnit: string;
    description: string;
    connotation: string;
    exceptContent: string;
    itemType: string;
    /***/
  }
  interface SaveExBasicDataParams {
    basicDataDictTypeId: string;
    exBasicDataList: {
      basicDataExtInfo?: string;
      basicDataName: string;
      basicDataNo: string;
      basicDataSourceCode: string;
      enabledFlag: number;
      memo?: string;
      spellNo?: string;
      versionNo?: string;
      wbNo?: string;
    }[];
  }
  interface SaveExMedicineBasicDataParams {
    basicDataDictTypeId: string;
    exMedicineBasicDataList: {
      basicDataNo: string;
      basicDataName: string;
      enabledFlag: number;
      basicDataSourceCode: string;
      startAt: string;
      endAt: string;
      approvalNo?: string;
      basicDataExtInfo?: string;
      ceilingPrice?: number;
      chargeItemLevelCode?: string;
      dedicineDosageForm?: string;
      eachDosage?: string;
      limitUseScope?: string;
      medicineCommonName?: string;
      memo?: string;
      provider?: string;
      selfPayProportion?: string;
      spec?: string;
      spellNo?: string;
      unitPrice?: number;
      usedFrequency?: string;
      usedMethod?: string;
      versionNo?: string;
      wbNo?: string;
    }[];
  }
  interface SaveExServiceItemBasicDataParams {
    basicDataDictTypeId: string;
    exServiceItemBasicDataList: {
      basicDataNo: string;
      basicDataName: string;
      enabledFlag: number;
      basicDataSourceCode: string;
      startAt: string;
      endAt: string;
      basicDataExtInfo?: string;
      billingUnit?: string;
      ceilingPrice?: number;
      chargeItemLevelCode?: string;
      connotation?: string;
      description?: string;
      exceptContent?: string;
      limitUseScope?: string;
      memo?: string;
      selfPayProportion?: string;
      spellNo?: string;
      versionNo?: string;
      wbNo?: string;
    }[];
  }
}
