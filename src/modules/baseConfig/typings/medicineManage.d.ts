declare namespace Medicine {
  interface MedicineListQueryParams {
    keyWord?: string;
    hospitalId?: string;
    enabledFlag?: number;
    commodityId?: string;
    pageNumber: number;
    pageSize: number;
  }

  interface MedicinePackUnitItem {
    medicinePackUnitId: string;
    packUnitId: string;
    packUnitName: string;
    convertFactor: number;
  }

  interface HospitalMedicineItem {
    hospitalCommodityId: string;
    hospitalId: string;
    hospitalName: string;
    commodityCategoryId: string;
    commodityCategoryName: string;
    outCommodityCategoryId: string;
    outCommodityCategoryName: string;
    inCommodityCategoryId: string;
    inCommodityCategoryName: string;
    accCommodityCategoryId: string;
    accCommodityCategoryName: string;
    fncCommodityCategoryId: string;
    fncCommodityCategoryName: string;
    mrCommodityCategoryId: string;
    mrCommodityCategoryName: string;
    enabledFlag: number;
    encounterTypeCodes: string[];
    commodityPurchasePrice: number;
    price: number;
    medicineUseSceneXUnitList: {
      medicineUseSceneXUnitId: string;
      medicineUseSceneCode: string;
      medicineUseSceneDesc: string;
      packUnitId: string;
      packUnitName: string;
      editable?: boolean;
    }[];
  }

  interface MedicineListItem {
    // 药品商品
    commodityId: string;
    commodityNo: string;
    commodityName: string;
    commodity2ndName: string;
    commodityExtName: string;
    commodityNameDisplay: string;
    commoditySpec: string;
    unitId: string;
    unitName: string;
    spellNo: string;
    wbNo: string;
    memo: string;
    producedByOrgId: string;
    producedByOrgName: string;
    approvalNo: string;
    validPeriod: number;
    alreadyUseFlag: number;

    // 药品服务表
    msId: string;
    medicineSpec: string;
    miniUnitId: string;
    miniUnitName: string;
    doseFactor: number;
    doseUnitCode: string;
    doseUnitDesc: string;

    // 药品通用名表
    cadnId: string;
    cadn: string;
    cadnExt: string;
    cadn2nd: string;
    cadnEng: string;
    medicineTypeCode: string;
    medicineTypeDesc: string;
    dosageFormCode: string;
    dosageFormDesc: string;
    pharmacologyClassCode: string;
    pharmacologyClassDesc: string;
    specialManageMedicineCode: string;
    specialManageMedicineDesc: string;

    medicinePackUnitList: MedicinePackUnitItem[];
    hospitalMedicineList: HospitalMedicineItem[];
  }

  interface MedicineUpsertParams {
    commodityId?: string;
    commodityNo?: string;
    commodityName?: string;
    commodity2ndName?: string;
    commodityExtName?: string;
    commoditySpec?: string;
    unitId?: string;
    spellNo?: string;
    wbNo?: string;
    memo?: string;
    producedByOrgId?: string;
    approvalNo?: string;
    validPeriod?: number;
    medicineSpecId?: string;
    medicinePackUnitList?: {
      medicinePackUnitId?: string;
      packUnitId?: string;
      convertFactor?: number;
    }[];
    hospitalMedicineList?: {
      hospitalCommodityId?: string;
      hospitalId?: string;
      commodityCategoryId?: string;
      outCommodityCategoryId?: string;
      inCommodityCategoryId?: string;
      accCommodityCategoryId?: string;
      fncCommodityCategoryId?: string;
      mrCommodityCategoryId?: string;
      enabledFlag?: number;
      encounterTypeCodes?: string[];
      commodityPriceId?: string;
      commodityPurchasePrice?: number;
      price?: number;
      medicineUseSceneXUnitList?: {
        medicineUseSceneCode?: string;
        medicineUseSceneXUnitId?: string;
        packUnitId?: string;
      }[];
    }[];
  }
}
