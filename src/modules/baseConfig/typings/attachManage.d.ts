declare namespace AttachManage {
  interface FileParams {
    fileName?: string;
    fileContent?: string;
    filePageSize?: number;
    filePageNo?: number;
    filePath?: string;
    filePosition?: string;
  }

  interface QueryAttachByExample {
    keyWord?: string;
    attachScopeCode?: string;
    deleteFlag?: number;
    pageSize?: number;
    pageNumber?: number;
  }

  interface AttachList {
    attachId?: string;
    attachName?: string;
    attachPosition?: string;
    attachCategoryCode?: string;
    attachCategoryCodeDesc?: string;
    sort?: string;
    deleteFlag?: number;
    attachScopeList?: AttachScopeList[];
    attachNo?: string;
  }

  interface AttachScopeList {
    attachScopeId?: string;
    attachScopeCode?: string;
    attachScopeCodeDesc?: string;
  }

  interface AddAttachManage {
    attachScopeCodes?: string[];
    attachList?: AttachList[];
  }

  interface DeleteAttach {
    attachId?: string;
    deleteFlag?: number;
  }

  interface EditAttachManage {
    attachId?: string;
    attachName?: string;
    attachCategoryCode?: string;
    attachScopeList?: AttachScopeList[];
  }

  interface AttachScopeList {
    attachScopeId?: string;
    attachScopeCode?: string;
  }
}
