declare namespace DataTypeManage {
  interface QueryDataTypeManageList {
    dataTypeIds?: string[];
    keyWord?: string;
    enabledFlag?: number;
    dtUsageScopeCode?: string;
  }

  interface UpsertDataTypeManageParams {
    dataTypeId?: string;
    dataTypeName?: string;
    dataTypeDesc?: string;
    enabledFlag?: number;
    dtUsageScopeCodes?: string[];
    dtUsageScopeCodeList?: DtUsageScopeCodeList[];
    editable?: boolean;
  }

  interface AddDataTypeManageParams {
    dataTypeName?: string;
    dataTypeDesc?: string;
    enabledFlag?: number;
    dtUsageScopeCodes?: string[];
    editable?: boolean;
  }

  interface DataTypeManageList {
    dataTypeId?: string;
    dataTypeName?: string;
    dataTypeDesc?: string;
    enabledFlag?: number;
    sort?: number;
    dtUsageScopeCodes?: string[];
    dtUsageScopeCodeList?: DtUsageScopeCodeList[];
    editable: boolean;
    value?: string;
    label?: string;
  }

  interface DtUsageScopeCodeList {
    dtUsageScopeId?: string;
    dtUsageScopeCode?: string;
    dtUsageScopeCodeDesc?: string;
  }
}
