declare namespace InterfaceManage {
  /**
   * 接口列表入参
   */
  interface InterfaceListQueryParams {
    interfaceTypeCode?: string;
    invokeTypeCode?: string;
    hospitalId?: string;
    keyWord?: string;
    enabledFlag?: number;
  }

  /**
   * 接口列表请求
   */
  interface ReqQueryInterfaceListByExample {
    interfaceTypeCode?: string;
    invokeTypeCode?: string;
    hospitalId?: string;
    keyWord?: string;
    enabledFlag?: number;
  }

  /**
   * 医院列表
   */
  interface HospitalInfo {
    interfaceXHospitalId: string;
    hospitalId: string;
    hospitalName: string;
    beginDate: string;
    endDate: string;
  }

  /**
   * 新增接口列表
   */
  interface AddInterface {
    interfaceName: string;
    enabledFlag: number;
    interfaceTypeCode: string;
    invokeTypeCode: string;
    url?: sting;
    dllName?: string;
    namespace?: string;
    className?: string;
    methodName?: string;
    hospitalList?: Omit<HospitalInfo, 'interfaceXHospitalId', 'hospitalName'>[];
    builtInFlag: number; //内置接口
  }

  /**
   * 新增接口列表
   */
  interface UpdateInterface extends AddInterface {
    interfaceId: string;
  }

  /**
   * 接口列表
   */
  interface InterfaceInfo extends AddInterface {
    interfaceId: string;
    interfaceTypeDesc: string;
    invokeTypeDesc: string;
    hospitalList?: HospitalInfo[];
  }

  /**
   * 接口的交易
   */
  interface InterfaceTransactionInfo {
    transactionId?: string;
    transactionCode?: string;
    transactionDesc?: string;
    interfaceTypeCodeTranId?: string;
    enabledFlag?: number;
    formId?: string;
    formName?: string;
    tranXBizEventList?: {
      tranXBizEventId: string;
      transactionId: string;
      transactionCode: string;
      transactionDesc: string;
      bizEventId: string;
      bizEventName: string;
      communicateTypeCode: string;
      communicateTypeDesc: string;
    }[];
  }

  /**
   * 业务事件
   */
  interface TranXBizEventReqItem {
    tranXBizEventId: string;
    transactionId: string;
    transactionCode: string;
    transactionDesc: string;
    bizEventId: string;
    bizEventName: string;
    communicateTypeCode: string;
    communicateTypeDesc: string;
  }

  /**
   * 接口的配置
   */
  interface InterfaceConfigInfo {
    interfaceSettingId?: string;
    configKey?: string;
    configDesc?: string;
    configDefaultValue?: string;
    configValue?: string;
  }

  /**
   * 交易列表
   */
  interface InterfaceTypeCodeTranInfo {
    interfaceTypeCodeTranId: string;
    transactionCode: string;
    transactionDesc: string;
  }

  /**
   * 组织列表
   */
  interface OrganizationInfo {
    orgId: string;
    orgNo: string;
    orgName: string;
    org2ndName: string;
    orgExtName: string;
    orgNameDisplay: string;
  }
}
