declare namespace ContactInfo {
  interface QueryContactInfoList {
    msgSendWayId?: string;
    enabledFlag?: string;
    contactInfoIds?: string[];
    keyWord?: string;
    pageNumber?: number;
    pageSize?: number;
  }

  interface DeleteContactInfoParams {
    contactInfoId?: string;
  }

  interface UpsertContactInfoParams {
    contactInfoId?: string;
    contactNo?: string;
    msgSendWayId?: string;
    enabledFlag?: string;
  }

  interface AddContactInfoParams {
    contactNo?: string;
    msgSendWayId?: string;
    enabledFlag?: string;
  }

  interface ContactInfoList {
    contactInfoId?: string;
    contactNo?: string;
    msgSendWayId?: string;
    msgSendWayName?: string;
    enabledFlag?: string | number;
    createdAt?: string;
    createdUserId?: string;
    createdUserName?: string;
    modifiedAt?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    editable?: boolean;
  }
}
