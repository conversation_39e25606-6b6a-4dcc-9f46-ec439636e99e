declare namespace Interface {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    interfaceTypeCode?: string;
    invokeTypeCode?: string;
    hospitalId?: string;
  }
  interface InterfaceInfo {
    interfaceId?: string;
    interfaceName?: string;
    enabledFlag?: number;
    interfaceTypeCode?: string;
    interfaceTypeDesc?: string;
    invokeTypeCode?: string;
    invokeTypeDesc?: string;
    url?: string;
    dllName?: string;
    namespace?: string;
    className?: string;
    methodName?: string;
    hospitalList?: HospitalInfo[];
  }
  interface HospitalInfo {
    interfaceXHospitalId?: string;
    hospitalId?: string;
    hospitalName?: string;
    beginDate?: string;
    endDate?: string;
  }
}
