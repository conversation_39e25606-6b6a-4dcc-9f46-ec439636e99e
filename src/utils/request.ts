import { ElMessage } from 'element-sun';
import { translation } from '@sun-toolkit/micro-app';

/**
 * 通用的调用壳子的请求封装方法
 * @param methodName - API 方法名（作为字符串传递）
 * @param params - 请求参数，可以是对象或字符串
 * @returns 返回请求结果
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const pluginMainRequest = async <T>(methodName: string, params: any) => {
  try {
    const paramsJson = JSON.stringify(params);
    // 检查方法是否存在
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    if (typeof Api === 'undefined' || !Api.PluginMain) {
      ElMessage.error(translation('clientSide.validate', '当前非客户端环境'));
      return [
        {
          errorMessage: translation('clientSide.validate', '当前非客户端环境'),
        },
        { success: false },
      ];
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    if (!Api.PluginMain[methodName]) {
      ElMessage.error(
        `${methodName}${translation('methods.not.exist', '方法不存在')}`,
      );
      return [
        {
          errorMessage: `${methodName}${translation('methods.not.exist', '方法不存在')}`,
        },
        { success: false },
      ];
    }
    // 调用对应的 API 方法
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await Api.PluginMain[methodName](paramsJson);
    const { ...res } = JSON.parse(result);
    if (!res.success) {
      ElMessage.error(res.errorMessage);
    }
    return [res.success ? undefined : res, { ...res, data: res.data as T }];
  } catch (error) {
    console.log(error);
    throw error;
  }
};
