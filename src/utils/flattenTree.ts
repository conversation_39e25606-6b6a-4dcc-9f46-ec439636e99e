export function flattenTree(
  tree: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
): CodeRepositoryManageAPIDesign.ApiRecordParaList[] {
  console.log('flattenTree', tree);
  if (!Array.isArray(tree)) {
    console.error('传入的参数不是数组类型');
    return [];
  }
  let result: CodeRepositoryManageAPIDesign.ApiRecordParaList[] = [];
  tree.forEach((node) => {
    result.push(node);
    if (node.children && node.children.length > 0) {
      result = result.concat(flattenTree(node.children));
    }
  });
  return result;
}
