import {
  CERTIFICATE_TYPE_CODE,
  CERTIFICATE_TYPE_CODE_NAME,
  COMPONENT_CODE_NAME,
  CONTROL_TYPE_CODE_NAME,
  DEV_GROUP_CODE_NAME,
  ENABLED_FLAG,
  ES_PROVIDER_TYPE_CODE,
  FLAG,
  FLAG_STR,
  INTERNAL_HOSPITAL_ID,
  LAYOUT_TYPE_CODE,
  LAYOUT_TYPE_CODE_NAME,
  MENU_SOURCE_CODE,
  ORG_TYPE_CODE,
  ORG_TYPE_CODE_NAME,
  SEARCH_TYPE_CODE_NAME,
  STORY_SOURCE_CODE_NAME,
  TRIGGER_TYPE_CODE_NAME,
  COMMODITY_TYPE_CODE_NAME,
} from '@sun-toolkit/enums';

export {
  CERTIFICATE_TYPE_CODE,
  CERTIFICATE_TYPE_CODE_NAME,
  COMPONENT_CODE_NAME,
  CONTROL_TYPE_CODE_NAME,
  ENABLED_FLAG,
  ES_PROVIDER_TYPE_CODE,
  FLAG,
  FLAG_STR,
  INTERNAL_HOSPITAL_ID,
  LAYOUT_TYPE_CODE,
  LAYOUT_TYPE_CODE_NAME,
  ORG_TYPE_CODE,
  // CONTROL_TYPE_CODE,
  ORG_TYPE_CODE_NAME,
  SEARCH_TYPE_CODE_NAME,
  TRIGGER_TYPE_CODE_NAME,
  COMMODITY_TYPE_CODE_NAME,
  MENU_SOURCE_CODE,
  DEV_GROUP_CODE_NAME,
  STORY_SOURCE_CODE_NAME,
};
/**
 * 组织下级类型代码
 */
export const ORG_TYPE_LOW_LEVEL_MAP = {
  [ORG_TYPE_CODE.GROUP]: ORG_TYPE_CODE.HOSPITAL,
  [ORG_TYPE_CODE.HOSPITAL]: ORG_TYPE_CODE.DEPARTMENT,
  [ORG_TYPE_CODE.DEPARTMENT]: ORG_TYPE_CODE.DEPARTMENT,
  [ORG_TYPE_CODE.AREA]: '0',
};

/**
 * 组织上级类型代码
 */
export const ORG_TYPE_TOP_LEVEL_MAP = {
  [ORG_TYPE_CODE.GROUP]: '0',
  [ORG_TYPE_CODE.HOSPITAL]: ORG_TYPE_CODE.GROUP,
  [ORG_TYPE_CODE.DEPARTMENT]: ORG_TYPE_CODE.HOSPITAL,
  [ORG_TYPE_CODE.AREA]: ORG_TYPE_CODE.DEPARTMENT,
};

/** 主系统管理ID */
export const SYSTEM_ID = '1613076696913780736';

/**
 * 医院等级代码
 */
export const HOSPITAL_LEVEL_CODE = 'HOSPITAL_LEVEL_CODE';

/** 科室类型代码 */
export const DEPT_TYPE_CODE = 'DEPT_TYPE_CODE';

/**
 * 职工类型代码
 */
export const EMPLOYEE_TYPE_CODE = 'EMPLOYEE_TYPE_CODE';

/**
 * 职工状态代码
 */
export const USER_STATUS_CODE = 'USER_STATUS_CODE';

/**
 * 参数分类代码
 */
export const PARAM_CATEGORY_CODE = 'PARAM_CATEGORY_CODE';

/**
 * 值类型代码
 */
export const VALUE_TYPE_CODE = 'VALUE_TYPE_CODE';

/**
 * 用户类别代码
 */
export const USER_TYPE_CODE_NAME = 'USER_TYPE_CODE';

/**
 * 专业技术职务代码
 */
export const PROFESSION_TYPE_CODE = 'GB/T 8561-2001';

/** 性别代码名称 */
export const GENDER_CODE_NAME = 'GB/T 2261.1-2003';

/**
 * 附件类型代码
 */
export const ATTACH_TYPE_CODE = 'ATTACH_TYPE_CODE';
/**
 * 附件分类代码
 */
export const ATTACH_CATEGORY_CODE = 'ATTACH_CATEGORY_CODE';

/**
 * 附件应用范围代码
 */
export const ATTACH_SCOPE_CODE = 'ATTACH_SCOPE_CODE';
/**
 * 通讯录类别代码
 */
export const CONTACTS_CATEGORY_CODE = 'CONTACTS_CATEGORY_CODE';

/** 性别代码 */
export enum GENDER_CODE {
  /**
   * 男
   */
  MALE = 'g1',
  /**
   * 女
   */
  FEMALE = 'g2',
}

/**
 * 日结方式代码
 */
export const PAY_SUM_TYPE_CODE = 'PAY_SUM_TYPE_CODE';

/**
 * 用户岗位
 */
export const USER_JOB_TYPE_CODE = 'USER_JOB_CODE';

/**
 * 参数影响范围代码
 */
export const PARAM_INFLUENCE_SCOPE_CODE = 'PARAM_INFLUENCE_SCOPE_CODE';

/**
 * 数据检索业务标识类型代码
 */
export const DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME = 'DATA_SEARCH_BIZ_ID_TYPE_CODE';

/**
 * 数据检索业务标识类型代码
 */
export const CONTACT_BIZ_ID_TYPE_CODE = 'BIZ_ID_TYPE_CODE';

/**
 * 索引类型代码
 */
export const INDEX_TYPE_CODE_NAME = 'INDEX_TYPE_CODE';

/**
 * 日志类型代码
 */
export const LOG_TYPE_CODE_NAME = 'LOG_TYPE_CODE';

/**
 * 组件影响范围
 */
export const INFLUENCE_SCOPE_CODE_NAME = 'INFLUENCE_SCOPE_CODE';

/**
 * 就诊类型代码
 */
export const ENCOUNTER_TYPE_CODE_NAME = 'ENCOUNTER_TYPE_CODE';

/**
 * 药品应用场景代码
 */
export const MEDICINE_USE_SCENE_CODE_NAME = 'MEDICINE_USE_SCENE_CODE';

/**
 * 药品类型代码
 */
export const MEDICINE_TYPE_CODE_NAME = 'MEDICINE_TYPE_CODE';

/**
 * 药物剂型代码表
 */
export const CV08_50_002_NAME = 'CV08.50.002';

/**
 * 特殊管理药物代码(精麻毒放)
 */
export const SPECIAL_MANAGE_MEDICINE_CODE_NAME = 'SPECIAL_MANAGE_MEDICINE_CODE';

/**
 * 剂量单位代码
 */
export const DOSE_UNIT_CODE_NAME = 'DOSE_UNIT_CODE';

/**
 * 业务来源代码
 */
export const BIZ_SOURCE_CODE_NAME = 'BIZ_SOURCE_CODE';

/**
 * 医保费别代码
 */
export const MED_INSURANCE_CODE_NAME = 'MED_INSURANCE_CODE';

/**
 * 午别代码
 */
export const NOON_TYPE_CODE_NAME = 'NOON_TYPE_CODE';

/**
 * 支付方式代码
 */
export const PAY_WAY_CODE_NAME = 'PAY_WAY_CODE';

/**
 * 分单方式代码
 */
export const RECEIPT_SPLIT_TYPE_CODE_NAME = 'RECEIPT_SPLIT_TYPE_CODE';

/**
 * 数据源类型代码
 */
export const DATA_SOURCE_TYPE_CODE_NAME = 'DATA_SOURCE_TYPE_CODE';

/**
 * 模板规则代码
 */
export const RECEIPT_TEMPLATE_RULE_CODE_NAME = 'RECEIPT_TEMPLATE_RULE_CODE';

/**
 * 打印规则代码
 */
export const RECEIPT_PRINT_RULE_CODE_NAME = 'RECEIPT_PRINT_RULE_CODE';

/**
 * 单据打印类别代码
 */
export const RECEIPT_PRINT_CATEG_CODE_NAME = 'RECEIPT_PRINT_CATEG_CODE';

/**
 *接口类型代码
 */
export const INTERFACE_TYPE_CODE_NAME = 'INTERFACE_TYPE_CODE';

/**
 *交互方式代码
 */
export const INVOKE_TYPE_CODE_NAME = 'INVOKE_TYPE_CODE';
/** 联系方式类型代码 */
export const CONTACT_TYPE_CODE_NAME = 'CONTACT_TYPE_CODE';
/*
 * 接口交易代码
 */
export const TRANSACTION_CODE_NAME = 'TRANSACTION_CODE';
/*
 * 日志写入方类型代码
 */
export const LOG_WRITER_TYPE_CODE_NAME = 'LOG_WRITER_TYPE_CODE';
/*
 * 有效期计算规则代码
 */
export const VALIDITY_PERIOD_CALC_RULE_CODE_NAME =
  'VALIDITY_PERIOD_CALC_RULE_CODE';

/** 机器类型 */
export const MACHINE_TYPE_CODE = 'MACHINE_TYPE_CODE';
/** 计算机类型 */
export const COMPUTER_TYPE_CODE = 'COMPUTER_TYPE_CODE';
/** 操作系统 */
export const OPERATING_SYSTEM_CODE = 'OPERATING_SYSTEM_CODE';
/** IP类型 */
export const IP_TYPE_CODE = 'IP_TYPE_CODE';

/** 计算机资源类型代码 */
export const COMPUTER_RES_TYPE_CODE = 'COMPUTER_RES_TYPE_CODE';

/** 阈值类型代码 */
export const CRITICAL_VALUE_TYPE_CODE = 'CRITICAL_VALUE_TYPE_CODE';

/** 时间单位代码 */
export const TIME_UNIT_CODE = 'TIME_UNIT_CODE';

/** 数据类型代码 */
export const DATA_TYPE_CODE = 'DATA_TYPE_CODE';

/** 数据类型应用范围代码 */
export const DT_USAGE_SCOPE_CODE = 'DT_USAGE_SCOPE_CODE';

/** 团队人员类型代码  */
export const TEAMER_TYPE_CODE_NAME = 'TEAMER_TYPE_CODE';

/** 项目状态代码  */
export const PROJECT_STATUS_CODE_NAME = 'PROJECT_STATUS_CODE';

/** 消息限制类型 代码  */
export const MSG_SEND_LIMIT_CODE = 'MSG_SEND_LIMIT_CODE';

export enum BIZ_ID_TYPE_CODE {
  /**
   *编码体系
   */
  DICT_CODE_SYSTEM = 'DICT_CODE_SYSTEM',
  /**
   *值域
   */
  DICT_DATA_SETS = 'DICT_DATA_SETS',
  /**
   *应用系统
   */
  DICT_SYSTEM = 'DICT_SYSTEM',
  /**
   *应用菜单
   */
  DICT_MENU = 'DICT_MENU',
  /**
   *系统的菜单
   */
  DICT_SYS_X_MENU = 'DICT_SYS_X_MENU',
  /**
   *页面元素
   */
  DICT_PAGE_ELEMENT = 'DICT_PAGE_ELEMENT',
  /**
   *其他受限对象
   */
  DICT_LIMIT_OBJECT = 'DICT_LIMIT_OBJECT',
  /**
   * 表单
   */
  DICT_FORM = 'DICT_FORM',
  /**
   * 表单控件
   */
  DICT_FORM_CONTROL = 'DICT_FORM_CONTROL',
  /**
   * 读卡检索组件
   */
  DICT_SEARCH_COMPONENT = 'DICT_SEARCH_COMPONENT',
  /**
   * 读卡检索组件的检索方式
   */
  DICT_COMPT_X_SEARCH_TYPE = 'DICT_COMPT_X_SEARCH_TYPE',
  /**
   * 检索方式
   */
  DICT_SEARCH_TYPE = 'DICT_SEARCH_TYPE',
  /**
   * 参数配置
   */
  DICT_PARAMETER = 'DICT_PARAMETER',
  /**
   * 表单设计
   */
  DICT_FORM_DESIGN = 'DICT_FORM_DESIGN',

  /**
   * 数据窗口组件
   */
  DICT_DBGRID_COMPONENT = 'DICT_DBGRID_COMPONENT',
  /**
   * 原因
   */
  DICT_REASON = 'DICT_REASON',

  /**
   * 支付方式
   */
  DICT_PAY_WAY = 'DICT_PAY_WAY',
  /**
   * 消息发送渠道
   */
  DICT_MSG_SEND_WAY = 'DICT_MSG_SEND_WAY',
  /**
   * 联系方式信息
   */
  DICT_CONTACT_INFO = 'DICT_CONTACT_INFO',
  /**
   * 业务编码规则
   */
  DICT_BIZ_NO_GENERATE_RULE = 'DICT_BIZ_NO_GENERATE_RULE',
  /**
   * banner组件
   */
  DICT_BANNER_COMPONENT = 'DICT_BANNER_COMPONENT',
  /**
   * 外部目录
   */
  DICT_EX_BASIC_DATA_DICTIONARY = 'DICT_EX_BASIC_DATA_DICTIONARY',
  /**
   * 单据
   */
  DICT_RECEIPT = 'DICT_RECEIPT',
  /** 组织 */
  DICT_ORGANIZATION = 'DICT_ORGANIZATION',
  /** 业务事件 */
  DICT_BIZ_EVENT = 'DICT_BIZ_EVENT',
  /*
   * 接口管理
   */
  DICT_INTERFACE = 'DICT_INTERFACE',
  /** 医保费别 */
  DICT_MED_INSURANCE = 'DICT_MED_INSURANCE',
  /** 代码分支 */
  CODE_BRANCH = 'CODE_BRANCH',
  /** 代码仓库 */
  CODE_REPOSITORY = 'CODE_REPOSITORY',

  /** DML导出 API */
  DICT_API = 'DICT_API',

  /** DML导出 API分类 */
  DICT_API_CATEGORY = 'DICT_API_CATEGORY',
  /**
   * 标签分组
   */
  DICT_TAG_GROUP = 'DICT_TAG_GROUP',
  /**
   * 标签
   */
  DICT_TAG = 'DICT_TAG',
  /**
   * 系统定义
   */
  DICT_SYSTEM_DEF = 'DICT_SYSTEM_DEF',
  /** 学科专业分类 */
  DICT_ES_CATEGORY = 'DICT_ES_CATEGORY',
  /** 定时任务 */
  DICT_TIMED_TASK = 'DICT_TIMED_TASK',
  /* 工作项描述对应关系 */
  WORK_ITEM_X_DESC = 'WORK_ITEM_X_DESC',
  /* 计算机指标 */
  DICT_COMPUTER_INDEX = 'DICT_COMPUTER_INDEX',
  /* 配置项导出排序 */
  DICT_APP_CONFIG_ITEM = 'DICT_APP_CONFIG_ITEM',
  /* 配置节点导出 */
  DICT_APP_CONFIG_KEY = 'DICT_APP_CONFIG_KEY',
  /* 配置项里的子节点排序 */
  DICT_APP_CONFIG_SETTING = 'DICT_APP_CONFIG_SETTING',
  /* 应用环境管理 */
  DICT_APP_ENV = 'DICT_APP_ENV',
  /* 数据类型管理 */
  DICT_DATA_TYPE = 'DICT_DATA_TYPE',
  /* 提示词变量模板 */
  DICT_TEMP_VARIABLE = 'DICT_TEMP_VARIABLE',
  /* 产品列表导出 */
  DICT_PRODUCT = 'DICT_PRODUCT',
  /* 医院产品导出 */
  DICT_ORG_X_PROD = 'DICT_ORG_X_PROD',
  /* 医院产品定义 */
  DICT_ORG_X_PROD_DEF = 'DICT_ORG_X_PROD_DEF',
  /* 软件配置项维护 */
  DICT_CODE_REPO_X_APP_CONFIG = 'DICT_CODE_REPO_X_APP_CONFIG',
  /* DICT_COMPUTER*/
  DICT_COMPUTER = 'DICT_COMPUTER',
  /** 报销类型 */
  DICT_REIMBURSE_TYPE = 'DICT_REIMBURSE_TYPE',
  /** 病历质控节点类型 */
  DICT_MANAGE_OBJECT = 'DICT_MANAGE_OBJECT',
  /** 质控规则设置 */
  DICT_MANAGE_RULE = 'DICT_MANAGE_RULE',
}

/**
 * 启用停用options
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ENABLE_OPTIONS = (t: any) => {
  return [
    {
      label: t('global:all'),
      value: ENABLED_FLAG.ALL,
    },
    {
      label: t('global:enabled'),
      value: ENABLED_FLAG.YES,
    },
    {
      label: t('global:disabled'),
      value: ENABLED_FLAG.NO,
    },
  ];
};

/** 多值标志 非值域*/
export enum PARAM_MULTI_VALUE_FLAG {
  /**
   * 是
   */
  YES = '1',
  /**
   * 否
   */
  NO = '0',
}

/** 开关 非值域*/
export enum SWITCH_CODE {
  /**
   * 是
   */
  YES = '1',
  /**
   * 否
   */
  NO = '0',
}

/**学科专业 出诊标志枚举 */
export enum VISIT_MARK_VALUE_FLAG {
  /**
   * 是
   */
  YES = 1,
  /**
   * 否
   */
  NO = 0,
}

/** 配置影响范围代码*/
export enum INFLUENCE_SCOPE_CODE {
  /**
   * 通用
   */
  PUBLIC = '1',
  /**
   * 个人
   */
  PRiVATE = '2',
}

/**
 * 参数配置
 * 参数值类型 非值域
 */
export enum PARAMSETTING_DEFAULT_TYPE {
  /**
   *数字
   */
  NUMBER = '1',
  /**
   * 字符串
   */
  INPUT = '2',
  /**
   * 日期
   */
  DATE_PICKER = '3',

  /**
   * 时间
   */
  TIME_PICKER = '4',

  /**
   * 日期时间
   */
  DATE_TIME_PICKER = '5',

  /**
   * 开关
   */
  SWTICH = '6',

  /**
   * 值域
   */
  CODESYSTEM = '7',
}

export enum ENCOUNTER_TYPE_CODE {
  /**
   * 门诊
   */
  OUTPATIENT = '1',
  /**
   * 住院
   */
  HOSPITALIZATION = '2',
  /**
   * 急诊
   */
  EMERGENCY = '3',
  /**
   * 体检
   */
  CHECKUP = '4',
  /**
   * 加床
   */
  BED_OCCUPANCY = '5',
  /**
   * 新生儿
   */
  NEWBORN = '6',
}

/**
 * 数据检索业务标识类型代码
 */
export enum DATA_SEARCH_BIZ_ID_TYPE_CODE {
  /**
   * 值域
   */
  DICT_DATA_SETS = 'DICT_DATA_SETS',
  /** 地址 */
  DICT_ADDRESS = 'DICT_ADDRESS',
  /** 地址3级 */
  DICT_ADDRESS3 = 'DICT_ADDRESS3',
}

/**
 * 控件类型
 * @param 之前录入过 不全
 * @returns
 */
export enum CONTROL_TYPE_CODE {
  /**
   * 文本
   */
  TEXT = '1',
  /**
   * 文本框
   */
  INPUT = '2',
  /**
   * 下拉框
   */
  SELECT = '3',
  /**
   * 日期框框
   */
  DATEPICKER = '4',
  /**
   * 表格
   */
  TABLE = '5',
  /**
   * 文本域
   */
  TEXTAREA = '6',
  /**
   * 单选框
   */
  RADIO = '7',
  /**
   * 开关
   */
  SWITCH = '8',
  /**
   * 复选框
   */
  CHECKBOX = '9',
  /**
   * 分割线
   */
  DIVIDER = '10',
  /**
   * 分组
   */
  GROUP = '11',
  /**
   * 折叠面板
   */
  COLLAPSE = '12',

  /**
   * 集合
   */
  SET = '13',
}

export const COMPONENT_MAP = {
  [CONTROL_TYPE_CODE.SELECT]: 'select',
  [CONTROL_TYPE_CODE.INPUT]: 'input',
  [CONTROL_TYPE_CODE.DATEPICKER]: 'date-picker',
  [CONTROL_TYPE_CODE.GROUP]: 'group',
  [CONTROL_TYPE_CODE.TEXTAREA]: 'textarea',
  [CONTROL_TYPE_CODE.TEXT]: 'text',
  [CONTROL_TYPE_CODE.SWITCH]: 'switch',
  [CONTROL_TYPE_CODE.CHECKBOX]: 'checkbox',
  [CONTROL_TYPE_CODE.DIVIDER]: 'divider',
};

/**
 * 午别代码
 */
export enum NOON_TYPE_CODE {
  /**
   * 凌晨
   */
  MIDNIGHT = '1',
  /**
   * 上午
   */
  MORNING = '2',
  /**
   * 中午
   */
  NOON = '3',
  /**
   * 下午
   */
  AFTERNOON = '4',
  /**
   * 夜间
   */
  EVENING = '5',
}

/**
 * 数据源类型代码
 */
export enum DATA_SOURCE_TYPE_CODE {
  /**
   * 存储过程
   */
  STORAGE = '1',
}

/**
 * 分单方式代码
 */
export enum RECEIPT_SPLIT_TYPE_CODE {
  /**
   * 不分单
   */
  NO_SPLIT = '1',
}

/**
 * 模板规则代码
 */
export enum RECEIPT_TEMPLATE_RULE_CODE {
  /**
   * 自由选择
   */
  FREE_SELECT = '1',
}

/**
 * 打印规则代码
 */
export enum RECEIPT_PRINT_RULE_CODE {
  /**
   * 通用
   */
  GENERAL = '1',
  /**
   * 按菜单
   */
  MENU = '2',
}

/**
 * 单据打印类别代码
 */
export enum RECEIPT_PRINT_CATEG_CODE {
  /**
   * 打印
   */
  PRINT = '1',
  /**
   * 不打印
   */
  NO_PRINT = '2',
  /**
   * 预览
   */
  PREVIEW = '3',
  /**
   * 询问打印
   */
  ASK_PRINT = '4',
  /**
   * 仅开具不打印
   */
  ONLY_ISSUE_NO_PRINT = '5',
  /**
   * 询问开具不打印
   */
  ASK_ISSUE_NO_PRINT = '6',
}

export enum FORM_OPERATION_TYPE {
  /**
   * 设计
   */
  DESIGN = 1,
  /**
   * 预览
   */
  PREVIEW = 2,
  /**
   * 打印
   */
  PRINT = 3,
}

export enum PRINT_TYPE {
  /**
   * 打印
   */
  PRINT = 0,
  /**
   * 生成图片
   */
  GENERATE_IMAGE = 1,
}

/*
 * 交互方式代码
 */
export enum INVOKE_TYPE_CODE {
  /**
   * http
   */
  HTTP = '1',
  /**
   * 动态库
   */
  DLL = '2',
}

/** 联系方式代码 */
export enum CONTACT_TYPE_CODE {
  /** 移动电话 */
  MOBILE = '1',
  /** 固话 */
  LANDLINE = '2',
  /** 传真 */
  FAX = '3',
  /** 邮箱 */
  EMAIL = '4',
  /** 其他 */
  OTHER = '99',
}

/**
 * 代码仓库类型代码
 */
export const CODE_REPOSITORY_TYPE_CODE_NAME = 'CODE_REPOSITORY_TYPE_CODE';

/**
 * 默认密码参数编号
 */
export const DEFAULT_PASSWORD_PARAM_NO = '9002';

/**
 * 通讯方式代码
 */
export const COMMUNICATE_TYPE_CODE_NAME = 'COMMUNICATE_TYPE_CODE';

/**
 * 通讯方式代码
 */
export enum COMMUNICATE_TYPE_CODE {
  /**
   * 同步
   */
  SYNC = '1',
  /**
   * 异步
   */
  ASYNC = '2',
}

/** 用户岗位(职工类型)代码 */
export enum USER_JOB_CODE {
  /**
   * 医生
   */
  DOCTOR = '1',
  /**
   * 护士
   */
  NURSE = '2',
  /**
   * 医技人员
   */
  MEDICAL_TECHNICIAN = '3',
  /**
   * 检验人员
   */
  LAB_TECHNICIAN = '4',
  /**
   * 药剂师
   */
  PHARMACIST = '5',
  /**
   * 行政
   */
  ADMINISTRATOR = '6',
  /**
   * 财务人员
   */
  FINANCIAL_STAFF = '7',
  /**
   * 后勤
   */
  LOGISTICS = '8',
  /**
   * 其它
   */
  OTHER = '99',
  /**
   * 管理员
   */
  ADMIN = '0',
}

/**
 * 日志类型代码
 */
export enum LOG_TYPE_CODE {
  /**
   * 常规日志
   */
  NORMAL_LOG = '1',
  /**
   * 入出参日志
   */
  REQUEST_RESPONSE_LOG = '2',
  /**
   * 报错日志
   */
  ERROR_LOG = '3',
  /**
   * SQL日志
   */
  SQL_LOG = '4',
}

/**
 * 写入方类型代码
 */
export enum LOG_WRITER_TYPE_CODE {
  /**
   * 前端
   */
  FRONTEND = '1',
  /**
   * 后端
   */
  BACKEND = '2',
  /**
   * 客户端
   */
  CLIENT = '3',
}

/**
 * 业务锁场景代码
 */
export enum BIZ_LOCK_SCENE_CODE {
  /**
   * 门诊
   */
  OUTPATIENT = '1',
  /**
   * 挂号
   */
  REGISTRATION = '2',
  /**
   * 住院
   */
  HOSPITALIZATION = '3',
}

/**
 * 接口类型代码
 */
export enum INTERFACE_TYPE_CODE {
  /**
   * 医保接口
   */
  MEDICAL_INSURANCE = '1',
  /**
   * 支付接口
   */
  PAYMENT = '2',
  /**
   * 优惠接口
   */
  DISCOUNT = '3',
  /**
   * 票据接口
   */
  RECEIPT = '4',
  /**
   * 读卡接口
   */
  CARD_READER = '5',
  /**
   * 打印接口
   */
  PRINT = '6',
  /**
   * 业务事件监听接口
   */
  BUSINESS_EVENT = '7',
}

/**
 * 项目状态代码
 */
export enum PROJECT_STATUS_CODE {
  // 未启动
  UN_START = 'Unstart',
  // 上线准备中
  PREPARING = 'Preparing',
  // 已上线
  ONLINE = 'Online',
  // 已初验
  PREACCEPTED = 'Preaccepted',
  // 已终验
  ACCEPTED = 'Accepted',
  // 免费维保中
  FREE_MAINTAINING = 'FreeMaintaining',
  // 收费维保中
  CHARGE_MAINTAINING = 'ChargeMaintaining',
  // 已终止
  TERMINATED = 'Terminated',
}

/**
 * 有效期计算规则代码
 */
export enum VALIDITY_PERIOD_CALC_RULE_CODE {
  /** 按天 */
  DAY = '1',
  /** 按小时 */
  HOUR = '2',
}

/**
 * 诊断管理
 */
export const DIAG_TYPE_CODE_NAME = 'DIAG_TYPE_CODE';

/*
 * 用户类别代码
 */
export enum USER_TYPE_CODE {
  /** 自然人 */
  NATURAL_PERSON = '1',
  /** 机器 */
  MACHINE = '2',
}

/**
 * 临床服务类型代码
 */
export const CS_TYPE_CODE = 'CS_TYPE_CODE';

/**
 * 用户类别代码
 */
export enum BILLING_SETTINGS {
  /** 未设置 */
  NOT_SET = 0,
  /** 已设置 */
  ALREADY_SET = 1,
}

/**
 * 医疗服务计费方式代码
 */
export const MS_CHARGE_TYPE_CODE_NAME = 'MS_CHARGE_TYPE_CODE';

/**
 * 商品类型
 */

export enum PRODUCT_TYPE {
  /** 收费项目 */
  NAME = '1',
  /** 药品 */
  MEDICINE = '2',
}

/*
 * 商品费用分类方式代码
 */
export enum COMMODITY_CATEGORY_WAY_CODE {
  /** 门诊发票 */
  OUTPATIENT_INVOICE = '1',

  /** 住院发票 */
  INPATIENT_INVOICE = '2',

  /** 会计 */
  ACCOUNTING = '3',

  /** 财务 */
  FINANCE = '4',

  /** 病案 */
  MEDICAL_RECORD = '5',

  /** 基本 */
  BASIC = '0',
}

/**
 * 药品应用场景代码
 */
export enum MEDICINE_USE_SCENE_CODE {
  // 门诊单位
  OUTPATIENT_UNIT = '1',
  // 住院单位
  INPATIENT_UNIT = '2',
}

export const CODE_SYSTEM_OPTION = '1688093788049874944';

/**
 * 业务编码
 */
export const BIZ_NO_OBJECT_CODE_NAME = 'BIZ_NO_OBJECT_CODE';

export enum CELL_PHONE_NUMBER {
  PHONE = '1',
}

/**
 * 业务来源标识代码
 */
export const BIZ_SOURCE = 'BIZ_SOURCE';

/**
 * 号源渠道标识代码
 */
export const DICT_ENC_RES_ACCESSER = 'DICT_ENC_RES_ACCESSER';
