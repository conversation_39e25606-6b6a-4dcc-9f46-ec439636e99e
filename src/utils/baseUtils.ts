import { Base64 as Base64Library } from 'js-base64';

/**
 * Base64编码工具类（使用js-base64库）
 * 提供文本与Base64之间的双向转换
 */
export const Base64 = {
  /**
   * 加密：将普通文本转换为Base64编码
   * @param {string | object} data - 待加密的数据，可以是字符串或对象
   * @returns {string} Base64编码结果
   */
  encode(data: string | object): string {
    try {
      // 处理对象类型数据
      if (typeof data === 'object' && data !== null) {
        data = JSON.stringify(data);
      }

      // 使用库进行编码
      return Base64Library.encode(data as string);
    } catch (error) {
      console.error('Base64编码失败:', error);
      return '';
    }
  },

  /**
   * 解密：将Base64编码转换为普通文本
   * @param {string} base64 - 待解密的Base64字符串
   * @param {boolean} [asObject=false] - 是否解析为对象
   * @returns {string | object} 解密后的普通文本或对象
   */
  decode(base64: string, asObject = false): string | object {
    try {
      // 基本解码
      const decoded = Base64Library.decode(base64);

      // 如果需要解析为对象
      if (asObject) {
        return JSON.parse(decoded);
      }

      return decoded;
    } catch (error) {
      console.error('Base64解码失败:', error);
      return asObject ? {} : '';
    }
  },

  /**
   * URL安全的Base64编码
   * @param {string} text - 待编码的文本
   * @returns {string} URL安全的Base64字符串
   */
  urlEncode(text: string): string {
    return this.encode(text)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');
  },

  /**
   * 解码URL安全的Base64字符串
   * @param {string} safeBase64 - URL安全的Base64字符串
   * @returns {string} 解码后的文本
   */
  urlDecode(safeBase64: string): string {
    let str = safeBase64.replace(/-/g, '+').replace(/_/g, '/');
    while (str.length % 4) str += '=';
    return this.decode(str) as string;
  },
};
