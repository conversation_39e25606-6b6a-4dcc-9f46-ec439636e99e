/**
 * 导出一个用于获取科室数据的钩子函数
 * 该钩子函数用于异步获取科室列表，并管理科室列表的数据
 */
import { computed, ref, Ref } from 'vue';
import { queryDepartmentList } from '@/api/common';
import { queryFlatOrgList } from '@modules/system/api/org';
import { ENABLED_FLAG, ORG_TYPE_CODE } from '@/utils/constant';
export interface OrgDept {
  enabledFlag?: number;
  orgTypeCodes?: string[];
  parentOrgId?: string;
  keyWord?: string;
  orgId?: string;
  orgNo?: string;
}

export interface Dept {
  enabledFlag?: number;
  hospitalId: string;
  keyWord?: string;
}

export const useGetOrgData = (
  defaultOptions?: Ref<{ label: string; value: string }[]>,
) => {
  // 使用默认参数
  const defaultParams: OrgDept = {
    enabledFlag: ENABLED_FLAG.YES,
    orgTypeCodes: [ORG_TYPE_CODE.DEPARTMENT],
  };
  const loading = ref(false);
  // 科室列表
  const orgList = ref<Org.Item[]>([]);
  const optionData = computed(() => {
    if (orgList.value?.length) {
      return orgList.value;
    }
    return defaultOptions?.value || orgList.value;
  });
  /**异步获取部门列表 */
  const getFlatOrgList = async (params: OrgDept) => {
    loading.value = true;
    const [, res] = await queryFlatOrgList({ ...defaultParams, ...params });
    loading.value = false;
    orgList.value = res.data.map((item: Org.Item) => ({
      label: item.orgName,
      value: item.orgId,
      tenantId: item.tenantId,
    }));
  };

  return {
    loading,
    orgList: optionData,
    getFlatOrgList,
  };
};

export const useGetDeptData = () => {
  const loading = ref(false);
  // 科室列表
  const deptList = ref<Org.Item[]>([]);
  /**异步获取部门列表 */
  const getDeptList = async (params: Dept) => {
    loading.value = true;
    const [, res] = await queryDepartmentList({
      ...params,
      enabledFlag: ENABLED_FLAG.YES,
    });
    loading.value = false;
    if (res?.data) {
      deptList.value = res.data;
    }
  };

  return {
    loading,
    deptList,
    getDeptList,
  };
};
