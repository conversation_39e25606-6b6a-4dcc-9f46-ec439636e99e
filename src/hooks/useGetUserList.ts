import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
// import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { queryUserList } from '@/api/common';
import { UserReqItem, UserReqParams } from '@/api/types';

/** 获取人员信息 */
export function useGetUserInfo() {
  const loading = ref(false);
  const userList = ref<UserReqItem[]>([]);
  const getUserList = async (
    params: Omit<UserReqParams, 'pageNumber' | 'pageSize'> & {
      pageNumber?: number;
      pageSize?: number;
    },
  ) => {
    loading.value = true;
    const defaultParams = {
      pageNumber: 1,
      pageSize: 100,
      enabledFlag: FLAG.YES,
    };
    const [, res] = await queryUserList({
      ...defaultParams,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      userList.value = res.data ?? [];
    }
  };
  return {
    loading,
    userList,
    getUserList,
  };
}
