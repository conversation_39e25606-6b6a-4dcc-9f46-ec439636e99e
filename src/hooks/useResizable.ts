import { ref } from 'vue';

export function useResizable(
  initialWidth: number = 300,
  minWidth: number = 100,
  maxWidth: number = 600,
) {
  const width = ref<number>(initialWidth);
  const isResizing = ref<boolean>(false);

  const onMouseMove = (event: MouseEvent) => {
    if (isResizing.value) {
      document.body.style.userSelect = 'none'; // 禁止文本选择
      document.body.style.cursor = 'ew-resize'; // 改变鼠标样式
      event.preventDefault();
      const newWidth = event.clientX;
      width.value = Math.max(minWidth, Math.min(newWidth, maxWidth));
    }
  };

  const onMouseUp = () => {
    isResizing.value = false;
    document.body.style.userSelect = ''; // 恢复文本选择
    document.body.style.cursor = ''; // 恢复鼠标样式
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };

  const onMouseDown = () => {
    isResizing.value = true;

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  return { width, onMouseDown };
}
