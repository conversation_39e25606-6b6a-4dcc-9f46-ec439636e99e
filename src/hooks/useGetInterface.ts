import { ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { queryInterfaceListByExample } from '@/api/common';
import { InterfaceReqItem, InterfaceReqParams } from '@/api/types';
export function useGetInterface() {
  const loading = ref<boolean>(false);
  const interfaceList = ref<InterfaceReqItem[]>([]);
  const getInterfaceList = async (params: InterfaceReqParams) => {
    loading.value = true;
    const [, res] = await queryInterfaceListByExample({
      ...params,
      enabledFlag: FLAG.YES,
    });
    loading.value = false;
    if (res) {
      interfaceList.value = res?.data ?? [];
    }
  };
  return {
    loading,
    interfaceList,
    getInterfaceList,
  };
}
