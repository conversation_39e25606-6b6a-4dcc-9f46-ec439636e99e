import { useFetchDataset } from 'sun-biz';
import { CodeSystemType } from '@/typings/codeManage';
import { computed } from 'vue';
export function useGetDMLList() {
  const dataSetList = useFetchDataset([CodeSystemType['HBI000.4001']]);

  const dmlList = computed(() =>
    (dataSetList?.value?.[CodeSystemType['HBI000.4001']] || []).map((item) => {
      return {
        value: item.dataValueNo,
        label: item.dataValueCnName,
      };
    }),
  );
  return dmlList;
}
