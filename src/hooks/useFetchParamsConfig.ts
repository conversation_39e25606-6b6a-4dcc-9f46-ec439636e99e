import { onBeforeMount, reactive } from 'vue';
import { dictRequest } from '@sun-toolkit/request';

interface ParamSettingItem {
  paramSettingId: string;
  paramValue: string;
}

interface ParamResItem {
  paramNo: string;
  paramSettingList: ParamSettingItem[];
}

interface OptionType {
  hospitalId: string;
  paramNos: readonly string[];
  callback?: (data: Record<string, ParamSettingItem[]>) => void;
}
/**
 * [1-10012-1]获取值域列表
 * @param data
 * @returns
 */
export const queryParamListByNos = (params: {
  paramNos: readonly string[];
  hospitalId: string;
}) => {
  return dictRequest<ParamResItem[]>('/parameter/queryParamListByNos', params);
};

export function useFetchParamsConfig(options: OptionType) {
  const { hospitalId, paramNos, callback } = options;
  const paramsConfig = reactive<Record<string, ParamSettingItem[]>>({});

  onBeforeMount(async () => {
    if (!hospitalId) return;
    const [, res] = await queryParamListByNos({
      hospitalId,
      paramNos,
    });
    if (res?.data) {
      res.data.reduce((acc: Record<string, ParamSettingItem[]>, cur) => {
        paramsConfig[cur.paramNo] = acc[cur.paramNo] = cur.paramSettingList;
        return acc;
      }, {});
      if (callback) {
        callback(paramsConfig);
      }
    }
  });
  return paramsConfig;
}

export default useFetchParamsConfig;
