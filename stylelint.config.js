module.exports = {
  extends: [
    'stylelint-config-standard',
    'stylelint-config-standard-scss',
    'stylelint-config-recommended-vue/scss',
    'stylelint-config-recess-order',
  ],
  rules: {
    'comment-empty-line-before': ['never'],
    'selector-class-pattern': [
      '^[a-z]([-]?[a-z0-9]+)*(__[a-z0-9]+)*(-[a-z0-9]+)*$',
      {
        message: 'Class names should follow BEM or kebab-case conventions',
      },
    ],
  },
};
