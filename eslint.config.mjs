import js from '@eslint/js';
import globals from 'globals';
import ts from 'typescript-eslint';
import pluginVue from 'eslint-plugin-vue';
import eslintPluginPrettierRecommended  from 'eslint-plugin-prettier/recommended';
import tsEslintPlugin from '@typescript-eslint/eslint-plugin';
import tsEslintParser from '@typescript-eslint/parser';
import vueParser from 'vue-eslint-parser';

const globalsConfig = [{ 
  languageOptions: { globals: { ...globals.browser, ...globals.node} },
  rules: {
    "no-var": "error", // 要求使用 let 或 const 而不是 var
    "semi": ['error', 'always'],
    "quotes": ['error', 'single']
  }
}]
const customTsConfig = [{
  name: 'typescript-eslint/base',
  files: ['**/*.{ts,tsx}'],
  languageOptions: {
    parser: tsEslintParser,
    ecmaVersion: 2020,
    sourceType: "module"
  },
  plugins: {
    // ts 语法特有的规则，例如泛型
    '@typescript-eslint': tsEslintPlugin,
  },
  // custom rules or use recommended rules
  rules: {
    ...tsEslintPlugin.configs.recommended.rules,
    // '@typescript-eslint/ban-types': 2,
    '@typescript-eslint/no-confusing-non-null-assertion': 2,
    'no-undef': 'off'
  },
}]

const customVueConfig = [{
  name: 'vue-eslint',
  plugins: eslintPluginPrettierRecommended.plugins,
  languageOptions: {
    parser: vueParser,
    parserOptions: {
      parser: tsEslintParser, // 在vue文件上使用ts解析器
      sourceType: 'module',
      ecmaFeatures: {
        jsx: true,
      },
    }
  },
  rules: {
    ...pluginVue.configs['vue3-strongly-recommended'].rules,
    ...eslintPluginPrettierRecommended.rules,
    'vue/multi-word-component-names': 'off',
    "no-undef": 'off',
    "semi": ['error', 'always'],
    "quotes": ['error', 'single']
  }
}]

export default [
  {ignores: ['dist', 'script', 'node_modules/*', '*.config.js', 'rsbuild.config.ts', 'eslint.config.mjs']},
  js.configs.recommended,
  ...pluginVue.configs["flat/strongly-recommended"],
  ...globalsConfig,
  ...ts.configs.recommended,
  ...customTsConfig,
  ...customVueConfig,
];
