{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "ES2020"], "module": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "noEmit": true, "strict": true, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "moduleResolution": "node", "useDefineForClassFields": true, "allowImportingTsExtensions": true, "baseUrl": "./", "paths": {"@": ["src"], "@/*": ["src/*"], "@modules": ["src/modules"]}, "types": ["node"], "typeRoots": ["./node_modules/@types", "src/typings/", "src/modules/**/typings"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/*.vue", "src/**/*.vue", "build/**/*.ts", "build/**/*.d.ts"], "exclude": ["node_modules", "dist", "**/*.js", "eslint.config.mjs"]}