// const colors = require('tailwindcss/colors')
/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: ['./src/**/*.{vue,js,ts,jsx,tsx}', 'node_modules/sun-biz/dist/*.js'],
  safelist: [
    // 动态grid列数
    {
      pattern: /^grid-cols-(([1-9]|1[0-6]))$/,
    },
    // 动态列跨度
    {
      pattern: /^col-span-(([1-9]|1[0-6]))$/,
    },
  ],
  theme: {
    fontSize: {
      sm: '0.75rem',
      base: '0.875rem',
      lg: '1rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.75rem',
      '4xl': '2rem',
      '5xl': '2.5rem',
    },
    extend: {
      colors: {
        primary: '#2468DA',
      },
      height: {
        128: '32rem',
        144: '36rem', // 添加自定义高度 144，等于 36rem
      },
    },
  },
  plugins: [],
};
