{"printWidth": 80, "tabWidth": 2, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "rangeStart": 0, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "disableLanguages": ["vue"], "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": true, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "plugins": ["prettier-plugin-tailwindcss"]}